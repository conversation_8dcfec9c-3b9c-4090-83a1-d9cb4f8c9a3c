<?php

namespace app\admin\controller\user;

use app\common\controller\Backend;
use think\Db;

/**
 * 消费记录
 * @icon fa fa-user
 */
class Money extends Backend
{
    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            $admin = get_admin_info();

            if ($admin['group_id'] == get_agent_group_id() && !isset($search_arr['user_id'])) {
                // 使用更规范的复合查询结构
                $where_arr[] = Db::raw('(u.p_id = ' . $admin['admin_id'] . ' OR l.user_id = ' . $admin['user_id'] . ')');
            }

            if ($search_arr) {
                //状态
                if (isset($search_arr['status'])){
                    $search_arr['l.status'] = $search_arr['status'];
                    unset($search_arr['status']);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('user_money_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->where($where)
                ->where($where_arr)
                ->count();

            $list = Db::name('user_money_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->where($where)
                ->where($where_arr)
                ->order('l.id', $order)
                ->field('l.*,u.nickname,u.mobile')
                ->limit($offset, $limit)
                ->select();

//            foreach ($list as &$v) {
//
//            }

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }



}
