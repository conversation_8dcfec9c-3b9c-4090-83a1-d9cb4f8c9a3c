<!DOCTYPE html>
<html>
<head>
    <title>游戏流程测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>游戏流程测试</h1>
    <div id="log"></div>
    
    <script>
        const log = document.getElementById('log');
        
        function addLog(message) {
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `[${time}] ${message}<br>`;
            console.log(`[${time}] ${message}`);
        }
        
        // 测试参数
        const gameSn = '00817784';
        const userId = 9;
        const coin = 19933;
        const number = 1;
        let gameLogId = null;
        let initialCoin = coin;
        let currentCoin = coin;
        let gameOperationCount = 0;
        let gameOperationTimer = null;
        
        addLog('=== 游戏流程测试开始 ===');
        addLog(`测试游戏: ${gameSn}`);
        addLog(`测试用户: ${userId}`);
        addLog(`初始金币: ${initialCoin}`);
        addLog(`测试座位: ${number}`);
        
        // 创建WebSocket连接
        const ws = new WebSocket('ws://127.0.0.1:2346');

        // 监控连接状态
        const statusMonitor = setInterval(() => {
            addLog(`WebSocket状态: ${ws.readyState} (0=CONNECTING, 1=OPEN, 2=CLOSING, 3=CLOSED)`);
            if (ws.readyState === 3) {
                clearInterval(statusMonitor);
            }
        }, 1000);
        
        ws.onopen = function() {
            addLog('✓ WebSocket连接成功');

            // 延迟一下再发送注册消息，确保连接稳定
            setTimeout(() => {
                // 发送注册消息
                const registerMsg = {
                    type: 'register',
                    client_type: 'uniapp',
                    gameSn: gameSn
                };
                addLog(`→ 发送注册: ${JSON.stringify(registerMsg)}`);
                ws.send(JSON.stringify(registerMsg));
            }, 100);
        };
        
        ws.onmessage = function(event) {
            const message = JSON.parse(event.data);
            // 解码中文字符
            let decodedData = event.data;
            try {
                decodedData = JSON.stringify(message, null, 0);
            } catch (e) {
                // 如果解析失败，使用原始数据
            }
            addLog(`← 收到消息: ${decodedData}`);
            
            switch (message.type) {
                case 'register_back':
                    if (message.code === 200) {
                        addLog('✓ 注册成功');
                        
                        // 延迟2秒后发送开始游戏
                        setTimeout(() => {
                            const startMsg = {
                                type: 'startGame',
                                number: number,
                                userId: userId,
                                coin: coin
                            };
                            addLog(`→ 发送开始游戏: ${JSON.stringify(startMsg)}`);
                            ws.send(JSON.stringify(startMsg));
                        }, 2000);
                    } else {
                        addLog(`✗ 注册失败: ${message.message || '未知错误'}`);
                    }
                    break;
                    
                case 'startGame_back':
                    if (message.code === 200) {
                        addLog('✓ 开始游戏成功');
                        gameLogId = message.data?.gameLogId;
                        addLog(`游戏记录ID: ${gameLogId}`);

                        // 开始游戏操作循环：每2秒发送一次按键，共10次
                        addLog('开始游戏操作循环 (每2秒按键一次，共10次)');
                        gameOperationCount = 0;
                        gameOperationTimer = setInterval(() => {
                            if (gameOperationCount < 10) {
                                // 随机选择不同的投币按钮进行测试
                                const buttons = [
                                    {id: 51, name: '投币', value: 1},
                                    {id: 52, name: '投5币', value: 5},
                                    {id: 53, name: '投30币', value: 30},
                                    {id: 54, name: '投100币', value: 100}
                                ];
                                const randomButton = buttons[gameOperationCount % buttons.length];

                                const opMsg = {
                                    type: 'command',
                                    number: number,
                                    status: randomButton.id
                                };
                                addLog(`→ 发送游戏操作 ${gameOperationCount + 1}/10 (${randomButton.name}): ${JSON.stringify(opMsg)}`);
                                ws.send(JSON.stringify(opMsg));
                                gameOperationCount++;
                            } else {
                                // 停止游戏操作，等待30秒后结束游戏
                                clearInterval(gameOperationTimer);
                                addLog('游戏操作完成，等待30秒后结束游戏...');
                                setTimeout(() => {
                                    const endMsg = {
                                        type: 'endGame',
                                        gameLogId: gameLogId
                                    };
                                    addLog(`→ 发送结束游戏: ${JSON.stringify(endMsg)}`);
                                    ws.send(JSON.stringify(endMsg));
                                }, 30000);
                            }
                        }, 2000);
                    } else {
                        addLog(`✗ 开始游戏失败: ${message.message || '未知错误'}`);
                        ws.close();
                    }
                    break;
                    
                case 'command_back':
                    if (message.code === 200) {
                        addLog(`✓ 游戏操作成功 (${gameOperationCount}/10)`);
                    } else {
                        addLog(`✗ 游戏操作失败: ${message.message || '未知错误'}`);
                    }
                    break;
                    
                case 'endGame_back':
                    if (message.code === 200) {
                        addLog('✓ 结束游戏成功');
                        // 显示金币变化
                        const finalCoin = message.data?.coin || currentCoin;
                        const coinChange = finalCoin - initialCoin;
                        addLog(`=== 金币对比 ===`);
                        addLog(`初始金币: ${initialCoin}`);
                        addLog(`最终金币: ${finalCoin}`);
                        addLog(`金币变化: ${coinChange > 0 ? '+' : ''}${coinChange}`);
                        addLog('=== 测试完成，关闭连接 ===');
                        ws.close();
                    } else {
                        addLog(`✗ 结束游戏失败: ${message.message || '未知错误'}`);
                        ws.close();
                    }
                    break;
                    
                case 'heartbeat_back':
                    addLog('✓ 心跳响应正常');
                    break;
                    
                case 'message_received':
                    addLog('✓ 消息已接收');
                    break;

                case 'update_game':
                    if (message.code === 200) {
                        addLog(`✓ 游戏状态更新: ${message.msg || '成功'}`);
                        // 更新当前金币数
                        if (message.data && message.data.coin !== undefined) {
                            currentCoin = message.data.coin;
                            addLog(`当前金币: ${currentCoin}`);
                        }
                    } else {
                        addLog(`✗ 游戏状态更新失败: ${message.msg || '未知错误'}`);
                    }
                    break;

                default:
                    addLog(`收到其他消息类型: ${message.type}`);
            }
        };
        
        ws.onclose = function(event) {
            addLog(`连接已关闭 - Code: ${event.code}, Reason: ${event.reason}, WasClean: ${event.wasClean}`);
            addLog('=== 测试结束 ===');
        };

        ws.onerror = function(error) {
            addLog(`✗ WebSocket错误: ${JSON.stringify(error)}`);
            addLog(`WebSocket状态: ${ws.readyState}`);
        };
        
        // 设置超时
        setTimeout(() => {
            addLog('测试超时，强制关闭连接');
            ws.close();
        }, 60000);
    </script>
</body>
</html>
