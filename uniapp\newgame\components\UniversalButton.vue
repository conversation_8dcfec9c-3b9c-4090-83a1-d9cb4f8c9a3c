<template>
  <button 
    :class="['universal-button', customClass, { 'disabled': disabled }]"
    :disabled="disabled"
    @click="handleClick"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchCancel"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseLeave"
  >
    <slot></slot>
  </button>
</template>

<script>
export default {
  name: 'UniversalButton',
  props: {
    // 自定义CSS类
    customClass: {
      type: String,
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否支持长按
    longPress: {
      type: Boolean,
      default: false
    },
    // 长按延迟时间（毫秒）
    longPressDelay: {
      type: Number,
      default: 500
    },
    // 长按重复间隔（毫秒）
    longPressInterval: {
      type: Number,
      default: 150
    }
  },
  
  data() {
    return {
      isPressed: false,
      longPressTimer: null,
      repeatTimer: null,
      isLongPressing: false
    }
  },
  
  methods: {
    // 统一的点击处理
    handleClick(event) {
      if (this.disabled) return
      
      // 防止长按时的点击事件
      if (this.isLongPressing) {
        this.isLongPressing = false
        return
      }
      
      console.log('按钮点击 (click):', event.type)
      this.$emit('click', event)
    },
    
    // 触摸/鼠标按下开始
    handleTouchStart(event) {
      this.handlePressStart(event, 'touch')
    },
    
    handleMouseDown(event) {
      this.handlePressStart(event, 'mouse')
    },
    
    // 统一的按下处理
    handlePressStart(event, type) {
      if (this.disabled) return
      
      console.log('按钮按下 (' + type + '):', event.type)
      this.isPressed = true
      this.isLongPressing = false
      
      this.$emit('pressStart', event)
      
      // 如果支持长按，启动长按定时器
      if (this.longPress) {
        this.longPressTimer = setTimeout(() => {
          this.isLongPressing = true
          console.log('长按开始')
          this.$emit('longPressStart', event)
          
          // 启动重复定时器
          this.repeatTimer = setInterval(() => {
            this.$emit('longPressRepeat', event)
          }, this.longPressInterval)
        }, this.longPressDelay)
      }
    },
    
    // 触摸/鼠标释放
    handleTouchEnd(event) {
      this.handlePressEnd(event, 'touch')
    },
    
    handleTouchCancel(event) {
      this.handlePressEnd(event, 'touch-cancel')
    },
    
    handleMouseUp(event) {
      this.handlePressEnd(event, 'mouse')
    },
    
    handleMouseLeave(event) {
      this.handlePressEnd(event, 'mouse-leave')
    },
    
    // 统一的释放处理
    handlePressEnd(event, type) {
      if (this.disabled) return
      
      console.log('按钮释放 (' + type + '):', event.type)
      this.isPressed = false
      
      // 清除定时器
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer)
        this.longPressTimer = null
      }
      
      if (this.repeatTimer) {
        clearInterval(this.repeatTimer)
        this.repeatTimer = null
      }
      
      // 发送事件
      this.$emit('pressEnd', event)
      
      if (this.isLongPressing) {
        this.$emit('longPressEnd', event)
      }
    }
  },
  
  beforeDestroy() {
    // 清理定时器
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer)
    }
    if (this.repeatTimer) {
      clearInterval(this.repeatTimer)
    }
  }
}
</script>

<style scoped>
.universal-button {
  border: none;
  outline: none;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  transition: all 0.2s ease;
}

.universal-button:active {
  transform: scale(0.95);
}

.universal-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.universal-button.disabled:active {
  transform: none;
}
</style>
