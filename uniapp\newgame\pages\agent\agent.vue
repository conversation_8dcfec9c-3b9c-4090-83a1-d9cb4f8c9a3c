<template>
  <view class="mine-container">
    <!-- 顶部用户信息区 -->
    <view class="top-bar">
      <view class="user-info-wrap">
        <view class="avatar-wrap">
          <view class="avatar-level-wrap large">
            <image class="avatar small" :src="userInfo.avatar || defaultAvatar" />
            <image class="level-avatar-bg large"
              :src="userInfo.level_avatar || '/static/level_avatar.png'" />
          </view>
        </view>
        <view class="user-detail" @tap="handleUserInfoBar">
          <view class="nickname-row">
            <view class="nickname">{{ userInfo.nickname || '未登录' }}</view>
          </view>
          <view class="level-assets-row" v-if="userInfo.nickname">
            <view class="id-bg-wrap">
              <text class="id-text">代理ID: {{ userInfo.agent_id }}</text>
            </view>
			<view class="level-badge">
				<image class="level-icon" src="/static/agent/agent.png" />
				<text class="level-name">代理商</text>
			</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 代理功能区 -->
    <view class="section-card">
      <view class="section-title-wrap">
        <image class="title-bg" src="/static/mine/mine.png" />
        <text class="section-title">我的账号</text>
      </view>
      <view class="section-content">
        <view class="entry-item" v-for="(item, idx) in accountEntries" :key="item.text"
          :style="{width: (100/accountEntries.length)+'%'}" @tap="item.action">
          <image class="entry-icon" :src="item.icon" />
          <text>{{ item.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
	import request from '@/utils/request.js'
	import config from '@/config.js'
export default {
  data() {
    return {
      userInfo: {},
      defaultAvatar: '/static/avatar.png',
      isFirstLoad: true,
      accountEntries: [
        { icon: '/static/agent/user.png', text: '我的用户', action: () => this.goUserList() },
        { icon: '/static/agent/withdrawal.png', text: '我的佣金', action: () => this.goCommissionLog() },
        { icon: '/static/agent/invite.png', text: '邀请用户', action: () => this.goTeen() },
	  ],
    }
  },
  onLoad() {
    // 检查是否登录
    if (!uni.getStorageSync(config.tokenKey)) {
      uni.navigateTo({ url: '/pages/login/index' })
      return
    }
    // 页面首次加载时获取用户信息
    this.getUserInfo()
  },
  onShow() {
    // 检查是否登录
    if (!uni.getStorageSync(config.tokenKey)) {
      uni.navigateTo({ url: '/pages/login/index' })
      return
    }
    // 如果不是首次加载，且有token，则刷新用户信息
    if (!this.isFirstLoad) {
      this.getUserInfo()
    }
    this.isFirstLoad = false
  },
  methods: {
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await request({
          url: '/api/user/detail',
        })
        if (res.code === 1) {
          this.userInfo = res.data
          // 更新本地存储
          uni.setStorageSync(config.userInfo, res.data)
        }
      } catch (error) {
        console.error('获取用户信息失败：', error)
      }
    },
    handleUserInfoBar() {
      if (!this.userInfo.nickname) {
        uni.navigateTo({ url: '/pages/login/index' })
      }
    },
    goUserList() {
      uni.navigateTo({
        url: '/pages/agent/user-list'
      })
    },
    goCommissionLog() {
      uni.navigateTo({
        url: '/pages/agent/commission-log'
      })
    },
    goTeen() {},
    goStatement() {
      const url = `${config.baseUrl}/api/user/platform`
      uni.navigateTo({
        url: '/pages/webview/webview?url=' + encodeURIComponent(url)
      })
    },
    goCancel() {},
    goVip() {},
    goInvite() {},
    goAbout() {},
  }
}
</script>

<style scoped>
.mine-container {
  background: #180F29;
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 顶部信息栏样式 */
.top-bar {
  display: flex;
  align-items: center;
  padding-bottom: 22rpx;
  justify-content: flex-start;
  background: url('/static/home/<USER>') no-repeat center/100vw 210rpx;
  width: 100vw;
  height: 180rpx;
  z-index: 100;
}

.user-info-wrap {
  display: flex;
  align-items: center;
  width: 100%;
}

.avatar-wrap {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}

.avatar-level-wrap.large {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar.small {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  position: absolute;
  left: 35rpx;
  top: 30rpx;
  z-index: 2;
  object-fit: cover;
}

.level-avatar-bg.large {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 3;
  object-fit: cover;
  pointer-events: none;
}

.user-detail {
  flex: 1;
  margin-left: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.nickname-row {
  display: flex;
  align-items: center;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  width: 85%;
}

.level-assets-row {
  display: flex;
  align-items: center;
  margin-top: 6rpx;
}

.id-bg-wrap {
  position: relative;
  width: 280rpx;
  height: 62rpx;
  margin-right: 5rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('/static/mine/id.png') no-repeat center/280rpx 72rpx;
}

.id-text {
  position: relative;
  z-index: 2;
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
  text-align: center;
  width: 100%;
  margin-top: 8rpx;
}

.level-badge {
	background: #4C4635;
	color: #D4BB75;
	font-size: 28rpx;
	border-radius: 30rpx;
	padding: 5rpx;
	display: flex;
	align-items: center;
	/* margin: 0 4rpx; */
	border: 1rpx solid #D4BB75;
	height: 40rpx;
	margin-top: 10rpx;
}
.level-icon {
	width: 48rpx;
	height: 48rpx;
	object-fit: contain;
}
.level-name{
	padding: 5rpx;
}

/* 统一的功能区域样式 */
.section-card {
  position: relative;
  background: #45325F;
  margin: 62rpx 32rpx 0 32rpx;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  padding: 40rpx 0 0 0;
  /* 顶部padding为标题悬浮预留空间 */
  overflow: visible;
}

.section-title-wrap {
  position: absolute;
  top: -50rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 350rpx;
  height: 100rpx;
}

.title-bg {
  width: 350rpx;
  height: 100rpx;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  object-fit: contain;
}

.section-title {
  position: relative;
  z-index: 2;
  font-size: 32rpx;
  color: #FFEF5B;
  padding: 8rpx 32rpx;
}

.section-content {
  display: flex;
  flex-wrap: wrap;
  padding: 24rpx;
}

.entry-item {
  /* width: 25%;  // 移除固定宽度，改为动态style */
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
  font-size: 24rpx;
  color: #fff;
}

.entry-icon {
  width: 72rpx;
  height: 72rpx;
  margin-bottom: 8rpx;
}
</style>