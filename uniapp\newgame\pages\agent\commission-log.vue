<template>
  <view class="container">
    <!-- 佣金统计区 -->
    <view class="commission-stats">
      <view class="stats-header">
        <view class="stat-item">
          <text class="stat-label">可提现佣金</text>
          <text class="stat-value">¥{{ commission }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">已提现佣金</text>
          <text class="stat-value">¥{{ withdrawCommission }}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">提现中佣金</text>
          <text class="stat-value">¥{{ withdraw }}</text>
        </view>
      </view>
      <button class="withdraw-btn" @tap="goWithdraw" :disabled="Number(commission) <= 0">立即提现</button>
    </view>

    <!-- 切换标签栏 -->
    <view class="tab-bar">
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'commission' }"
        @tap="switchTab('commission')"
      >
        佣金记录
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'withdraw' }"
        @tap="switchTab('withdraw')"
      >
        提现记录
      </view>
    </view>

    <!-- 佣金记录列表 -->
    <view class="commission-list" v-if="activeTab === 'commission'">
      <view class="commission-item" v-for="(item, index) in commissionList" :key="index">
        <view class="commission-info">
          <view class="amount-row">
            <text class="amount">¥{{ item.commission }}</text>
            <text :class="['status', item.status === 1 ? 'success' : 'refund']">
              {{ item.status === 1 ? '已到账' : '未到账' }}
            </text>
          </view>
          <view class="memo-row">
            <text class="memo">{{ item.memo }}</text>
          </view>
          <view class="time-row">
            <text class="time">{{ formatTime(item.createtime) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提现记录列表 -->
    <view class="commission-list" v-if="activeTab === 'withdraw'">
      <view class="commission-item" v-for="(item, index) in withdrawList" :key="index">
        <view class="commission-info">
          <view class="amount-row">
            <text class="amount">¥{{ item.money }}</text>
            <text :class="['status', getWithdrawStatusClass(item.status)]">
              {{ item.status_text }}
            </text>
          </view>
          <view class="detail-row">
            <text class="payment-type">提现方式：{{ item.type_text }}</text>
            <text class="fee" v-if="Number(item.fee) > 0">手续费：{{ item.fee }}%</text>
          </view>
          <view class="detail-row" v-if="Number(item.fee_money) > 0">
            <text class="real-money">实际到账：¥{{ item.real_money }}</text>
            <text class="fee-money">手续费：¥{{ item.fee_money }}</text>
          </view>
          <view class="time-row">
            <text class="time">{{ item.createtime }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view class="loading-more" v-if="hasMore">
      <text>加载中...</text>
    </view>
    <view class="no-more" v-else>
      <text>没有更多数据了</text>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      activeTab: 'commission',
      commissionList: [],
      withdrawList: [],
      page: 1,
      show_num: 10,
      hasMore: true,
      loading: false,
      commission: '0.00',
      withdrawCommission: '0.00',
      withdraw: '0.00'
    }
  },
  onLoad() {
    this.getCommissionList()
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.page++
      if (this.activeTab === 'commission') {
        this.getCommissionList(true)
      } else {
        this.getWithdrawList(true)
      }
    }
  },
  methods: {
    switchTab(tab) {
      if (this.activeTab === tab) return
      this.activeTab = tab
      this.page = 1
      this.hasMore = true
      if (tab === 'commission') {
        this.getCommissionList()
      } else {
        this.getWithdrawList()
      }
    },
    getWithdrawStatusClass(status) {
      switch (status) {
        case 0: return 'pending'
        case 1: return 'success'
        case 2: return 'failed'
        default: return ''
      }
    },
    async getWithdrawList(loadMore = false) {
      if (this.loading) return
      this.loading = true

      try {
        const res = await request({
          url: '/api/agent/withdraw_log',
          data: {
            page: this.page,
            show_num: this.show_num
          }
        })

        if (res.code === 1) {
          const newList = res.data.data || []
          this.withdrawList = loadMore ? [...this.withdrawList, ...newList] : newList
          this.hasMore = this.page < res.data.last_page
        } else {
          uni.showToast({
            title: res.msg || '获取提现记录失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取提现记录失败：', error)
        uni.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    async getCommissionList(loadMore = false) {
      if (this.loading) return
      this.loading = true

      try {
        const res = await request({
          url: '/api/agent/commission_log',
          data: {
            page: this.page,
            show_num: this.show_num
          }
        })

        if (res.code === 1) {
          const newList = res.data.data || []
          this.commissionList = loadMore ? [...this.commissionList, ...newList] : newList
          this.hasMore = this.page < res.data.last_page

          // 更新佣金统计
          this.commission = res.data.commission || '0.00'
          this.withdrawCommission = res.data.withdraw_commission || '0.00'
          this.withdraw = res.data.withdraw || '0.00'
        } else {
          uni.showToast({
            title: res.msg || '获取佣金记录失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取佣金记录失败：', error)
        uni.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    goWithdraw() {
      uni.navigateTo({
        url: '/pages/agent/withdraw'
      })
    },
    formatTime(timestamp) {
      const date = new Date(timestamp * 1000)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }
}
</script>

<style scoped>


.commission-stats {
  background: #2A1840;
  border: 1px solid #4C3A62;
  border-radius: 12rpx;
  padding: 32rpx;
  margin: 20rpx 32rpx 24rpx 32rpx;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.withdraw-btn {
  width: 100%;
  height: 80rpx;
  background: url('/static/mine/button.png') no-repeat center/100% 100%;
  color: #fff;
  font-size: 32rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.withdraw-btn[disabled] {
  background: #4C3A62;
  color: rgba(255, 255, 255, 0.5);
}

.commission-list {
  padding: 0 32rpx;
}

.commission-item {
  background: #2A1840;
  border: 1px solid #4C3A62;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
}

.status.success {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.status.refund {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8rpx;
}

.memo-row {
  margin-bottom: 12rpx;
}

.memo {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.time-row {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 8rpx;
}

.loading-more,
.no-more {
  text-align: center;
  padding: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  font-size: 24rpx;
}

.tab-bar {
  display: flex;
  background: #2A1840;
  border: 1px solid #4C3A62;
  border-radius: 12rpx;
  padding: 0 32rpx;
  margin: 0 32rpx 24rpx 32rpx;
  border-bottom: 1rpx solid #4C3A62;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
}

.tab-item.active {
  color: #FFEF5B;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #FFEF5B;
  border-radius: 2rpx;
}

.status.pending {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.status.failed {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}
</style> 