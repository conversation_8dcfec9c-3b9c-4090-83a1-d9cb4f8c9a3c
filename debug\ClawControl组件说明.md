# ClawControl 娃娃机控制组件

## 概述
基于 FishControl.vue 创建的竖屏娃娃机控制组件，保持了相同的样式风格和基础功能，专门适配娃娃机游戏操作。

## 主要功能

### 1. 方向控制
- **上下左右键** - 控制娃娃机爪子移动
- **操作方式** - 点击发送一次，松开发送一次（不连续发送）
- **按键值映射**：
  - 上移：10
  - 下移：11
  - 左移：12
  - 右移：13

### 2. 松开按键信号
- **特殊功能**：每个方向键松开时发送 `按键值 + 32` 的信号
- **信号值**：
  - 上移松开：42 (10 + 32)
  - 下移松开：43 (11 + 32)
  - 左移松开：44 (12 + 32)
  - 右移松开：45 (13 + 32)

### 3. 功能按键
- **投币按钮** - 保留投币功能，支持配置化投币选项
- **发炮按钮** - 改为抓取功能，指令值：15

### 4. 界面布局
- **竖屏设计** - 专为竖屏娃娃机优化
- **左侧头像区** - 头像靠左边，金币数量显示在头像下方
- **下方座位按钮** - 开始游戏按钮放在下方中间位置
- **右侧功能按钮** - 发炮和投币按钮贴近右边
- **响应式布局** - 支持不同屏幕尺寸

## 技术实现

### 组件结构
```
ClawControl.vue
├── template - 界面模板
├── script - 逻辑控制
└── style - 样式定义
```

### 关键方法
- `handleUpStart/End()` - 上移控制
- `handleDownStart/End()` - 下移控制
- `handleLeftStart/End()` - 左移控制
- `handleRightStart/End()` - 右移控制
- `handleFireStart/End()` - 抓取控制
- `sendGameCommand()` - 发送游戏指令
- `exitGame()` - 退出游戏（含等待机制）
- `sendEndGameWS()` - 发送结束游戏指令

### WebSocket 通信
```javascript
// 指令格式
{
  type: 'command',
  number: this.gameInfo.number,  // 座位号
  status: command                // 指令值
}
```

## 使用方法

### 1. 引入组件
```javascript
import ClawControl from '@/components/GameControls/ClawControl.vue'
```

### 2. 注册组件
```javascript
components: {
  ClawControl
}
```

### 3. 使用组件
```vue
<ClawControl
  :gameInfo="gameInfo"
  :userInfo="userInfo" 
  :seats="seats"
  :isVideoMuted="isVideoMuted"
  :gameSettings="gameSettings"
  @startGame="handleStartGame"
  @exitGame="handleExitGame"
  @toggleVideoAudio="handleToggleAudio"
/>
```

## 配置说明

### Props 参数
- `gameInfo` - 游戏信息对象
- `userInfo` - 用户信息对象
- `seats` - 座位列表数组
- `isVideoMuted` - 视频静音状态
- `gameSettings` - 游戏设置对象

### 投币配置
支持通过 `gameInfo.control_config` 配置投币按钮：
```json
{
  "buttons": [
    {"id": 51, "name": "投1币", "value": 1},
    {"id": 52, "name": "投5币", "value": 5},
    {"id": 53, "name": "投30币", "value": 30}
  ]
}
```

## 退出机制

### 智能退出逻辑
参考 FishControl 实现，包含以下特性：

1. **操作时间记录** - 记录每次操作的时间戳
2. **等待机制** - 距离最后操作3秒内需等待
3. **倒计时显示** - 显示剩余等待时间
4. **状态管理** - 完整的退出状态控制

### 退出流程
```javascript
// 游戏中退出
if (gameInfo.is_full === 2) {
  if (距离最后操作 < 3秒) {
    显示等待倒计时
  } else {
    显示退出确认对话框
  }
} else {
  直接退出
}
```

## 注意事项

1. **按键响应** - 支持触摸和鼠标事件，确保移动端和PC端兼容
2. **防重复触发** - 通过状态标记防止重复按键
3. **禁用右键菜单** - 防止长按触发浏览器右键菜单
4. **单次发送** - 方向键改为点击发送一次，松开发送一次，避免连续发送
5. **WebSocket 依赖** - 需要 `window.gameWebSocket` 全局对象
6. **退出保护** - 防止误操作，确保游戏状态正确结束

## 版本更新

### v0.0.3 (2025-01-21)
- 优化界面布局：头像靠左边，金币显示在头像下方
- 座位开始按钮移到下方中间位置
- 发炮投币按钮贴近右边，增大按钮尺寸
- 改进头像显示样式，增加背景和阴影效果

### v0.0.2 (2025-01-21)
- 修复退出机制，参考 FishControl 实现
- 改进方向控制：点击发送一次，松开发送一次
- 禁用长按右键菜单和文本选择
- 移除连续发送定时器，避免性能问题

### v0.0.1 (2025-01-21)
- 创建时间：2025-01-21
- 基于版本：FishControl.vue
- 适用游戏：竖屏娃娃机
- 软件版本：+0.0.1
