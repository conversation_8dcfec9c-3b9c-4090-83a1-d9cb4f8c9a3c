<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-bar-wrapper">
      <view class="search-bar">
        <view class="search-row">
          <input v-model="searchOrderNo" class="search-input" placeholder="请输入订单号搜索" />
          <button class="search-btn" @tap="searchRecharge">搜索</button>
        </view>
        <view class="search-row">
          <view class="date-range">
            <picker mode="date" :value="startDate" @change="onStartDateChange" class="date-picker">
              <view class="picker-text">{{ startDate || '开始日期' }}</view>
            </picker>
            <text class="date-separator">至</text>
            <picker mode="date" :value="endDate" @change="onEndDateChange" class="date-picker">
              <view class="picker-text">{{ endDate || '结束日期' }}</view>
            </picker>
          </view>
          <button class="reset-btn" @tap="resetSearch">重置</button>
        </view>
      </view>
    </view>

    <!-- 充值记录列表 -->
    <view class="recharge-list">
      <view class="recharge-item" v-for="(item, index) in rechargeList" :key="index">
        <view class="recharge-info">
          <view class="amount-row">
            <text class="amount">¥{{ item.price }}</text>
            <text :class="['status', getStatusClass(item.status)]">
              {{ getStatusText(item.status) }}
            </text>
          </view>
          <view class="time-row">
            <text class="time">{{ formatTime(item.createtime) }}</text>
            <text class="order-no">订单号：{{ item.order_no }}</text>
          </view>
          <view class="coin-row">
            <text class="coin">获得金币：{{ item.coin }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view class="loading-more" v-if="hasMore">
      <text>加载中...</text>
    </view>
    <view class="no-more" v-else>
      <text>没有更多数据了</text>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      userId: null,
      rechargeList: [],
      page: 1,
      show_num: 10,
      hasMore: true,
      loading: false,
      searchOrderNo: '',
      startDate: '',
      endDate: ''
    }
  },
  onLoad(options) {
    if (options.userId) {
      this.userId = options.userId
      this.getRechargeList()
    }
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.page++
      this.getRechargeList(true)
    }
  },
  methods: {
    onStartDateChange(e) {
      this.startDate = e.detail.value
    },
    onEndDateChange(e) {
      this.endDate = e.detail.value
    },
    searchRecharge() {
      // 验证日期范围
      if (this.startDate && this.endDate) {
        const start = new Date(this.startDate)
        const end = new Date(this.endDate)
        if (start > end) {
          uni.showToast({
            title: '开始日期不能大于结束日期',
            icon: 'none'
          })
          return
        }
      }
      
      this.page = 1
      this.rechargeList = []
      this.hasMore = true
      this.getRechargeList()
    },
    resetSearch() {
      this.searchOrderNo = ''
      this.startDate = ''
      this.endDate = ''
      this.page = 1
      this.rechargeList = []
      this.hasMore = true
      this.getRechargeList()
    },
    async getRechargeList(loadMore = false) {
      if (this.loading) return
      this.loading = true

      try {
        const params = {
          user_id: this.userId,
          page: this.page,
          show_num: this.show_num
        }

        // 添加搜索参数
        if (this.searchOrderNo) {
          params.order_no = this.searchOrderNo
        }
        if (this.startDate) {
          const start = new Date(this.startDate)
          params.start_date = Math.floor(start.getTime() / 1000)
        }
        if (this.endDate) {
          const end = new Date(this.endDate)
          // 设置为当天的最后一秒
          end.setHours(23, 59, 59, 999)
          params.end_date = Math.floor(end.getTime() / 1000)
        }

        const res = await request({
          url: '/api/agent/user_recharge_log',
          data: params
        })

        if (res.code === 1) {
          const newList = res.data.data || []
          this.rechargeList = loadMore ? [...this.rechargeList, ...newList] : newList
          this.hasMore = this.page < res.data.last_page
        } else {
          uni.showToast({
            title: res.msg || '获取充值记录失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取充值记录失败：', error)
        uni.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    formatTime(timestamp) {
      const date = new Date(timestamp * 1000)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    getStatusText(status) {
      switch (status) {
        case 0:
          return '待支付'
        case 1:
          return '已支付'
        case 2:
          return '已退款'
        default:
          return '未知状态'
      }
    },
    getStatusClass(status) {
      switch (status) {
        case 0:
          return 'pending'
        case 1:
          return 'success'
        case 2:
          return 'refund'
        default:
          return ''
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  padding-bottom: 32rpx;
}

.search-bar-wrapper {
  background: #2A1840;
  padding: 24rpx 32rpx;
  margin: 0 32rpx 24rpx 32rpx;
  border: 1px solid #4C3A62;
  border-radius: 12rpx;
  margin-top: 20rpx;
}

.search-bar {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background: #3F3055;
  color: #fff;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.date-range {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.date-picker {
  flex: 1;
  height: 64rpx;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3F3055;
}

.date-separator {
  color: rgba(255, 255, 255, 0.7);
  font-size: 28rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #fff;
}

.search-btn,
.reset-btn {
  width: 160rpx;
  height: 64rpx;
  font-size: 28rpx;
  border: none;
  border-radius: 8rpx;
  line-height: 64rpx;
}

.search-btn {
  background: #2196F3;
  color: #fff;
}

.reset-btn {
  background: #4C3A62;
  color: rgba(255, 255, 255, 0.7);
}

.recharge-list {
  padding: 0 32rpx;
}

.recharge-item {
  background: #2A1840;
  border: 1px solid #4C3A62;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.status {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
}

.status.success {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.status.pending {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.status.refund {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.time-row {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8rpx;
}

.coin-row {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.loading-more,
.no-more {
  text-align: center;
  padding: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  font-size: 24rpx;
}
</style> 