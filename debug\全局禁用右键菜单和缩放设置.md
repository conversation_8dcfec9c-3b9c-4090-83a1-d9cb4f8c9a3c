# 全局禁用右键菜单和缩放设置

## 概述
为了提供更好的游戏体验，防止用户误操作，已在整个应用中添加了全局的禁用右键菜单和禁用缩放功能。

## 实现方式

### 1. HTML Meta 标签设置
**文件：** `index.html`

```html
<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover" />
```

**功能：**
- `user-scalable=no` - 禁用用户缩放
- `initial-scale=1.0` - 初始缩放比例为1
- `maximum-scale=1.0` - 最大缩放比例为1
- `minimum-scale=1.0` - 最小缩放比例为1

### 2. JavaScript 事件监听
**文件：** `App.vue`

#### 禁用右键菜单
```javascript
// 禁用右键菜单
document.addEventListener('contextmenu', function(e) {
  e.preventDefault()
  return false
}, false)

// 禁用选择文本
document.addEventListener('selectstart', function(e) {
  e.preventDefault()
  return false
}, false)

// 禁用拖拽
document.addEventListener('dragstart', function(e) {
  e.preventDefault()
  return false
}, false)
```

#### 禁用缩放
```javascript
// 禁用双击缩放
let lastTouchEnd = 0
document.addEventListener('touchend', function(event) {
  const now = (new Date()).getTime()
  if (now - lastTouchEnd <= 300) {
    event.preventDefault()
  }
  lastTouchEnd = now
}, false)

// 禁用手势缩放
document.addEventListener('gesturestart', function(e) {
  e.preventDefault()
}, false)

// 禁用键盘缩放 (Ctrl + +/-)
document.addEventListener('keydown', function(e) {
  if ((e.ctrlKey || e.metaKey) && (e.keyCode === 61 || e.keyCode === 107 || e.keyCode === 173 || e.keyCode === 109 || e.keyCode === 187 || e.keyCode === 189)) {
    e.preventDefault()
  }
}, false)

// 禁用滚轮缩放
document.addEventListener('wheel', function(e) {
  if (e.ctrlKey || e.metaKey) {
    e.preventDefault()
  }
}, { passive: false })
```

### 3. CSS 全局样式
**文件：** `App.vue` - `<style>` 部分

```css
/* 全局禁用右键菜单和缩放 */
* {
  /* 禁用文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  
  /* 禁用长按菜单 */
  -webkit-touch-callout: none;
  
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: transparent;
  
  /* 禁用拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  
  /* 禁用双击缩放 */
  touch-action: manipulation;
}

/* 针对HTML和body的特殊设置 */
html, body {
  /* 禁用缩放 */
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  
  /* 禁用滚动条 */
  overflow-x: hidden;
  
  /* 固定视口，防止缩放 */
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
}
```

## 功能特性

### ✅ 已禁用的操作
1. **右键菜单** - 右键点击不会显示浏览器菜单
2. **文本选择** - 无法选择页面文本
3. **拖拽操作** - 无法拖拽页面元素
4. **双击缩放** - 双击不会触发缩放
5. **手势缩放** - 双指缩放手势被禁用
6. **键盘缩放** - Ctrl/Cmd + +/- 被禁用
7. **滚轮缩放** - Ctrl/Cmd + 滚轮缩放被禁用
8. **长按菜单** - 移动端长按不会显示菜单
9. **点击高亮** - 点击元素不会显示高亮效果

### 🎮 游戏体验优化
- **防误操作** - 避免游戏中意外触发浏览器功能
- **沉浸体验** - 提供更专注的游戏环境
- **跨平台一致** - 在不同设备上保持一致的交互体验
- **性能优化** - 减少不必要的浏览器事件处理

## 兼容性

### 支持的浏览器
- ✅ Chrome/Chromium (移动端和桌面端)
- ✅ Safari (iOS和macOS)
- ✅ Firefox (移动端和桌面端)
- ✅ Edge (移动端和桌面端)

### 支持的平台
- ✅ iOS Safari
- ✅ Android Chrome
- ✅ 微信内置浏览器
- ✅ 各种WebView环境

## 注意事项

1. **输入框例外** - 如果需要在某些输入框中允许文本选择，需要单独设置
2. **特殊组件** - 某些第三方组件可能需要单独处理
3. **调试模式** - 开发时可能需要临时禁用这些限制进行调试

## 版本信息
- 实现时间：2025-01-21
- 适用范围：整个uniapp应用
- 软件版本：+0.0.1
