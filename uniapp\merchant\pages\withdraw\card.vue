<template>
  <view class="card-container">
    <view class="card-list">
      <view class="card-item" v-for="item in cards" :key="item.id">
        <view class="card-content">
          <view class="card-info">
            <view class="info-row">
              <text class="info-label">持卡人姓名</text>
              <text class="info-value">{{ item.name || '红豆' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">账户类型</text>
              <text class="info-value">{{ item.type === 'bank' ? '银行卡' : '支付宝' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">账号</text>
              <text class="info-value">{{ item.account || '10' }}</text>
            </view>
          </view>
        </view>
        <view class="card-actions">
          <button class="action-btn edit-btn" @click="goEdit(item)">编辑</button>
          <button class="action-btn del-btn" @click="delCard(item)">删除</button>
        </view>
      </view>
      <view v-if="!cards.length" class="empty">没有更多了</view>
      <view v-if="cards.length < total && !loading" class="load-more" @click="loadMore">加载更多</view>
      <view v-else-if="total > 0 && cards.length >= total" class="load-more">没有更多了</view>
    </view>
    <view class="add-btn-container">
      <button class="add-btn" @click="goAdd">添加账户</button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
export default {
  data() {
    return {
      cards: [],
      page: 1,
      show_num: 10,
      total: 0,
      loading: false
    }
  },
  onLoad() {
    this.getCards()
  },
  onShow() {
    this.page = 1
    this.getCards()
  },
  methods: {
    async getCards(loadMore = false) {
      if (this.loading) return
      this.loading = true
      
      try {
        const res = await request({
          url: '/api/merchant/withdraw_account_list',
          method: 'POST',
          data: {
            page: this.page,
            show_num: this.show_num
          }
        })
        
        if (res.code === 1 && res.data && res.data.data) {
          const list = res.data.data
          if (loadMore) {
            this.cards = this.cards.concat(list)
          } else {
            this.cards = list
          }
          this.total = res.data.total
        } else {
          uni.showToast({ title: res.msg || '获取账户列表失败', icon: 'none' })
        }
      } catch (e) {
        uni.showToast({ title: '网络错误', icon: 'none' })
      } finally {
        this.loading = false
      }
    },
    loadMore() {
      if (this.cards.length < this.total) {
        this.page++
        this.getCards(true)
      }
    },
    goAdd() {
      uni.navigateTo({ url: '/pages/withdraw/edit' })
    },
    goEdit(item) {
      // 将账户数据编码后传递到编辑页面
      const accountData = encodeURIComponent(JSON.stringify(item))
      uni.navigateTo({ url: `/pages/withdraw/edit?data=${accountData}` })
    },
    delCard(item) {
      uni.showModal({
        title: '提示',
        content: '确定删除该账户？',
        success: (res) => {
          if (res.confirm) {
            // 调用删除接口
            request({
              url: '/api/merchant/withdraw_account_del',
              method: 'POST',
              data: { id: item.id }
            }).then(res => {
              if (res.code === 1) {
                uni.showToast({ title: '删除成功', icon: 'success' })
                // 重新加载第一页
                this.page = 1
                this.getCards()
              } else {
                uni.showToast({ title: res.msg || '删除失败', icon: 'none' })
              }
            }).catch(() => {
              uni.showToast({ title: '网络错误', icon: 'none' })
            })
          }
        }
      })
    },
    // 格式化账号显示
    formatAccount(account) {
      if (!account) return ''
      // 银行卡号显示前4位和后4位，中间用*代替
      if (account.length > 8) {
        const start = account.substring(0, 4)
        const end = account.substring(account.length - 4)
        return `${start}****${end}`
      }
      return account
    }
  }
}
</script>

<style scoped>
/* 容器样式 */
.card-container {
  min-height: 100vh;
  background: #180F29;
  padding: 0 24rpx 24rpx 24rpx;
  position: relative;
}

/* 列表容器 */
.card-list {
  padding-bottom: 120rpx;
}

/* 卡片项样式 */
.card-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  padding: 32rpx 28rpx;
}

/* 卡片内容 */
.card-content {
  margin-bottom: 24rpx;
}

.card-info {
  width: 100%;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}

.info-value {
  font-size: 28rpx;
  color: #fff;
  font-weight: 500;
}

/* 卡片操作按钮 */
.card-actions {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
  margin-top: 24rpx;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 编辑按钮样式 */
.edit-btn {
  background: rgba(74, 144, 226, 0.2);
  color: #4A90E2;
  border: 1rpx solid rgba(74, 144, 226, 0.3);
}

/* 删除按钮样式 */
.del-btn {
  background: rgba(255, 71, 87, 0.2);
  color: #ff4757;
  border: 1rpx solid rgba(255, 71, 87, 0.3);
}

/* 空状态提示 */
.empty {
  text-align: center;
  color: rgba(255, 255, 255, 0.4);
  font-size: 28rpx;
  margin-top: 120rpx;
  margin-bottom: 120rpx;
}

/* 加载更多按钮 */
.load-more {
  text-align: center;
  color: rgba(255, 255, 255, 0.4);
  font-size: 28rpx;
  padding: 40rpx 0;
  margin-bottom: 120rpx;
}

/* 底部添加按钮容器 */
.add-btn-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #180F29;
  padding: 32rpx 40rpx 60rpx 40rpx;
}

/* 添加按钮样式 */
.add-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #8B7CF6 0%, #6366F1 50%, #4F46E5 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(139, 124, 246, 0.3);
}
</style> 