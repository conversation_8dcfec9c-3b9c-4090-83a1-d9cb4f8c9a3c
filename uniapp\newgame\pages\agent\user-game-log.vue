<template>
  <view class="game-log-container">
    <!-- 游戏记录列表 -->
    <view class="game-log-list">
      <view class="game-log-item" v-for="log in gameLogs" :key="log.id">
        <view class="game-detail-section">
          <view class="game-info">
            <view class="game-row">
              <text class="game-name">{{ log.name }}</text>
            </view>
            <view class="game-row">座位号：<text class="game-number">{{ log.number }}</text></view>
            <view class="game-row">创建时间：<text class="game-time">{{ formatTime(log.createtime) }}</text></view>
          </view>
          <view class="game-status-section">
            <text class="game-status" :class="getStatusClass(log.status)">{{ getStatusText(log.status) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view class="loading-more" v-if="loading">
      <text>加载中...</text>
    </view>
    <view class="no-more" v-if="!hasMore && gameLogs.length > 0">
      <text>没有更多数据了</text>
    </view>
    <view class="empty-state" v-if="!loading && gameLogs.length === 0">
      <text>暂无游戏记录</text>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import config from '@/config.js'

export default {
  data() {
    return {
      gameLogs: [],
      page: 1,
      per_page: 10,
      hasMore: true,
      loading: false,
      currentAgentId: null,
      userId: null,
    }
  },
  onLoad(options) {
    if (options.userId) {
      this.userId = options.userId
    }
    
    const userInfo = uni.getStorageSync(config.userInfo)
    if (userInfo && userInfo.agent_id) {
      this.currentAgentId = userInfo.agent_id
      this.getGameLogs()
    } else {
      uni.showToast({
        title: '请先登录代理账户',
        icon: 'none'
      })
    }
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.page++
      this.getGameLogs(true)
    }
  },
  methods: {
    async getGameLogs(loadMore = false) {
      if (this.loading || this.currentAgentId === null || !this.userId) return
      this.loading = true

      try {
        const params = {
          page: this.page,
          per_page: this.per_page,
          user_id: this.userId
        }

        const res = await request({
          url: '/api/agent/user_game_log',
          data: params
        })

        if (res.code === 1) {
          const newLogs = res.data.data
          this.gameLogs = loadMore ? [...this.gameLogs, ...newLogs] : newLogs
          this.hasMore = this.page < res.data.last_page
        } else {
          uni.showToast({
            title: res.msg || '获取游戏记录失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取游戏记录失败：', error)
        uni.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    getStatusText(status) {
      const statusMap = {
        '-2': '结束失败',
        '-1': '开始失败',
        '0': '开始待确认',
        '1': '进行中',
        '2': '结束待确认',
        '3': '已完成'
      }
      return statusMap[status] || '未知状态'
    },
    getStatusClass(status) {
      const classMap = {
        '-2': 'status-failed',
        '-1': 'status-failed',
        '0': 'status-pending',
        '1': 'status-ongoing',
        '2': 'status-pending',
        '3': 'status-completed'
      }
      return classMap[status] || ''
    },
    formatTime(timestamp) {
      const date = new Date(timestamp * 1000)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }
}
</script>

<style scoped>
.game-log-container {
  min-height: 100vh;
  padding: 32rpx;
}

.game-log-list {
  margin-bottom: 32rpx;
}

.game-log-item {
  background: #2A1840;
  border: 1px solid #4C3A62;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.game-detail-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.game-info {
  flex: 1;
  margin-right: 16rpx;
}

.game-status-section {
  display: flex;
  align-items: flex-start;
}

.game-row {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}

.game-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-right: 16rpx;
}

.game-status {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.status-failed {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.status-pending {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.status-ongoing {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.status-completed {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.game-number,
.game-time {
  color: #fff;
  font-weight: 500;
}

.loading-more,
.no-more,
.empty-state {
  text-align: center;
  padding: 32rpx;
  color: rgba(255, 255, 255, 0.5);
  font-size: 28rpx;
}
</style> 