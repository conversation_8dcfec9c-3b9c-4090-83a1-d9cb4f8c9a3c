<?php

namespace app\admin\controller\recharge;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 充值记录
 * @Authod JW
 * @Time 2023/02/02
 * @package app\admin\controller\agent
 */
class Log extends Backend
{
    /**
     * Withdraw模型对象
     * @var \app\admin\model\Agent
     */

    protected $noNeedRight = '';
    protected $multiFields = "deleted,rent_limit";
    public function _initialize()
    {
        $this->noNeedRight = array('');
        $this->model = new \app\admin\model\Recharge();
        $this->view->assign("statusList", $this->model->getStatusList());

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            $admin = get_admin_info();
            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
                //代理只查看自己的用户
                $where_arr['u.p_id'] = $admin['admin_id'];
            }

            if ($search_arr) {
                foreach ($search_arr as $k => $v) {
                    //执行时间
                    if ($k == 'createtime') {//创建时间
                        $create_time = explode(' - ',$v);
                        $where_arr['l.createtime'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    } elseif ($k == 'pay_time') {//支付时间
                        $create_time = explode(' - ',$v);
                        $where_arr['l.pay_time'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    }  elseif ($k == 'trade_no') {//流水号
                        $where_arr['l.order_no'] = Db::name('payment')->where('trade_no',$v)->value('order_no');
                    } else {
                        $str = 'l.'.$k;
                        $where_arr[$str] = $v;
                    }

                    unset($search_arr[$k]);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('user_recharge_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->where($where)
                ->where($where_arr)
                ->count();

            $list = Db::name('user_recharge_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->join(['fa_payment p'],'l.order_no=p.order_no','left')
                ->where($where)
                ->where($where_arr)
                ->order('l.id', $order)
                ->field('l.*,u.nickname,u.mobile,u.avatar,p.trade_no')
                ->limit($offset, $limit)
                ->select();

            foreach ($list as $k => &$v) {
                $agent = [];
                if ($v['agent_id'] > 0) {
                    $agent = Db::name("admin")->where('id',$v['agent_id'])->find();
                }
                $v['agent_name'] = $agent ? $agent['nickname'] : '';
                $v['agent_mobile'] = $agent ? $agent['mobile'] : '';
            }

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

}
