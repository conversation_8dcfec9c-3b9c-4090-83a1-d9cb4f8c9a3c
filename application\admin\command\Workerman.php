<?php

namespace app\admin\command;

use Events;
use think\console\Command;
use think\console\input\Argument;
use app\workerman\controller\WorkerEvents;
use GatewayWorker\BusinessWorker;
use GatewayWorker\Gateway;
use GatewayWorker\Register;
use think\console\Input;
use think\console\input\Option;
use think\console\Output;
use think\Env;
use Workerman\Worker;

class Workerman extends Command
{
    protected function configure()
    {

        $this->setName('workerman')
            ->addArgument('action', Argument::OPTIONAL, "action  start|stop|restart")
            ->addArgument('type', Argument::OPTIONAL, "d -d")
            ->setDescription('workerman chat');
    }

    protected function execute(Input $input, Output $output)
    {
        global $argv;
        $action = trim($input->getArgument('action'));
        $type   = trim($input->getArgument('type')) ? '-d' : '';

        $argv[0] = 'chat';
        $argv[1] = $action ?: 'start';
        if ($type) {
            $argv[2] = '-d';
        }

        // 只有 start 时注册 Worker，其它命令让 Workerman 自己处理
        if ($action === 'start' || !$action) {
            $this->start();
        } else {
            // stop/restart 时不注册 Worker，直接 runAll 让 Workerman 处理
            \Workerman\Worker::runAll();
        }
    }

    private function start()
    {
        $this->startGateWay();
        $this->startBusinessWorker();
        $this->startRegister();
        Worker::runAll();
    }

    private function startBusinessWorker()
    {
        $worker                  = new BusinessWorker();
        $worker->name            = 'BusinessWorker';
        $worker->count           = 1;
        $worker->registerAddress = '127.0.0.1:1238';
        $worker->eventHandler    = WorkerEvents::class;
    }

    private function startGateWay()
    {
        $gateway = new Gateway("websocket://0.0.0.0:2346");
        $gateway->name                 = 'gameGateway';
        $gateway->count                = 1;
        $gateway->lanIp                = '127.0.0.1';
        $gateway->startPort            = 2900;
        $gateway->pingInterval         = 15;  // 15秒心跳间隔，与客户端匹配
        $gateway->pingNotResponseLimit = 4;   // 允许4次无响应，更宽松
        $gateway->pingData             = '{"type":"ping"}'; // 心跳数据
        $gateway->registerAddress      = '127.0.0.1:1238';
    }

    private function startRegister()
    {
        new Register('text://0.0.0.0:1238');
    }

}