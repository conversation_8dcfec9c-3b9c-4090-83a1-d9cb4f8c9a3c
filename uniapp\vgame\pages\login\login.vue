<template>
  <view class="container">
    <view class="auth-box">
      <!-- 切换选项卡 -->
      <view class="tab-bar">
        <text 
          :class="['tab-item', isLogin ? 'active' : '']"
          @click="switchTab(true)"
        >登录</text>
        <text 
          :class="['tab-item', !isLogin ? 'active' : '']"
          @click="switchTab(false)"
        >注册</text>
      </view>

      <!-- 登录表单 -->
      <view v-if="isLogin" class="form-container">
        <input class="input" v-model="loginForm.account" placeholder="请输入手机号" />
        <input 
          class="input" 
          v-model="loginForm.password" 
          placeholder="请输入密码" 
          password 
        />
        <button class="btn" :loading="loginLoading" @click="handleLogin">登录</button>
      </view>

      <!-- 注册表单 -->
      <view v-else class="form-container">
        <input class="input" v-model="regForm.mobile" placeholder="请输入手机号" />
        <input 
          class="input" 
          v-model="regForm.password" 
          placeholder="请输入密码" 
          password 
        />
        <input 
          class="input" 
          v-model="regForm.rePassword" 
          placeholder="请确认密码" 
          password 
        />
        <!-- <view class="sms-code">
          <input class="input" v-model="regForm.code" placeholder="短信验证码" />
          <button 
            class="sms-btn" 
            :disabled="smsDisabled" 
            @click="sendSmsCode"
          >{{ smsBtnText }}</button>
        </view> -->
        <label class="agreement">
          <checkbox :checked="agreementChecked" @click="toggleAgreement" />
          <text>已阅读并同意《用户协议》</text>
        </label>
        <button class="btn" :loading="regLoading" @click="handleRegister">注册</button>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request'
import config from '@/config.js'

export default {
	data() {
		return {
			isLogin: true,
			loginForm: { account: '', password: '' },
			regForm: { mobile: '', password: '', rePassword: '', code: '' },
			smsDisabled: false,
			smsBtnText: '获取验证码',
			agreementChecked: false,
			loginLoading: false,
			regLoading: false
		}
	},
	methods: {
		switchTab(isLogin) {
			this.isLogin = isLogin
		},
		//登录
		async handleLogin() {
			if (!this.validatePhone(this.loginForm.account)) return
			if (!this.loginForm.password) {
				return uni.showToast({ title: '请输入密码', icon: 'none' })
			}
		  
			this.loginLoading = true

			try {
				const res = await request({
					url: '/api/user/login',
					method: 'POST',
					data: this.loginForm
				})
				uni.setStorageSync(config.tokenKey, res.data.userinfo.token)//token
				uni.setStorageSync(config.tokenExpireKey, (res.data.userinfo.expiretime * 1000))//token过期时间
				uni.setStorageSync(config.userInfo, res.data.userinfo)//用户信息
				uni.reLaunch({ url: '/pages/my/my' })
			} finally {
					this.loginLoading = false
			}
		},
		//注册
		async handleRegister() {
			if (!this.validateRegister()) return
			
			if (this.regForm.password != this.regForm.rePassword) {
				return uni.showToast({ title: '两次密码不一致', icon: 'none' })
			}  
			this.regLoading = true
			try {
				await request({
					url: '/api/user/register',
					method: 'POST',
					data: this.regForm
				})
				uni.showToast({ title: '注册成功' })
				this.isLogin = true // 切换到登录
			} finally {
				this.regLoading = false
			}
		},

		async sendSmsCode() {
			if (!this.validatePhone(this.regForm.account)) return
		  
			await request({
				url: '/api/sms/send',
				method: 'POST',
				data: { account: this.regForm.account }
			})
		  
			this.smsDisabled = true
			let count = 60
			this.smsBtnText = `${count}s后重发`
			const timer = setInterval(() => {
				count--
				if (count <= 0) {
					clearInterval(timer)
					this.smsDisabled = false
					this.smsBtnText = '获取验证码'
				} else {
					this.smsBtnText = `${count}s后重发`
				}
			}, 1000)
		},

		validatePhone(mobile) {
			if (!/^1[3-9]\d{9}$/.test(mobile)) {
				uni.showToast({ title: '手机号格式错误', icon: 'none' })
				return false
			}
			return true
		},

		validateRegister() {
			if (!this.validatePhone(this.regForm.mobile)) return false
			if (this.regForm.password !== this.regForm.rePassword) {
				uni.showToast({ title: '两次密码不一致', icon: 'none' })
				return false
			}
			// if (!this.regForm.code) {
			// 	uni.showToast({ title: '请输入验证码', icon: 'none' })
			// 	return false
			// }
			if (!this.regForm.password) {
				uni.showToast({ title: '请输入密码', icon: 'none' })
				return false
			}
			if (!this.agreementChecked) {
				uni.showToast({ title: '请同意用户协议', icon: 'none' })
				return false
			}
			return true
		},

		toggleAgreement() {
			this.agreementChecked = !this.agreementChecked
		}
	}
}
</script>

<style scoped>
.container {
	height: 100vh;
	background-color: #f0f2f5;
	display: flex;
	justify-content: center;
	align-items: center;
}

.auth-box {
	width: 90%;
	padding: 30rpx;
	background: white;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.tab-bar {
	display: flex;
	margin-bottom: 40rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	font-size: 32rpx;
	padding: 20rpx;
	color: #666;
}

.tab-item.active {
	color: #007AFF;
	border-bottom: 4rpx solid #007AFF;
}

.input {
	height: 80rpx;
	margin-bottom: 30rpx;
	padding: 0 20rpx;
	border: 1rpx solid #eee;
	border-radius: 8rpx;
}

.sms-code {
	display: flex;
	gap: 20rpx;
}

.sms-btn {
	width: 200rpx;
	height: 80rpx;
	line-height: 80rpx;
	font-size: 28rpx;
}

.btn {
	margin-top: 40rpx;
	background: #007AFF;
	color: white;
	border-radius: 8rpx;
}

.agreement {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #666;
	margin: 20rpx 0;
}

</style>