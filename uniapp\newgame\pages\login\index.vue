<template>
	<view class="container">
		<!-- 顶部切换 -->
		<view class="tab-header">
			<view class="tab-item" :class="{ active: currentTab === 'login' }" @tap="switchToLogin">登录</view>
			<view class="tab-item" :class="{ active: currentTab === 'register' }" @tap="switchToRegister">注册
			</view>
		</view>

		<!-- 登录表单 -->
		<view class="form-box" v-if="currentTab === 'login'">
			<view class="input-group">
				<input type="text" v-model="loginForm.account" placeholder="请输入登录账号" maxlength="20" />
			</view>
			<view class="input-group">
				<input type="password" v-model="loginForm.password" placeholder="请输入密码" />
			</view>
			<button class="submit-btn" @tap="handleLogin">登录</button>
		</view>

		<!-- 注册表单 -->
		<view class="form-box" v-else>
			<view class="input-group">
				<input type="text" v-model="registerForm.account" placeholder="请输入登录账号" maxlength="20" />
			</view>
			<view class="input-group">
				<input type="password" v-model="registerForm.password" placeholder="请输入密码" />
			</view>
			<view class="input-group">
				<input type="password" v-model="registerForm.confirmPassword" placeholder="请确认密码" />
			</view>
			<view class="input-group">
				<input type="text" v-model="registerForm.inviteCode" placeholder="请输入推荐码（选填）" maxlength="6" />
			</view>
			<button class="submit-btn" @tap="handleRegister">注册</button>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request.js'
	import config from '@/config.js'
	export default {
		data() {
			return {
				currentTab: 'login', // login 或 register
				loginForm: {
					account: '',
					password: ''
				},
				registerForm: {
					account: '',
					password: '',
					confirmPassword: '',
					inviteCode: ''
				},
				from: '',
				gameConfig: {} // 游戏配置
			}
		},
		onLoad(options) {
			this.from = options.from || ''
			// 不在页面加载时获取配置，避免循环重定向
			// 只在用户切换到注册页面时才获取配置
		},
		methods: {
			// 切换到登录标签
			switchToLogin() {
				this.currentTab = 'login'
			},
			// 切换到注册标签
			switchToRegister() {
				this.currentTab = 'register'
				// 只在切换到注册时才获取配置，检查注册开关
				if (!this.gameConfig.register) {
					this.getGameConfig()
				}
			},
			async getGameConfig() {
				try {
					const res = await request({
						url: '/api/game/get_config'
					})
					if (res.code === 1) {
						this.gameConfig = res.data
					} else {
						console.log('获取游戏配置失败：', res.msg || '未知错误')
					}
				} catch (error) {
					console.error('获取游戏配置失败：', error)
					// 配置获取失败时，允许注册但不检查注册开关
					this.gameConfig = { register: '1' }
				}
			},
			// 登录
			async handleLogin() {
				if (!this.loginForm.account) {
					uni.showToast({
						title: '请输入登录账号',
						icon: 'none'
					})
					return
				}
				if (!this.loginForm.password) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none'
					})
					return
				}
				try {
					const res = await request({
						url: '/api/user/login',
						data: {
							account: this.loginForm.account,
							password: this.loginForm.password
						}
					})
					// 保存token和用户信息
					uni.setStorageSync(config.tokenKey, res.data.userinfo.token) //token
					uni.setStorageSync(config.tokenExpireKey, (res.data.userinfo.expiretime * 1000)) //token过期时间
					uni.setStorageSync(config.userInfo, res.data.userinfo) //用户信息
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/index/index'
						})
					}, 1500)
				} catch (error) {
					console.error('登录失败：', error)
				}
			},
			// 注册
			async handleRegister() {
				// 检查注册开关
				if (this.gameConfig.register === '0') {
					uni.showToast({
						title: '注册通道正在维护中',
						icon: 'none'
					})
					return
				}

				if (!this.registerForm.account) {
					uni.showToast({
						title: '请输入登录账号',
						icon: 'none'
					})
					return
				}
				if (!this.registerForm.password) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none'
					})
					return
				}
				if (this.registerForm.password !== this.registerForm.confirmPassword) {
					uni.showToast({
						title: '两次密码输入不一致',
						icon: 'none'
					})
					return
				}
				try {
					const res = await request({
						url: '/api/user/register',
						data: {
							account: this.registerForm.account,
							password: this.registerForm.password,
							p_id: this.registerForm.inviteCode
						}
					})
					if (res.code === 1) {
						uni.showToast({
							title: '注册成功',
							icon: 'success'
						})
						this.currentTab = 'login'
						this.loginForm.account = this.registerForm.account
					} else {
						uni.showToast({
							title: res.msg || '注册失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('注册失败：', error)
					uni.showToast({
						title: '注册失败，请稍后再试',
						icon: 'none'
					})
				}
			}
		}
	}
</script>

<style scoped>
	.container {
		padding: 40rpx;
		background-color: #301E46;
		min-height: 100vh;
	}

	.tab-header {
		display: flex;
		justify-content: center;
		margin-bottom: 60rpx;
	}

	.tab-item {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.6);
		padding: 20rpx 40rpx;
		position: relative;
		font-weight: 500;
	}

	.tab-item.active {
		color: #fff;
		font-weight: bold;
	}

	.tab-item.active::after {
		content: '';
		position: absolute;
		left: 50%;
		bottom: 0;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background: #7B68EE;
		border-radius: 2rpx;
	}

	.form-box {
		padding: 0 20rpx;
	}

	.input-group {
		margin-bottom: 30rpx;
	}

	.input-group input {
		width: 100%;
		height: 80rpx;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
		color: #fff;
		box-sizing: border-box;
		background-color: #3F3055;
		border: none;
		outline: none;
	}

	.input-group input:focus {
		color: #fff;
	}

	.input-group input::placeholder {
		color: #999;
	}

	.submit-btn {
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		border-radius: 45rpx;
		font-size: 30rpx;
		color: #fff;
		background: url('/static/mine/button.png') no-repeat center/100% 100%;
		border: none;
		text-align: center;
		margin-top: 40rpx;
	}
</style>