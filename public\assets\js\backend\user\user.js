define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'user/user/index',
                    add_url: 'user/user/add',
                    edit_url: 'user/user/edit',
                    del_url: 'user/user/del',
                    multi_url: 'user/user/multi',
                    table: 'user',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='p_id']", form).addClass("selectpage").data("source", "agent/agent/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                // fixedColumns: true,
                // fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        // {field: 'group.name', title: __('Group')},
                        // {field: 'role_name', title: __('角色')},
                        // {field: 'username', title: __('Username'), operate: 'LIKE'},
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE'},
                        // {field: 'email', title: __('Email'), operate: 'LIKE'},
                        {field: 'mobile', title: __('Mobile'), operate: 'LIKE'},
                        {field: 'avatar', title: __('Avatar'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'p_id',title: __('所属代理'),formatter: function (value, row, index) {
                                if (row.agent_name) {
                                    return row.agent_name+'<br>'+'代理ID：'+row.p_id;
                                }else{
                                    return row.p_id;
                                }
                            }},
                        // {field: 'p_id', title: __('代理信息'), visible: false},
                        {field: 'level', title: __('Level'), operate: false, sortable: true},
                        // {field: 'gender', title: __('Gender'), visible: false, searchList: {1: __('Male'), 0: __('Female')}},
                        {field: 'money', title: __('金币'), operate: false, sortable: true},
                        {field: 'score', title: __('Score'), operate: false, sortable: true},
                        // {field: 'successions', title: __('Successions'), visible: false, operate: 'BETWEEN', sortable: true},
                        // {field: 'maxsuccessions', title: __('Maxsuccessions'), visible: false, operate: 'BETWEEN', sortable: true},
                        {field: 'logintime', title: __('最近登录时间'), formatter: Table.api.formatter.datetime, operate: false, addclass: 'datetimerange', sortable: true},
                        // {field: 'loginip', title: __('Loginip'), formatter: Table.api.formatter.search},
                        {field: 'createtime', title: __('Createtime'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        // {field: 'joinip', title: __('Joinip'), formatter: Table.api.formatter.search},
                        {field: 'status', title: __('Status'), formatter: Table.api.formatter.status, searchList: {normal: __('Normal'), hidden: __('Hidden')}},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('游戏记录'),
                                    text:'游戏记录',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","70%"]\'',
                                    icon: 'fa fa-list',
                                    url: function (row){
                                        return "game/log/index?user_id=" + row.id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('充值记录'),
                                    text:'充值记录',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","70%"]\'',
                                    icon: 'fa fa-list',
                                    url: function (row){
                                        return "recharge/log/index?user_id=" + row.id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('金币记录'),
                                    text:'金币记录',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","70%"]\'',
                                    icon: 'fa fa-list',
                                    url: function (row){
                                        return "user/money/index?user_id=" + row.id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('积分记录'),
                                    text:'积分记录',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","70%"]\'',
                                    icon: 'fa fa-list',
                                    url: function (row){
                                        return "user/score/index?user_id=" + row.id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('签到记录'),
                                    text:'签到记录',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","70%"]\'',
                                    icon: 'fa fa-list',
                                    url: function (row){
                                        return "user/sign_log/index?user_id=" + row.id;
                                    }
                                },

                                {
                                    dropdown: "更多",
                                    name: 'set',
                                    title: __('设为代理商'),
                                    text:'设为代理商',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["50%","50%"]\'',
                                    icon: 'fa fa-user-circle-o',
                                    url: function (row){
                                        return "user/user/set_agent?ids=" + row.id;
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        if (!row.agent_id && row.is_show && row.p_id==0){
                                            return true;
                                        }else{
                                            return false;
                                        }
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'ps_edit',
                                    title: __('修改登录密码'),
                                    text:'修改登录密码',
                                    classname: 'btn btn-magic btn-dialog',
                                    icon: 'fa fa-key',
                                    url: function (row){
                                        return "user/user/reset_password/ids/" + row.id;
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        if (row.is_show){
                                            return true;
                                        }else{
                                            return false;
                                        }
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'ps_edit',
                                    title: __('修改金币'),
                                    text:'修改金币',
                                    classname: 'btn btn-magic btn-dialog',
                                    icon: 'fa fa-money',
                                    url: function (row){
                                        return "user/user/set_money/ids/" + row.id;
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        if (row.is_show){
                                            return true;
                                        }else{
                                            return false;
                                        }
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'ps_edit',
                                    title: __('修改积分'),
                                    text:'修改积分',
                                    classname: 'btn btn-magic btn-dialog',
                                    icon: 'fa fa-eyedropper',
                                    url: function (row){
                                        return "user/user/set_score/ids/" + row.id;
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        if (row.is_show){
                                            return true;
                                        }else{
                                            return false;
                                        }
                                    }
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        set_agent: function () {
            Controller.api.bindevent();
        },
        reset_password: function () {
            Controller.api.bindevent();
        },
        set_money: function () {
            Controller.api.bindevent();
        },
        set_score: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});