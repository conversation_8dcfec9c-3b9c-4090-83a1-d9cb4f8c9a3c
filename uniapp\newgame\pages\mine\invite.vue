<template>
	<view class="container invite-container">
		<!-- 邀请规则 -->
		<view class="invite-rules-card">
			<view class="card-title">
				<view class="title-line"></view>
				<text class="title-text">邀请规则</text>
				<view class="title-line"></view>
			</view>
			<view class="rules-content" v-html="inviteRules"></view>

			<!-- 我的邀请码 -->
			<view class="my-invite-code">
				<text class="code-label">我的邀请码：</text>
				<text class="code-value">{{ userInfo.id || '未登录' }}</text>
				<view class="copy-btn" @tap="copyInviteCode">
					<image src="/static/mine/copy.png" class="copy-icon" />
				</view>
			</view>
		</view>

		<!-- 去邀请按钮 -->
		<view class="invite-button-container">
			<view class="invite-button" @tap="goInvite">
				<image src="/static/mine/invite_button.png" class="invite-button-bg" />
				<text class="invite-button-text">去邀请</text>
			</view>
		</view>

		<!-- 邀请统计 -->
		<view class="stats-card">
			<view class="card-title">
				<view class="title-line"></view>
				<text class="title-text">邀请统计</text>
				<view class="title-line"></view>
			</view>
			<view class="stats-content">
				<view class="stat-item">
					<text class="stat-number">{{ inviteStats.total_invites || 0 }}</text>
					<text class="stat-label">累计邀请</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">{{ inviteStats.total_rewards || 0 }}</text>
					<text class="stat-label">累计奖励</text>
				</view>
			</view>
		</view>

		<!-- 邀请记录 -->
		<view class="records-card">
			<view class="card-title">
				<view class="title-line"></view>
				<text class="title-text">邀请记录</text>
				<view class="title-line"></view>
			</view>
			<view class="records-list" v-if="inviteRecords.length > 0">
				<view class="record-item" v-for="(item, index) in inviteRecords" :key="index">
					<view class="record-info">
						<text class="record-user">用户ID: {{ item.invitee_people_user_id }}</text>
						<text class="record-time">{{ formatTime(item.createtime) }}</text>
					</view>
					<view class="record-reward">
						<text class="reward-amount">+{{ item.invite_people_get_coin }}金币</text>
					</view>
				</view>
			</view>
			<view class="empty-records" v-else>
				<text>暂无邀请记录</text>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request.js'
	import config from '@/config.js'

	export default {
		data() {
			return {
				userInfo: {},
				inviteConfig: {},
				inviteStats: {},
				inviteRecords: [],
				inviteRules: ''
			}
		},
		onLoad() {
			this.getUserInfo();
			this.getInviteConfig();
			this.getInviteStats();
			this.getInviteRecords();
			this.getGameConfig();
		},
		methods: {
			// 获取游戏配置（邀请规则）
			async getGameConfig() {
				try {
					const res = await request({
						url: '/api/game/get_config',
						method: 'POST'
					});
					if (res.code === 1 && res.data.invite_rule) {
						this.inviteRules = res.data.invite_rule;
					}
				} catch (error) {
					console.error('获取游戏配置失败：', error);
				}
			},

			// 去邀请
			goInvite() {
				// 这里可以添加分享逻辑或跳转到其他页面
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},

			// 获取用户信息
			async getUserInfo() {
				try {
					const userInfo = uni.getStorageSync(config.userInfo);
					if (userInfo) {
						this.userInfo = userInfo;
					}
				} catch (error) {
					console.error('获取用户信息失败：', error);
				}
			},

			// 获取邀请配置
			async getInviteConfig() {
				try {
					const res = await request({
						url: '/api/user/getInviteConfig',
						method: 'POST'
					});
					if (res.code === 1) {
						this.inviteConfig = res.data;
					}
				} catch (error) {
					console.error('获取邀请配置失败：', error);
				}
			},

			// 获取邀请统计
			async getInviteStats() {
				try {
					const res = await request({
						url: '/api/user/getInviteStats',
						method: 'POST'
					});
					if (res.code === 1) {
						this.inviteStats = res.data;
					}
				} catch (error) {
					console.error('获取邀请统计失败：', error);
				}
			},

			// 获取邀请记录
			async getInviteRecords() {
				try {
					const res = await request({
						url: '/api/user/getInviteRecords',
						method: 'POST',
						data: {
							page: 1,
							show_num: 20
						}
					});
					if (res.code === 1) {
						this.inviteRecords = res.data.data || [];
					}
				} catch (error) {
					console.error('获取邀请记录失败：', error);
				}
			},

			// 复制邀请码
			copyInviteCode() {
				if (!this.userInfo.id) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					return;
				}

				uni.setClipboardData({
					data: this.userInfo.id.toString(),
					success: () => {
						uni.showToast({
							title: '邀请码已复制',
							icon: 'success'
						});
					},
					fail: () => {
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						});
					}
				});
			},

			// 格式化时间
			formatTime(timestamp) {
				if (!timestamp) return '';
				const date = new Date(timestamp * 1000);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			}
		}
	}
</script>

<style scoped>
	.invite-container {
		background: #FE909A url('/static/mine/mine-invite.png') no-repeat;
		background-size: 100% auto;
		background-position: top center;
		min-height: 100vh;
		width: 100%;
		position: relative;
		overflow: visible;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-bottom: 42rpx;
	}

	/* 邀请规则卡片 */
	.invite-rules-card {
		background: url('/static/mine/invite-rules-card.png') no-repeat;
		background-size: cover;
		background-position: center;
		width: calc(100% - 64rpx);
		max-width: 600rpx;
		border-radius: 24rpx;
		padding: 32rpx;
		margin-top: 50vh;
		z-index: 10;
	}

	/* 其他卡片通用样式 */
	.stats-card,
	.records-card {
		background: #FF7092;
		width: calc(100% - 64rpx);
		max-width: 600rpx;
		border-radius: 24rpx;
		padding: 32rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
		margin-top: 32rpx;
		z-index: 5;
	}

	.card-title {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 24rpx;
		gap: 20rpx;
	}

	.title-line {
		flex: 1;
		height: 2rpx;
		background: #fff;
		max-width: 150rpx;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		white-space: nowrap;
	}

	/* 邀请规则内容 */
	.rules-content {
		background: #FFE4E8;
		color: #333;
		font-size: 28rpx;
		line-height: 1.6;
		padding: 24rpx;
		border-radius: 12rpx;
		margin-bottom: 24rpx;
	}

	/* 我的邀请码 */
	.my-invite-code {
		display: flex;
		align-items: center;
		background: #FFA1B4;
		border-radius: 50rpx;
		padding: 10rpx 24rpx;
		border: 1rpx solid #ddd;
		width: 300rpx;
		margin: 0 auto;
	}

	.code-label {
		font-size: 28rpx;
		color: #333;
		margin-right: 12rpx;
	}

	.code-value {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		flex: 1;
	}

	.copy-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 50rpx;
		height: 50rpx;
		background: transparent;
		margin-left: 12rpx;
	}

	.copy-icon {
		width: 32rpx;
		height: 32rpx;
	}

	/* 去邀请按钮 */
	.invite-button-container {
		display: flex;
		justify-content: center;
		width: 100%;
		margin-top: 32rpx;
		z-index: 5;
	}

	.invite-button {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.invite-button-bg {
		width: 300rpx;
		height: 80rpx;
	}

	.invite-button-text {
		position: absolute;
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		z-index: 2;
	}

	/* 统计信息 */
	.stats-content {
		display: flex;
		justify-content: space-around;
	}

	.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
	}

	.stat-number {
		font-size: 48rpx;
		font-weight: bold;
		color: #FFD700;
	}

	.stat-label {
		font-size: 24rpx;
		color: #fff;
	}

	/* 邀请记录 */
	.records-list {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	.record-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #FFE4E8;
		border-radius: 12rpx;
		padding: 24rpx;
	}

	.record-info {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.record-user {
		font-size: 28rpx;
		color: #333;
	}

	.record-time {
		font-size: 24rpx;
		color: #333;
	}

	.record-reward {
		display: flex;
		align-items: center;
	}

	.reward-amount {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
	}

	.empty-records {
		text-align: center;
		padding: 60rpx 0;
		color: #333;
		font-size: 28rpx;
	}
</style>
