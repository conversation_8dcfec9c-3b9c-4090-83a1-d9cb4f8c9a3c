<template>
  <view class="super-container">
    <!-- 奖励按钮，右侧垂直居中，竖直排列并有圆角边框 -->
    <view class="super-reward-btn">
      <text>奖</text>
      <text>励</text>
    </view>
    <!-- 顶部区域 -->
    <view class="super-header">
      <view class="super-title">超级玩家</view>
    </view>
    <!-- 冠军区 -->
    <view class="super-champion-box">
      <view class="super-champion-season">冠军上赛季</view>
      <view class="super-champion-info">
        <image class="champion-avatar" src="/static/avatar.png" />
        <view class="champion-detail">
          <view class="champion-name">无足鸟不回头</view>
          <view class="champion-level">黄金勇士III</view>
          <view class="champion-score">46674400分</view>
        </view>
      </view>
    </view>
    <!-- 期数和名人堂 -->
    <view class="super-period-row">
      <view class="super-period">2025年第五期</view>
      <image class="super-hall-btn" src="/static/hall.png" />
    </view>
    <!-- TOP1~TOP3 -->
    <view class="super-top3">
      <view class="top3-item gold">
        <view class="top3-avatar-wrap"><image class="top3-avatar" src="/static/avatar.png" /></view>
        <view class="top3-name">玩个蛋</view>
        <view class="top3-level">黄金勇士III</view>
        <view class="top3-score">34606540分</view>
      </view>
      <view class="top3-item blue">
        <view class="top3-avatar-wrap"><image class="top3-avatar" src="/static/avatar.png" /></view>
        <view class="top3-name">无足鸟不回头</view>
        <view class="top3-level">黄金勇士III</view>
        <view class="top3-score">23148400分</view>
      </view>
      <view class="top3-item red">
        <view class="top3-avatar-wrap"><image class="top3-avatar" src="/static/avatar.png" /></view>
        <view class="top3-name">有点意思</view>
        <view class="top3-level">黄金勇士III</view>
        <view class="top3-score">23019200分</view>
      </view>
    </view>
    <!-- 4-10名列表，可滚动 -->
    <scroll-view class="super-list-scroll" scroll-y="true">
      <view class="super-list">
        <view class="super-list-item" v-for="item in rankList" :key="item.rank">
          <view class="list-rank-flag">
            <image class="flag-img" src="/static/flag.png" />
            <view class="flag-num">{{ item.rank }}</view>
          </view>
          <view class="list-avatar-wrap"><image class="list-avatar" :src="item.avatar" /></view>
          <view class="list-info">
            <view class="list-name">{{ item.name }}</view>
            <view class="list-level-box" :class="item.levelClass">{{ item.level }}</view>
          </view>
          <view class="list-score">{{ item.score }}分</view>
        </view>
      </view>
      <!-- 底部安全区域，防止内容被固定栏遮挡 -->
      <view class="bottom-safe-area"></view>
    </scroll-view>
    <!-- 当前用户排名，固定底部 -->
    <view class="super-mine-fixed">
      <view class="mine-rank-flag">
        <image class="flag-img" src="/static/flag.png" />
        <view class="flag-num">{{ myRank.rank }}</view>
      </view>
      <view class="mine-avatar-wrap"><image class="mine-avatar" :src="myRank.avatar" /></view>
      <view class="mine-info">
        <view class="mine-name">{{ myRank.name }}</view>
        <view class="mine-level-box" :class="myRank.levelClass">{{ myRank.level }}</view>
      </view>
      <view class="mine-score">{{ myRank.score }}分</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      rankList: [
        { rank: 4, avatar: '/static/avatar.png', name: '***驾驶', level: '黄金勇士III', levelClass: 'gold', score: 22165520 },
        { rank: 5, avatar: '/static/avatar.png', name: '🐴警长', level: '黄金勇士I', levelClass: 'gold', score: 18459360 },
        { rank: 6, avatar: '/static/avatar.png', name: '脸大就不要脸?', level: '铂金领主I', levelClass: 'silver', score: 16546850 },
        { rank: 7, avatar: '/static/avatar.png', name: '不来神', level: '黄金勇士II', levelClass: 'gold', score: 16338000 },
        { rank: 8, avatar: '/static/avatar.png', name: '还能玩不', level: '青铜战神II', levelClass: 'bronze', score: 12000000 },
        { rank: 9, avatar: '/static/avatar.png', name: '伯赏香氛', level: '青铜战神II', levelClass: 'bronze', score: 8000000 },
        { rank: 10, avatar: '/static/avatar.png', name: '还想玩', level: '青铜战神I', levelClass: 'bronze', score: 5000000 },
        { rank: 11, avatar: '/static/avatar.png', name: '还想玩', level: '青铜战神I', levelClass: 'bronze', score: 5000000 },
        { rank: 12, avatar: '/static/avatar.png', name: '还想玩', level: '青铜战神I', levelClass: 'bronze', score: 5000000 },
        { rank: 13, avatar: '/static/avatar.png', name: '还想玩', level: '青铜战神I', levelClass: 'bronze', score: 5000000 },
        { rank: 14, avatar: '/static/avatar.png', name: '还想玩', level: '青铜战神I', levelClass: 'bronze', score: 5000000 },
      
      ],
      myRank: {
        rank: '-',
        avatar: '/static/avatar.png',
        name: '佰赏香氛',
        level: '青铜战神II',
        levelClass: 'bronze',
        score: 0
      }
    }
  }
}
</script>

<style scoped>
.super-container {
  min-height: 100vh;
  background: #1F2E94;
  padding-bottom: 0; /* 移除底部内边距，由安全区域提供 */
  position: relative;
}
.super-header {
  position: relative;
  width: 100%;
  height: 120rpx;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  background: #1F2E94;
}
.super-title {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  letter-spacing: 8rpx;
  margin-top: 32rpx;
}
.super-reward-btn {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.12);
  border: 2rpx solid #3bb6ff;
  border-radius: 24rpx 0 0 24rpx;
  padding: 18rpx 0;
  width: 56rpx;
  box-shadow: 0 2rpx 8rpx #b3d8ff;
}
.super-reward-btn text {
  color: #3bb6ff;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1.2;
}
.super-champion-box {
  margin: 24rpx 24rpx 0 24rpx;
  border: 2rpx solid #fff;
  border-radius: 24rpx;
  background: rgba(255,255,255,0.08);
  padding: 16rpx 0 0 0;
}
.super-champion-season {
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 8rpx;
}
.super-champion-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 16rpx;
}
.champion-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  border: 6rpx solid #ffd700;
  background: #fff;
  margin-right: 24rpx;
}
.champion-detail {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.champion-name {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}
.champion-level {
  color: #ffd700;
  font-size: 24rpx;
  margin: 4rpx 0 0 0;
}
.champion-score {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  margin-top: 4rpx;
}
.super-period-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 8rpx 0 0 0;
}
.super-period {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 16rpx;
}
.super-hall-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #fff;
  border: 2rpx solid #3bb6ff;
}
.super-top3 {
  display: flex;
  justify-content: space-around;
  margin: 24rpx 0 0 0;
}
.top3-item {
  width: 30%;
  background: rgba(255,255,255,0.12);
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 0 18rpx 0;
  position: relative;
}
.top3-item.gold {
  border: 4rpx solid #ffd700;
}
.top3-item.blue {
  border: 4rpx solid #3bb6ff;
}
.top3-item.red {
  border: 4rpx solid #ff7b2c;
}
.top3-avatar-wrap {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  border: 4rpx solid #fff;
}
.top3-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
}
.top3-name {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  margin: 8rpx 0 0 0;
  max-width: 90%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.top3-level {
  color: #ffd700;
  font-size: 22rpx;
  margin: 2rpx 0 0 0;
}
.top3-score {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  margin-top: 2rpx;
}
.super-list-scroll {
  height: auto; /* 改为自适应高度 */
  flex: 1; /* 占据剩余空间 */
  overflow: auto;
  margin: 24rpx 0 0 0;
  padding-bottom: 120rpx; /* 为底部固定栏留出空间 */
}
.super-list {
  background: transparent;
  padding: 0 24rpx;
}
.super-list-item {
  display: flex;
  align-items: center;
  background: transparent;
  padding: 24rpx 0;
  border-bottom: 2rpx solid rgba(59, 182, 255, 0.3);
  margin-bottom: 8rpx;
}
.super-list-item:last-child {
  border-bottom: none;
}
.list-rank-flag {
  position: relative;
  width: 48rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.flag-img {
  width: 48rpx;
  height: 64rpx;
  display: block;
}
.flag-num {
  position: absolute;
  top: 0;
  left: 0;
  width: 48rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-size: 28rpx;
  font-weight: bold;
  z-index: 2;
}
.list-avatar-wrap {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 16rpx;
  border: 2rpx solid #fff;
  flex-shrink: 0;
}
.list-avatar {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
}
.list-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0; /* 确保flex子项可以收缩 */
}
.list-name {
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.list-level-box,
.list-level-box.gold,
.list-level-box.silver,
.list-level-box.bronze {
  color: #fff !important;
  border-color: #fff !important;
}
.list-level-box {
  font-size: 22rpx;
  font-weight: bold;
  margin: 8rpx 0 0 0;
  padding: 4rpx 18rpx;
  border-radius: 16rpx;
  background: rgba(255,255,255,0.18);
  display: inline-block;
}
.list-score {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  margin-left: 16rpx;
  flex-shrink: 0;
  text-align: right;
  min-width: 150rpx;
}
/* 底部安全区域，防止内容被固定栏遮挡 */
.bottom-safe-area {
  height: 100rpx; /* 与底部固定栏高度匹配 */
  width: 100%;
}
.super-mine-fixed {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  background: #3377FF;
  border-radius: 0;
  box-shadow: 0 -2rpx 8rpx #b3d8ff;
  padding: 16rpx 24rpx;
  z-index: 99;
  box-sizing: border-box;
}
.mine-rank-flag {
  position: relative;
  width: 48rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.mine-avatar-wrap {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 16rpx;
  border: 2rpx solid #fff;
  flex-shrink: 0;
}
.mine-avatar {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
}
.mine-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0; /* 确保flex子项可以收缩 */
}
.mine-name {
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.mine-level-box,
.mine-level-box.gold,
.mine-level-box.silver,
.mine-level-box.bronze {
  color: #fff !important;
  border-color: #fff !important;
}
.mine-level-box {
  font-size: 22rpx;
  font-weight: bold;
  margin: 8rpx 0 0 0;
  padding: 4rpx 18rpx;
  border-radius: 16rpx;
  background: rgba(255,255,255,0.18);
  display: inline-block;
}
.mine-score {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  margin-left: 16rpx;
  flex-shrink: 0;
  text-align: right;
  min-width: 150rpx;
}
</style> 