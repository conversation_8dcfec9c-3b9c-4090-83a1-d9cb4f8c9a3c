<template>
  <view class="easy-player-container">
    <view class="player-pro-live">
      <view :id="playerId"></view>
      <!-- 新增点击阻止层 -->
      <view class="video-overlay"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'EasyPlayer',
  props: {
    // 视频URL
    videoUrl: {
      type: String,
      required: true
    },
    // 播放器ID，用于支持多个播放器实例
    playerId: {
      type: String,
      default: 'easyplayer'
    },
    // 播放器配置
    playerConfig: {
      type: Object,
      default: () => ({
        decoder: './static/js/EasyPlayer-decode.js',
        videoBuffer: 1,
        isResize: true,
        text: "",
        loadingText: "加载中",
        useMSE: true,
        useSIMD: false,
        useWCS: false,
        isMulti: true,
        hasAudio: false,
        reconnection: true,
        showPerformance: false,
        operateBtns: {
          fullscreen: false,
          screenshot: false,
          play: false,
          audio: false,
          record: false,
          quality: false,
          performance: false,
        },
        watermarkConfig: {
          text: {
            content: 'easyplayer-pro'
          },
          right: 10,
          top: 10
        },
        quality: ['高清'],
        playbackForwardMaxRateDecodeIFrame: 1,
        isWebrtcForOthers: true,
        demuxUseWorker: true,
        supportHls265: false,
        canvasRender: false,
        aspectRatio: '16:9',
        easyStretch: true,
      })
    },
    // 是否自动播放
    autoPlay: {
      type: Boolean,
      default: true
    },
    // 播放延迟（毫秒）
    playDelay: {
      type: Number,
      default: 500
    }
  },
  
  data() {
    return {
      playerInfo: null,
      isPlayerReady: false,
      retryCount: 0,
      maxRetries: 10
    }
  },
  
  mounted() {
    this.initPlayer()
  },
  
  beforeDestroy() {
    this.destroyPlayer()
  },
  
  watch: {
    videoUrl(newUrl) {
      if (newUrl && this.playerInfo) {
        this.play(newUrl)
      }
    }
  },
  
  methods: {
    // 初始化播放器
    initPlayer() {
      this.fixSafariLayout()
      // 添加窗口变化监听
      window.addEventListener('resize', this.fixSafariLayout)
      window.addEventListener('orientationchange', this.handleOrientationChange)
      
      this.checkPlayerAndInit()
    },
    
    // 检查播放器是否加载并初始化
    checkPlayerAndInit() {
      const checkAndInit = () => {
        if (typeof EasyPlayerPro !== 'undefined') {
          this.createPlayer()
          return
        }
        
        if (this.retryCount < this.maxRetries) {
          this.retryCount++
          setTimeout(checkAndInit, 500)
        } else {
          console.error('播放器加载失败')
          this.$emit('error', '播放器加载失败')
          uni.showToast({
            title: '播放器加载失败',
            icon: 'none'
          })
        }
      }
      
      checkAndInit()
    },
    
    // 创建播放器实例
    createPlayer() {
      try {
        const container = document.getElementById(this.playerId)
        
        if (!container) {
          console.error('播放器容器未找到')
          this.$emit('error', '播放器容器未找到')
          return
        }
        
        console.log('容器元素:', container)
        
        const easyplayer = new EasyPlayerPro({
          container: container,
          ...this.playerConfig
        })
        
        // 绑定事件
        easyplayer.on("fullscreen", (flag) => {
          console.log('is fullscreen', flag)
          this.$emit('fullscreen', flag)
        })
        
        easyplayer.on('playbackPreRateChange', (rate) => {
          easyplayer.forward(rate)
          this.$emit('rateChange', rate)
        })
        
        easyplayer.on('playbackSeek', (data) => {
          easyplayer.setPlaybackStartTime(data.ts)
          this.$emit('seek', data)
        })
        
        easyplayer.on('play', () => {
          this.$emit('play')
        })
        
        easyplayer.on('pause', () => {
          this.$emit('pause')
        })
        
        easyplayer.on('error', (error) => {
          console.error('播放器错误:', error)
          this.$emit('error', error)
        })
        
        this.playerInfo = easyplayer
        this.isPlayerReady = true
        this.$emit('ready', easyplayer)
        
        // 自动播放
        if (this.autoPlay && this.videoUrl) {
          setTimeout(() => {
            this.play(this.videoUrl)
          }, this.playDelay)
        }
        
      } catch (error) {
        console.error('播放器初始化失败:', error)
        this.$emit('error', error)
      }
    },
    
    // 播放视频
    play(url = this.videoUrl) {
      if (!this.playerInfo) {
        console.warn('播放器未初始化')
        return Promise.reject(new Error('播放器未初始化'))
      }
      
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          this.playerInfo.play(url).then(() => {
            this.$emit('playing', url)
            resolve()
          }).catch((e) => {
            console.error('播放失败:', e)
            this.$emit('error', e)
            reject(e)
          })
        }, 0)
      })
    },
    
    // 暂停播放
    pause() {
      if (this.playerInfo) {
        this.playerInfo.pause()
      }
    },
    
    // 停止播放
    stop() {
      if (this.playerInfo) {
        this.playerInfo.stop()
      }
    },
    
    // 销毁播放器
    destroyPlayer() {
      if (this.playerInfo) {
        this.playerInfo.destroy()
        this.playerInfo = null
      }
      
      this.isPlayerReady = false
      
      // 移除事件监听
      window.removeEventListener('resize', this.fixSafariLayout)
      window.removeEventListener('orientationchange', this.handleOrientationChange)
      
      // 清理Safari布局修复定时器
      if (document.canvasfix) {
        clearInterval(document.canvasfix)
        document.canvasfix = null
      }
    },
    
    // 精准检测Safari浏览器
    isSafariBrowser() {
      const ua = navigator.userAgent
      const isIOS = /iPad|iPhone|iPod/.test(ua)
      const isSafari = /Safari/.test(ua) && !/Chrome|CriOS|Edg|FxiOS|OPX/.test(ua)
      return isSafari || (isIOS && !window.MSStream)
    },
    
    // 修复Safari布局问题
    fixSafariLayout() {
      if (!this.isSafariBrowser()) return
      
      // 清理之前的定时器
      if (document.canvasfix) {
        clearInterval(document.canvasfix)
      }
      
      document.canvasfix = setInterval(() => {
        const canvas = document.querySelector(`#${this.playerId} canvas`)
        if (canvas) {
          canvas.style["margin-left"] = "118px"
          canvas.style["margin-top"] = "-120px"
          canvas.style.transform = 'scale(0.70)'
          canvas.style.transformOrigin = '0 0'
          clearInterval(document.canvasfix)
          document.canvasfix = null
        }
      }, 500)
    },
    
    // 处理屏幕方向变化
    handleOrientationChange() {
      const isPortrait = window.matchMedia("(orientation: portrait)").matches
      this.$nextTick(() => {
        if (this.playerInfo) {
          this.playerInfo.resize() // 通知播放器重置尺寸
        }
      })
      this.$emit('orientationChange', isPortrait)
    },
    
    // 进入全屏
    enterFullscreen() {
      if (this.playerInfo) {
        const container = document.getElementById(this.playerId)
        if (container) {
          // 使用原生全屏API
          if (container.requestFullscreen) {
            container.requestFullscreen()
          } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen()
          } else if (container.mozRequestFullScreen) {
            container.mozRequestFullScreen()
          } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen()
          }
        }
      }
    },
    
    // 获取播放器实例
    getPlayer() {
      return this.playerInfo
    },
    
    // 检查播放器是否准备就绪
    isReady() {
      return this.isPlayerReady && this.playerInfo !== null
    }
  }
}
</script>

<style scoped>
.easy-player-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 视频容器 */
.player-pro-live {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 播放器元素 */
.player-pro-live > view {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover; /* 视频填充方式 */
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: auto; 
  background-color: transparent; 
}
</style>
