<template>
	<view class="onekey-game-control">
		<!-- 左上角头像区域，样式与 FishControl.vue 一致，只显示 seats 数据中的所有头像 -->
		<view class="avatar-container left" style=" right: auto; top: 5%;" v-if="gameInfo.is_full !== 2">
			<view class="avatar-item" v-for="(seat, index) in seats" :key="seat.id" style="margin: 0 2px; flex-direction: row; align-items: center;">
				<image
					:src="seat.status === 1 ? seat.avatar : `/static/dpad/p${seat.number}.png`"
					mode="aspectFill"
					class="avatar-img-group"
				/>
				<text class="avatar-name" style="margin-left: 8px; margin-top: 0;">{{ seat.status === 1 ? seat.nickname : '空闲中' }}</text>
				<!-- 可选：头像下方显示金币和状态信息 -->
				<text v-if="seat.status === 1" class="avatar-coin">{{ seat.coin ? seat.coin + '币' : '' }}</text>
			</view>
		</view>

		<!-- 游戏中时的左上角用户信息区域 - 三行显示 -->
		<view class="game-user-info-container" v-if="gameInfo.is_full === 2">
			<!-- 第一行：用户头像、昵称、座位号 -->
			<view class="user-info-row">
				<image
					:src="userInfo.avatar || `/static/dpad/p${gameInfo.number || 1}.png`"
					mode="aspectFill"
					class="user-avatar"
				/>
				<text class="user-nickname">{{ userInfo.nickname || '玩家' }}</text>
				<text class="user-seat">{{ gameInfo.number }}P</text>
			</view>

			<!-- 第二行：金币框 -->
			<view class="user-info-row">
				<view class="stat-box coin-box" @click="showRechargeDialog">
					<image src="/static/coin.png" class="stat-icon" />
					<text class="stat-value">{{ displayCoin || 0 }}</text>
					<image src="/static/add.png" class="add-icon" />
				</view>
			</view>

			<!-- 第三行：积分框 -->
			<view class="user-info-row">
				<view class="stat-box score-box" @click="showScoreExchangeDialog">
					<image src="/static/score.png" class="stat-icon" />
					<text class="stat-value">{{ userInfo.score || 0 }}</text>
					<image src="/static/change.png" class="exchange-icon" />
				</view>
			</view>
		</view>

		<!-- 右上角头像、静音按钮、设置按钮和退出按钮，结构与 FishControl.vue 一致 -->
		<view class="avatar-container right">
			<!-- 退出按钮 -->
			<button class="exit-button" @click="onExitClick">
				<text class="exit-icon">×</text>
			</button>
			
			<!-- 静音按钮 -->
			<view class="setting-button" @click="toggleVideoAudio">
				<image class="setting-btn-icon" :src="isVideoMuted ? '/static/dpad/off.png' : '/static/dpad/on.png'" alt="静音" />
			</view>
			<!-- 设置按钮 -->
			<button class="setting-button" @click="$emit('show-func-dialog')">
				<img class="setting-btn-icon" src="/static/func/gear.png" alt="设置" />
			</button>
			<view class="avatar-group-box" v-if="gameInfo.is_full !== 2">
				<view v-for="(user, idx) in onlineAvatars" :key="user.id" class="avatar-group-item" :style="{ zIndex: 100 - idx, marginLeft: idx === 0 ? '0' : '-12px' }">
					<image class="avatar-img-group" :src="user.avatar || `/static/dpad/p${(idx % 4) + 1}.png`" />
				</view>
			</view>
		</view>

		<!-- 底部按钮区域 -->
		<view class="button-container" v-if="gameInfo.is_full !== 2">
			<view class="button-column"
				v-for="(seat, index) in availableSeats"
				:key="seat.id">
				<view v-if="seat.status === 1" class="avatar-item" style="flex-direction: row; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.7); border-radius: 30px; padding: 5px 16px; margin-bottom: 16px; box-shadow: 0 2px 8px #0003; border: none;">
					<image class="avatar-img-group" :src="seat.avatar || `/static/dpad/p${seat.number}.png`" style="margin-right: 8px;" />
					<text class="avatar-name" style="color: #fff; font-size: 18px; font-weight: bold; max-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ seat.nickname }}</text>
				</view>
				<view v-else>
					<view class="seat-button-container" v-if="seat.status === 0">
						<view class="game-button" @click="startGame(seat)">
							<image src="/static/dpad/play.png" class="play-button-img" mode="aspectFit" />
						</view>
						<!-- 在开始按钮底部新增对应的座位头像 -->
						<image :src="`/static/dpad/p${seat.number}.png`" class="seat-avatar-img" mode="aspectFit" />
					</view>
				</view>
			</view>
		</view>

		<!-- 单键游戏操作区域 -->
		<view v-if="gameInfo.is_full === 2 && showControls && !isGameEnded" class="onekey-controls">
			<!-- 动态按钮区域 -->
			<view class="control-buttons">
				<button
					v-for="button in controlButtons"
					:key="button.id"
					class="control-button"
					:class="{
						'active': pressedButtons[button.id],
						'cooldown': (buttonCooldowns[button.id] || 0) > 0,
						[`position-${button.position}`]: true
					}"
					@touchstart="handleButtonStart(button)"
					@touchend="handleButtonEnd(button)"
					@touchcancel="handleButtonEnd(button)"
					@mousedown="handleButtonStart(button)"
					@mouseup="handleButtonEnd(button)"
					@mouseleave="handleButtonEnd(button)"
					:disabled="(buttonCooldowns[button.id] || 0) > 0">
					<view class="button-content">
						<text class="button-text" v-if="!buttonCooldowns[button.id] || buttonCooldowns[button.id] <= 0">{{ button.name }}</text>
						<text class="cooldown-text" v-else>{{ ((buttonCooldowns[button.id] || 0) / 10).toFixed(1) }}s</text>
						<text class="button-value" v-if="!buttonCooldowns[button.id] || buttonCooldowns[button.id] <= 0">{{ button.value }}币</text>
					</view>
					<view class="button-effect" v-if="pressedButtons[button.id]"></view>
				</button>
			</view>

			<!-- 操作提示 -->
			<view class="operation-bar">
				<!-- 托管图片按钮，无 button 框 -->
				<image
					:src="autoMode ? '/static/dpad/auto_stop.png' : '/static/dpad/auto.png'"
					class="action-btn-img auto-btn-img"
					v-if="gameInfo.is_full === 2"
					@click="handleAuto"
					mode="aspectFit"
				/>
				<view class="operation-hint">
					<text>{{ operationHint }}</text>
				</view>
				<image
					class="action-btn-img wiper-btn-img"
					src="/static/dpad/wiper.png"
					v-if="gameInfo.is_full === 2"
					@click="handleWiper"
					mode="aspectFit"
				/>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'OnekeyControl',
	emits: [
		'show-func-dialog',
		'toggleVideoAudio',
		'show-exit-dialog',
		'exitGame',
		'startGame',
		'show-settle-mask',
		'show-score-exchange-dialog'
	],
	props: {
		gameInfo: {
			type: Object,
			default: () => ({})
		},
		userInfo: {
			type: Object,
			default: () => ({})
		},
		seats: {
			type: Array,
			default: () => []
		},
		isVideoMuted: {
			type: Boolean,
			default: false
		},
		cachedCoin: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			// 动态按钮状态
			pressedButtons: {}, // 记录每个按钮的按下状态
			buttonCooldowns: {}, // 记录每个按钮的冷却时间
			cooldownIntervals: {}, // 记录每个按钮的冷却定时器
			operationHint: '选择投币数量进行操作',

			// 金币显示相关
			displayCoin: 0, // 显示的金币数量

			// 消息冒泡相关
			showBubble: false,
			bubbleMessage: '',
			bubbleFadeOut: false,
			bubbleTimer: null,

			// 按钮状态
			isStartGameDisabled: false,
			startGameCooldown: 0, // 开始游戏冷却时间（秒）
			startGameCooldownTimer: null,

			// 游戏控制状态
			isGameEnded: false, // 游戏是否已结束
			showControls: true, // 是否显示控制按钮
			autoMode: false, // 托管状态
			autoInterval: null, // 托管定时器
		}
	},
	computed: {
		gameStatusText() {
			if (!this.gameInfo) return '加载中...'
			switch (this.gameInfo.is_full) {
				case 0: return '当前空闲'
				case 1: return '座位已满'
				case 2: return this.userInfo.nickname || '正在游戏中'
				default: return '等待启动中...'
			}
		},

		// 过滤可用座位（排除隐藏的座位）
		availableSeats() {
			return this.seats.filter(seat => seat.status !== undefined && seat.status !== null)
		},

		// 右上角在线玩家头像（与 FishControl.vue 逻辑一致）
		onlineAvatars() {
			return this.seats.filter(seat => seat.status === 1).map(seat => ({
				id: seat.id,
				avatar: seat.avatar
			}))
		},

		// 解析控制按钮配置
		controlButtons() {
			// 如果没有配置或配置为空，返回默认的启动按钮
			if (!this.gameInfo || !this.gameInfo.control_config || this.gameInfo.control_config.trim() === '') {
				return [
					{ id: 51, name: '启动', position: 'center', value: 1 }
				]
			}

			try {
				// 尝试解析 JSON 字符串
				const config = JSON.parse(this.gameInfo.control_config)

				if (config.buttons && Array.isArray(config.buttons)) {
					return config.buttons.map(button => ({
						id: button.id || 51,
						name: button.name || '投币',
						position: button.position || 'center',
						value: button.value || 1
					}))
				}
			} catch (e) {
				console.warn('解析 control_config 失败:', e)
			}

			// 如果解析失败或格式不正确，返回默认的启动按钮
			return [
				{ id: 51, name: '启动', position: 'center', value: 1 }
			]
		},
		autoBtnText() {
			return this.autoMode ? '取消' : '托管'
		}
	},

	watch: {
		// 监听缓存金币变化（优先级最高）
		cachedCoin: {
			handler(newVal) {
				if (newVal !== undefined && newVal !== null) {
					this.displayCoin = newVal
					console.log('OnekeyControl: 缓存金币更新 (通过prop):', newVal)
				}
			},
			immediate: true
		},
		// 监听用户金币变化，实时更新显示（仅在游戏开始前）
		'userInfo.money': {
			handler(newVal) {
				// 只有在游戏未开始时且没有缓存金币时才从用户信息更新金币
				if (newVal !== undefined && newVal !== null &&
					(!this.gameInfo || this.gameInfo.is_full !== 2) &&
					this.cachedCoin === 0) {
					this.displayCoin = newVal
					console.log('用户金币更新:', newVal)
				}
			},
			immediate: true
		},
		// 监听游戏状态变化
		'gameInfo.is_full': {
			handler(newVal, oldVal) {
				console.log('游戏状态变化:', oldVal, '->', newVal)
				// 游戏开始时（从其他状态变为2），设置初始金币
				if (newVal === 2 && oldVal !== 2) {
					// 游戏开始，重置游戏结束状态
					this.isGameEnded = false
					this.showControls = true

					// 优先使用缓存金币，否则使用用户金币
					const coinToUse = this.cachedCoin > 0 ? this.cachedCoin : this.userInfo?.money || 0
					this.displayCoin = coinToUse
					console.log('游戏开始，设置初始金币:', coinToUse)
				}
				// 游戏结束时（从2变为其他状态），重新初始化金币显示
				else if (oldVal === 2 && newVal !== 2) {
					// 游戏状态变为非进行中，重置控制状态
					this.isGameEnded = false
					this.showControls = true
					this.operationHint = '选择投币数量进行操作'
					this.initializeCoinDisplay()
					console.log('游戏状态变为非进行中，重置控制状态')
				}
			}
		},
		isVideoMuted(newVal) {
		}
	},

	mounted() {
		console.log('OnekeyControl 组件已挂载')
		// 组件挂载时初始化金币显示
		this.initializeCoinDisplay()

		// 直接监听 WebSocket 消息
		this.setupWebSocketListener()

		// 测试事件系统
		setTimeout(() => {
			console.log('OnekeyControl 测试事件发送')
			uni.$emit('test-event', 'test-data')
		}, 2000)
	},

	methods: {
		// 初始化金币显示
		initializeCoinDisplay() {
			let coinValue = 0

			// 如果游戏已开始（is_full === 2），金币应该已经在游戏缓存中
			if (this.gameInfo && this.gameInfo.is_full === 2) {
				// 游戏中状态，保持当前 displayCoin 值
				console.log('游戏中状态，保持当前金币显示:', this.displayCoin)
				return
			}

			// 游戏前状态，从用户信息获取金币
			if (this.userInfo && this.userInfo.money !== undefined) {
				coinValue = this.userInfo.money
			} else if (this.gameInfo && this.gameInfo.userInfo && this.gameInfo.userInfo.money !== undefined) {
				coinValue = this.gameInfo.userInfo.money
			}

			this.displayCoin = coinValue
			console.log('初始化金币显示:', coinValue, '游戏状态:', this.gameInfo?.is_full)
		},

		// 设置 WebSocket 监听
		setupWebSocketListener() {
			console.log('OnekeyControl: 设置 WebSocket 监听')

			// 监听全局 WebSocket 消息事件
			uni.$on('websocket-message', (data) => {
				console.log('OnekeyControl: 收到事件总线消息', data?.type)
				this.handleWebSocketMessage(data)
			})

			// 监听测试事件
			uni.$on('test-event', (data) => {
				console.log('OnekeyControl: 收到测试事件', data)
			})

			// 监听用户确认退出事件
			uni.$on('user-confirmed-exit', (data) => {
				console.log('OnekeyControl: 用户确认退出，隐藏控制按钮', data)
				this.handleUserConfirmedExit(data)
			})
		},

		// 处理 WebSocket 消息
		handleWebSocketMessage(data) {
			if (!data) return

			console.log('OnekeyControl 收到 WebSocket 消息:', data.type)

			if (data.type === 'coin_update' && data.code === 200) {
				this.handleDirectCoinUpdate(data)
			} else if (data.type === 'queryGameState_back' && data.code === 200) {
				this.handleDirectGameStateUpdate(data)
			}
			// 移除自动游戏结束检测，只在用户确认时隐藏按钮
		},

		// 直接处理金币更新
		handleDirectCoinUpdate(data) {
			if (data.cachedCoin !== undefined) {
				const oldCoin = this.displayCoin
				this.displayCoin = data.cachedCoin
				console.log('OnekeyControl 直接金币更新:', oldCoin, '->', data.cachedCoin)
				this.$forceUpdate()
			}
		},

		// 直接处理游戏状态更新
		handleDirectGameStateUpdate(data) {
			if (data.data && data.data.cachedCoin !== undefined) {
				const oldCoin = this.displayCoin
				this.displayCoin = data.data.cachedCoin
				console.log('OnekeyControl 直接游戏状态更新:', oldCoin, '->', data.data.cachedCoin)
				this.$forceUpdate()
			}
		},

		// 处理用户确认退出
		handleUserConfirmedExit(data) {
			console.log('OnekeyControl: 用户确认退出，隐藏控制按钮', data)

			// 用户确认退出，立即隐藏所有控制按钮
			this.isGameEnded = true
			this.showControls = false
			this.operationHint = '正在返回首页...'

			console.log('用户确认退出，隐藏所有控制按钮')

			// 清理所有按钮状态
			this.pressedButtons = {}
			this.buttonCooldowns = {}

			// 清理所有定时器
			Object.values(this.cooldownIntervals).forEach(interval => {
				if (interval) {
					clearInterval(interval)
				}
			})
			this.cooldownIntervals = {}
		},
		// 获取玩家头像
		getPlayerAvatar(seat) {
			const player = this.gameInfo.players?.find(p => p.seat_id === seat.id)
			return player?.avatar || `/static/dpad/p${seat.number || 1}.png`
		},

		// 获取玩家名称
		getPlayerName(seat) {
			const player = this.gameInfo.players?.find(p => p.seat_id === seat.id)
			return player?.nickname || '空座位'
		},

		// 显示充值对话框 - 跳转到充值页面
		showRechargeDialog() {
			console.log('点击金币，跳转到充值页面')

			// 保存游戏信息到本地存储，以便充值页面知道来源
			const gameInfo = {
				gameId: this.gameInfo?.id,
				gameName: this.gameInfo?.name,
				is_landscape: this.gameInfo?.is_landscape, // 添加横竖屏参数
				fromGame: true,
				timestamp: Date.now()
			}
			uni.setStorageSync('recharge_from_game', gameInfo)

			uni.switchTab({
				url: '/pages/pay/index'
			})
		},

		// 显示积分兑换对话框 - 显示兑换弹窗
		showScoreExchangeDialog() {
			console.log('点击积分，显示兑换弹窗')
			this.$emit('show-score-exchange-dialog')
		},
		
		// 切换视频音频
		toggleVideoAudio() {
			this.$emit('toggleVideoAudio')
		},
		
		// 退出游戏
		onExitClick() {
			// 只判断是否在游戏中，emit 事件通知主页面
			if (this.gameInfo.is_full === 2) {
				this.$emit('show-exit-dialog');
			} else {
				this.$emit('exitGame');
			}
		},
		
		// 开始游戏
		startGame(seat) {
			if (this.isStartGameDisabled) return

			// 禁用按钮，避免重复点击
			this.isStartGameDisabled = true
			this.startGameCooldown = 3 // 3秒冷却

			// 开始倒计时
			this.startGameCooldownTimer = setInterval(() => {
				this.startGameCooldown--
				if (this.startGameCooldown <= 0) {
					this.isStartGameDisabled = false
					clearInterval(this.startGameCooldownTimer)
					this.startGameCooldownTimer = null
				}
			}, 1000)

			this.$emit('startGame', seat)
		},

		// 处理金币更新消息
		handleCoinUpdate(data) {
			if (data.type === 'coin_update' && data.code === 200) {
				// 实时更新金币显示
				if (data.cachedCoin !== undefined) {
					this.displayCoin = data.cachedCoin
					console.log('金币更新:', data.cachedCoin, '已使用:', data.coinUsed)
				}
			}
		},

		// 显示消息冒泡
		showMessageBubble(message) {
			console.log('showMessageBubble被调用，消息:', message)
			// 清除之前的定时器
			if (this.bubbleTimer) {
				clearTimeout(this.bubbleTimer)
			}

			this.bubbleMessage = message
			this.showBubble = true
			this.bubbleFadeOut = false
			console.log('消息冒泡状态设置完成，showBubble:', this.showBubble)

			// 2秒后开始淡出动画
			this.bubbleTimer = setTimeout(() => {
				this.bubbleFadeOut = true

				// 淡出动画完成后隐藏
				setTimeout(() => {
					this.showBubble = false
					this.bubbleFadeOut = false
				}, 500) // 淡出动画时间
			}, 2000) // 显示时间
		},

		// 处理消息接收确认
		handleMessageReceived(data) {
			console.log('OnekeyControl收到消息接收确认:', data)
			if (data.type === 'message_received' && data.code === 200) {
				const message = data.message || '消息已接收，正在处理中...'
				console.log('准备显示消息冒泡:', message)
				this.showMessageBubble(message)
			}
		},
		
		// 按钮开始处理
		handleButtonStart(button) {
			if (this.pressedButtons[button.id] || (this.buttonCooldowns[button.id] || 0) > 0) return

			console.log('金币检查:', {
				displayCoin: this.displayCoin,
				buttonValue: button.value,
				gameStatus: this.gameInfo?.is_full
			})

			// 检查金币是否足够（游戏中只使用 displayCoin）
			if (this.displayCoin < button.value) {
				this.showMessageBubble(`金币不足，需要${button.value}币，当前仅有${this.displayCoin}币`)
				return
			}

			this.$set(this.pressedButtons, button.id, true)
			console.log(`投币按钮: 开始操作 - ${button.name} (${button.value}币)`)
			this.operationHint = `正在${button.name}...`

			// 发送投币指令
			this.sendGameCommand(button.id)
		},

		// 按钮结束处理
		handleButtonEnd(button) {
			if (!this.pressedButtons[button.id]) return

			this.$set(this.pressedButtons, button.id, false)
			console.log(`投币按钮: 结束操作 - ${button.name}`)
			this.operationHint = '操作完成，等待冷却...'

			// 开始冷却
			this.startButtonCooldown(button.id)
		},

		// 开始按钮冷却
		startButtonCooldown(buttonId) {
			this.$set(this.buttonCooldowns, buttonId, 5) // 0.5秒冷却
			this.cooldownIntervals[buttonId] = setInterval(() => {
				this.buttonCooldowns[buttonId]--
				if (this.buttonCooldowns[buttonId] <= 0) {
					this.$set(this.buttonCooldowns, buttonId, 0)
					clearInterval(this.cooldownIntervals[buttonId])
					delete this.cooldownIntervals[buttonId]
					this.operationHint = '选择投币数量进行操作'
				}
			}, 100) // 每100ms减1
		},

		// 处理金币更新消息（从父组件调用）
		handleCoinUpdate(data) {
			console.log('OnekeyControl.handleCoinUpdate 被调用:', data)
			console.log('当前 displayCoin:', this.displayCoin)

			if (data.type === 'coin_update' && data.code === 200) {
				if (data.cachedCoin !== undefined) {
					const oldCoin = this.displayCoin
					this.displayCoin = data.cachedCoin
					console.log('OnekeyControl 金币更新:', oldCoin, '->', data.cachedCoin, '已使用:', data.coinUsed)

					// 强制触发响应式更新
					this.$forceUpdate()
				}
			} else {
				console.warn('OnekeyControl 金币更新条件不满足:', data.type, data.code)
			}
		},

		// 处理游戏状态更新（从父组件调用）
		handleGameStateUpdate(gameState) {
			console.log('OnekeyControl 游戏状态更新:', gameState)

			// 更新缓存金币显示
			if (gameState.cachedCoin !== undefined) {
				this.displayCoin = gameState.cachedCoin
				console.log('从游戏状态更新金币显示:', gameState.cachedCoin)
			}

			// 可以在这里添加其他游戏状态相关的UI更新
			if (gameState.gameStatus === 'playing') {
				this.operationHint = '游戏进行中，可以投币操作'
			} else {
				this.operationHint = '选择投币数量进行操作'
			}
		},
		
		// 发送游戏指令 - 独立的WebSocket通信
		sendGameCommand(command) {
			// 直接发送WebSocket指令，不依赖父组件
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const message = {
					type: 'command',
					number: this.gameInfo.number,
					status: command,
				}
				// GameWebSocket的send方法内部会自动调用JSON.stringify，所以直接传对象
				window.gameWebSocket.send(message)
				console.log('单键游戏发送指令:', command, '消息:', message)
			} else {
				console.warn('WebSocket未连接，无法发送指令')
			}
		},
		handleSettleAndExit() {
			// 发送 endGame 指令，结算并退出
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const message = {
					type: 'endGame',
					gameLogId: this.gameInfo.gameLogId
				};
				window.gameWebSocket.send(message);
			} else {
				console.warn('WebSocket未连接，无法发送endGame指令');
			}
			this.$emit('exitGame');
		},
		handleSettleCurrent() {
			// 发送 endGame 指令，结算本局
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const message = {
					type: 'endGame',
					gameLogId: this.gameInfo.gameLogId
				};
				window.gameWebSocket.send(message);
			} else {
				console.warn('WebSocket未连接，无法发送endGame指令');
			}
			this.$emit('show-settle-mask', true);
		},
		// 托管按钮点击事件
		handleAuto() {
			this.autoMode = !this.autoMode
			if (this.autoMode) {
				this.autoInterval = setInterval(() => {
					if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
						const message = {
							type: 'command',
							number: this.gameInfo.number,
							status: 2, // type: '02' 对应 status: 2
						}
						window.gameWebSocket.send(message)
					}
				}, 1000)
			} else {
				if (this.autoInterval) {
					clearInterval(this.autoInterval)
					this.autoInterval = null
				}
			}
		},
		// 雨刷按钮点击事件
		handleWiper() {
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const message = {
					type: 'command',
					number: this.gameInfo.number,
					status: 3, // type: '03' 对应 status: 3
				}
				window.gameWebSocket.send(message)
			}
		}
	},
	
	// 组件销毁时清理定时器
	beforeUnmount() {
		// 清理所有按钮冷却定时器
		Object.values(this.cooldownIntervals).forEach(interval => {
			if (interval) {
				clearInterval(interval)
			}
		})
		this.cooldownIntervals = {}

		if (this.bubbleTimer) {
			clearTimeout(this.bubbleTimer)
		}
		if (this.startGameCooldownTimer) {
			clearInterval(this.startGameCooldownTimer)
		}
		if (this.autoInterval) {
			clearInterval(this.autoInterval)
			this.autoInterval = null
		}

		// 清理事件监听
		uni.$off('websocket-message')
		uni.$off('test-event')
		uni.$off('user-confirmed-exit')
	}
}
</script>

<style scoped>
/* 单键游戏控制界面样式 */
.onekey-game-control {
	position: absolute;
	width: 100%;
	height: 100%;
}

/* 这些样式已删除，因为没有在模板中使用 */

/* 消息冒泡样式 */
.message-bubble {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 10;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 15px 25px;
	border-radius: 25px;
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	animation: bubbleIn 0.3s ease-out;
	max-width: 80%;
	text-align: center;
}

.message-bubble.fade-out {
	animation: bubbleOut 0.5s ease-in forwards;
}

.message-bubble text {
	font-size: 16px;
	font-weight: 500;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes bubbleIn {
	0% {
		opacity: 0;
		transform: translate(-50%, -50%) scale(0.8);
	}
	100% {
		opacity: 1;
		transform: translate(-50%, -50%) scale(1);
	}
}

@keyframes bubbleOut {
	0% {
		opacity: 1;
		transform: translate(-50%, -50%) scale(1);
	}
	100% {
		opacity: 0;
		transform: translate(-50%, -50%) scale(0.8);
	}
}

/* 左上角头像容器 */
.avatar-container.left {
	position: absolute;
	top: 5%;
	left: 2%;
	display: flex;
	align-items: center;
	background-color: rgba(0,0,0,0.7);
	border-radius: 30px;
	padding: 5px 10px;
	backdrop-filter: blur(4px);
	z-index: 9999;
}

/* 头像项目 */
.avatar-item {
	display: flex;
	flex-direction: row;
	align-items: center;
}

/* 头像图片 */
.avatar-img-group {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px #0003;
}

/* 头像名称 */
.avatar-name {
	color: white;
	font-size: 12px;
	margin-left: 8px;
	max-width: 60px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 右上角头像容器 */
.avatar-container.right {
  position: absolute;
  top: 5%;
  right: 2%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0,0,0,0.7);
  border-radius: 30px;
  padding: 10px 5px;
  backdrop-filter: blur(4px);
  z-index: 9999;
  gap: 8px;
}

/* 头像组容器 */
.avatar-group-box {
  display: flex;
  align-items: center;
  position: relative;
  flex-direction: column;
  gap: 4px;
}

.avatar-group-item {
  position: relative;
  margin-left: -12px;
}

.avatar-group-item:first-child {
  margin-left: 0;
}
/* 右上角按钮通用样式 */
.exit-button, .setting-button {
  width: 30px;
  height: 30px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  margin-top: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
}

.setting-button:first-child,
.exit-button:first-child {
  margin-top: 0;
}

/* 退出按钮 */
.exit-button {
  background-color: rgba(255, 0, 0, 0.8);
  color: white;
  border-radius: 20px;
  backdrop-filter: blur(4px);
}

/* 设置按钮（包括静音按钮） */
.setting-button {
  background: none;
  color: white;
}

.setting-btn-icon {
  width: 20px;
  height: 20px;
  display: block;
}

/* 游戏中的用户信息容器 - 三行布局 */
.game-user-info-container {
	position: absolute;
	top: 5%;
	left: 2%;
	display: flex;
	flex-direction: column;
	gap: 8px;
	z-index: 3;
}

/* 用户信息行 */
.user-info-row {
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 30px;
	padding: 5px 10px;
	display: flex;
	align-items: center;
	gap: 8px;
	backdrop-filter: blur(4px);
	box-shadow: 0 2px 8px #0003;
	min-width: 120px;
}

.user-avatar {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	border: 2px solid #fff;
	box-shadow: 0 2px 8px #0003;
	flex-shrink: 0;
}

.user-nickname {
	color: #fff;
	font-size: 16px;
	font-weight: bold;
	max-width: 80px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	flex: 1;
}

.user-seat {
	color: #F17676;
	font-size: 14px;
	font-weight: bold;
	flex-shrink: 0;
}

/* 统计信息框 */
.stat-box {
	/* background-color: rgba(0, 0, 0, 0.7); */
	/* border-radius: 30px; */
	/* padding: 5px 10px; */
	display: flex;
	align-items: center;
	gap: 8px;
	/* backdrop-filter: blur(4px); */
	/* box-shadow: 0 2px 8px #0003; */
	min-width: 80px;
	cursor: pointer;
}

.stat-box:active {
	transform: scale(0.95);
	opacity: 0.8;
}

.stat-icon {
	width: 20px;
	height: 20px;
	flex-shrink: 0;
}

.stat-value {
	color: #fff;
	font-size: 16px;
	font-weight: bold;
	flex: 1;
}

.add-icon,
.exchange-icon {
	width: 20px;
	height: 20px;
	flex-shrink: 0;
	opacity: 0.8;
}

/* 座位按钮容器 - 上下两行显示 */
.seat-button-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 8px;
}

/* 座位头像样式 */
.seat-avatar-img {
	width: 60rpx;
	height: 60rpx;
	display: block;
	border-radius: 50%;
	border: 2px solid #fff;
	box-shadow: 0 2px 8px #0003;
	margin-bottom: 85rpx;
}

/* 底部按钮区域样式同步 FishControl.vue */
.button-container {
  position: absolute;
  bottom: 15%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 30px;
  z-index: 3;
  justify-content: center;
}
.button-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 0;
}
.game-button {
	background: none;
	border: none;
	outline: none;
	padding: 0;
	margin: 0 40rpx;
	margin-bottom: 5rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	box-shadow: none;
}
.button-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.game-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}
.play-button-img {
	width: 150rpx;
	height: 150rpx;
	display: block;
}
/* 这些样式已删除，因为重复或未使用 */

/* 单键控制区域 */
.onekey-controls {
	position: absolute;
	bottom: 5%;
	left: 0;
	right: 0;
	z-index: 2;
	display: flex;
	flex-direction: column;
	align-items: center;
}

/* 控制按钮容器 */
.control-buttons {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: center;
	align-items: center;
	gap: 10px;
	max-width: 95vw;
	padding: 0 10px;
}

/* 控制按钮样式 */
.control-button {
	flex: 0 0 calc(30% - 10px);
	min-width: 100px;
	max-width: 140px;
	height: 70px;
	border-radius: 15px;
	border: none;
	background: linear-gradient(45deg, #2196F3, #1976D2);
	color: white;
	font-size: 14px;
	font-weight: bold;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	backdrop-filter: blur(4px);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.control-button:active:not(:disabled) {
	transform: scale(0.95);
}

.control-button.active {
	background: linear-gradient(45deg, #4CAF50, #45a049);
	box-shadow: 0 0 25px rgba(76, 175, 80, 0.6);
}

.control-button.cooldown {
	background: linear-gradient(45deg, #999, #666);
	cursor: not-allowed;
}

/* 按钮位置样式 */
.control-button.position-left {
	order: 1;
}

.control-button.position-center-left {
	order: 2;
}

.control-button.position-center {
	order: 3;
}

.control-button.position-center-right {
	order: 4;
}

.control-button.position-right {
	order: 5;
}

.button-content {
	position: relative;
	z-index: 2;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
}

.button-text, .cooldown-text {
	white-space: nowrap;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
	font-size: 14px;
	margin-bottom: 2px;
	line-height: 1.2;
}

.button-value {
	font-size: 11px;
	opacity: 0.9;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
	line-height: 1;
}

.button-effect {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
	border-radius: 50%;
	animation: pulse 0.6s ease-out;
}

@keyframes pulse {
	0% {
		transform: scale(0);
		opacity: 1;
	}
	100% {
		transform: scale(1);
		opacity: 0;
	}
}

/* 操作提示 */
.operation-hint {
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 15px;
	padding: 10px 20px;
	backdrop-filter: blur(4px);
	margin: 0 18rpx;
}

.operation-hint text {
	color: white;
	font-size: 14px;
	text-align: center;
}

/* 操作栏 */
.operation-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin: 10px 0 18% 0;
}

/* 按钮图片样式 */
.action-btn-img {
	width: 48rpx;
	height: 48rpx;
	display: block;
}
.auto-btn-img,.wiper-btn-img {
	width: 92rpx;
	height: 92rpx;
}
.action-btn-img:active {
	transform: scale(0.92);
}

</style>
