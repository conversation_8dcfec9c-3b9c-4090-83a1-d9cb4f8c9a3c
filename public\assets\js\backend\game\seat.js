define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'game/seat/index',
                    add_url: 'game/seat/add',
                    edit_url: '',
                    del_url: '',
                    multi_url: 'game/seat/multi',
                    table: 'game',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='c_id']", form).addClass("selectpage").data("source", "game/category/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc").data("multiple","true");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'id',
                sortName: 'id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true, operate:false},
                        {field: 'game_id',title: __('所属游戏'),formatter: function (value, row, index) {
                                if (row.game_name) {
                                    return row.game_name+'<br>'+row.game_id;
                                }else{
                                    return row.game_id;
                                }
                            }},
                        {field: 'number', title: __('座位号')},
                        {field: 'status', title: __('状态'), formatter: Table.api.formatter.status, searchList: {"0": __('空闲'), "1": __('进行中')}},
                        {field: 'createtime', title: __('创建时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},

                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});