<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>WebRTC 移动端视频流接收端</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
        }
        h1 {
            font-size: 1.5rem;
            text-align: center;
            margin: 10px 0;
        }
        .video-container {
            position: relative;
            width: 100%;
            background-color: #000;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        video {
            width: 100%;
            height: auto;
            display: block;
        }
        .controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 15px;
        }
        .input-group {
            display: flex;
            flex-direction: row;
            gap: 5px;
        }
        input, button, select {
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            -webkit-appearance: none;
        }
        input {
            flex: 1;
            min-width: 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: bold;
            min-width: 80px;
        }
        button:hover, button:active {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
            background-color: #f8f8f8;
            border-left: 4px solid #4CAF50;
            font-size: 14px;
        }
        .error {
            border-left-color: #f44336;
            color: #f44336;
        }
        .log-container {
            max-height: 150px;
            overflow-y: auto;
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
        }
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .log-time {
            color: #666;
            margin-right: 5px;
        }
        .log-info {
            color: #2196F3;
        }
        .log-error {
            color: #f44336;
        }
        .log-success {
            color: #4CAF50;
        }
        .sender-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 10px 0;
        }
        .sender-item {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 14px;
            flex: 1;
            min-width: 120px;
            text-align: center;
        }
        .sender-item:hover, .sender-item:active {
            background-color: #e0e0e0;
        }
        .sender-item.active {
            background-color: #4CAF50;
            color: white;
        }
        .button-group {
            display: flex;
            gap: 10px;
        }
        .button-group button {
            flex: 1;
        }
        .fullscreen-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10;
        }
        .fullscreen-btn svg {
            width: 24px;
            height: 24px;
        }
        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px 20px;
            border-radius: 20px;
            display: none;
        }
        .loading-indicator.active {
            display: block;
        }

        /* 视频控制样式 */
        .video-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px;
            display: flex;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .video-container:hover .video-controls,
        .video-controls:hover,
        .video-controls.active {
            opacity: 1;
        }

        .control-btn {
            background: transparent;
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            cursor: pointer;
            padding: 8px;
            margin-right: 10px;
            border-radius: 50%;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .control-btn svg {
            width: 24px;
            height: 24px;
        }

        .seek-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .seek-slider {
            width: 100%;
            height: 4px;
            -webkit-appearance: none;
            background: rgba(255, 255, 255, 0.3);
            outline: none;
            border-radius: 2px;
            margin-bottom: 5px;
        }

        .seek-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
        }

        .time-display {
            color: white;
            font-size: 12px;
            text-align: right;
        }

        /* 针对移动设备的优化 */
        @media (max-width: 768px) {
            .container {
                padding: 5px;
            }
            h1 {
                font-size: 1.2rem;
                margin: 5px 0;
            }
            .controls {
                gap: 5px;
            }
            input, button, select {
                padding: 10px;
                font-size: 14px;
            }
            .status {
                padding: 8px;
                font-size: 12px;
            }
            .log-container {
                max-height: 100px;
                font-size: 10px;
            }
            .sender-item {
                padding: 8px;
                font-size: 12px;
                min-width: 100px;
            }
        }

        /* iOS特定样式 */
        .ios-device .video-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            -webkit-overflow-scrolling: none;
        }

        .ios-device video {
            object-fit: contain;
            width: 100% !important;
            height: 100% !important;
            position: absolute;
            top: 0;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC 移动端视频流接收端</h1>

        <div class="video-container">
            <video id="video" autoplay playsinline muted webkit-playsinline
                   x-webkit-airplay="allow"
                   preload="auto"
                   style="width: 100%; height: 100%; object-fit: contain;"></video>
            <button class="fullscreen-btn" id="fullscreen-btn">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
                    <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                </svg>
            </button>
            <div class="loading-indicator" id="loading-indicator">连接中...</div>

            <!-- 视频控制面板 -->
            <div class="video-controls" id="video-controls" style="display: none;">
                <button id="play-pause-btn" class="control-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" class="play-icon">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" class="pause-icon" style="display:none;">
                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                    </svg>
                </button>
                <div class="seek-container">
                    <input type="range" id="seek-slider" min="0" max="100" value="0" step="1" class="seek-slider">
                    <div class="time-display">
                        <span id="current-time">00:00</span> / <span id="duration">00:00</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="input-group">
                <input type="text" id="signaling-url" value="wss://sling.91jdcd.com/ws/" placeholder="信令服务器URL">
            </div>
            <div class="input-group">
                <input type="text" id="sender-id" value="android-0dee0bba">
            </div>
            <div class="button-group">
                <button id="connect-btn">连接</button>
                <button id="disconnect-btn" disabled>断开</button>
                <button id="reconnect-btn" disabled>重连</button>
                <button id="quality-btn" disabled>高质量</button>
            </div>
        </div>

        <div class="status" id="status">
            未连接
        </div>

        <div class="sender-list" id="sender-list">
            <!-- 发送端列表将在这里动态生成 -->
        </div>

        <div class="log-container" id="log">
            <!-- 日志将在这里动态生成 -->
        </div>
    </div>

    <script>
        // 检测iOS设备
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        const isIOSChrome = isIOS && /CriOS/.test(navigator.userAgent);
        const isIOSSafari = isIOS && /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);

        // 记录设备信息
        console.log(`设备信息: iOS=${isIOS}, iOS Chrome=${isIOSChrome}, iOS Safari=${isIOSSafari}`);
        // WebRTC配置
        const config = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:stun2.l.google.com:19302' },
                { urls: 'stun:stun3.l.google.com:19302' },
                { urls: 'stun:stun4.l.google.com:19302' },
                { urls: 'stun:stun.ekiga.net' },
                { urls: 'stun:stun.ideasip.com' },
                { urls: 'stun:stun.schlund.de' },
                { urls: 'stun:stun.stunprotocol.org:3478' },
                { urls: 'stun:stun.voiparound.com' },
                { urls: 'stun:stun.voipbuster.com' },
                { urls: 'stun:stun.voipstunt.com' },
                { urls: 'stun:stun.voxgratia.org' },
                {
                    urls: 'turn:numb.viagenie.ca',
                    username: '<EMAIL>',
                    credential: 'muazkh'
                },
                {
                    urls: 'turn:turn.anyfirewall.com:443?transport=tcp',
                    username: 'webrtc',
                    credential: 'webrtc'
                }
            ],
            iceCandidatePoolSize: 10,
            iceTransportPolicy: 'all',
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require',
            sdpSemantics: 'unified-plan'
        };

        // iOS特定配置
        if (isIOS) {
            // 最新的iOS版本更适合使用unified-plan
            config.sdpSemantics = 'unified-plan';

            // 确保使用适合iOS的ICE配置
            config.iceTransportPolicy = 'all';

            // 增加ICE候选池大小，提高连接成功率
            config.iceCandidatePoolSize = 20;

            // 添加TCP和UDP的TURN服务器，提高NAT穿透能力
            config.iceServers.push(
                {
                    urls: 'turn:turn.anyfirewall.com:443?transport=tcp',
                    username: 'webrtc',
                    credential: 'webrtc'
                },
                {
                    urls: 'turn:turn.anyfirewall.com:443?transport=udp',
                    username: 'webrtc',
                    credential: 'webrtc'
                }
            );

            // 设置ICE收集超时时间，避免长时间等待
            config.iceGatheringTimeout = 5000;

            // 不要在这里调用log函数，因为logEl还没有初始化
            console.log('已应用iOS特定WebRTC配置');
        }


        const signalingUrlInput = document.getElementById('signaling-url');
        const senderIdInput = document.getElementById('sender-id');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const reconnectBtn = document.getElementById('reconnect-btn');
        const qualityBtn = document.getElementById('quality-btn');
        const statusEl = document.getElementById('status');
        const videoEl = document.getElementById('video');
        const logEl = document.getElementById('log');
        const senderListEl = document.getElementById('sender-list');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        const loadingIndicator = document.getElementById('loading-indicator');


        const videoControls = document.getElementById('video-controls');
        const playPauseBtn = document.getElementById('play-pause-btn');
        const playIcon = playPauseBtn.querySelector('.play-icon');
        const pauseIcon = playPauseBtn.querySelector('.pause-icon');
        const seekSlider = document.getElementById('seek-slider');
        const currentTimeEl = document.getElementById('current-time');
        const durationEl = document.getElementById('duration');


        let pc = null;
        let dataChannel = null;
        let ws = null;
        let receiverId = `receiver-${Math.random().toString(36).substring(2, 10)}`;
        let activeSenderId = null;
        let senders = [];
        let connectionTimeout = null;
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 3;


        let isPlaying = true;
        let videoDuration = 0;
        let currentPosition = 0;
        let isLocalFile = false;
        let seekUpdateInterval = null;
        let controlsTimeout = null;

        // 如果是iOS设备，添加特定的类名
        if (isIOS) {
            document.body.classList.add('ios-device');
        }


        function log(message, type = 'info') {
            const now = new Date();
            const timeStr = now.toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';

            const timeSpan = document.createElement('span');
            timeSpan.className = 'log-time';
            timeSpan.textContent = timeStr;

            const messageSpan = document.createElement('span');
            messageSpan.className = `log-${type}`;
            messageSpan.textContent = message;

            logEntry.appendChild(timeSpan);
            logEntry.appendChild(messageSpan);

            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;

            console.log(`[${type}] ${message}`);
        }


        function updateStatus(message, isError = false) {
            statusEl.textContent = message;
            if (isError) {
                statusEl.classList.add('error');
            } else {
                statusEl.classList.remove('error');
            }
        }


        function setLoading(isLoading) {
            if (isLoading) {
                loadingIndicator.classList.add('active');
            } else {
                loadingIndicator.classList.remove('active');
            }
        }


        async function connectToSignalingServer() {
            const url = signalingUrlInput.value;

            try {
                ws = new WebSocket(url);

                ws.onopen = () => {
                    log('已连接到信令服务器');
                    reconnectAttempts = 0;

                    // 确保WebSocket连接已经建立
                    setTimeout(() => {
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                type: 'register',
                                id: receiverId,
                                role: 'viewer',
                                name: '移动端接收端',
                                description: '移动设备WebRTC接收端'
                            }));
                            log('已发送注册信息');
                        } else {
                            log('WebSocket连接未就绪，无法发送注册信息', 'error');
                        }
                    }, 100); // 短暂延迟，确保连接已完全建立
                };

                ws.onmessage = async (event) => {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${data.type}`);

                    if (data.type === 'registered') {
                        updateStatus(`已注册，ID: ${receiverId}`);

                        ws.send(JSON.stringify({
                            type: 'list_clients'
                        }));
                    }
                    else if (data.type === 'client_list') {
                        handleClientList(data.clients || []);
                    }
                    else if (data.type === 'client_joined') {

                        if (data.role === 'source' || data.id.startsWith('sender-')) {
                            addSender(data);
                        }
                    }
                    else if (data.type === 'client_left') {

                        removeSender(data.id);
                    }
                    else if (data.type === 'offer' && data.from) {

                        try {

                            if (activeSenderId && data.from !== activeSenderId) {
                                log(`忽略来自 ${data.from} 的offer，当前连接到 ${activeSenderId}`);
                                return;
                            }


                            if (!activeSenderId) {
                                activeSenderId = data.from;
                                log(`设置活跃发送端为 ${activeSenderId}`);


                                if (!pc) {
                                    pc = new RTCPeerConnection(config);
                                    setupPeerConnection(pc, activeSenderId);
                                    log(`为 ${activeSenderId} 创建了新的PeerConnection`);




                                }
                            }


                            try {
                                // 在设置远程描述前，检查信令状态
                                if (pc.signalingState !== 'stable') {
                                    log(`当前信令状态: ${pc.signalingState}，尝试回滚`);

                                    // 如果信令状态不是stable，尝试回滚
                                    await pc.setLocalDescription({type: "rollback"});
                                    log('已回滚本地描述');
                                }

                                // 检查offer SDP是否包含视频媒体行
                                // let offerSdp = data.sdp;
                                // if (!offerSdp.includes('m=video')) {
                                //     log('警告：接收到的offer SDP中没有视频媒体行，尝试添加', 'error');

                                //     try {
                                //         // 添加视频媒体行的逻辑与之前类似
                                //         // 提取ICE和指纹信息
                                //         const iceUfrag = offerSdp.match(/a=ice-ufrag:(.*)/);
                                //         const icePwd = offerSdp.match(/a=ice-pwd:(.*)/);
                                //         const fingerprint = offerSdp.match(/a=fingerprint:sha-256 (.*)/);
                                //         const setup = offerSdp.match(/a=setup:(.*)/);

                                //         if (iceUfrag && icePwd && fingerprint && setup) {
                                //             // 创建视频媒体行
                                //             const videoSection = `m=video 9 UDP/TLS/RTP/SAVPF 96 97\r\n` +
                                //                 `c=IN IP4 0.0.0.0\r\n` +
                                //                 `a=rtcp:9 IN IP4 0.0.0.0\r\n` +
                                //                 `a=ice-ufrag:${iceUfrag[1]}\r\n` +
                                //                 `a=ice-pwd:${icePwd[1]}\r\n` +
                                //                 `a=ice-options:trickle\r\n` +
                                //                 `a=fingerprint:sha-256 ${fingerprint[1]}\r\n` +
                                //                 `a=setup:${setup[1]}\r\n` +
                                //                 `a=mid:1\r\n` +
                                //                 `a=sendrecv\r\n` +
                                //                 `a=rtcp-mux\r\n` +
                                //                 `a=rtcp-rsize\r\n` +
                                //                 `a=rtpmap:96 VP8/90000\r\n` +
                                //                 `a=rtcp-fb:96 nack\r\n` +
                                //                 `a=rtcp-fb:96 nack pli\r\n` +
                                //                 `a=rtcp-fb:96 ccm fir\r\n` +
                                //                 `a=rtpmap:97 H264/90000\r\n` +
                                //                 `a=rtcp-fb:97 nack\r\n` +
                                //                 `a=rtcp-fb:97 nack pli\r\n` +
                                //                 `a=rtcp-fb:97 ccm fir\r\n` +
                                //                 `a=fmtp:97 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\n`;

                                //             // 更新SDP中的媒体行组
                                //             let offerSdpLines = offerSdp.split('\r\n');
                                //             for (let i = 0; i < offerSdpLines.length; i++) {
                                //                 if (offerSdpLines[i].startsWith('a=group:BUNDLE')) {
                                //                     offerSdpLines[i] += ' 1';
                                //                     break;
                                //                 }
                                //             }

                                //             // 将视频媒体行添加到SDP中
                                //             offerSdp = offerSdpLines.join('\r\n') + '\r\n' + videoSection;
                                //             log('已添加视频媒体行到接收到的offer SDP');
                                //         }
                                //     } catch (e) {
                                //         log(`添加视频媒体行到接收到的offer SDP失败: ${e.message}`, 'error');
                                //     }
                                // }

                                const offer = new RTCSessionDescription({
                                    type: 'offer',
                                    sdp: offerSdp
                                });

                                await pc.setRemoteDescription(offer);
                                log('已设置远程描述(offer)');
                            } catch (e) {
                                log(`设置远程描述错误: ${e.message}，尝试重新创建连接`, 'error');

                                // 如果设置远程描述失败，尝试重新创建连接
                                disconnectFromPeer();
                                setTimeout(() => {
                                    pc = new RTCPeerConnection(config);
                                    setupPeerConnection(pc, activeSenderId);
                                    log(`为 ${activeSenderId} 重新创建了PeerConnection`);

                                    const offer = new RTCSessionDescription({
                                        type: 'offer',
                                        sdp: data.sdp
                                    });

                                    pc.setRemoteDescription(offer).then(() => {
                                        log('已设置远程描述(offer)');
                                        return pc.createAnswer();
                                    }).then(answer => {
                                        return pc.setLocalDescription(answer);
                                    }).then(() => {
                                        log('已设置本地描述(answer)');

                                        const formattedSdp = pc.localDescription.sdp.replace(/\r\n|\n|\r/g, '\\n');

                                        ws.send(JSON.stringify({
                                            type: 'answer',
                                            target: activeSenderId,
                                            sdp: formattedSdp
                                        }));
                                        log('已发送answer');
                                    }).catch(e => {
                                        log(`重新创建连接失败: ${e.message}`, 'error');
                                        disconnectFromPeer();
                                    });
                                }, 1000);

                                return; // 中断当前处理流程
                            }


                            if (window.pendingIceCandidates && window.pendingIceCandidates.length > 0) {
                                log(`添加 ${window.pendingIceCandidates.length} 个缓存的ICE候选`);
                                for (const candidate of window.pendingIceCandidates) {
                                    try {
                                        await pc.addIceCandidate(candidate);
                                        log('已添加缓存的ICE候选');
                                    } catch (e) {
                                        log(`添加缓存的ICE候选错误: ${e.message}`, 'error');
                                    }
                                }
                                window.pendingIceCandidates = [];
                            }


                            const answer = await pc.createAnswer();
                            await pc.setLocalDescription(answer);
                            log('已设置本地描述(answer)');



                            const formattedSdp = pc.localDescription.sdp.replace(/\r\n|\n|\r/g, '\\n');

                            ws.send(JSON.stringify({
                                type: 'answer',
                                target: activeSenderId,
                                sdp: formattedSdp
                            }));
                            log('已发送answer');

                            updateStatus(`正在连接到发送端 ${activeSenderId}...`);
                            setLoading(true);
                        } catch (e) {
                            log(`处理offer错误: ${e.message}`, 'error');
                            updateStatus(`处理offer错误: ${e.message}`, true);
                        }
                    }
                    else if (data.type === 'answer' && data.from === activeSenderId) {

                        try {
                            try {
                                const answer = new RTCSessionDescription({
                                    type: 'answer',
                                    sdp: data.sdp
                                });
                                await pc.setRemoteDescription(answer);
                                log('已设置远程描述(answer)');
                            } catch (e) {
                                log(`设置远程描述错误: ${e.message}`, 'error');
                                updateStatus(`设置远程描述错误: ${e.message}`, true);

                                // 如果是ICE重启相关的错误，尝试重新连接
                                if (e.message.includes('ICE restart')) {
                                    log('检测到ICE重启错误，尝试重新连接');
                                    disconnectFromPeer();
                                    setTimeout(() => {
                                        connectToPeer();
                                    }, 1000);
                                }
                                return; // 中断当前处理流程
                            }


                            if (window.pendingIceCandidates && window.pendingIceCandidates.length > 0) {
                                log(`添加 ${window.pendingIceCandidates.length} 个缓存的ICE候选`);
                                for (const candidate of window.pendingIceCandidates) {
                                    try {
                                        await pc.addIceCandidate(candidate);
                                        log('已添加缓存的ICE候选');
                                    } catch (e) {
                                        log(`添加缓存的ICE候选错误: ${e.message}`, 'error');
                                    }
                                }
                                window.pendingIceCandidates = [];
                            }



                            if (connectionTimeout) {
                                clearTimeout(connectionTimeout);
                                connectionTimeout = null;
                            }
                        } catch (e) {
                            log(`设置远程描述错误: ${e.message}`, 'error');
                            updateStatus(`设置远程描述错误: ${e.message}`, true);
                        }
                    }
                    else if (data.type === 'candidate' && data.from === activeSenderId) {

                        try {
                            const candidate = data.candidate;
                            if (candidate) {

                                if (!pc || !pc.remoteDescription) {
                                    if (!window.pendingIceCandidates) {
                                        window.pendingIceCandidates = [];
                                    }
                                    window.pendingIceCandidates.push(candidate);
                                    log('缓存ICE候选，等待远程描述设置');
                                } else {
                                    await pc.addIceCandidate(candidate);
                                    log('已添加ICE候选');
                                }
                            }
                        } catch (e) {
                            log(`添加ICE候选错误: ${e.message}`, 'error');
                        }
                    }
                };

                ws.onclose = () => {
                    log('信令服务器连接已关闭', 'error');
                    updateStatus('信令服务器连接已关闭', true);


                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        log(`尝试重新连接 (${reconnectAttempts}/${maxReconnectAttempts})...`);
                        setTimeout(connectToSignalingServer, 2000);
                    } else {
                        disconnectFromPeer();
                        connectBtn.disabled = false;
                        disconnectBtn.disabled = true;
                    }
                };

                ws.onerror = (error) => {
                    log(`信令服务器错误: ${error.message}`, 'error');
                    updateStatus(`信令服务器错误: ${error.message}`, true);
                };

            } catch (e) {
                log(`连接信令服务器错误: ${e.message}`, 'error');
                updateStatus(`连接信令服务器错误: ${e.message}`, true);
            }
        }


        function handleClientList(clients) {
            senders = clients.filter(client =>
                client.role === 'source' || client.id.startsWith('sender-')
            );

            updateSenderList();
        }




        function logWebRTCVideoStats(peerConnection) {
            if (!(peerConnection instanceof RTCPeerConnection)) {
                console.error("Invalid RTCPeerConnection instance.");
                return;
            }

            peerConnection.getStats(null).then(stats => {
                let result = {
                    codec: null,
                    fps: null,
                    width: null,
                    height: null
                };

                // 遍历所有统计信息
                stats.forEach(report => {
                    // 获取帧率 (framesPerSecond)
                    if ((report.type === 'inbound-rtp' || report.type === 'outbound-rtp') && 'framesPerSecond' in report) {
                        result.fps = report.framesPerSecond;
                    }

                    // 获取分辨率 (frameWidth & frameHeight)
                    if (report.type === 'inbound-rtp' && report.kind === 'video') {
                        if (report.frameWidth !== undefined && report.frameHeight !== undefined) {
                            result.width = report.frameWidth;
                            result.height = report.frameHeight;
                        }
                    }

                    // 获取编码器类型 (mimeType)
                    if (report.type === 'codec' && report.payloadType && report.mimeType?.startsWith('video/')) {
                        result.codec = report.mimeType; // e.g., "video/VP8", "video/H264", "video/VP9"
                    }
                });

                // 打印结果
                console.log(`Codec: ${result.codec}`);
                console.log(`Resolution: ${result.width}x${result.height}`);
                console.log(`Frame Rate: ${result.fps ? `${result.fps.toFixed(2)} FPS` : 'N/A'}`);
            }).catch(error => {
                console.error("Failed to get stats:", error);
            });
        }

        function updateSenderList() {
            senderListEl.innerHTML = '';

            if (senders.length === 0) {
                const noSenders = document.createElement('div');
                noSenders.textContent = '没有可用的发送端';
                senderListEl.appendChild(noSenders);
                return;
            }

            senders.forEach(sender => {
                const senderItem = document.createElement('div');
                senderItem.className = 'sender-item';
                if (sender.id === activeSenderId) {
                    senderItem.classList.add('active');
                }

                const displayName = sender.name || sender.id;
                senderItem.textContent = displayName.length > 15 ? displayName.substring(0, 12) + '...' : displayName;
                senderItem.title = `${sender.name || sender.id} ${sender.description ? `(${sender.description})` : ''}`;
                senderItem.dataset.id = sender.id;

                senderItem.addEventListener('click', () => {
                    senderIdInput.value = sender.id;
                });

                senderListEl.appendChild(senderItem);
            });
        }


        function addSender(sender) {
            const existingIndex = senders.findIndex(s => s.id === sender.id);
            if (existingIndex >= 0) {
                senders[existingIndex] = sender;
            } else {
                senders.push(sender);
            }
            updateSenderList();
        }


        function removeSender(senderId) {
            senders = senders.filter(s => s.id !== senderId);
            updateSenderList();

            if (senderId === activeSenderId) {
                disconnectFromPeer();
                updateStatus(`发送端 ${senderId} 已离开`, true);
            }
        }


        async function connectToPeer() {

            const senderId = senderIdInput.value.trim();

            if (!senderId) {
                updateStatus('请输入发送端ID', true);
                return;
            }

            if (!ws || ws.readyState !== WebSocket.OPEN) {
                await connectToSignalingServer();
            }


            if (pc) {
                await disconnectFromPeer();
            }

            setLoading(true);

            try {

                // 创建RTCPeerConnection，强制使用H.264编解码器
                const h264Codec = { mimeType: 'video/H264', clockRate: 90000, sdpFmtpLine: 'level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f' };

                // 创建RTCRtpTransceiver的初始化参数，优先使用H.264
                const transceiverInit = {
                    direction: 'recvonly',
                    sendEncodings: [],
                    streams: []
                };

                // 创建RTCPeerConnection
                pc = new RTCPeerConnection(config);

                // 添加视频收发器，优先使用H.264
                try {
                    // 添加视频收发器
                    const videoTransceiver = pc.addTransceiver('video', transceiverInit);

                    // 设置编解码器偏好
                    try {
                        if (videoTransceiver.setCodecPreferences) {
                            // 获取支持的编解码器
                            const codecs = RTCRtpReceiver.getCapabilities('video').codecs;
                            log(`支持的视频编解码器: ${codecs.map(c => c.mimeType).join(', ')}`);

                            // 找到H.264编解码器
                            const h264Codecs = codecs.filter(codec => codec.mimeType.includes('H264'));
                            const vp8Codecs = codecs.filter(codec => codec.mimeType.includes('VP8'));
                            const vp9Codecs = codecs.filter(codec => codec.mimeType.includes('VP9'));
                            const otherCodecs = codecs.filter(codec =>
                                !codec.mimeType.includes('H264') &&
                                !codec.mimeType.includes('VP8') &&
                                !codec.mimeType.includes('VP9')
                            );

                            // 重新排序编解码器，使H.264优先
                            const reorderedCodecs = [...h264Codecs, ...vp8Codecs, ...vp9Codecs, ...otherCodecs];

                            // 设置编解码器偏好
                            videoTransceiver.setCodecPreferences(reorderedCodecs);
                            log(`已设置视频编解码器偏好: H.264优先`);
                        } else {
                            log('当前浏览器不支持setCodecPreferences方法，无法设置编解码器偏好');
                        }
                    } catch (e) {
                        log(`设置编解码器偏好失败: ${e.message}`, 'error');
                    }
                } catch (e) {
                    log(`添加视频收发器失败: ${e.message}`, 'error');
                }

                // 添加音频收发器
                try {
                    pc.addTransceiver('audio', { direction: 'recvonly' });
                    log('已添加音频收发器');
                } catch (e) {
                    log(`添加音频收发器失败: ${e.message}`, 'error');
                }

                // 添加一个空的视频轨道，确保SDP中包含视频媒体行
                try {
                    log('添加空视频轨道以确保SDP中包含视频媒体行');
                    const videoStream = new MediaStream();
                    // 创建一个空的视频轨道
                    const emptyVideoTrack = createEmptyVideoTrack();
                    videoStream.addTrack(emptyVideoTrack);

                    // 将轨道添加到PeerConnection
                    pc.addTrack(emptyVideoTrack, videoStream);
                    log('已添加空视频轨道');
                } catch (e) {
                    log(`添加空视频轨道失败: ${e.message}`, 'error');
                }

                setupPeerConnection(pc, senderId);


                dataChannel = pc.createDataChannel('control');

                dataChannel.onopen = () => {
                    log('数据通道已打开');
                    dataChannel.send('Hello from web receiver!');
                };

                dataChannel.onmessage = (event) => {
                    log(`收到数据通道消息: ${event.data}`);


                    try {
                        if (event.data.startsWith('{') && event.data.endsWith('}')) {
                            const data = JSON.parse(event.data);


                            if (data.type === 'source_info') {
                                log(`收到源信息: ${data.source_id}`, 'success');

                            }


                            else if (data.type === 'command_result') {
                                log(`命令结果: ${data.command} - ${data.success ? '成功' : '失败'}`, data.success ? 'success' : 'error');


                                if (data.command === 'pause' || data.command === 'resume' || data.command === 'toggle_pause') {
                                    updatePlayPauseState(data.state === 'playing');
                                }


                                if (data.command === 'seek' && data.success) {

                                }
                            }


                            else if (data.type === 'status') {
                                log(`收到状态信息: ${data.state}`, 'info');


                                updatePlayPauseState(data.state === 'playing');


                                if (data.duration > 0) {
                                    videoDuration = data.duration;
                                    currentPosition = data.position;
                                    isLocalFile = data.is_local_file;


                                    updateSeekBar();


                                    if (isLocalFile) {
                                        videoControls.classList.add('active');
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        log(`解析消息错误: ${e.message}`, 'error');
                    }
                };

                dataChannel.onclose = () => {
                    log('数据通道已关闭');
                };


                connectionTimeout = setTimeout(() => {
                    log('连接超时，尝试重新连接');
                    updateStatus('连接超时，尝试重新连接', true);


                    if (pc) {
                        const iceState = pc.iceConnectionState;
                        const connState = pc.connectionState;
                        log(`连接状态: ICE=${iceState}, Conn=${connState}`);

                        if (iceState !== 'connected' && iceState !== 'completed' && connState !== 'connected') {

                            try {
                                log('尝试重新创建offer');


                                pc.createOffer({
                                    offerToReceiveAudio: true,
                                    offerToReceiveVideo: true,
                                    iceRestart: true  // 添加iceRestart选项，确保ICE重启
                                }).then(offer => {
                                    return pc.setLocalDescription(offer);
                                }).then(() => {

                                    ws.send(JSON.stringify({
                                        type: 'offer',
                                        target: senderId,
                                        sdp: pc.localDescription.sdp
                                    }));
                                    log('已重新发送offer');


                                    connectionTimeout = setTimeout(() => {
                                        log('重新连接超时，断开连接');
                                        updateStatus('重新连接超时，断开连接', true);
                                        disconnectFromPeer();
                                    }, 15000);
                                }).catch(e => {
                                    log(`重新创建offer失败: ${e.message}`, 'error');
                                    disconnectFromPeer();
                                });
                            } catch (e) {
                                log(`重新连接失败: ${e.message}`, 'error');
                                disconnectFromPeer();
                            }
                        } else {

                            log('连接已建立，但可能没有收到媒体流');


                            if (!videoEl.srcObject || !videoEl.srcObject.getVideoTracks().length) {
                                log('没有视频轨道，尝试重新连接');
                                disconnectFromPeer();
                                setTimeout(() => {
                                    connectToPeer();
                                }, 1000);
                            }
                        }
                    } else {
                        disconnectFromPeer();
                    }
                }, 30000);


                const offer = await pc.createOffer({
                    offerToReceiveAudio: true,
                    offerToReceiveVideo: true,
                    iceRestart: true
                });


                let modifiedSdp = offer.sdp;


                // // 检查SDP是否包含视频媒体行
                // if (!modifiedSdp.includes('m=video')) {
                //     log('警告：SDP中没有视频媒体行，尝试添加', 'error');

                //     try {
                //         // 添加视频媒体行
                //         // 找到SDP中的最后一个媒体行
                //         const lastMediaLineIndex = modifiedSdp.lastIndexOf('m=');
                //         if (lastMediaLineIndex !== -1) {
                //             // 找到这个媒体行的结束位置
                //             let endOfMediaSection = modifiedSdp.indexOf('m=', lastMediaLineIndex + 2);
                //             if (endOfMediaSection === -1) {
                //                 endOfMediaSection = modifiedSdp.length;
                //             }

                //             // 提取ICE和指纹信息
                //             const iceUfrag = modifiedSdp.match(/a=ice-ufrag:(.*)/);
                //             const icePwd = modifiedSdp.match(/a=ice-pwd:(.*)/);
                //             const fingerprint = modifiedSdp.match(/a=fingerprint:sha-256 (.*)/);
                //             const setup = modifiedSdp.match(/a=setup:(.*)/);

                //             if (iceUfrag && icePwd && fingerprint && setup) {
                //                 // 创建视频媒体行
                //                 const videoSection = `m=video 9 UDP/TLS/RTP/SAVPF 96 97\r\n` +
                //                     `c=IN IP4 0.0.0.0\r\n` +
                //                     `a=rtcp:9 IN IP4 0.0.0.0\r\n` +
                //                     `a=ice-ufrag:${iceUfrag[1]}\r\n` +
                //                     `a=ice-pwd:${icePwd[1]}\r\n` +
                //                     `a=ice-options:trickle\r\n` +
                //                     `a=fingerprint:sha-256 ${fingerprint[1]}\r\n` +
                //                     `a=setup:${setup[1]}\r\n` +
                //                     `a=mid:1\r\n` +
                //                     `a=sendrecv\r\n` +
                //                     `a=rtcp-mux\r\n` +
                //                     `a=rtcp-rsize\r\n` +
                //                     `a=rtpmap:96 VP8/90000\r\n` +
                //                     `a=rtcp-fb:96 nack\r\n` +
                //                     `a=rtcp-fb:96 nack pli\r\n` +
                //                     `a=rtcp-fb:96 ccm fir\r\n` +
                //                     `a=rtpmap:97 H264/90000\r\n` +
                //                     `a=rtcp-fb:97 nack\r\n` +
                //                     `a=rtcp-fb:97 nack pli\r\n` +
                //                     `a=rtcp-fb:97 ccm fir\r\n` +
                //                     `a=fmtp:97 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\r\n`;

                //                 // 更新SDP中的媒体行组
                //                 let modifiedSdpLines = modifiedSdp.split('\r\n');
                //                 for (let i = 0; i < modifiedSdpLines.length; i++) {
                //                     if (modifiedSdpLines[i].startsWith('a=group:BUNDLE')) {
                //                         modifiedSdpLines[i] += ' 1';
                //                         break;
                //                     }
                //                 }

                //                 // 将视频媒体行添加到SDP中
                //                 modifiedSdp = modifiedSdpLines.join('\r\n') + '\r\n' + videoSection;
                //                 log('已添加视频媒体行到SDP');
                //             } else {
                //                 log('无法从SDP中提取ICE和指纹信息，无法添加视频媒体行', 'error');
                //             }
                //         } else {
                //             log('无法在SDP中找到媒体行，无法添加视频媒体行', 'error');
                //         }
                //     } catch (e) {
                //         log(`添加视频媒体行失败: ${e.message}`, 'error');
                //     }
                // }

                // // 修改SDP以提高帧率和优化编解码器
                // log('优化SDP以提高帧率和视频质量');

                // try {
                //     // 1. 提高帧率限制
                //     // 查找并修改x-google-max-framerate参数，提高帧率限制
                //     const fmtpRegex = /a=fmtp:(\d+) (.*)/g;
                //     let match;
                //     while ((match = fmtpRegex.exec(modifiedSdp)) !== null) {
                //         const payloadType = match[1];
                //         const params = match[2];

                //         // 如果参数中包含x-google-max-framerate，则修改其值
                //         if (params.includes('x-google-max-framerate')) {
                //             const newParams = params.replace(/x-google-max-framerate=\d+/, 'x-google-max-framerate=60');
                //             modifiedSdp = modifiedSdp.replace(
                //                 `a=fmtp:${payloadType} ${params}`,
                //                 `a=fmtp:${payloadType} ${newParams}`
                //             );
                //             log(`已提高帧率限制: 修改 ${payloadType} 的x-google-max-framerate为60`);
                //         }
                //         // 如果参数中不包含x-google-max-framerate，则添加
                //         else {
                //             modifiedSdp = modifiedSdp.replace(
                //                 `a=fmtp:${payloadType} ${params}`,
                //                 `a=fmtp:${payloadType} ${params};x-google-max-framerate=60`
                //             );
                //             log(`已添加帧率限制: 为 ${payloadType} 添加x-google-max-framerate=60`);
                //         }
                //     }

                //     // 2. 添加b=AS参数，提高带宽限制
                //     // 查找媒体行
                //     const mediaLines = modifiedSdp.match(/m=(video|audio).*\r\n/g);
                //     if (mediaLines) {
                //         for (const mediaLine of mediaLines) {
                //             // 如果是视频媒体行，添加高带宽限制
                //             if (mediaLine.includes('m=video')) {
                //                 // 检查是否已经有b=AS行
                //                 const hasAS = modifiedSdp.includes(`${mediaLine}b=AS:`);
                //                 if (!hasAS) {
                //                     // 添加高带宽限制（10Mbps）
                //                     modifiedSdp = modifiedSdp.replace(
                //                         mediaLine,
                //                         `${mediaLine}b=AS:10000\r\n`
                //                     );
                //                     log('已添加视频带宽限制: 10Mbps');
                //                 } else {
                //                     // 修改现有的带宽限制
                //                     modifiedSdp = modifiedSdp.replace(
                //                         /b=AS:\d+/g,
                //                         'b=AS:10000'
                //                     );
                //                     log('已修改视频带宽限制: 10Mbps');
                //                 }
                //             }
                //         }
                //     }

                //     // 设备特定优化
                //     if (isIOS) {
                //         log('iOS设备：优化SDP以提高兼容性');

                //         // 确保H.264编解码器优先级更高，因为iOS对它支持更好
                //         if (modifiedSdp.includes('H264')) {
                //             log('检测到H.264编解码器，iOS设备将优先使用');

                //             // 1. 确保包含关键帧请求支持和正确的时钟频率
                //             if (!modifiedSdp.includes('a=rtcp-fb:* nack pli')) {
                //                 modifiedSdp = modifiedSdp.replace(/a=rtpmap:(.*) H264(?!\/)/g,
                //                     'a=rtpmap:$1 H264/90000\r\na=rtcp-fb:$1 nack pli\r\na=rtcp-fb:$1 ccm fir');
                //                 log('已添加H.264关键帧请求支持和时钟频率');
                //             }

                //             // 2. 确保使用适合iOS的H.264配置
                //             if (!modifiedSdp.includes('a=fmtp:')) {
                //                 const h264PayloadType = modifiedSdp.match(/a=rtpmap:(\d+) H264/);
                //                 if (h264PayloadType && h264PayloadType[1]) {
                //                     modifiedSdp = modifiedSdp.replace(
                //                         new RegExp(`a=rtpmap:${h264PayloadType[1]} H264.*`),
                //                         `a=rtpmap:${h264PayloadType[1]} H264/90000\r\n` +
                //                         `a=fmtp:${h264PayloadType[1]} level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f;x-google-max-framerate=60`
                //                     );
                //                     log('已设置适合iOS的H.264参数，并提高帧率限制');
                //                 }
                //             }
                //         } else {
                //             log('警告：未检测到H.264编解码器，iOS设备可能无法正常显示视频', 'error');
                //         }

                //         // 添加特定的SDP修改，以提高iOS兼容性
                //         modifiedSdp = modifiedSdp.replace(/a=rtcp-fb:\*\s*nack\s*pli/g, 'a=rtcp-fb:* nack pli');
                //         modifiedSdp = modifiedSdp.replace(/a=rtcp-fb:\*\s*ccm\s*fir/g, 'a=rtcp-fb:* ccm fir');

                //         // 确保使用适合iOS的ICE配置
                //         if (!modifiedSdp.includes('a=ice-options:trickle')) {
                //             modifiedSdp = modifiedSdp.replace(/a=ice-pwd:.*\r\n/g, '$&a=ice-options:trickle\r\n');
                //         }

                //         log('已为iOS设备优化SDP配置');
                //     } else {
                //         // 非iOS设备，优先使用VP8/VP9
                //         if (modifiedSdp.includes('VP8') || modifiedSdp.includes('VP9')) {
                //             log('检测到VP8/VP9编解码器，将优先使用');

                //             // 确保VP8/VP9有关键帧请求支持
                //             const vpCodecTypes = ['VP8', 'VP9'];
                //             for (const codecType of vpCodecTypes) {
                //                 if (modifiedSdp.includes(codecType)) {
                //                     const vpPayloadType = modifiedSdp.match(new RegExp(`a=rtpmap:(\\d+) ${codecType}`));
                //                     if (vpPayloadType && vpPayloadType[1]) {
                //                         // 添加关键帧请求支持
                //                         if (!modifiedSdp.includes(`a=rtcp-fb:${vpPayloadType[1]} nack pli`)) {
                //                             modifiedSdp = modifiedSdp.replace(
                //                                 new RegExp(`a=rtpmap:${vpPayloadType[1]} ${codecType}.*`),
                //                                 `$&\r\na=rtcp-fb:${vpPayloadType[1]} nack pli\r\na=rtcp-fb:${vpPayloadType[1]} ccm fir`
                //                             );
                //                             log(`已添加${codecType}关键帧请求支持`);
                //                         }

                //                         // 添加或修改帧率限制
                //                         const fmtpLine = modifiedSdp.match(new RegExp(`a=fmtp:${vpPayloadType[1]} (.*)`));
                //                         if (fmtpLine) {
                //                             const params = fmtpLine[1];
                //                             if (params.includes('x-google-max-framerate')) {
                //                                 modifiedSdp = modifiedSdp.replace(
                //                                     /x-google-max-framerate=\d+/g,
                //                                     'x-google-max-framerate=60'
                //                                 );
                //                             } else {
                //                                 modifiedSdp = modifiedSdp.replace(
                //                                     `a=fmtp:${vpPayloadType[1]} ${params}`,
                //                                     `a=fmtp:${vpPayloadType[1]} ${params};x-google-max-framerate=60`
                //                                 );
                //                             }
                //                             log(`已为${codecType}设置帧率限制: 60fps`);
                //                         } else {
                //                             // 如果没有fmtp行，添加一个
                //                             modifiedSdp = modifiedSdp.replace(
                //                                 new RegExp(`a=rtpmap:${vpPayloadType[1]} ${codecType}.*`),
                //                                 `$&\r\na=fmtp:${vpPayloadType[1]} x-google-max-framerate=60`
                //                             );
                //                             log(`已为${codecType}添加帧率限制: 60fps`);
                //                         }
                //                     }
                //                 }
                //             }
                //         } else if (modifiedSdp.includes('H264')) {
                //             // 如果没有VP8/VP9，但有H.264，也优化H.264
                //             log('未检测到VP8/VP9编解码器，将优化H.264');

                //             const h264PayloadType = modifiedSdp.match(/a=rtpmap:(\d+) H264/);
                //             if (h264PayloadType && h264PayloadType[1]) {
                //                 // 添加关键帧请求支持
                //                 if (!modifiedSdp.includes(`a=rtcp-fb:${h264PayloadType[1]} nack pli`)) {
                //                     modifiedSdp = modifiedSdp.replace(
                //                         new RegExp(`a=rtpmap:${h264PayloadType[1]} H264.*`),
                //                         `$&\r\na=rtcp-fb:${h264PayloadType[1]} nack pli\r\na=rtcp-fb:${h264PayloadType[1]} ccm fir`
                //                     );
                //                     log('已添加H.264关键帧请求支持');
                //                 }

                //                 // 添加或修改帧率限制
                //                 const fmtpLine = modifiedSdp.match(new RegExp(`a=fmtp:${h264PayloadType[1]} (.*)`));
                //                 if (fmtpLine) {
                //                     const params = fmtpLine[1];
                //                     if (params.includes('x-google-max-framerate')) {
                //                         modifiedSdp = modifiedSdp.replace(
                //                             /x-google-max-framerate=\d+/g,
                //                             'x-google-max-framerate=60'
                //                         );
                //                     } else {
                //                         modifiedSdp = modifiedSdp.replace(
                //                             `a=fmtp:${h264PayloadType[1]} ${params}`,
                //                             `a=fmtp:${h264PayloadType[1]} ${params};x-google-max-framerate=60`
                //                         );
                //                     }
                //                     log('已为H.264设置帧率限制: 60fps');
                //                 }
                //             }
                //         }
                //     }

                //     log('已完成SDP优化');
                // } catch (e) {
                //     log(`SDP修改错误: ${e.message}`, 'error');
                // }


                await pc.setLocalDescription(new RTCSessionDescription({
                    type: offer.type,
                    sdp: modifiedSdp
                }));
                log('已设置本地描述');



                const formattedSdp = modifiedSdp.replace(/\r\n|\n|\r/g, '\\n');

                ws.send(JSON.stringify({
                    type: 'offer',
                    target: senderId,
                    sdp: formattedSdp
                }));
                log('已发送offer');

                activeSenderId = senderId;
                updateStatus(`正在连接到发送端 ${senderId}...`);
                updateSenderList();

                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                reconnectBtn.disabled = false;

            } catch (e) {
                log(`连接错误: ${e.message}`, 'error');
                updateStatus(`连接错误: ${e.message}`, true);
                setLoading(false);
                disconnectFromPeer();
            }
        }


        function setupPeerConnection(peerConnection, senderId) {

            peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    const candidate = {
                        candidate: event.candidate.candidate,
                        sdpMid: event.candidate.sdpMid,
                        sdpMLineIndex: event.candidate.sdpMLineIndex
                    };

                    // 详细记录ICE候选信息
                    const candidateInfo = event.candidate.candidate;
                    const candidateParts = candidateInfo.split(' ');
                    if (candidateParts.length >= 7) {
                        const foundation = candidateParts[0].split(':')[1];
                        const component = candidateParts[1];
                        const protocol = candidateParts[2];
                        const priority = candidateParts[3];
                        const ip = candidateParts[4];
                        const port = candidateParts[5];
                        const type = candidateParts[7];

                        log(`ICE候选详情: 类型=${type}, IP=${ip}, 端口=${port}, 协议=${protocol}`);
                    }

                    // 确保WebSocket连接已建立
                    if (ws && ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'candidate',
                            target: senderId,
                            candidate: candidate
                        }));
                        log('已发送ICE候选');
                    } else {
                        log('WebSocket未连接，无法发送ICE候选', 'error');
                    }
                } else {
                    log('ICE候选收集完成');
                }
            };


            peerConnection.oniceconnectionstatechange = () => {
                const state = peerConnection.iceConnectionState;
                log(`ICE连接状态变化: ${state}`);

                // 获取并记录所有的ICE候选对和IP地址信息
                try {
                    peerConnection.getStats(null).then(stats => {
                        // 记录所有的本地候选
                        const localCandidates = [];
                        // 记录所有的远程候选
                        const remoteCandidates = [];
                        // 记录所有的候选对
                        const candidatePairs = [];

                        stats.forEach(report => {
                            if (report.type === 'local-candidate') {
                                localCandidates.push({
                                    id: report.id,
                                    ip: report.ip,
                                    port: report.port,
                                    protocol: report.protocol,
                                    candidateType: report.candidateType,
                                    priority: report.priority
                                });
                            } else if (report.type === 'remote-candidate') {
                                remoteCandidates.push({
                                    id: report.id,
                                    ip: report.ip,
                                    port: report.port,
                                    protocol: report.protocol,
                                    candidateType: report.candidateType,
                                    priority: report.priority
                                });
                            } else if (report.type === 'candidate-pair') {
                                candidatePairs.push({
                                    id: report.id,
                                    localCandidateId: report.localCandidateId,
                                    remoteCandidateId: report.remoteCandidateId,
                                    state: report.state,
                                    nominated: report.nominated,
                                    bytesSent: report.bytesSent,
                                    bytesReceived: report.bytesReceived
                                });
                            }
                        });

                        // 记录所有的本地候选
                        if (localCandidates.length > 0) {
                            log(`本地ICE候选 (${localCandidates.length}个):`);
                            localCandidates.forEach((candidate, index) => {
                                log(`  ${index + 1}. ${candidate.ip}:${candidate.port} (${candidate.protocol}/${candidate.candidateType})`);
                            });
                        }

                        // 记录所有的远程候选
                        if (remoteCandidates.length > 0) {
                            log(`远程ICE候选 (${remoteCandidates.length}个):`);
                            remoteCandidates.forEach((candidate, index) => {
                                log(`  ${index + 1}. ${candidate.ip}:${candidate.port} (${candidate.protocol}/${candidate.candidateType})`);
                            });
                        }

                        // 记录所有的候选对
                        if (candidatePairs.length > 0) {
                            log(`ICE候选对 (${candidatePairs.length}个):`);
                            candidatePairs.forEach((pair, index) => {
                                const local = localCandidates.find(c => c.id === pair.localCandidateId);
                                const remote = remoteCandidates.find(c => c.id === pair.remoteCandidateId);

                                if (local && remote) {
                                    log(`  ${index + 1}. ${local.ip}:${local.port} -> ${remote.ip}:${remote.port} (${pair.state}${pair.nominated ? ', 已提名' : ''})`);
                                    log(`     发送: ${formatBytes(pair.bytesSent)}, 接收: ${formatBytes(pair.bytesReceived)}`);
                                }
                            });

                            // 找到已提名的候选对
                            const nominatedPair = candidatePairs.find(pair => pair.nominated);
                            if (nominatedPair) {
                                const local = localCandidates.find(c => c.id === nominatedPair.localCandidateId);
                                const remote = remoteCandidates.find(c => c.id === nominatedPair.remoteCandidateId);

                                if (local && remote) {
                                    log(`已提名的ICE候选对: ${local.ip}:${local.port} (${local.protocol}/${local.candidateType}) -> ${remote.ip}:${remote.port} (${remote.protocol}/${remote.candidateType})`);
                                }
                            }
                        }
                    }).catch(e => {
                        console.error('获取ICE候选统计信息失败:', e);
                    });
                } catch (e) {
                    console.error('获取ICE候选时出错:', e);
                }

                if (state === 'failed') {
                    log('ICE连接失败，尝试重启ICE', 'error');
                    updateStatus(`ICE连接失败，尝试重启`, true);
                    setLoading(false);

                    // 尝试重启ICE
                    try {
                        if (peerConnection.restartIce) {
                            peerConnection.restartIce();
                            log('已请求重启ICE');
                        } else {
                            // 如果不支持restartIce方法，尝试创建新的offer
                            peerConnection.createOffer({iceRestart: true})
                                .then(offer => peerConnection.setLocalDescription(offer))
                                .then(() => {
                                    log('已通过新offer重启ICE');

                                    // 发送新的offer到发送端
                                    if (ws && ws.readyState === WebSocket.OPEN) {
                                        ws.send(JSON.stringify({
                                            type: 'offer',
                                            target: senderId,
                                            sdp: peerConnection.localDescription.sdp
                                        }));
                                        log('已发送重启ICE的offer');
                                    }
                                })
                                .catch(e => log(`重启ICE失败: ${e.message}`, 'error'));
                        }
                    } catch (e) {
                        log(`尝试重启ICE时出错: ${e.message}`, 'error');
                    }

                    // 启用重连按钮
                    reconnectBtn.disabled = false;
                } else if (state === 'disconnected') {
                    log('ICE连接断开，等待重新连接', 'error');
                    updateStatus(`ICE连接断开，等待重新连接`, true);
                    setLoading(false);
                    reconnectBtn.disabled = false;
                } else if (state === 'closed') {
                    log('ICE连接已关闭', 'error');
                    updateStatus(`ICE连接已关闭`, true);
                    setLoading(false);
                    reconnectBtn.disabled = false;
                } else if (state === 'connected' || state === 'completed') {
                    log(`ICE连接已${state === 'connected' ? '建立' : '完成'}`);
                    updateStatus(`已连接到发送端 ${senderId}`);
                    setLoading(false);
                    reconnectBtn.disabled = false;

                    // 在iOS上，连接建立后请求关键帧
                    if (isIOS && dataChannel && dataChannel.readyState === 'open') {
                        log('iOS设备：连接建立后请求关键帧');
                        setTimeout(() => {
                            sendCommand('request_keyframe');
                        }, 500);
                    }
                } else if (state === 'checking') {
                    log('ICE连接检查中...');
                    updateStatus('ICE连接检查中...');
                } else if (state === 'new') {
                    log('ICE连接初始化...');
                    updateStatus('ICE连接初始化...');
                }
            };


            peerConnection.onconnectionstatechange = () => {
                const state = peerConnection.connectionState;
                log(`连接状态变化: ${state}`);

                // 记录当前的信令状态和ICE连接状态
                log(`当前状态: 信令=${peerConnection.signalingState}, ICE=${peerConnection.iceConnectionState}, 连接=${state}`);

                // 获取并记录所有的ICE候选对和IP地址信息
                try {
                    peerConnection.getStats(null).then(stats => {
                        // 记录所有的本地候选
                        const localCandidates = [];
                        // 记录所有的远程候选
                        const remoteCandidates = [];
                        // 记录所有的候选对
                        const candidatePairs = [];

                        stats.forEach(report => {
                            if (report.type === 'local-candidate') {
                                localCandidates.push({
                                    id: report.id,
                                    ip: report.ip,
                                    port: report.port,
                                    protocol: report.protocol,
                                    candidateType: report.candidateType,
                                    priority: report.priority
                                });
                            } else if (report.type === 'remote-candidate') {
                                remoteCandidates.push({
                                    id: report.id,
                                    ip: report.ip,
                                    port: report.port,
                                    protocol: report.protocol,
                                    candidateType: report.candidateType,
                                    priority: report.priority
                                });
                            } else if (report.type === 'candidate-pair') {
                                candidatePairs.push({
                                    id: report.id,
                                    localCandidateId: report.localCandidateId,
                                    remoteCandidateId: report.remoteCandidateId,
                                    state: report.state,
                                    nominated: report.nominated,
                                    bytesSent: report.bytesSent,
                                    bytesReceived: report.bytesReceived
                                });
                            }
                        });

                        // 记录所有的本地候选
                        if (localCandidates.length > 0) {
                            log(`本地ICE候选 (${localCandidates.length}个):`);
                            localCandidates.forEach((candidate, index) => {
                                log(`  ${index + 1}. ${candidate.ip}:${candidate.port} (${candidate.protocol}/${candidate.candidateType})`);
                            });
                        }

                        // 记录所有的远程候选
                        if (remoteCandidates.length > 0) {
                            log(`远程ICE候选 (${remoteCandidates.length}个):`);
                            remoteCandidates.forEach((candidate, index) => {
                                log(`  ${index + 1}. ${candidate.ip}:${candidate.port} (${candidate.protocol}/${candidate.candidateType})`);
                            });
                        }

                        // 记录所有的候选对
                        if (candidatePairs.length > 0) {
                            log(`ICE候选对 (${candidatePairs.length}个):`);
                            candidatePairs.forEach((pair, index) => {
                                const local = localCandidates.find(c => c.id === pair.localCandidateId);
                                const remote = remoteCandidates.find(c => c.id === pair.remoteCandidateId);

                                if (local && remote) {
                                    log(`  ${index + 1}. ${local.ip}:${local.port} -> ${remote.ip}:${remote.port} (${pair.state}${pair.nominated ? ', 已提名' : ''})`);
                                    log(`     发送: ${formatBytes(pair.bytesSent)}, 接收: ${formatBytes(pair.bytesReceived)}`);
                                }
                            });

                            // 找到已提名的候选对
                            const nominatedPair = candidatePairs.find(pair => pair.nominated);
                            if (nominatedPair) {
                                const local = localCandidates.find(c => c.id === nominatedPair.localCandidateId);
                                const remote = remoteCandidates.find(c => c.id === nominatedPair.remoteCandidateId);

                                if (local && remote) {
                                    log(`已提名的ICE候选对: ${local.ip}:${local.port} (${local.protocol}/${local.candidateType}) -> ${remote.ip}:${remote.port} (${remote.protocol}/${remote.candidateType})`);
                                }
                            }
                        }
                    }).catch(e => {
                        console.error('获取ICE候选统计信息失败:', e);
                    });
                } catch (e) {
                    console.error('获取ICE候选时出错:', e);
                }

                if (state === 'failed') {
                    log('连接失败', 'error');
                    updateStatus(`连接失败`, true);
                    setLoading(false);

                    // 在iOS上，尝试特殊的重连逻辑
                    if (isIOS) {
                        log('iOS设备：连接失败，尝试特殊重连逻辑');

                        // 延迟一段时间后尝试重连
                        setTimeout(() => {
                            if (peerConnection.connectionState === 'failed') {
                                log('iOS设备：执行特殊重连');
                                disconnectFromPeer();
                                setTimeout(() => {
                                    connectToPeer();
                                }, 1000);
                            }
                        }, 2000);
                    }
                } else if (state === 'disconnected') {
                    log('连接断开', 'error');
                    updateStatus(`连接断开`, true);
                    setLoading(false);
                } else if (state === 'closed') {
                    log('连接已关闭', 'error');
                    updateStatus(`连接已关闭`, true);
                    setLoading(false);
                } else if (state === 'connected') {
                    log('连接已建立');
                    updateStatus(`已连接到发送端 ${senderId}`);
                    setLoading(false);

                    // // 连接成功后，设置视频质量
                    // setTimeout(async () => {
                    //     try {
                    //         // 设置高质量视频参数：3Mbps最大比特率，500kbps最小比特率，60fps最大帧率
                    //         await adjustVideoQuality(3000000, 500000, 60);
                    //         log('已设置高质量视频参数', 'success');
                    //     } catch (e) {
                    //         log(`设置视频质量参数失败: ${e.message}`, 'error');
                    //     }
                    // }, 2000); // 延迟2秒，确保连接稳定后再设置

                    // 在iOS上，连接建立后请求关键帧（减少频率）
                    if (isIOS) {
                        log('iOS设备：连接建立后请求关键帧');
                        // 只请求一次关键帧，避免过度请求
                        setTimeout(() => {
                            if (dataChannel && dataChannel.readyState === 'open') {
                                sendCommand('request_keyframe');
                            }
                        }, 1000); // 延迟1秒后请求一次
                    }
                } else if (state === 'connecting') {
                    log('正在建立连接...');
                    updateStatus('正在建立连接...');
                } else if (state === 'new') {
                    log('连接初始化...');
                    updateStatus('连接初始化...');
                }
            };


            peerConnection.onsignalingstatechange = () => {
                const state = peerConnection.signalingState;
                log(`信令状态变化: ${state}`);

                if (state === 'stable') {
                    log('信令状态稳定');
                } else if (state === 'have-local-offer') {
                    log('已创建本地offer');
                } else if (state === 'have-remote-offer') {
                    log('已收到远程offer');
                } else if (state === 'have-local-pranswer') {
                    log('已创建本地临时answer');
                } else if (state === 'have-remote-pranswer') {
                    log('已收到远程临时answer');
                } else if (state === 'closed') {
                    log('信令连接已关闭');
                }

                // 在iOS上，如果信令状态变为stable，可能需要请求关键帧
                if (isIOS && state === 'stable') {
                    log('iOS设备：信令状态稳定，尝试请求关键帧');
                    setTimeout(() => {
                        if (dataChannel && dataChannel.readyState === 'open') {
                            sendCommand('request_keyframe');
                        }
                    }, 1000);
                }
            };


            peerConnection.ontrack = (event) => {
                log(`收到轨道: ${event.track.kind}`);

                if (event.track.kind === 'video') {
                    // 在iOS上，我们需要特殊处理视频流
                    if (isIOS) {
                        log('iOS设备：使用特殊视频处理');

                        // 确保视频元素已准备好
                        videoEl.onloadedmetadata = () => {
                            log('iOS: 视频元数据已加载');
                        };

                        videoEl.oncanplay = () => {
                            log('iOS: 视频可以播放');
                            tryPlayVideo();
                        };
                    }

                    // 设置视频源
                    videoEl.srcObject = event.streams[0];

                    // 在iOS上，我们保持视频静音以确保自动播放
                    if (!isIOS) {
                        videoEl.muted = false;
                    }

                    log('已设置视频源');

                    // 尝试播放视频的函数
                    const tryPlayVideo = () => {
                        // 在iOS上，我们需要确保视频元素可见
                        if (isIOS) {
                            videoEl.style.display = 'block';
                            // 强制重绘
                            videoEl.offsetHeight;
                        }

                        // 尝试播放视频
                        const playPromise = videoEl.play();

                        if (playPromise !== undefined) {
                            playPromise.then(() => {
                                log('视频播放成功');

                                // 在iOS上，我们可能需要请求关键帧
                                if (isIOS && dataChannel && dataChannel.readyState === 'open') {
                                    log('iOS设备：请求关键帧');
                                    sendCommand('request_keyframe');
                                }

                                // 在iOS上，我们需要在用户交互后取消静音
                                if (isIOS) {
                                    log('iOS设备：需要用户交互来启用音频');
                                    updateStatus('点击视频区域启用音频', true);

                                    // 添加一次性点击事件来取消静音
                                    videoEl.addEventListener('click', () => {
                                        videoEl.muted = false;
                                        log('已启用音频');
                                        updateStatus('已连接到发送端 ' + activeSenderId);
                                    }, { once: true });
                                }
                            }).catch(e => {
                                log(`自动播放失败: ${e.message}`, 'error');

                                updateStatus('点击视频区域开始播放', true);

                                // 添加点击事件来启动播放
                                videoEl.addEventListener('click', () => {
                                    videoEl.play().then(() => {
                                        if (isIOS) {
                                            videoEl.muted = false;
                                            // 再次请求关键帧
                                            if (dataChannel && dataChannel.readyState === 'open') {
                                                sendCommand('request_keyframe');
                                            }
                                        }
                                        log('视频播放成功');
                                    }).catch(e => {
                                        log(`播放失败: ${e.message}`, 'error');
                                    });
                                }, { once: true });
                            });
                        }
                    };

                    // 立即尝试播放视频
                    tryPlayVideo();

                    // 在iOS上，我们可能需要多次尝试播放
                    if (isIOS) {
                        // 第一次延迟尝试
                        setTimeout(() => {
                            if (videoEl.paused) {
                                log('iOS设备：延迟后再次尝试播放');
                                tryPlayVideo();
                            }
                        }, 1000);

                        // 第二次延迟尝试
                        setTimeout(() => {
                            if (videoEl.paused) {
                                log('iOS设备：第二次尝试播放');
                                videoEl.muted = true; // 确保静音以允许自动播放
                                tryPlayVideo();

                                // 添加点击事件来取消静音
                                videoEl.addEventListener('click', () => {
                                    videoEl.muted = false;
                                    log('iOS设备：用户点击，取消静音');
                                }, { once: true });
                            }
                        }, 3000);

                        // 监听页面可见性变化
                        document.addEventListener('visibilitychange', () => {
                            if (document.visibilityState === 'visible' && videoEl.paused) {
                                log('iOS设备：页面变为可见，尝试播放');
                                tryPlayVideo();
                            }
                        });
                    }
                }
            };


            peerConnection.ondatachannel = (event) => {
                const channel = event.channel;
                log(`收到数据通道: ${channel.label}, ID: ${channel.id}, 协商: ${channel.negotiated}, 有序: ${channel.ordered}`);

                dataChannel = channel;

                channel.onopen = () => {
                    log(`数据通道 ${channel.label} 已打开，状态: ${channel.readyState}`);

                    try {
                        channel.send('Hello from web receiver!');
                        log('已发送初始消息到数据通道');

                        // 在iOS上，数据通道打开后立即请求关键帧
                        if (isIOS) {
                            log('iOS设备：数据通道打开后请求关键帧');
                            setTimeout(() => {
                                sendCommand('request_keyframe');
                            }, 500);
                        }
                    } catch (e) {
                        log(`发送初始消息失败: ${e.message}`, 'error');
                    }
                };

                channel.onmessage = (event) => {
                    log(`收到数据通道消息: ${event.data.substring(0, 100)}${event.data.length > 100 ? '...' : ''}`);

                    try {
                        if (event.data.startsWith('{') && event.data.endsWith('}')) {
                            const data = JSON.parse(event.data);

                            if (data.type === 'source_info') {
                                log(`收到源信息: ${data.source_id}`, 'success');

                                // 在iOS上，收到源信息后请求关键帧
                                if (isIOS) {
                                    log('iOS设备：收到源信息后请求关键帧');
                                    setTimeout(() => {
                                        sendCommand('request_keyframe');
                                    }, 500);
                                }
                            } else if (data.type === 'command_result') {
                                log(`命令结果: ${data.command} - ${data.success ? '成功' : '失败'}`, data.success ? 'success' : 'error');

                                if (data.command === 'pause' || data.command === 'resume' || data.command === 'toggle_pause') {
                                    updatePlayPauseState(data.state === 'playing');
                                } else if (data.command === 'request_keyframe') {
                                    log(`关键帧请求结果: ${data.success ? '成功' : '失败'}`);

                                    // 如果是iOS设备且关键帧请求失败，尝试其他方法
                                    if (isIOS && !data.success) {
                                        log('iOS设备：关键帧请求失败，尝试其他方法');
                                        // 可以在这里添加其他方法
                                    }
                                }
                            } else if (data.type === 'status') {
                                log(`收到状态信息: ${JSON.stringify(data)}`, 'info');
                            }
                        }
                    } catch (e) {
                        log(`解析消息错误: ${e.message}`, 'error');
                    }
                };

                channel.onclose = () => {
                    log(`数据通道已关闭，最后状态: ${channel.readyState}`);

                    // 在iOS上，如果数据通道关闭，尝试重新连接
                    if (isIOS && peerConnection && peerConnection.connectionState === 'connected') {
                        log('iOS设备：数据通道关闭但连接仍然存在，尝试重新创建数据通道');
                        try {
                            dataChannel = peerConnection.createDataChannel('control');
                            log('已重新创建数据通道');

                            dataChannel.onopen = () => {
                                log('重新创建的数据通道已打开');
                                dataChannel.send('Hello from web receiver after reconnect!');
                            };

                            dataChannel.onclose = () => {
                                log('重新创建的数据通道已关闭');
                            };
                        } catch (e) {
                            log(`重新创建数据通道失败: ${e.message}`, 'error');
                        }
                    }
                };

                channel.onerror = (error) => {
                    log(`数据通道错误: ${error.message || '未知错误'}`, 'error');
                };
            };



            // 定期获取WebRTC视频统计信息和ICE候选信息
            const statsInterval = setInterval(() => {
                // 确保PeerConnection有效
                if (pc && pc.connectionState !== 'closed') {
                    try {
                        pc.getStats(null).then(stats => {
                            // 视频统计信息
                            let videoStats = null;
                            let codecInfo = null;
                            let videoSize = null;

                            // ICE候选信息
                            const localCandidates = [];
                            const remoteCandidates = [];
                            const candidatePairs = [];

                            stats.forEach(report => {
                                // 收集视频统计信息
                                if (report.type === 'inbound-rtp' && report.kind === 'video') {
                                    videoStats = {
                                        framesPerSecond: report.framesPerSecond || 0,
                                        framesDecoded: report.framesDecoded || 0,
                                        bytesReceived: report.bytesReceived || 0,
                                        packetsReceived: report.packetsReceived || 0,
                                        packetsLost: report.packetsLost || 0,
                                        jitter: report.jitter || 0,
                                        codecId: report.codecId,
                                        decoderImplementation: report.decoderImplementation || '未知',
                                        totalDecodeTime: report.totalDecodeTime || 0,
                                        keyFramesDecoded: report.keyFramesDecoded || 0,
                                        frameWidth: report.frameWidth || 0,
                                        frameHeight: report.frameHeight || 0,
                                        framesDropped: report.framesDropped || 0
                                    };
                                }

                                // 收集编解码器信息
                                if (report.type === 'codec') {
                                    codecInfo = {
                                        id: report.id,
                                        mimeType: report.mimeType || '未知',
                                        clockRate: report.clockRate || 0,
                                        channels: report.channels,
                                        sdpFmtpLine: report.sdpFmtpLine || ''
                                    };
                                }

                                // 收集视频大小信息
                                if (report.type === 'track' && report.kind === 'video') {
                                    videoSize = {
                                        frameWidth: report.frameWidth || 0,
                                        frameHeight: report.frameHeight || 0,
                                        framesReceived: report.framesReceived || 0,
                                        framesDropped: report.framesDropped || 0,
                                        framesPerSecond: report.framesPerSecond || 0
                                    };
                                }

                                // 收集ICE候选信息
                                if (report.type === 'local-candidate') {
                                    localCandidates.push({
                                        id: report.id,
                                        ip: report.ip,
                                        port: report.port,
                                        protocol: report.protocol,
                                        candidateType: report.candidateType
                                    });
                                } else if (report.type === 'remote-candidate') {
                                    remoteCandidates.push({
                                        id: report.id,
                                        ip: report.ip,
                                        port: report.port,
                                        protocol: report.protocol,
                                        candidateType: report.candidateType
                                    });
                                } else if (report.type === 'candidate-pair') {
                                    candidatePairs.push({
                                        id: report.id,
                                        localCandidateId: report.localCandidateId,
                                        remoteCandidateId: report.remoteCandidateId,
                                        state: report.state,
                                        nominated: report.nominated,
                                        bytesSent: report.bytesSent,
                                        bytesReceived: report.bytesReceived
                                    });
                                }
                            });

                            // 记录视频统计信息
                            if (videoStats) {
                                // 查找对应的编解码器信息
                                let codec = '未知';
                                if (videoStats.codecId && codecInfo && codecInfo.id === videoStats.codecId) {
                                    codec = codecInfo.mimeType.split('/')[1] || codecInfo.mimeType;
                                }

                                // 获取分辨率信息
                                let resolution = '未知';
                                if (videoStats.frameWidth && videoStats.frameHeight) {
                                    resolution = `${videoStats.frameWidth}x${videoStats.frameHeight}`;
                                } else if (videoSize && videoSize.frameWidth && videoSize.frameHeight) {
                                    resolution = `${videoSize.frameWidth}x${videoSize.frameHeight}`;
                                }

                                // 计算解码延迟
                                const decodeTimePerFrame = videoStats.framesDecoded > 0 ?
                                    (videoStats.totalDecodeTime / videoStats.framesDecoded).toFixed(2) : 0;

                                // 记录详细的视频统计信息
                                console.log(`视频统计: ${videoStats.framesPerSecond} fps, 编码: ${codec}, 分辨率: ${resolution}`);
                                console.log(`  解码帧数: ${videoStats.framesDecoded}, 关键帧: ${videoStats.keyFramesDecoded}, 丢帧: ${videoStats.framesDropped}`);
                                console.log(`  接收: ${formatBytes(videoStats.bytesReceived)}, 丢包: ${videoStats.packetsLost}/${videoStats.packetsReceived}, 抖动: ${(videoStats.jitter * 1000).toFixed(2)}ms`);
                                console.log(`  解码器: ${videoStats.decoderImplementation}, 解码延迟: ${decodeTimePerFrame}ms/帧`);

                                // 将信息也添加到页面日志中
                                log(`视频: ${videoStats.framesPerSecond} fps, ${codec}, ${resolution}, 接收: ${formatBytes(videoStats.bytesReceived)}`);
                            }

                            // 记录活跃的ICE候选对
                            const activePairs = candidatePairs.filter(pair => pair.state === 'succeeded' || pair.nominated);
                            if (activePairs.length > 0) {
                                activePairs.forEach(pair => {
                                    const local = localCandidates.find(c => c.id === pair.localCandidateId);
                                    const remote = remoteCandidates.find(c => c.id === pair.remoteCandidateId);

                                    if (local && remote) {
                                        console.log(`活跃ICE对: ${local.ip}:${local.port} -> ${remote.ip}:${remote.port} (${pair.state}${pair.nominated ? ', 已提名' : ''}), 发送: ${formatBytes(pair.bytesSent)}, 接收: ${formatBytes(pair.bytesReceived)}`);
                                    }
                                });
                            }
                        }).catch(e => {
                            console.error('获取WebRTC统计信息失败:', e);
                        });
                    } catch (e) {
                        console.error('获取WebRTC统计信息时出错:', e);
                    }
                }
            }, 5000);

            // 在PeerConnection关闭时清除统计信息间隔
            peerConnection.addEventListener('connectionstatechange', () => {
                if (peerConnection.connectionState === 'closed' && statsInterval) {
                    clearInterval(statsInterval);
                }
            });
        }


        async function disconnectFromPeer() {
            setLoading(false);

            if (connectionTimeout) {
                clearTimeout(connectionTimeout);
                connectionTimeout = null;
            }

            if (pc) {
                pc.oniceconnectionstatechange = null;
                pc.onconnectionstatechange = null;
                pc.onsignalingstatechange = null;
                pc.ontrack = null;
                pc.ondatachannel = null;
                pc.onicecandidate = null;

                pc.getSenders().forEach(sender => {
                    if (sender.track) {
                        sender.track.stop();
                    }
                });

                pc.close();
                pc = null;
            }

            if (dataChannel) {
                dataChannel.close();
                dataChannel = null;
            }

            if (videoEl.srcObject) {
                const tracks = videoEl.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                videoEl.srcObject = null;
            }

            activeSenderId = null;
            updateSenderList();

            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            reconnectBtn.disabled = true;
            qualityBtn.disabled = true;

            updateStatus('已断开连接');
            log('已断开连接');
        }


        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                if (videoEl.requestFullscreen) {
                    videoEl.requestFullscreen();
                } else if (videoEl.webkitRequestFullscreen) {
                    videoEl.webkitRequestFullscreen();
                } else if (videoEl.mozRequestFullScreen) {
                    videoEl.mozRequestFullScreen();
                } else if (videoEl.msRequestFullscreen) {
                    videoEl.msRequestFullscreen();
                }
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        }


        function updatePlayPauseState(playing) {
            isPlaying = playing;

            if (playing) {
                playIcon.style.display = 'none';
                pauseIcon.style.display = 'block';
            } else {
                playIcon.style.display = 'block';
                pauseIcon.style.display = 'none';
            }
        }


        function updateSeekBar() {
            if (videoDuration > 0) {
                const percent = (currentPosition / videoDuration) * 100;
                seekSlider.value = percent;


                currentTimeEl.textContent = formatTime(currentPosition / 30);
                durationEl.textContent = formatTime(videoDuration / 30);
            }
        }


        function formatTime(seconds) {
            seconds = Math.floor(seconds);
            const minutes = Math.floor(seconds / 60);
            seconds = seconds % 60;
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }


        // 格式化字节数为可读格式
        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0 || bytes === undefined) return '0 Bytes';

            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }


        // 动态调整WebRTC视频比特率和帧率
        async function adjustVideoQuality(maxBitrate = 3000000, minBitrate = 500000, maxFramerate = 60) {
            if (!pc) {
                log('没有活动的WebRTC连接，无法调整视频质量', 'error');
                return false;
            }

            try {
                // 获取视频发送器
                const senders = pc.getSenders();
                const videoSender = senders.find(s => s.track && s.track.kind === 'video');

                if (!videoSender) {
                    log('没有找到视频发送器，无法调整视频质量', 'error');
                    return false;
                }

                // 获取当前参数
                const params = videoSender.getParameters();

                // 确保有encodings数组
                if (!params.encodings) {
                    params.encodings = [{}];
                }

                // 如果encodings数组为空，添加一个空对象
                if (params.encodings.length === 0) {
                    params.encodings.push({});
                }

                log(`调整视频质量: 最大比特率=${maxBitrate/1000}kbps, 最小比特率=${minBitrate/1000}kbps, 最大帧率=${maxFramerate}fps`);

                // 设置最大、最小比特率和最大帧率
                params.encodings[0].maxBitrate = maxBitrate;  // 例如：3Mbps
                params.encodings[0].minBitrate = minBitrate;  // 例如：500kbps
                params.encodings[0].maxFramerate = maxFramerate;  // 例如：60fps

                // 应用新参数
                await videoSender.setParameters(params);

                log(`成功调整视频质量参数`, 'success');
                return true;
            } catch (e) {
                log(`调整视频质量失败: ${e.message}`, 'error');
                return false;
            }
        }


        // 创建一个空的视频轨道
        function createEmptyVideoTrack() {
            // 创建一个空的canvas元素
            const canvas = document.createElement('canvas');
            canvas.width = 640;
            canvas.height = 480;

            // 获取canvas的上下文并绘制一个简单的背景
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = 'black';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 添加一些文本
            ctx.fillStyle = 'white';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('等待视频流...', canvas.width / 2, canvas.height / 2);

            // 从canvas创建一个视频流
            const stream = canvas.captureStream(60); // 30fps

            // 返回第一个视频轨道
            return stream.getVideoTracks()[0];
        }


        function sendCommand(command, params = {}) {
            if (dataChannel && dataChannel.readyState === 'open') {
                const message = {
                    command: command,
                    ...params
                };

                dataChannel.send(JSON.stringify(message));
                log(`发送命令: ${command}`);
                return true;
            } else {
                log('数据通道未就绪，无法发送命令', 'error');
                return false;
            }
        }


        function requestVideoStatus() {
            return sendCommand('get_status');
        }


        function requestKeyframe() {
            return sendCommand('request_keyframe');
        }


        function getVideoStats() {
            // 返回当前的视频统计信息
            return currentVideoStats;
        }


        function togglePlayPause() {
            return sendCommand('toggle_pause');
        }


        function seekTo(position) {
            return sendCommand('seek', { position: Math.floor(position) });
        }


        function startProgressUpdates() {

            if (seekUpdateInterval) {
                clearInterval(seekUpdateInterval);
            }


            seekUpdateInterval = setInterval(() => {
                if (isPlaying && videoDuration > 0) {
                    currentPosition++;
                    if (currentPosition > videoDuration) {
                        currentPosition = 0;
                    }
                    updateSeekBar();
                }
            }, 1000 / 30);


            // 定期请求视频状态
            setInterval(() => {
                if (dataChannel && dataChannel.readyState === 'open') {
                    requestVideoStatus();
                }
            }, 10000);

            // 在iOS上，定期请求关键帧以确保视频正确显示（减少频率）
            if (isIOS) {
                setInterval(() => {
                    if (dataChannel && dataChannel.readyState === 'open' && !videoEl.paused) {
                        // 检查视频是否真的停止了（帧率为0）
                        const stats = getVideoStats();
                        if (stats && stats.fps === 0) {
                            log('iOS设备：检测到视频停止，请求关键帧');
                            requestKeyframe();
                        }
                    }
                }, 10000); // 增加到10秒间隔
            }
        }


        function showControls() {
            videoControls.classList.add('active');


            if (controlsTimeout) {
                clearTimeout(controlsTimeout);
            }


            controlsTimeout = setTimeout(() => {
                videoControls.classList.remove('active');
            }, 3000);
        }


        connectBtn.addEventListener('click', connectToPeer);
        disconnectBtn.addEventListener('click', disconnectFromPeer);
        reconnectBtn.addEventListener('click', () => {
            log('手动重新连接');
            disconnectFromPeer();
            setTimeout(() => {
                connectToPeer();
            }, 1000);
        });
        qualityBtn.addEventListener('click', async () => {
            log('手动调整视频质量');
            try {
                // 设置超高质量视频参数：5Mbps最大比特率，1Mbps最小比特率，60fps最大帧率
                const success = await adjustVideoQuality(5000000, 1000000, 60);
                if (success) {
                    log('已设置超高质量视频参数: 5Mbps, 60fps', 'success');
                    qualityBtn.textContent = '超高质量';
                    qualityBtn.disabled = true;

                    // 同时发送命令到Android端
                    if (dataChannel && dataChannel.readyState === 'open') {
                        const command = {
                            command: 'adjust_video_quality',
                            maxBitrate: 5000000,
                            minBitrate: 1000000,
                            maxFramerate: 60
                        };
                        dataChannel.send(JSON.stringify(command));
                        log('已发送视频质量调整命令到Android端');
                    }

                    // 5秒后重新启用按钮
                    setTimeout(() => {
                        qualityBtn.disabled = false;
                        qualityBtn.textContent = '高质量';
                    }, 5000);
                } else {
                    log('设置视频质量参数失败', 'error');
                }
            } catch (e) {
                log(`调整视频质量失败: ${e.message}`, 'error');
            }
        });
        fullscreenBtn.addEventListener('click', toggleFullscreen);


        playPauseBtn.addEventListener('click', togglePlayPause);


        seekSlider.addEventListener('input', () => {
            if (videoDuration > 0) {
                const position = Math.floor((seekSlider.value / 100) * videoDuration);
                currentTimeEl.textContent = formatTime(position / 30);
            }
        });

        seekSlider.addEventListener('change', () => {
            if (videoDuration > 0) {
                const position = Math.floor((seekSlider.value / 100) * videoDuration);
                seekTo(position);
            }
        });


        videoEl.addEventListener('click', (e) => {

            if (e.target === videoEl) {
                showControls();
            }
        });


        videoEl.addEventListener('playing', () => {
            if (dataChannel && dataChannel.readyState === 'open') {

                requestVideoStatus();

                //startProgressUpdates();
            }
        });


        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {

                if (pc && (pc.iceConnectionState === 'disconnected' || pc.iceConnectionState === 'failed')) {
                    log('检测到连接已断开，尝试重新连接');
                    connectToPeer();
                }
            }
        });


        window.addEventListener('online', () => {
            log('网络已恢复');
            if (activeSenderId && (!pc || pc.iceConnectionState !== 'connected')) {
                log('尝试重新连接');
                connectToPeer();
            }
        });

        window.addEventListener('offline', () => {
            log('网络已断开', 'error');
            updateStatus('网络已断开', true);
        });


        // 连接到信令服务器
        connectToSignalingServer();

        // 自动连接到发送端
        setTimeout(() => {
            if (connectBtn && !connectBtn.disabled) {
                console.log('自动连接到发送端');
                log('自动连接到发送端');

                // 确保在连接前清除任何现有连接
                if (pc) {
                    disconnectFromPeer();
                }

                // 检查WebSocket连接状态
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    log('等待WebSocket连接建立...');

                    // 等待WebSocket连接建立
                    const checkInterval = setInterval(() => {
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            clearInterval(checkInterval);
                            log('WebSocket连接已建立，准备连接到发送端');

                            // 延迟一段时间后再连接，确保注册过程完成
                            setTimeout(() => {
                                connectBtn.click();
                            }, 1000);
                        }
                    }, 500);

                    // 设置超时，避免无限等待
                    setTimeout(() => {
                        clearInterval(checkInterval);
                        if (!ws || ws.readyState !== WebSocket.OPEN) {
                            log('WebSocket连接超时，尝试重新连接', 'error');
                            connectToSignalingServer();
                        }
                    }, 10000);
                } else {
                    // WebSocket已连接，直接连接到发送端
                    log('WebSocket已连接，准备连接到发送端');
                    setTimeout(() => {
                        connectBtn.click();
                    }, 1000);
                }
            }
        }, 5000);

        // iOS特定的初始化 - 在连接到信令服务器后执行
        if (isIOS) {
            // 记录iOS设备信息
            log('检测到iOS设备，应用iOS特定优化');
            log('已应用iOS特定WebRTC配置：unified-plan SDP语义和额外的STUN服务器');

            // 添加iOS特定的事件处理
            window.addEventListener('pageshow', function(event) {
                if (event.persisted) {
                    // 页面从缓存中恢复，可能需要重新初始化
                    log('iOS: 页面从缓存中恢复');
                    if (activeSenderId && (!pc || pc.iceConnectionState !== 'connected')) {
                        log('iOS: 尝试重新连接');
                        connectToPeer();
                    }
                }
            });

            // 在iOS上，我们可能需要处理方向变化
            window.addEventListener('orientationchange', function() {
                log('iOS: 屏幕方向已更改');
                // 强制重绘视频元素
                setTimeout(() => {
                    if (videoEl && videoEl.style) {
                        const display = videoEl.style.display;
                        videoEl.style.display = 'none';
                        videoEl.offsetHeight; // 强制重绘
                        videoEl.style.display = display;
                    }
                }, 500);
            });

            log('iOS特定初始化完成');
        }
    </script>
</body>
</html>
