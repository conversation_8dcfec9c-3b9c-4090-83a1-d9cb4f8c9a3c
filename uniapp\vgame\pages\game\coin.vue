<template>
    <view class="container">
        <!-- 视频背景 -->
        <view class="player-pro-live">
            <view id="easyplayer" ></view>
            <!-- 新增点击阻止层 -->
            <view class="video-overlay"></view>
        </view>
        <!-- 状态显示和退出按钮 -->
        <view>
            <view class="status-container">
                <view class="status-item">
                    {{ gameStatusText }}
                </view>
				<view class="status-item">{{ userInfo.money || 0 }}金币</view>
            </view>
            <view class="avatar-container">
                <button class="exit-button" @click="exitGame">
                    <text class="exit-icon">×</text>
                </button>
            </view>
        </view>

        <!-- 底部操作按钮区域 -->
        <view class="action-btns-container">
            <!-- 空闲状态 -->
            <button v-if="gameInfo.is_full === 0" class="start-btn" @click="startGame">
                开始
            </button>

            <!-- 已满状态 -->
            <button v-if="gameInfo.is_full === 1" class="disabled-btn" disabled>
                占用中
            </button>

            <button v-if="gameInfo.is_full === -1" class="disabled-btn" disabled>
                设备启动中...
            </button>

            <!-- 游戏中状态 -->
            <!-- 修改后的game-controls部分 -->
            <view v-if="gameInfo.is_full === 2" class="game-controls">
              <view class="button-group left-group">
                <button v-for="btn in controlButtons.slice(0,4)" 
                        :key="btn.value" 
                        class="control-btn left-btn"
                        @click="handleAction(btn.value)">
                  {{ btn.label }}
                </button>
              </view>
              <view class="button-group right-group">
                <button v-for="btn in controlButtons.slice(4)" 
                        :key="btn.value" 
                        class="control-btn"
                        @click="handleAction(btn.value)">
                  {{ btn.label }}
                </button>
              </view>
            </view>
        </view>
    </view>
</template>

<script>
    import request from '@/utils/request'
    import mqtt from 'mqtt' // 需要先npm install mqtt
    // import flvjs from 'flv.js';

    export default {
        data() {
            return {
                flvPlayer: null,
                gameId: null,
                gameInfo: {},
                userInfo: {},
                videoUrl: '',
                controlButtons: [{
                        label: '比倍',
                        value: 10
                    },
					{
					    label: '小',
					    value: 13
					},
					{
					    label: '和',
					    value: 12
					},
                    {
                        label: '大',
                        value: 11
                    },
                    {
                        label: '切换',
                        value: 14
                    },
                    {
                        label: '拍',
                        value: 15
                    }
                ],
                //mqtt
                client: null,
                loading: false,
                timeoutTimer: null,
                clientId: 'uniappMQTT-subscriber-'+ Math.random().toString(36).substr(2, 6) + Date.now().toString(36), // 替换为实际客户端ID
                mqttBrokerUrl: 'ws://'+window.location.hostname+':8083/mqtt' ,// 替换为实际MQTT地址
                
                playerInfo: null,
    
            }
        },
        mounted() {
            this.fixSafariLayout()
            // 添加窗口变化监听
            window.addEventListener('resize', this.fixSafariLayout)
            // window.addEventListener('resize', this.handleOrientationChange);
            window.addEventListener('orientationchange', this.handleOrientationChange);

        },
        computed: {
            gameStatusText() {
                if (!this.gameInfo) return '加载中...';
                switch (this.gameInfo.is_full) {
                    case -1:
                        return '';
                    case 0:
                        return '当前空闲';
                    case 1:
                        return '座位已满';
                    case 2:
                        return this.userInfo.nickname ? `${this.userInfo.nickname}` : '';
                    default:
                        return '状态未知';
                }
            }
        },
        async onLoad(options) {
            this.gameId = options.gameId
            await this.fetchGameData()

            if (!this.client || !this.client.connected) {
                await this.initMqtt()
            }
        },
        onUnload() {
            // 页面卸载时断开mqtt连接
            this.disconnectMqtt()
        },
        onHide() {
            // 页面隐藏时断开连接（包括返回首页）
            this.disconnectMqtt()
        },
        beforeDestroy() { // 页面卸载时
            this.disconnectMqtt()
            if (this.flvPlayer) {
                this.flvPlayer.destroy();
            }
            if (this.playerInfo) {
                this.playerInfo.destroy();
              }

            window.removeEventListener('resize', this.fixSafariLayout)
            // window.removeEventListener('resize', this.handleOrientationChange);
            window.removeEventListener('orientationchange', this.handleOrientationChange);
        },
        onReady() {
            this.checkPlayerAndInit()
            setTimeout(() => {
                   this.play()
            }, 500); // 延迟 500ms
        },
        methods: {
			// 精准检测 Safari 浏览器（排除 iOS 上其他浏览器）
			  isSafariBrowser() {
			    const ua = navigator.userAgent
			    const isIOS = /iPad|iPhone|iPod/.test(ua)
			    const isSafari = /Safari/.test(ua) && !/Chrome|CriOS|Edg|FxiOS|OPX/.test(ua)
			    return isSafari || (isIOS && !window.MSStream)
			  },

			  // 修复 Safari 布局问题
			  fixSafariLayout() {
			    if (!this.isSafariBrowser()) return


            document.canvasfix=setInterval(function(){
                const canvas = document.querySelector('#easyplayer canvas')
                if(canvas){
                    canvas.style["margin-left"]="118px";
                    canvas.style["margin-top"]="-120px";
                    canvas.style.transform = 'scale(0.70)';
                    canvas.style.transformOrigin = '0 0';
                    //alert(canvas.style["margin-left"]);
                    clearInterval(document.canvasfix);
                }

                console.log(123)

            },500);


			  },

            handleOrientationChange() {
              const isPortrait = window.matchMedia("(orientation: portrait)").matches;
              this.$nextTick(() => {
                if(this.playerInfo) {
                  this.playerInfo.resize(); // 通知播放器重置尺寸
                }
              });
            },
            play(){
                setTimeout((url) => {
                    this.playerInfo && this.playerInfo.play(url).then(() => {
                    }).catch((e) => {
                      console.error(e);
                    });
                  }, 0, this.videoUrl)
            },
            checkPlayerAndInit() {
                let retryCount = 0
                const maxRetries = 10
                
                const checkAndInit = () => {
                    if (typeof EasyPlayerPro !== 'undefined') {
                        this.playCreate()
                        return
                    }
                    
                    if (retryCount < maxRetries) {
                        retryCount++
                        setTimeout(checkAndInit, 500)
                    } else {
                        console.error('播放器加载失败')
                        uni.showToast({
                            title: '播放器加载失败',
                            icon: 'none'
                        })
                    }
                }
                
                checkAndInit()
            },
            playCreate() {
                try {
                    // 使用 uni.createSelectorQuery 获取元素
                    // 直接获取DOM元素
                    const container = document.getElementById('easyplayer');
            
                    if (!container) {
                        console.error('播放器容器未找到');
                        return;
                    }
            
                    console.log('容器元素:', container);
            
                    var easyplayer = new EasyPlayerPro({
                        container: container,
                        decoder: './static/js/EasyPlayer-decode.js', // 使用正确的解码器路径
                        videoBuffer: 1, // 视频缓存时长（单位：秒）
                        isResize: true, // 是否支持播放器大小调整
                        text: "",
                        loadingText: "加载中", // 加载视频时显示的文本
                        useMSE: true, // 是否使用 MSE（Media Source Extensions）
                        useSIMD: false, // 是否使用 SIMD（Single Instruction Multiple Data）
                        useWCS: false, // 是否使用 WCS（WebRTC通讯服务）
                        isMulti: true, // 是否支持多路视频播放
                        hasAudio: false, // 是否有音频
                        reconnection: true, // 是否支持自动重连
                        // showBandwidth: true, // 是否显示网速（注释掉，未启用）
                        showPerformance: false, // 是否显示性能信息
                        operateBtns: { // 播放器操作按钮配置
                            fullscreen: false, // 是否显示全屏按钮
                            screenshot: false, // 是否显示截图按钮
                            play: false, // 是否显示播放按钮
                            audio: false, // 是否显示音频控制按钮
                            record: false, // 是否显示录制按钮
                            quality: false, // 是否显示画质选择按钮
                            performance: false, // 是否显示性能按钮
                        },
            
                        watermarkConfig: { // 水印配置
                            text: { // 水印文本配置
                                content: 'easyplayer-pro' // 水印显示的文本内容
                            },
                            right: 10, // 水印距离右边的距离（单位：像素）
                            top: 10 // 水印距离顶部的距离（单位：像素）
                        },
                        quality: ['高清'],
                        playbackForwardMaxRateDecodeIFrame: 1,
                        isWebrtcForOthers: true,
                        demuxUseWorker: true,
                        supportHls265: false,
                        canvasRender: false,
                
                        aspectRatio: '16:9',// 设置视频显示区域的宽高比
                        easyStretch: true,  // 强制不同分辨率视频铺满窗口
            
                    });
            
                    easyplayer.on("fullscreen", function(flag) {
                        console.log('is fullscreen', flag)
                    })
                    easyplayer.on('playbackPreRateChange', (rate) => {
                        easyplayer.forward(rate);
                    })
            
                    easyplayer.on('playbackSeek', (data) => {
                        easyplayer.setPlaybackStartTime(data.ts);
                    })
                    this.playerInfo = easyplayer
            
                } catch (error) {
                    console.error('播放器初始化失败:', error);
                }
            },
            // 单独的全屏方法，由用户点击触发
                enterFullscreen() {
                  if (this.playerInfo) {
                    const container = document.getElementById("easyplayer");
                    if (container) {
                      // 使用原生全屏API
                      if (container.requestFullscreen) {
                        container.requestFullscreen();
                      } else if (container.webkitRequestFullscreen) {
                        container.webkitRequestFullscreen();
                      } else if (container.mozRequestFullScreen) {
                        container.mozRequestFullScreen();
                      } else if (container.msRequestFullscreen) {
                        container.msRequestFullscreen();
                      }
                    }
                  }
                },
            async initFlvPlayer(videoElement) {
                if (!flvjs.isSupported()) {
                  this.showUnsupportedMessage();
                  return;
                }
            
                try {
                  this.createFlvPlayer(videoElement);
                  await this.attemptAutoPlay(videoElement);
                } catch (error) {
                  console.error('播放失败:', error);
                  this.handlePlaybackError();
                }
              },
            
              createFlvPlayer(videoElement) {
                this.flvPlayer = flvjs.createPlayer({
                  type: 'flv',
                  url: this.videoUrl,
                  isLive: true
                });
                this.flvPlayer.attachMediaElement(videoElement);
                this.flvPlayer.load();
              },
            
              async attemptAutoPlay(videoElement) {
                try {
                  // 首次尝试自动播放（静音）
                  await videoElement.play();
                } catch (error) {
                  console.warn('自动播放被阻止:', error);
                  // 如果静音自动播放失败，尝试用户手势后播放
                  await this.waitForUserInteraction();
                  await videoElement.play();
                }
              },
            
              waitForUserInteraction() {
                return new Promise(resolve => {
                  const handleInteraction = () => {
                    document.removeEventListener('click', handleInteraction);
                    document.removeEventListener('touchstart', handleInteraction);
                    resolve();
                  };
                  
                  document.addEventListener('click', handleInteraction);
                  document.addEventListener('touchstart', handleInteraction);
                  
                  // 提示用户点击
                  uni.showToast({
                    title: '点击页面任意位置开始播放',
                    icon: 'none',
                    duration: 2000
                  });
                });
              },
            
              handlePlaybackError() {
                uni.showModal({
                  title: '播放错误',
                  content: '无法启动视频播放，请检查网络或格式支持',
                  showCancel: false
                });
              },
            
              showUnsupportedMessage() {
                uni.showModal({
                  title: '提示',
                  content: '当前浏览器不支持FLV播放，建议使用Chrome/Firefox或切换HLS格式',
                  showCancel: false
                });
              },
            
        
        // 获取游戏数据
        async fetchGameData() {
            try {
                const res = await request({
                    url: '/api/game/detail',
                    data: {
                        gameId: this.gameId
                    }
                })
                this.gameInfo = res.data
                this.userInfo = res.data.userInfo || {}
                this.videoUrl = res.data.video_url
                
                // this.$nextTick(() => {
                //     const videoWrapper = document.getElementById('h5-video');
                //     if (!videoWrapper) {
                //         console.error('未找到video容器');
                //         return;
                //     }
                //     const nativeVideo = videoWrapper.querySelector('video');
                //     if (nativeVideo && typeof nativeVideo.play === 'function') {
                //         this.initFlvPlayer(nativeVideo);
                //     } else {
                //         console.error('原生video元素无效', nativeVideo);
                //     }
                // });
            } catch (error) {
                console.error('获取数据失败:', error)
                uni.showToast({
                    title: '获取数据失败',
                    icon: 'none'
                })
            }
        },

        // 开始游戏
        async startGame() {
            try {
				const currentMoney = this.userInfo.money || 0;
				  if (currentMoney <= 0) {
					uni.showToast({
					  title: '金币不足，请先充值',
					  icon: 'none',
					  duration: 2000
					});
					
					// 跳转充值页面（假设充值页面路径为/pages/recharge/recharge）
					setTimeout(() => {
					  uni.switchTab({
						url: '/pages/recharge/recharge'
					  });
					}, 1500);
					return;
				  }

                const res = await request({
                    url: '/api/game/start',
                    data: {
                        sn: this.gameInfo.sn, //游戏序列号
                        number: 1 //座位号
                    }
                })

                const pubTopic = `mqtt/${this.gameInfo.sn}/sub/02` //mqtt主题
                const payload = JSON.stringify({
                    type: "startGame",
                    number: 1,
                    client_type: 'uniapp',
                    userId: this.userInfo.id,
                    coin: this.userInfo.money || 0,
                })

                this.sendMessage(pubTopic, payload)
                
                await this.fetchGameData()
            } catch (error) {
                console.error('开始游戏失败:', error)
                uni.showToast({
                    title: '操作失败',
                    icon: 'none'
                })
            }
        },

        // 游戏操作
        async handleAction(actionValue) {
            try {
                const pubTopic = `mqtt/${this.gameInfo.sn}/sub/02` //mqtt主题
                const payload = JSON.stringify({
                    type: "command",
                    number: 1,
                    status: actionValue || 0,
                    userId: this.userInfo.id,
                })
                this.sendMessage(pubTopic, payload);
            } catch (error) {
                console.error('操作失败:', error)
                uni.showToast({
                    title: '操作失败',
                    icon: 'none'
                })
            }
        },

        // 退出游戏
        async exitGame() {
            uni.showModal({
                title: '提示',
                content: '确定要退出游戏吗？',
                success: async (res) => {
                    if (res.confirm) {
                        if (this.gameInfo.is_full === -1 || this.gameInfo.is_full === 2) { //正在游戏中
                            try {
                                const res = await request({
                                    url: '/api/game/end',
                                    data: { // 正确参数格式
                                        sn: this.gameInfo.sn
                                    }
                                });

                                const pubTopic = `mqtt/${this.gameInfo.sn}/sub/02` //mqtt主题
                                const payload = JSON.stringify({
                                    type: "endGame",
                                    number: 1,
                                    client_type: 'uniapp',
                                    userId: this.userInfo.id,
                                })
                                this.sendMessage(pubTopic, payload);
                            } catch (error) {
                                console.error('请求失败:', error);
                            }
                        }

                        uni.navigateBack();
                        return;
                    }
                }
            });
        },
        // 初始化MQTT连接
        initMqtt() {
            this.client = mqtt.connect(this.mqttBrokerUrl, {
                username: 'game',
                password: 'sdoifj239874fh97g34fdg34',
                clientId: this.clientId,
                clean: true,
                connectTimeout: 4000,
                reconnectPeriod: 1000,
				keepalive: 30
            })
    
            // 连接成功回调
            this.client.on('connect', () => {
				 console.log('MQTT连接成功', 
				    '心跳间隔:', this.client.options.keepalive + '秒',
				    '实际超时:', this.client.options.keepalive * 1.5 + '秒'
				  )
                if (!this.loading) {
                    this.subscribeAckTopic()
                }
                this.loading = true
            })

            // 消息接收处理
            this.client.on('message', (topic, message) => {
                this.handleAckMessage(topic, message)
            })
			
			this.client.on('close', () => {
			  console.log('连接已关闭')
			})
			this.client.on('offline', () => {
			  console.log('客户端离线')
			})

            // 错误处理
            this.client.on('error', (error) => {
                console.error('MQTT error:', error)
                this.loading = false
            })
        },
        // 发送消息
        async sendMessage(pubTopic, payload) {
            if (!this.client || !this.client.connected) {
                await this.initMqtt()
            }

            this.client.publish(pubTopic, payload, {
                qos: 0
            }, (err) => {
                if (err) {
                    console.error('消息发送失败:', err)
                    this.loading = false
                    uni.showToast({
                        title: '指令发送失败',
                        icon: 'none'
                    })
                }
            })
        },

        disconnectMqtt() {
            if (this.client && this.client.connected) {
                this.client.end()
				this.loading = false
                console.log('MQTT disconnected')
            }
        },

        // 订阅ACK主题
        subscribeAckTopic() {
            const ackTopic = 'mqtt/+/pub/02ack'
			if (this.client) {
				this.client.subscribe(ackTopic, {
					qos: 1
				}, (err) => {
					if (!err) {
						console.log('订阅ACK主题成功:', ackTopic)
					}
				})
			} 
        },
        // 处理ACK消息
        async handleAckMessage(topic, message) {
            try {
                // 解析主题中的clientId
                const topicParts = topic.split('/')
                const senderClientId = topicParts[3]
                if (senderClientId !== '02ack') {
                    return;
                }
                const data = JSON.parse(message.toString())
                console.log('收到ACK响应:', data)
                if (data.type == 'update_game') {
                    await this.fetchGameData()
                }
            } catch (e) {
                console.error('消息解析失败:', e)
            }
        },

        // 超时处理
        startTimeoutTimer() {
            this.timeoutTimer = setTimeout(() => {
                if (!this.loading) {
                    this.loading = true
                    uni.showToast({
                        title: '响应超时',
                        icon: 'none'
                    })
                    // 这里可以添加重试逻辑
                }
            }, 10000) // 10秒超时
        }

    }
    }
</script>

<style scoped>
    .container {
      position: fixed;
      width: 100vh; /* 竖屏时高度作为宽度 */
      height: 100vw; /* 竖屏时宽度作为高度 */
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(90deg);
      transform-origin: center;
      background: #000;
      overflow: hidden;
    }
	/* 视频容器 */
	.player-pro-live {
	  position: absolute;
	  width: 100%;
	  height: 100%;
	  z-index: 1;
	}
	/* 播放器元素 */
	#easyplayer {
	  width: 100% !important;
	  height: 100% !important;
	  object-fit: cover; /* 视频填充方式 */
	}
    #h5-video{
        height: 100%;
    }
    .video-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
    }


    .start-btn-container {
        position: absolute;
        bottom: 40px;
        left: 50%;
        transform: translateX(-50%);
    }

    .start-btn {
        background: #ffcc00;
        color: white;
        width: 100px;
        border-radius: 30px;
        font-size: 14px;
    }

    .using-btn {
        background: #F44336;
    }

    /* 左上角状态信息 */
    .status-container {
        position: absolute;
        top: 9%;
        left: 10%;
        z-index: 99;
    }

    .status-item {
        color: #fff;
        font-size: 16px;
        line-height: 1.5;
        text-shadow: 1px 1px 2px #000;
    }

    /* 右上角头像和退出按钮 */
    .avatar-container {
        position: absolute;
        top: 5%;
        right: 10%;
        z-index: 99;
    }

    .exit-button {
        width: 30px;
        height: 30px;
        border-radius: 20px;
        background-color: #ff3333;
        border: none;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 10px;
        padding: 0;
    }

    .exit-icon {
        color: white;
        font-size: 24px;
        line-height: 1;
        font-weight: bold;
    }

    .exit-button:active {
        transform: scale(0.95);
    }

    .action-btns-container {
      position: absolute;
      bottom: 6%;
      left: 50%;
      transform: translateX(-50%);
      width: 90%;
      z-index: 99;
      padding: 0 10px;
    }

    .disabled-btn {
        background: #666 !important;
        color: #999 !important;
        width: 200px;
        border-radius: 30px;
        font-size: 14px;
    }

    .game-controls {
      display: flex;
      justify-content: space-between;
      width: 100%;
      gap: 10px;
    }
    .button-group {
      display: flex;
      gap: 10px;
    }
    .left-group {

        margin-left: 17%;
      justify-content: flex-start;
    }
    .right-group {
        margin-right: 10%;
      justify-content: flex-end;
    }

    .control-btn {
            background: #f0f0f0;
            color: #333;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            min-width: 60px; /* 最小宽度 */
                  height: 60px; /* 固定高度 */
				  display: flex;
				  align-items: center;
				        justify-content: center;
						margin: 0 2px;
            transition: all 0.1s ease-in-out;
            transform: scale(1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

    .start-btn {
        background: #ffcc00;
        color: white;
        width: 200px;
        border-radius: 30px;
        font-size: 14px;
    }

    /* 添加全局点击效果 */
    button {
        -webkit-tap-highlight-color: transparent;
    }
    /* 点击效果 */
    .control-btn:active {
      background: rgba(255, 64, 0, 0.8);
      transform: scale(0.95);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
    /* 开始按钮点击效果 */
    .start-btn:active {
        background: #ffb300;
        transform: scale(0.95);
    }

    /* 禁用状态不需要效果 */
    .disabled-btn:active {
        transform: scale(1);
    }

    
	.left-btn{
		min-width: 50px; /* 最小宽度 */
		      height: 50px; /* 固定高度 */
	}
 .video-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
      pointer-events: auto; 
      background-color: transparent; 
    }
</style>