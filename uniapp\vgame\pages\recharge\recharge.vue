<template>
	<view class="container">
		<!-- 主内容区域 -->
		<scroll-view scroll-y class="content" v-if="!loading">
			<!-- 特惠活动区块 -->
			<view class="section" v-if="specialList.length">
				<view class="section-title">特惠活动</view>
				<view class="grid">
					<view v-for="(item, index) in specialList" :key="index" class="card active-card"
						@click="handleRecharge(item)">
						<view class="coin">{{ item.coin }}金币</view>
						<view class="price">￥{{ item.price }}</view>
						<view class="gift" v-if="item.gift_coin > 0">赠送{{ item.gift_coin }}金币</view>
					</view>
				</view>
			</view>

			<!-- 普通充值区块 -->
			<view class="section" v-if="normalList.length">
				<view class="section-title">普通充值</view>
				<view class="grid">
					<view v-for="(item, index) in normalList" :key="index" class="card" @click="handleRecharge(item)">
						<view class="coin">{{ item.coin }}金币</view>
						<view class="price">￥{{ item.price }}</view>
						<view class="gift" v-if="item.gift_coin > 0">赠送{{ item.gift_coin }}金币</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<uni-load-more status="loading"></uni-load-more>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request'
	export default {
		data() {
			return {
				loading: true,
				specialList: [],
				normalList: []
			}
		},
		onLoad() {
			this.fetchRechargeData()
		},
		methods: {
			// 获取充值数据
			async fetchRechargeData() {
				try {
					const res = await request({
						url: '/api/recharge/list',
					})

					if (res.code !== 1) {
						throw new Error(res.msg || '获取数据失败')
					}

					// 处理数据格式
					const processData = res.data.map(item => ({
						...item,
						coin: Number(item.coin),
						gift_coin: Number(item.gift_coin),
						price: Number(item.price).toFixed(2)
					}))

					// 分类数据
					this.specialList = processData.filter(item => item.type === 2)
					this.normalList = processData.filter(item => item.type === 1)
				} catch (error) {
					uni.showToast({
						title: error.message,
						icon: 'none',
						duration: 3000
					})
				} finally {
					this.loading = false
				}
			},

			// 处理充值操作
			async handleRecharge(item) {
				console.log(item)
				uni.showModal({
					title: '确认充值',
					content: `确认购买 ${parseInt(item.coin, 10)}金币？`,
					success:async (res) => {
						if (res.confirm) {
							// 这里可以调用支付接口
							const res = await request({
								url: '/api/recharge/recharge',
								data: {
									id: item.id
								}
							})
							
							uni.showToast({
								title: '充值成功',
								icon: 'success'
							})
							
							uni.switchTab({
								url: '/pages/my/my'
							})
						
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.loading-container {
		height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.container {
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.content {
		flex: 1;
		padding: 20rpx;
	}

	.section {
		margin-bottom: 40rpx;

		&-title {
			font-size: 36rpx;
			font-weight: bold;
			margin: 20rpx 0;
			color: #333;
		}
	}

	.grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
	}

	.card {
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		text-align: center;
		border: 2rpx solid #eee;

		&.active-card {
			border-color: #ffd700;
			background: #fff9e6;
		}

		.coin {
			font-size: 40rpx;
			color: #f0a73a;
			font-weight: bold;
			margin-bottom: 10rpx;
		}

		.price {
			font-size: 32rpx;
			color: #666;
			margin-bottom: 10rpx;
		}

		.gift {
			font-size: 24rpx;
			color: #e64340;
		}
	}

	.tab-bar {
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
		border-top: 1rpx solid #eee;
		background: #fff;

		.tab-item {
			font-size: 28rpx;
			color: #666;
		}
	}
</style>