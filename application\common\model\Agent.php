<?php

namespace app\common\model;

use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;
use think\Model;
use think\Db;

/**
 * 代理
 */
class Agent extends Model
{
    /**
     * 申请提现
     * @param int $agent_id 代理ID
     * @param float $money 提现金额
     * @param int $type 提现类型 1=微信 2=支付宝
     * @return array
     */
    public function applyWithdraw($agent_id, $params)
    {
        $lock_key = 'withdraw_lock_' . $agent_id;
        $lock = cache($lock_key);
        if ($lock) {
            return ['code' => 0, 'msg' => '操作太频繁，请稍后再试'];
        }
        cache($lock_key, 1, 5); // 设置5秒锁

        $money = $params['money'];
        if ($money <= 0) {
            return ['code' => 0, 'msg' => '金额不正确'];
        }
        $type = $params['type'];

        Db::startTrans();
        try {
            // 检查是否有未完成的提现申请
            $id = Db::name('withdraw')
                ->where(['agent_id' => $agent_id, 'status' => 0])
                ->field('id')
                ->find();
            if ($id) {
                throw new Exception('已存在提现申请记录');
            }

            // 获取代理信息并加行锁
            $agent = Db::name('admin')
                ->where('id', $agent_id)
                ->lock(true)
                ->find();
            if (!$agent) {
                throw new Exception('代理不存在');
            }

            // 检查余额是否足够
            if ($agent['commission'] < $money) {
                throw new Exception('余额不足');
            }

            // 计算手续费
            $fee = $agent['withdraw_fee'] ?? 0;
            $fee_money = round($money * $fee / 100, 2);
            $real_money = $money - $fee_money;

            $time = time();
            // 创建提现记录
            $withdraw_data = [
                'type' => $type, //1：微信2：支付宝
                'money' => $money,
                'real_money' => $real_money,
                'agent_id' => $agent_id,
                'status' => 0, //0：待打款；1：已打款；2：已驳回；3：失败
                'fee' => $fee,
                'fee_money' => $fee_money,
                'createtime' => $time,
                'updatetime' => $time
            ];

            $withdraw_id = Db::name('withdraw')->insertGetId($withdraw_data);

            // 扣除代理余额
            $update_result = Db::name('admin')
                ->where('id', $agent_id)
                ->where('commission', '>=', $money) // 再次确认余额充足
                ->setDec('commission', $money);

            if (!$update_result) {
                throw new Exception('余额不足或已被其他操作修改');
            }

            // 记录余额变动
            $commission_log = [
                'agent_id' => $agent_id,
                'commission' => $money,
                'before' => $agent['commission'],
                'after' => $agent['commission'] - $money,
                'memo' => '申请提现',
                'createtime' => $time,
                'status' => -1 //状态：-1=支出 1=收入
            ];
            Db::name('admin_commission_log')->insert($commission_log);

            Db::commit();
            cache($lock_key, null); // 释放锁
            return ['code' => 1, 'msg' => '提现申请成功', 'data' => ['withdraw_id' => $withdraw_id]];
        } catch (Exception $e) {
            Db::rollback();
            cache($lock_key, null); // 释放锁
            return ['code' => 0, 'msg' => $e->getMessage()];
        } catch (DbException $e) {
            Db::rollback();
            cache($lock_key, null); // 释放锁
            return ['code' => 0, 'msg' => $e->getMessage()];
        } catch (PDOException $e) {
            Db::rollback();
            cache($lock_key, null); // 释放锁
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }
    
    /**
     * 审核提现
     * @param int $withdraw_id 提现记录ID
     * @param int $status 审核状态 1=通过 2=拒绝
     * @param string $remark 备注
     * @return array
     */
    public function auditWithdraw($withdraw_id, $status, $remark = '')
    {
        Db::startTrans();
        try {
            // 获取提现记录
            $withdraw = Db::name('withdraw')->where('id', $withdraw_id)->lock(true)->find();
            if (!$withdraw) {
                throw new \Exception('提现记录不存在');
            }
            
            if ($withdraw['status'] != 0) {
                throw new \Exception('该提现记录已处理');
            }
            
            $update_data = [
                'status' => $status,
                'status_timestamp' => date('Y-m-d H:i:s'),
                'remark' => $remark,
                'updatetime' => date('Y-m-d H:i:s')
            ];
            
            if ($status == 1) {
                // 通过审核

            } else {
                // 拒绝提现，退还余额
                $agent = Db::name('admin')
                    ->where('id', $withdraw['agent_id'])
                    ->lock(true)
                    ->find();

                if ($agent) {
                    // 退还余额
                    Db::name('admin')->where('id', $withdraw['agent_id'])->setInc('commission', $withdraw['money']);
                    
                    // 记录余额变动
                    $commission_log = [
                        'agent_id' => $withdraw['agent_id'],
                        'commission' => $withdraw['money'],
                        'before' => $agent['commission'],
                        'after' => $agent['commission'] + $withdraw['money'],
                        'memo' => '提现被拒绝，退还余额',
                        'createtime' => time(),
                        'status' => 1
                    ];
                    Db::name('admin_commission_log')->insert($commission_log);
                }
            }
            
            // 更新提现记录
            Db::name('withdraw')->where('id', $withdraw_id)->update($update_data);
            
            Db::commit();
            return ['code' => 1, 'msg' => '审核成功'];
        } catch (Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => $e->getMessage()];
        } catch (DbException $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => $e->getMessage()];
        } catch (PDOException $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 获取佣金记录
     * @param int $agent_id 代理ID
     * @param array $params 查询参数
     * @return array
     */
    public function getCommissionLog($agent_id, $params)
    {
        try {
            $query = Db::name('admin_commission_log')
                ->alias('c')
                ->join(['fa_user_recharge_log r'],'r.id=c.correlation_id and c.type=1', 'LEFT')
                ->join(['fa_user u'],'u.id = r.user_id', 'LEFT')
                ->where('c.agent_id', $agent_id)
                ->field('c.*, r.order_no, r.fee,r.price,u.nickname')
                ->order('c.createtime', 'desc');

            // 时间范围过滤
            if (!empty($params['start_time'])) {
                $query->where('c.createtime', '>=', $params['start_time']);
            }
            if (!empty($params['end_time'])) {
                $query->where('c.createtime', '<=', $params['end_time']);
            }

            // 状态过滤
            if (isset($params['status']) && is_numeric($params['status'])) {
                $query->where('c.status', $params['status']);
            }

            // 分页配置
            $page = $params['page'] ?? 1;
            $pageSize = $params['show_num'] ?? 10;

            $data = $query->paginate([
                'list_rows' => $pageSize,
                'page' => $page,
                'path' => 'javascript:ajaxPage([PAGE]);'
            ])->toArray();


            $agent = Db::name('admin')->where('id',$agent_id)->find();
            $data['commission'] = $agent['commission'];//可提现佣金
            $data['withdraw_commission'] = $agent['withdraw_commission'];//已提现佣金
            //提现中佣金
            $data['withdraw'] = Db::name('withdraw')->where(['agent_id' => $agent_id, 'status' => 0])->sum('money');
            return ['code' => 1, 'msg'=> '成功', 'data' => $data];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '查询失败: '.$e->getMessage()];
        }
    }

    /**
     * 获取提现记录
     * @param int $agent_id 代理ID
     * @param array $params 查询参数
     * @return array
     */
    public function getWithdrawLog($agent_id, $params)
    {
        try {
            $query = Db::name('withdraw')
                ->where('agent_id', $agent_id)
                ->field('id,type,money,real_money,status,fee,fee_money,createtime,remark')
                ->order('createtime', 'desc');

            // 状态过滤
            if (isset($params['status']) && is_numeric($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 时间范围过滤
            if (isset($params['start_time']) && is_numeric($params['start_time'])) {
                $query->where('createtime', '>=', $params['start_time']);
            }
            if (isset($params['end_time']) && is_numeric($params['end_time'])) {
                $query->where('createtime', '<=', $params['end_time']);
            }

            // 分页配置
            $page = $params['page'] ?? 1;
            $pageSize = $params['show_num'] ?? 10;

            $data = $query->paginate([
                'list_rows' => $pageSize,
                'page' => $page,
                'path' => 'javascript:ajaxPage([PAGE]);'
            ])->toArray();

            // 格式化数据
            $statusMap = [
                0 => '待打款',
                1 => '已打款',
                2 => '已驳回',
                3 => '失败'
            ];

            foreach ($data['data'] as &$item) {
                $item['status_text'] = $statusMap[$item['status']] ?? '未知状态';
                $item['type_text'] = $item['type'] == 1 ? '微信' : '支付宝';
                $item['createtime'] = date('Y-m-d H:i', $item['createtime']);
                $item['money'] = number_format($item['money'], 2);
                $item['real_money'] = number_format($item['real_money'], 2);
            }

            return ['code' => 1, 'data' => $data];
        } catch (\Exception $e) {
            return ['code' => 0, 'msg' => '查询失败: '.$e->getMessage()];
        }
    }
}
