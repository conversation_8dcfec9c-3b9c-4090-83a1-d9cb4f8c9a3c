/*
 * Skin: Black
 * -----------
 */
@import "../bootstrap-less/mixins.less";
@import "../bootstrap-less/variables.less";
@import "../fastadmin/variables.less";
@import "../fastadmin/mixins.less";

@sidebar-dark-submenu-bg: darken(@sidebar-dark-bg, 3%);
.skin-black {
    //Navbar & Logo
    .main-header {
        .box-shadow(0px 1px 1px rgba(0, 0, 0, 0.05));

        .navbar-toggle {
            color: #333;
        }

        .navbar-brand {
            color: #333;
            border-right: 1px solid #eee;
        }

        .navbar {
            .navbar-variant(#fff; #666; #444; rgba(0, 0, 0, .02));

            > .sidebar-toggle {
                color: #333;
                border-right: 1px solid #eee;
            }

            .navbar-nav {
                > li > a {
                    border-right: 1px solid #eee;
                }
            }

            .navbar-custom-menu .navbar-nav,
            .navbar-right {
                > li {
                    > a {
                        border-left: 1px solid #eee;
                        border-left: none;
                        border-right-width: 0;
                    }
                }
            }
        }

        .logo {
            .logo-variant(@sidebar-dark-bg; #fff);
            border-right: 1px solid @sidebar-dark-bg;
            @media (max-width: @screen-header-collapse) {
                .logo-variant(#fff; #222);
                border-right: none;
            }
        }

        li.user-header {
            background-color: #222;
        }

        .nav-addtabs > li > a, .nav-addtabs > li.active > a {
            border-right-color: transparent;
        }    }

    //Content Header
    .content-header {
        background: transparent;
        box-shadow: none;
    }

    //Create the sidebar skin
    .skin-dark-sidebar(#fff);

    .sidebar-menu > li {
        > a {
            border-left: 3px solid transparent;
            padding-left: 12px;
        }
    }

    @media (min-width: @screen-sm) {
        &.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
            margin-left: -3px;
        }
    }

    &.multiplenav {
        @media (max-width: @screen-header-collapse) {
            .main-header {
                .navbar {
                    .navbar-variant(@sidebar-dark-bg; #fff);
                }


                > .logo {
                    .logo-variant(@sidebar-dark-bg; #fff);
                }
            }

            .sidebar .mobilenav a.btn-app {
                background: lighten(@sidebar-dark-bg, 10%);
                color: #fff;

                &.active {
                    background: #fff;
                    color: lighten(@sidebar-dark-bg, 10%);
                }
            }
        }
    }
}
