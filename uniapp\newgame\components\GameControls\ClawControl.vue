<template>
	<view class="claw-game-control">
		<!-- 左上角头像区域 -->
		<view class="avatar-container left-avatars" v-if="gameInfo.is_full !== 2">
			<view class="avatar-item" v-for="(seat, index) in seats.slice(0, 4)" :key="seat.id">
				<view class="avatar-wrapper">
					<image
						:src="seat.status === 1 ? seat.avatar : `/static/dpad/p${seat.number}.png`"
						mode="aspectFill"
						class="avatar-img-group"
					/>
					<text class="avatar-name">{{ seat.status === 1 ? seat.nickname : '空闲' }}</text>
					<!-- 金币数量显示在头像下面 -->
					<text class="avatar-coins" v-if="seat.status === 1">{{ seat.coins || 0 }}币</text>
				</view>
			</view>
		</view>

		<!-- 游戏中时的左上角用户信息区域 -->
		<view class="game-user-info-container" v-if="gameInfo.is_full === 2">
			<!-- 左侧：用户头像、昵称、座位号 -->
			<view class="user-info-box">
				<image
					:src="userInfo.avatar || `/static/dpad/p${gameInfo.number || 1}.png`"
					mode="aspectFill"
					class="user-avatar"
				/>
				<view class="user-details">
					<text class="user-nickname">{{ userInfo.nickname || '玩家' }}</text>
					<text class="user-seat">{{ gameInfo.number }}P</text>
				</view>
			</view>

			<!-- 右侧：统计信息区域 -->
			<view class="user-stats-container">
				<!-- 金币框 -->
				<view class="stat-box coin-box" @click="showRechargeDialog">
					<image src="/static/coin.png" class="stat-icon" />
					<text class="stat-value">{{ userInfo.money || 0 }}</text>
					<image src="/static/add.png" class="add-icon" />
				</view>

				<!-- 积分框 -->
				<view class="stat-box score-box" @click="showScoreExchangeDialog">
					<image src="/static/score.png" class="stat-icon" />
					<text class="stat-value">{{ userInfo.score || 0 }}</text>
					<image src="/static/change.png" class="exchange-icon" />
				</view>
			</view>
		</view>

		<!-- 右上角头像、静音按钮和退出按钮 -->
		<view class="avatar-container right">
			<view class="avatar-group-box" v-if="gameInfo.is_full !== 2">
				<view v-for="(user, idx) in onlineAvatars" :key="user.id" class="avatar-group-item" :style="{ zIndex: 100 - idx, marginLeft: idx === 0 ? '0' : '-12px' }">
					<image class="avatar-img-group" :src="user.avatar || `/static/dpad/p${(idx % 4) + 1}.png`" />
				</view>
			</view>
			<!-- 静音按钮 -->
			<view class="setting-button" @click="toggleVideoAudio">
				<image class="setting-btn-icon" :src="isVideoMuted ? '/static/dpad/off.png' : '/static/dpad/on.png'" alt="静音" />
			</view>
			<!-- 设置按钮 -->
			<view class="setting-button" @click="$emit('show-func-dialog')">
				<image class="setting-btn-icon" src="/static/func/setting.png" alt="设置" />
			</view>
			<!-- 退出按钮 -->
			<button class="exit-button"
				@click="exitGame">
				<text class="exit-icon">×</text>
			</button>
		</view>

		<!-- 座位按钮区域 - 下方中间 -->
		<view class="seats-container bottom-center" v-if="gameInfo.is_full !== 2">
			<view class="seats-row">
				<view
					v-for="(seat, index) in availableSeats"
					:key="seat.id"
					class="seat-button-wrapper"
				>
					<view v-if="seat.status === 1" class="occupied-seat">
						<image class="avatar-img-group" :src="seat.avatar || `/static/dpad/p${seat.number}.png`" />
						<text class="avatar-name">{{ seat.nickname }}</text>
					</view>
					<view v-else class="seat-button-container">
						<view class="game-button" @click="startGame(seat)">
							<image src="/static/dpad/play.png" class="play-button-img" mode="aspectFit" />
						</view>
						<image :src="`/static/dpad/p${seat.number}.png`" class="seat-avatar-img" mode="aspectFit" />
					</view>
				</view>
			</view>
		</view>

		<!-- 游戏操作区域 -->
		<view v-if="gameInfo.is_full === 2" class="game-controls">
			<!-- 左侧方向轮盘 -->
			<view class="directional-pad left-control">
				<view class="dpad-circle">
					<button class="dpad-btn up"
						@touchstart="handleUpStart"
						@touchend="handleUpEnd"
						@touchcancel="handleUpEnd"
						@mousedown="handleUpStart"
						@mouseup="handleUpEnd"
						@mouseleave="handleUpEnd"
						@contextmenu.prevent>
						<image src="/static/dpad/up.png" class="dpad-arrow-img" mode="aspectFit" />
					</button>
					<button class="dpad-btn down"
						@touchstart="handleDownStart"
						@touchend="handleDownEnd"
						@touchcancel="handleDownEnd"
						@mousedown="handleDownStart"
						@mouseup="handleDownEnd"
						@mouseleave="handleDownEnd"
						@contextmenu.prevent>
						<image src="/static/dpad/down.png" class="dpad-arrow-img" mode="aspectFit" />
					</button>
					<button class="dpad-btn left"
						@touchstart="handleLeftStart"
						@touchend="handleLeftEnd"
						@touchcancel="handleLeftEnd"
						@mousedown="handleLeftStart"
						@mouseup="handleLeftEnd"
						@mouseleave="handleLeftEnd"
						@contextmenu.prevent>
						<image src="/static/dpad/left.png" class="dpad-arrow-img" mode="aspectFit" />
					</button>
					<button class="dpad-btn right"
						@touchstart="handleRightStart"
						@touchend="handleRightEnd"
						@touchcancel="handleRightEnd"
						@mousedown="handleRightStart"
						@mouseup="handleRightEnd"
						@mouseleave="handleRightEnd"
						@contextmenu.prevent>
						<image src="/static/dpad/right.png" class="dpad-arrow-img" mode="aspectFit" />
					</button>
				</view>
			</view>

			<!-- 右侧功能按钮 - 贴近右边 -->
			<view class="action-group right-edge">
				<!-- 发炮按钮（抓取） -->
				<view class="fire-button-container">
					<view class="fire-button"
						@touchstart="onFireStart"
						@touchend="onFireEnd"
						@touchcancel="onFireEnd"
						@mousedown="onFireStart"
						@mouseup="onFireEnd"
						@mouseleave="onFireEnd"
					>
						<image src="/static/dpad/fire.png" class="action-btn-img" :class="{ active: isActiveFire }" mode="aspectFit" />
					</view>
				</view>
				<!-- 投币按钮 -->
				<view class="coin-button-container">
					<view class="action-btn"
						@touchstart="isActiveCoin = true"
						@touchend="isActiveCoin = false"
						@mousedown="isActiveCoin = true"
						@mouseup="isActiveCoin = false"
						@mouseleave="isActiveCoin = false"
						@touchcancel="isActiveCoin = false"
						@click="showCoinDialog"
					>
						<image src="/static/dpad/coin.png" class="action-btn-img" :class="{ active: isActiveCoin }" mode="aspectFit" />
					</view>
				</view>
			</view>
		</view>

		<!-- 投币弹窗 - 使用UniversalModal组件 -->
		<UniversalModal
			:show="showCoinModal"
			title="选择投币数量"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="coin"
			@close="hideCoinDialog"
		>
			<view v-if="coinButtons.length === 0" class="no-coin-tip">
				暂无投币选项配置
			</view>
			<view v-else class="coin-buttons-grid">
				<button
					v-for="button in coinButtons"
					:key="button.id"
					class="coin-button"
					@click="handleCoinSelect(button)"
				>
					<text class="coin-button-text">{{ button.name }}</text>
					<text class="coin-button-value">{{ button.value }}币</text>
				</button>
			</view>
		</UniversalModal>
	</view>
</template>

<script>
import UniversalModal from '@/components/UniversalModal.vue'

export default {
	name: 'ClawControl',
	components: {
		UniversalModal
	},
	inheritAttrs: false,
	emits: [
		'show-func-dialog',
		'show-reward-dialog',
		'show-score-exchange-dialog',
		'toggleVideoAudio',
		'show-exit-dialog',
		'exitGame',
		'show-settle-mask',
		'show-wait-mask',
		'startGame',
		'showSettingDialog',
		'updateIdleCountdownStyle'
	],
	props: {
		gameInfo: {
			type: Object,
			default: () => ({})
		},
		userInfo: {
			type: Object,
			default: () => ({})
		},
		seats: {
			type: Array,
			default: () => []
		},
		isVideoMuted: {
			type: Boolean,
			default: false
		},
		showIdleCountdown: {
			type: Boolean,
			default: false
		},
		idleCountdown: {
			type: Number,
			default: 0
		},
		cachedCoin: {
			type: Number,
			default: 0
		},
		gameSettings: {
			type: Object,
			default: () => ({
				gameSound: true
			})
		}
	},
	data() {
		return {
			// 娃娃机游戏内部状态
			leftPressed: false,
			rightPressed: false,
			upPressed: false,
			downPressed: false,
			firePressed: false,
			// 上下按键需要连发定时器
			upInterval: null,
			downInterval: null,
			onlineAvatars: [],
			isActiveFire: false,
			isActiveCoin: false,
			showCoinModal: false,
			coinButtons: [
				{id: 51, name: "投1币", position: "center-left", value: 1},
				{id: 52, name: "投5币", position: "center", value: 5},
				{id: 53, name: "投30币", position: "center-right", value: 30}
			],
			isVideoMuted: false,
			// 退出相关状态
			isExitWaiting: false,
			exitCountdown: 0,
			exitCountdownTimer: null,
			lastActionTime: 0 // 记录最后操作时间
		}
	},
	watch: {
		userInfo: {
			handler(newVal) {
				if (newVal && newVal.avatar && newVal.id) {
					if (!this.onlineAvatars.some(u => u.id === newVal.id)) {
						this.onlineAvatars.push({
							id: newVal.id,
							avatar: newVal.avatar
						})
					}
				}
			},
			immediate: true,
			deep: true
		},
		gameInfo: {
			handler(newVal) {
				console.log('gameInfo watch触发:', newVal);
				if (newVal && typeof newVal.coin !== 'undefined' && newVal.coin !== null && !isNaN(newVal.coin) && Number(newVal.coin) >= 0) {
					this.$emit('show-reward-dialog', Number(newVal.coin));
				}
				if (newVal) {
					this.initCoinButtons();
					this.$nextTick(() => {
						this.adjustButtonPosition();
						this.$forceUpdate();
					});
				}
			},
			immediate: true,
			deep: true
		},
		'gameSettings.gameSound': {
			handler(newVal) {
				console.log('ClawControl - 游戏声音设置变化:', newVal)
				this.isVideoMuted = !newVal
			},
			immediate: true
		}
	},
	computed: {
		gameStatusText() {
			if (!this.gameInfo) return '加载中...'
			switch (this.gameInfo.is_full) {
				case 0: return '当前空闲'
				case 1: return '座位已满'
				case 2: return this.userInfo.nickname || '正在游戏中'
				default: return '等待启动中...'
			}
		},
		availableSeats() {
			const filtered = this.seats.filter(seat => seat.status !== undefined && seat.status !== null);
			return filtered;
		},
		currentMuteState() {
			return !this.gameSettings.gameSound
		}
	},
	mounted() {
		this.initCoinButtons();
		this.adjustButtonPosition();
		window.addEventListener('resize', this.adjustButtonPosition);
		window.addEventListener('orientationchange', () => {
			setTimeout(this.adjustButtonPosition, 100);
		});
	},
	methods: {
		// 初始化投币按钮配置
		initCoinButtons() {
			console.log('初始化投币按钮配置');
			try {
				if (this.gameInfo && this.gameInfo.control_config) {
					let config;
					if (typeof this.gameInfo.control_config === 'string') {
						config = JSON.parse(this.gameInfo.control_config);
					} else {
						config = this.gameInfo.control_config;
					}
					if (config && config.buttons && Array.isArray(config.buttons)) {
						this.coinButtons = config.buttons;
						console.log('使用配置的投币按钮:', this.coinButtons);
					} else {
						this.coinButtons = [
							{id: 51, name: "投1币", position: "center-left", value: 1},
							{id: 52, name: "投5币", position: "center", value: 5},
							{id: 53, name: "投30币", position: "center-right", value: 30}
						];
						console.log('使用默认投币按钮配置:', this.coinButtons);
					}
				} else {
					this.coinButtons = [
						{id: 51, name: "投1币", position: "center-left", value: 1},
						{id: 52, name: "投5币", position: "center", value: 5},
						{id: 53, name: "投30币", position: "center-right", value: 30}
					];
					console.log('使用默认投币按钮配置(无gameInfo):', this.coinButtons);
				}
			} catch (error) {
				console.error('解析投币按钮配置失败:', error);
				this.coinButtons = [
					{id: 51, name: "投1币", position: "center-left", value: 1},
					{id: 52, name: "投5币", position: "center", value: 5},
					{id: 53, name: "投30币", position: "center-right", value: 30}
				];
			}
		},
		// 显示充值对话框
		showRechargeDialog() {
			console.log('点击金币，跳转到充值页面')
			const gameInfo = {
				gameId: this.gameInfo?.id,
				gameName: this.gameInfo?.name,
				is_landscape: this.gameInfo?.is_landscape,
				fromGame: true,
				timestamp: Date.now()
			}
			uni.setStorageSync('recharge_from_game', gameInfo)
			uni.switchTab({
				url: '/pages/pay/index'
			})
		},
		// 显示积分兑换对话框
		showScoreExchangeDialog() {
			console.log('点击积分，显示兑换弹窗')
			this.$emit('show-score-exchange-dialog')
		},
		// 切换视频音频
		toggleVideoAudio() {
			console.log('ClawControl - 点击静音按钮')
			this.$emit('toggleVideoAudio')
		},
		// 退出游戏
		exitGame() {
			// 娃娃机退出判断
			if (this.gameInfo.is_full === 2) {
				// 判断距离最后操作时间是否小于3秒，需等待
				const now = Date.now();
				const waitTime = 3000;
				const timeSinceLastAction = now - (this.lastActionTime || 0);

				if (this.lastActionTime && timeSinceLastAction < waitTime) {
					// 需要等待，显示倒计时
					this.exitCountdown = Math.ceil((waitTime - timeSinceLastAction) / 1000);
					this.isExitWaiting = true;
					this.$emit('show-exit-dialog');
					this.exitCountdownTimer = setInterval(() => {
						this.exitCountdown--;
						if (this.exitCountdown <= 0) {
							clearInterval(this.exitCountdownTimer);
							this.isExitWaiting = false;
						}
					}, 1000);
				} else {
					// 可以直接退出
					this.isExitWaiting = false;
					this.$emit('show-exit-dialog');
				}
			} else {
				// 非游戏状态，直接退出
				this.$emit('exitGame');
			}
		},
		// 退出弹窗相关方法
		cancelExit() {
			if (this.exitCountdownTimer) {
				clearInterval(this.exitCountdownTimer);
				this.exitCountdownTimer = null;
			}
			this.isExitWaiting = false;
			this.$emit('show-exit-dialog');
		},
		handleSettleAndExit() {
			this.sendEndGameWS();
			this.$emit('show-exit-dialog');
			this.$emit('exitGame');
		},
		handleSettleCurrent() {
			this.sendEndGameWS();
			this.$emit('show-exit-dialog');
			this.$emit('show-settle-mask', true);
		},
		// 发送结束游戏WebSocket消息
		sendEndGameWS() {
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const message = {
					type: 'endGame',
					number: this.gameInfo.number
				};
				window.gameWebSocket.send(message);
				console.log('【LOG】娃娃机发送结束游戏指令:', message);
			} else {
				console.warn('【LOG】WebSocket未连接，无法发送结束游戏指令');
			}
		},
		// 开始游戏
		startGame(seat) {
			if (this.gameInfo.is_full === 0) {
				this.$emit('show-wait-mask', true);
			}
			this.$emit('startGame', seat)
		},
		// 上键开始 - 娃娃机上移
		handleUpStart(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (this.upPressed) return
			this.upPressed = true
			this.lastActionTime = Date.now() // 记录操作时间
			console.log('娃娃机: 开始上移')
			this.sendGameCommand(10)
		},
		// 上键结束 - 发送松开信号
		handleUpEnd(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (!this.upPressed) return
			this.upPressed = false
			console.log('娃娃机: 结束上移')
			// 发送松开信号：按键值+32
			this.sendGameCommand(10 + 32) // 42
		},
		// 下键开始 - 娃娃机下移
		handleDownStart(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (this.downPressed) return
			this.downPressed = true
			this.lastActionTime = Date.now() // 记录操作时间
			console.log('娃娃机: 开始下移')
			this.sendGameCommand(11)
		},
		// 下键结束 - 发送松开信号
		handleDownEnd(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (!this.downPressed) return
			this.downPressed = false
			console.log('娃娃机: 结束下移')
			// 发送松开信号：按键值+32
			this.sendGameCommand(11 + 32) // 43
		},
		// 左键开始 - 娃娃机左移
		handleLeftStart(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (this.leftPressed) return;
			this.leftPressed = true;
			this.lastActionTime = Date.now() // 记录操作时间
			console.log('娃娃机: 开始左移')
			this.sendGameCommand(12);
		},
		// 左键结束 - 发送松开信号
		handleLeftEnd(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (!this.leftPressed) return;
			this.leftPressed = false;
			console.log('娃娃机: 结束左移')
			// 发送松开信号：按键值+32
			this.sendGameCommand(12 + 32); // 44
		},
		// 右键开始 - 娃娃机右移
		handleRightStart(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (this.rightPressed) return;
			this.rightPressed = true;
			this.lastActionTime = Date.now() // 记录操作时间
			console.log('娃娃机: 开始右移')
			this.sendGameCommand(13);
		},
		// 右键结束 - 发送松开信号
		handleRightEnd(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (!this.rightPressed) return;
			this.rightPressed = false;
			console.log('娃娃机: 结束右移')
			// 发送松开信号：按键值+32
			this.sendGameCommand(13 + 32); // 45
		},
		// 发炮开始 - 娃娃机抓取
		handleFireStart() {
			if (this.firePressed) return;
			this.firePressed = true;
			this.lastActionTime = Date.now() // 记录操作时间
			console.log('娃娃机: 开始抓取')
			this.sendGameCommand(15);
		},
		// 发炮结束
		handleFireEnd() {
			if (!this.firePressed) return;
			this.firePressed = false;
			console.log('娃娃机: 结束抓取')
		},
		// 发送游戏指令
		sendGameCommand(command) {
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const message = {
					type: 'command',
					number: this.gameInfo.number,
					status: command,
				}
				window.gameWebSocket.send(message)
				console.log('【LOG】娃娃机发送指令:', command, '消息:', message)
			} else {
				console.warn('【LOG】WebSocket未连接，无法发送指令', window.gameWebSocket)
			}
		},
		// 显示投币弹窗
		showCoinDialog() {
			console.log('显示投币弹窗');
			if (this.$parent && this.$parent.showCoinDialog) {
				this.$parent.showCoinDialog(this.coinButtons);
			} else {
				this.showCoinModal = true;
			}
		},
		// 隐藏投币弹窗
		hideCoinDialog() {
			this.showCoinModal = false;
		},
		// 处理投币确认
		handleCoinConfirm(button) {
			console.log('处理投币确认:', button);
			this.sendPutInCoins(button.value);
		},
		// 处理投币选择
		handleCoinSelect(button) {
			console.log('选择投币:', button);
			this.hideCoinDialog();
			this.sendPutInCoins(button.value);
		},
		// 发送投币指令
		sendPutInCoins(coinAmount) {
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const requestId = 'putInCoins_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
				const message = {
					type: 'putInCoins',
					number: this.gameInfo.number,
					coin: coinAmount,
					requestId: requestId
				};
				window.gameWebSocket.send(message);
				console.log('【LOG】投币指令已发送');
				uni.showToast({
					title: `投币${coinAmount}个`,
					icon: 'none',
					duration: 1500
				});
			} else {
				console.warn('WebSocket未连接，无法发送投币指令');
				uni.showToast({
					title: 'WebSocket未连接',
					icon: 'error'
				});
			}
		},
		// 发炮按钮事件处理
		onFireStart(event) {
			if (event) {
				event.preventDefault();
				event.stopPropagation();
			}
			this.isActiveFire = true;
			this.handleFireStart();
		},
		onFireEnd(event) {
			if (event) {
				event.preventDefault();
				event.stopPropagation();
			}
			this.isActiveFire = false;
			this.handleFireEnd();
		},

		// 调整按钮位置
		adjustButtonPosition() {
			this.$nextTick(() => {
				console.log('娃娃机座位按钮位置已调整');
			});
		}
	},
	// 组件销毁时清理定时器
	beforeUnmount() {
		if (this.exitCountdownTimer) clearInterval(this.exitCountdownTimer)
		window.removeEventListener('resize', this.adjustButtonPosition);
		window.removeEventListener('orientationchange', this.adjustButtonPosition);
	}
}
</script>

<style scoped>
/* 娃娃机游戏控制界面样式 */
.claw-game-control {
	position: absolute;
	width: 100% !important;
	height: 100% !important;
	top: 0;
	left: 0;

	/* 禁用浏览器默认的选择和长按操作 */
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	/* 禁用长按上下文菜单 */
	-webkit-touch-callout: none;
	-webkit-tap-highlight-color: transparent;

	/* 禁用拖拽 */
	-webkit-user-drag: none;
	-khtml-user-drag: none;
	-moz-user-drag: none;
	-o-user-drag: none;
	user-drag: none;

	/* 禁用双击缩放 */
	touch-action: manipulation;
}

/* 左侧头像容器样式 */
.avatar-container.left-avatars {
	position: absolute;
	top: 5%;
	left: 3%;
	z-index: 3;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 8px;
}

.avatar-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 20px;
	padding: 8px;
	backdrop-filter: blur(4px);
	box-shadow: 0 2px 8px #0003;
	min-width: 80px;
}

.avatar-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.avatar-name {
	color: white;
	font-size: 12px;
	margin-top: 4px;
	max-width: 70px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: center;
}

.avatar-coins {
	color: #FFD700;
	font-size: 11px;
	font-weight: bold;
	margin-top: 2px;
	text-align: center;
}

/* 退出按钮样式 */
.exit-button {
	width: 30px;
	height: 30px;
	border-radius: 20px;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 5px;
	font-size: 16px;
	font-weight: bold;
	cursor: pointer;
	backdrop-filter: blur(4px);
	background-color: rgba(255, 0, 0, 0.8);
	color: white;
}

/* 头像叠加样式 */
.avatar-group-box {
	display: flex;
	align-items: center;
	position: relative;
}
.avatar-group-item {
	position: relative;
	margin-left: -12px;
}
.avatar-group-item:first-child {
	margin-left: 0;
}
.avatar-img-group {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	border: 2px solid #fff;
	box-shadow: 0 2px 8px #0003;
}

/* 座位按钮容器 - 下方中间 */
.seats-container.bottom-center {
	position: absolute;
	bottom: 8%;
	left: 50%;
	transform: translateX(-50%);
	z-index: 3;
	pointer-events: none;
}

.seats-row {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20px;
}

.seat-button-wrapper {
	pointer-events: auto;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.seat-button-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 8px;
}

.occupied-seat {
	display: flex;
	flex-direction: column;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 20px;
	padding: 8px;
	backdrop-filter: blur(4px);
	box-shadow: 0 2px 8px #0003;
}

.game-button {
	background: none;
	border: none;
	outline: none;
	padding: 0;
	margin: 0 40rpx;
	margin-bottom: 5rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	box-shadow: none;
}

.game-button:focus {
	outline: none;
	border: none;
	box-shadow: none;
}

.game-button:active {
	transform: scale(0.95);
}

.play-button-img {
	width: 130rpx;
	height: 130rpx;
	display: block;
}

.seat-avatar-img {
	width: 70rpx;
	height: 70rpx;
	display: block;
	border-radius: 50%;
	border: 2px solid #fff;
	box-shadow: 0 2px 8px #0003;
}

/* 游戏控制区域 */
.game-controls {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 40%;
	z-index: 2;
	pointer-events: auto;
}

/* 方向轮盘样式 - 左侧 */
.directional-pad.left-control {
	position: absolute;
	left: 5vw;
	bottom: 15vh;
	width: 18vmax;
	height: 18vmax;
}

.dpad-circle {
	position: relative;
	width: 100%;
	height: 100%;
	background-image: url('/static/dpad/circle.png');
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
	border: none;
	background-color: transparent;
}

.dpad-btn {
	position: absolute;
	background: transparent !important;
	border: none !important;
	border-radius: 0;
	width: 6vmax;
	height: 6vmax;
	cursor: pointer;
	transition: all 0.2s ease;
	backdrop-filter: none;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: none !important;
	min-width: unset;
	min-height: unset;
	padding: 0;
	outline: none !important;

	/* 禁用长按右键菜单和选择 */
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-touch-callout: none;
	-webkit-tap-highlight-color: transparent;
	touch-action: manipulation;
}

.dpad-btn:focus {
    outline: none;
}

.dpad-btn:active {
	transform: scale(0.95);
	filter: brightness(0.9);
	opacity: 0.9;
}

.dpad-btn.up {
	top: 1vmax;
	left: 50%;
	transform: translateX(-50%);
}

.dpad-btn.down {
	bottom: 1vmax;
	left: 50%;
	transform: translateX(-50%);
}

.dpad-btn.left {
	left: 1vmax;
	top: 50%;
	transform: translateY(-50%);
}

.dpad-btn.right {
	right: 1vmax;
	top: 50%;
	transform: translateY(-50%);
}

.dpad-arrow-img {
  width: 100%;
  height: 100%;
  display: block;
  margin: auto;
}

/* 右侧功能按钮组 - 贴近右边 */
.action-group.right-edge {
  position: absolute;
  right: 2vw;
  bottom: 15vh;
  width: 12vmax;
  height: 20vmax;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2vmax;
}

/* 发炮按钮容器 */
.fire-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 投币按钮容器 */
.coin-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn,
.fire-button {
  background: transparent;
	border: none;
	padding: 0;
	margin: 0;
	outline: none;
  border-radius: 0 !important;
  transform-origin: 50% 50%;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  width: 10vmax;
  height: 10vmax;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.action-btn:active,
.fire-button:active {
	transform: scale(0.95);
	filter: brightness(0.9);
	opacity: 0.9;
}

.action-btn-img {
  width: 100%;
  height: 100%;
  display: block;
  margin: 0;
  pointer-events: none;
}

.action-btn-img.active {
  filter: brightness(1.5);
}

.fire-button[disabled],
.action-btn[disabled] {
  filter: grayscale(1) brightness(0.7) opacity(0.7);
  cursor: not-allowed;
}

.avatar-container.left {
	position: absolute;
	top: 5%;
	left: 0;
	margin-left: 5%;
	gap: 10px;
}

/* 游戏中的用户信息容器 */
.game-user-info-container {
	position: absolute;
	top: 5%;
	left: 10%;
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	z-index: 3;
}

/* 用户信息框 */
.user-info-box {
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 30px;
	padding: 5px 16px;
	display: flex;
	align-items: center;
	gap: 8px;
	backdrop-filter: blur(4px);
	box-shadow: 0 2px 8px #0003;
}

.user-avatar {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	border: 2px solid #fff;
	background: #fff;
	box-shadow: 0 2px 8px #0003;
}

.user-details {
	display: flex;
	flex-direction: column;
	gap: 2px;
	justify-content: center;
}

.user-nickname {
	color: #fff;
	font-size: 18px;
	font-weight: bold;
	max-width: 80px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.user-seat {
	color: #F17676;
	font-size: 14px;
	font-weight: bold;
}

/* 统计信息容器 */
.user-stats-container {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

/* 统计信息框 */
.stat-box {
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 30px;
	padding: 5px 10px;
	display: flex;
	align-items: center;
	gap: 8px;
	backdrop-filter: blur(4px);
	box-shadow: 0 2px 8px #0003;
	min-width: 80px;
	cursor: pointer;
}

.stat-box:active {
	transform: scale(0.95);
	opacity: 0.8;
}

.stat-icon {
	width: 20px;
	height: 20px;
	flex-shrink: 0;
}

.stat-value {
	color: #fff;
	font-size: 16px;
	font-weight: bold;
	flex: 1;
}

.add-icon,
.exchange-icon {
	width: 20px;
	height: 20px;
	flex-shrink: 0;
	opacity: 0.8;
}

.avatar-container.right {
	position: absolute;
	top: 5%;
	right: 0;
	left: auto;
	margin-right: 10%;
	margin-left: 0;
}

.setting-button {
  width: 30px;
  height: 30px;
  border: none;
  background: none;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  color: white;
  box-shadow: none;
  backdrop-filter: none;
}

.setting-btn-icon {
  width: 25px;
  height: 25px;
  display: block;
}

/* 投币弹窗样式 */
.coin-buttons-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding: 16px 0;
  width: 100%;
  max-width: 100%;
}

.coin-button {
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  color: #fff;
  border: none;
  border-radius: 12px;
  padding: 16px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
  white-space: nowrap;
  overflow: hidden;
}

.coin-button:hover {
  background: linear-gradient(135deg, #357ABD, #2968A3);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(74, 144, 226, 0.4);
}

.coin-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.coin-button-text {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 4px;
  color: #fff;
  white-space: nowrap;
}

.coin-button-value {
  font-size: 16px;
  font-weight: bold;
  color: #FFD700;
  white-space: nowrap;
}

.no-coin-tip {
  text-align: center;
  color: #fff;
  font-size: 14px;
  padding: 20px;
}
</style>
