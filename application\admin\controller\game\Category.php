<?php

namespace app\admin\controller\game;

use app\common\controller\Backend;
use think\Db;

/**
 * 游戏分类
 * @Authod JW
 * @Time 2023/02/02
 * @package app\admin\controller\agent
 */
class Category extends Backend
{
    /**
     * Withdraw模型对象
     * @var \app\admin\model\Agent
     */
    protected $model = null;

    protected $noNeedRight = '';
    protected $multiFields = "";
    public function _initialize()
    {
        $this->noNeedRight = array('select_list');

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            if ($search_arr) {
                if (isset($search_arr['member_id'])) {
                    $search_arr['openid'] = Db::table('ims_cc_wifi_member')->where('id',$search_arr['member_id'])->value('openid');
                    unset($search_arr['member_id']);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('game_category')
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->count();

            $list = Db::name('game_category')
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            if ($list) {
                foreach ($list as &$v) {

                }
            }

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     * @Authod Jw
     * @Time 2023/2/16
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $time = time();

                $params['createtime'] = $time;
                $params['updatetime'] = $time;

                $result = Db::name('game_category')->insert($params);
                if ($result) {
                    $this->success('成功');
                }
                $this->error('失败');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * @Method 编辑
     * @Authod JW
     * @Time 2020/12/8
     * @param null $ids
     * @return string
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function edit($ids = null)
    {
        $row = Db::name('game_category')->where('id',$ids)->find();

        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $time = time();
                $params['updatetime'] = $time;

                $result = Db::name('game_category')->where('id',$ids)->update($params);
                if ($result) {
                    $this->success('成功');
                }
                $this->error('失败');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * @Method 删除
     * @Authod JW
     * @Time 2021/1/28
     * @param string $ids
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function del($ids = "")
    {
        if ($ids) {
            $id = Db::name('game_category')->where('id',$ids)->value('id');
            if (empty($id)) {
                $this->error('删除失败，分类不存在');
            }
            $result = Db::name('game_category')->where('id',$ids)->delete();
            if ($result) {
                $this->success('成功');
            }
            $this->error('删除失败');
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    /**
     * @Method 下拉列表搜索
     * @Authod Jw
     * @Time 2021/6/1
     * @return \think\response\Json
     * @throws \think\exception\DbException
     */
    public function select_list()
    {
        if($this->request->isAjax()){
            $page = $this->request->post("pageNumber/d", 1);
            $limit = $this->request->post("pageSize/d", 15);
            $keyword= $this->request->post("name", '');

            $keyValue = $this->request->post('keyValue','');
            if ($keyValue) {
                $where['id'] = $keyValue;
            }else{
                $where['name|id'] = ['like','%'.$keyword.'%'];
            }

            $list = Db::name('game_category')
                ->field('concat(name) as name,id')
                ->where($where)
                ->order('id', 'desc')
                ->page($page, $limit)
                ->paginate($limit,false,['page'=>$page]);

            $result = array("total" => $list->total(), "list" => $list->items());

            return json($result);
        }
    }

}
