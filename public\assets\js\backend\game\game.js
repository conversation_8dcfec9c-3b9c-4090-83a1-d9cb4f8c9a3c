define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'game/game/index',
                    add_url: 'game/game/add',
                    edit_url: 'game/game/edit',
                    del_url: 'game/game/del',
                    multi_url: 'game/game/multi',
                    table: 'game',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='c_id']", form).addClass("selectpage").data("source", "game/category/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc").data("multiple","true");

                $("input[name='agent_id']", form).addClass("selectpage").data("source", "agent/agent/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'id',
                sortName: 'id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true},
                        {field: 'img', title: __('封面'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'name', title: __('游戏名称')},
                        {field: 'sn', title: __('序列号')},
                        {field: 'c_id', title: __('分类'),visible:false},
                        {field: 'category_name', title: __('分类'), operate:false},
                        {field: 'level', title: __('最低vip等级')},
                        {field: 'm_power', title: __('机台倍率'), operate:false},
                        {field: 'players', title: __('游戏中人数'),operate:false},
                        {field: 'max_players', title: __('座位数量'),operate:false},
                        {field: 'union',title: __('联机信息'), operate:false,formatter: function (value, row, index) {
                                return '标识：'+row.union_tip+'<br>'+'名称：'+row.union_name;
                            }},
                        {field: 'sort', title: __('排序'),operate:false},
                        {field: 'status',visible:false,title: __('状态'),searchList: {"0":__('下线'),"1":__('上线'),"2":__('维护中')}},
                        {field: 'status_name', title: __('状态'), operate:false,formatter:Table.api.formatter.flag,custom:{'下线':'danger','上线':'info','维护中':'warning'}},
                        {field: 'is_landscape',title: __('展示'),searchList: {"0":__('竖屏'),"1":__('横屏')},formatter: function (value, row, index) {
                                if (row.is_landscape == 1) {
                                    return '横屏';
                                }else{
                                    return '竖屏';
                                }
                            }},

                        {field: 'agent_id',title: __('所属代理'),formatter: function (value, row, index) {
                                if (row.agent_name) {
                                    return row.agent_name+'<br>'+row.mobile+'<br>'+'代理ID：'+row.agent_id;
                                }else{
                                    return row.agent_id;
                                }
                            }},
                        {field: 'end_time', title: __('到期时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},

                        {field: 'createtime', title: __('创建时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('游戏记录列表'),
                                    text:'游戏记录列表',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","70%"]\'',
                                    icon: 'fa fa-gamepad',
                                    url: function (row){
                                        return "game/log/index?game_id=" + row.id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('座位列表'),
                                    text:'座位列表',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","70%"]\'',
                                    icon: 'fa fa-gamepad',
                                    url: function (row){
                                        return "game/seat/index?game_id=" + row.id;
                                    }
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});