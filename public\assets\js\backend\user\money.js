define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'user/money/index',
                    add_url: 'user/money/add',
                    edit_url: '',
                    del_url: '',
                    multi_url: 'user/money/multi',
                    table: 'user',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='user_id']", form).addClass("selectpage").data("source", "user/user/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'id',
                sortName: 'id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true, operate:false},
                        {field: 'type',title: __('类型'), searchList: {"1":__('游戏'),"2":__('充值'),"3":__('邀请'),"4":__('注册')},formatter: function (value, row, index) {
                                if (row.type == 1) {
                                    return '游戏';
                                } else if (row.type == 2) {
                                    return '充值';
                                } else if (row.type == 3) {
                                    return '邀请';
                                } else if (row.type == 4) {
                                    return '注册赠送';
                                }
                                else{
                                    return '-';
                                }
                            }},
                        {field: 'user_id',title: __('用户'),formatter: function (value, row, index) {
                                if (row.mobile && row.nickname) {
                                    return row.nickname+'<br>'+row.mobile+'<br>'+row.user_id;
                                }else{
                                    return row.user_id;
                                }
                            }},
                        {field: 'money', title: __('变更余额'), operate:false},
                        {field: 'before', title: __('变更前'), operate:false},
                        {field: 'after', title: __('变更后'), operate:false},
                        {field: 'memo', title: __('备注'), operate:false},
                        {field: 'game_log_id', title: __('游戏记录ID')},
                        {field: 'status',title: __('状态'), searchList: {"-1":__('支出'),"1":__('收入')},formatter: function (value, row, index) {
                                if (row.status == 1) {
                                    return '收入';
                                }else{
                                    return '支出';
                                }
                            }},
                        {field: 'createtime', title: __('创建时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},

                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});