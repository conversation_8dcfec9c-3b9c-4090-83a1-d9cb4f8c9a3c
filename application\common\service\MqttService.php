<?php

namespace app\common\service;

use app\common\library\phpMQTT;
use app\common\model\Game;
use think\Cache;
use think\Exception;
use think\Log;
use think\Env;
use think\Db;

/**
 * MQTT服务类
 * 负责处理MQTT协议通信、消息订阅发布、设备心跳维护
 */
class MqttService
{
    protected $mqtt;

    protected $broker;
    protected $port;
    protected $clientId;
    protected $username;
    protected $password;

    public function __construct()
    {
        // 从环境变量读取MQTT配置
        $this->broker   = Env::get('mqtt.server', '127.0.0.1');      // MQTT服务器地址
        $this->port     = (int)Env::get('mqtt.port', 1883);         // MQTT服务端口
        $this->clientId = Env::get('mqtt.client_id', 'fastadmin_mqtt_client_' . uniqid()); // 客户端唯一标识
        $this->username = Env::get('mqtt.username', '');            // 认证用户名
        $this->password = Env::get('mqtt.password', '');             // 认证密码

        // 初始化MQTT客户端实例
        $this->mqtt = new \app\common\library\phpMQTT($this->broker, $this->port, $this->clientId);
        $this->mqtt->debug = true; // 开启调试模式
        // 调整心跳间隔为30秒（需小于服务器设置的超时时间）
        $this->mqtt->keepalive = 30;
    }

    /**
     * 建立MQTT连接
     * @return bool 连接成功返回true，失败返回false
     */
    public function connect()
    {
        $maxRetry = (int)Env::get('mqtt.retry', 5);
        $retryInterval = (int)Env::get('mqtt.retry_interval', 2);
        $retry = 0;
        while ($retry < $maxRetry) {
            Log::info("MQTT尝试连接第" . ($retry+1) . "次...");
            // 每次重连都 new 一个新实例，防止状态混乱
            $this->mqtt = new phpMQTT($this->broker, $this->port, $this->clientId);
            $this->mqtt->debug = true;
            $result = $this->mqtt->connect_auto(true, null, $this->username, $this->password);
            Log::info('connect_auto 返回值: ' . var_export($result, true));
            if ($result) {
                Log::info('MQTT 连接成功');
                // 注意：主题订阅已由WorkerEvents.php统一管理，避免重复订阅
                // 如果需要单独的MQTT服务，请确保不与WorkerEvents.php冲突
                Log::info('MqttService连接成功，主题订阅由WorkerEvents.php统一管理');
                return true;
            }
            $retry++;
            Log::warning("MQTT连接失败，{$retryInterval}秒后重试...");
            sleep($retryInterval);
        }
        Log::error('MQTT连接失败，已达最大重试次数');
        return false;
    }

    public function subscribe(array $topics)
    {
        if (empty($topics)) {
            Log::error('订阅主题不能为空');
            return;
        }
        $this->mqtt->subscribe($topics);
    }

    /**
     * 处理心跳
     * <AUTHOR>
     * @date 2025-6-23
     * @return
     */
    public function heartbeat($gameSn,$client_type=null)
    {
        if ($client_type == 'app') {
            $cacheKey = "device_online:{$gameSn}";

            $cache = Cache::get($cacheKey);
            if (empty($cache)) {// 缓存不存在 -> 在线状态
                Db::name('game')
                    ->where('sn', $gameSn)
                    ->update([
                        'device_status' => 1,
                        'online_time' => date('Y-m-d H:i:s',time())
                    ]);
            }

            // 设置100秒过期的缓存标记
            Cache::set($cacheKey, 1, 100);
        }

        $this->mqtt->publish(
            "mqtt/{$gameSn}/pub/02ack",
            json_encode([
                'code'   => 200,
                'type'   => 'heartbeat_back',
                'status' => 'success',
                'message' => null,
            ]),
            0,
            false
        );
        return true;
    }

    /**
     * 有其它不是自己系统的sn，排除掉
     * <AUTHOR>
     * @date 2025-6-23
     * @return
     */
    public function excludeSn($gameSn)
    {
        $game_sn = Cache::get('game_sn');
        if (empty($game_sn)) {
            $game_model = new \app\admin\model\Game();
            $game_model->get_game_sn();

            $game_sn = Cache::get('game_sn');
        }
        if (!in_array($gameSn, $game_sn)) {
            return false;
        }
        return true;
    }

    /**
     * 处理主题1 订阅消息
     * @param string $topic 消息主题
     * @param string $message JSON格式消息内容
     */
    public function handleSubMessage($topic, $message)
    {
        try {
            $params = json_decode($message, true);

            // 从主题路径解析设备SN号
            $parts = explode('/', $topic);
            $params['gameSn'] = $parts[1];

            //有其它不是自己系统的sn，排除掉
            if (!self::excludeSn($params['gameSn'])) {
                return false;
            }

            // 处理心跳包响应
            if (isset($params['type']) && $params['type'] == 'heartbeat') {
                $client_type = $params['client_type'] ?? '';
                return self::heartbeat($params['gameSn'], $client_type);
            }

            Log::info("收到订阅消息 [{$topic}]: " . $message);

            $game = new Game();
            $result = $game->mqttRequest($params);
            Log::info("[{$topic}] 业务处理结果:" . json_encode($result));
        } catch (Exception $e){
            Log::error("游戏处理异常 [{$topic}]: " . $e->getMessage());
        }
        return true;
    }

    /**
     * 处理主题2 订阅消息
     * @param string $topic 消息主题
     * @param string $message JSON格式消息内容
     */
    public function handleAckMessage($topic, $message)
    {
        try {
            $params = json_decode($message,1);

            $parts = explode('/', $topic);
            $params['gameSn'] = $parts[1]; // 设备sn

            //有其它不是自己系统的sn，排除掉
            if (!self::excludeSn($params['gameSn'])) {
                return false;
            }
            if (!isset($params['type']) || $params['type'] == 'heartbeat_back') {
                return false;
            }
            Log::info("收到ACK消息 [{$topic}]: " . $message);

            if ($params['type'] == '01') {//开始游戏
                $params['type'] = 'startGameResult';
            } elseif ($params['type'] == '02') {//投币
                $params['type'] = 'putInCoins';
            }  elseif ($params['type'] == '06') {//结束游戏
                $params['type'] = 'endGameResult';
            }

            // 这里添加业务处理逻辑
            $game = new Game();
            $result = $game->mqttRequest($params);
            Log::info("[{$topic}] 业务处理结果:" . json_encode($result));

            if ($result['code'] == 1) {
                $coin = isset($result['data']['coin']) ? $result['data']['coin'] : -1;

                $topic = "mqtt/{$params['gameSn']}/pub/02ack";
                $message = json_encode([
                    'code'   => 200,
                    'type'   => 'update_game',
                    'status' => 'success',
                    'message' => null,
                    'coin' => $coin,//把金币返还给uniapp
                ]);
                $this->mqtt->publish($topic, $message, 0, false);
            }
        } catch (Exception $e){
            Log::error("游戏处理异常 [{$topic}]: ". $e->getMessage());
        }
        return true;
    }

    /**
     * 保持MQTT长连接
     * 通过循环调用proc()方法维持连接，并控制CPU占用率
     */
    public function keepAlive()
    {
        $lastPing = 0;
        $reconnectInterval = 5;

        while (true) {
            try {
                if (!$this->mqtt->isConnected()) {
                    Log::warning('MQTT连接断开，尝试自动重连...');
                    // 先关闭旧连接再创建新实例
                    $this->mqtt->close(true);
                    $connected = $this->connect();
                    if (!$connected) {
                        sleep($reconnectInterval);
                        continue;
                    }
                    $lastReconnect = time();
                } else {
                    // 主动心跳维护（关键修改）
                    if (time() - $lastPing >= $this->mqtt->keepalive) {
                        $this->mqtt->ping();
                        $lastPing = time();
//                        Log::info("主动发送心跳包 [间隔:{$this->mqtt->keepalive}秒]");
                    }
                    $this->mqtt->proc();
                    usleep(100000); // 调整为100ms轮询
                }
            } catch (\Exception $e) {
                Log::error("MQTT异常: " . $e->getMessage());
                sleep(1);
            }
        }
    }

}