<template>
  <view class="withdraw-container">
    <!-- 可提现金额 -->
    <view class="balance-section">
      <text class="balance-label">可提现金额</text>
      <text class="balance-amount">¥{{ balance }}</text>
    </view>

    <!-- 提现表单 -->
    <view class="withdraw-form">
      <!-- 提现方式选择 -->
      <view class="form-section">
        <text class="section-title">提现方式</text>
        <view class="payment-methods">
          <view 
            class="payment-method" 
            :class="{ active: selectedMethod === 1 }"
            @tap="selectMethod(1)"
          >
            <image class="method-icon" src="/static/wechat-pay.png" mode="aspectFit" />
            <text class="method-name">微信</text>
          </view>
          <view 
            class="payment-method" 
            :class="{ active: selectedMethod === 2 }"
            @tap="selectMethod(2)"
          >
            <image class="method-icon" src="/static/alipay.png" mode="aspectFit" />
            <text class="method-name">支付宝</text>
          </view>
        </view>
      </view>

      <!-- 提现金额输入 -->
      <view class="form-section">
        <text class="section-title">提现金额</text>
        <view class="amount-input-wrapper">
          <text class="currency-symbol">¥</text>
          <input 
            type="digit" 
            v-model="amount" 
            class="amount-input" 
            placeholder="请输入提现金额"
            @input="validateAmount"
          />
        </view>
        <text class="amount-tip">可提现金额：¥{{ balance }}</text>
      </view>

      <!-- 提现按钮 -->
      <button 
        class="submit-btn" 
        :disabled="!isValidAmount || !selectedMethod"
        @tap="submitWithdraw"
      >
        确认提现
      </button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      balance: '0.00',
      amount: '',
      selectedMethod: '',
      isValidAmount: false
    }
  },
  onLoad() {
    this.getBalance()
  },
  methods: {
    async getBalance() {
      try {
        const res = await request({
          url: '/api/agent/commission_log'
        })
        if (res.code === 1) {
          this.balance = res.data.balance || '0.00'
        }
      } catch (error) {
        console.error('获取余额失败：', error)
      }
    },
    selectMethod(method) {
      this.selectedMethod = method
    },
    validateAmount() {
      const amount = Number(this.amount)
      const balance = Number(this.balance)
      this.isValidAmount = amount > 0 && amount <= balance
    },
    async submitWithdraw() {
      if (!this.isValidAmount || !this.selectedMethod) return

      try {
        const res = await request({
          url: '/api/agent/applyWithdraw',
          method: 'POST',
          data: {
            money: this.amount,
            type: this.selectedMethod
          }
        })

        if (res.code === 1) {
          uni.showToast({
            title: '提现申请已提交',
            icon: 'success'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: res.msg || '提现失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('提现失败：', error)
        uni.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style scoped>
.withdraw-container {
  background: #f6f8ff;
  min-height: 100vh;
  padding: 32rpx;
}

.balance-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  text-align: center;
}

.balance-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.balance-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.withdraw-form {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
}

.form-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 24rpx;
  display: block;
}

.payment-methods {
  display: flex;
  gap: 24rpx;
}

.payment-method {
  flex: 1;
  height: 160rpx;
  border: 2rpx solid #e3e8f7;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.payment-method.active {
  border-color: #1a5cff;
  background: #f0f5ff;
}

.method-icon {
  width: 64rpx;
  height: 64rpx;
}

.method-name {
  font-size: 28rpx;
  color: #333;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #e3e8f7;
  padding: 16rpx 0;
}

.currency-symbol {
  font-size: 36rpx;
  color: #333;
  margin-right: 16rpx;
}

.amount-input {
  flex: 1;
  font-size: 36rpx;
  color: #333;
}

.amount-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
  display: block;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #1a5cff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  margin-top: 48rpx;
}

.submit-btn[disabled] {
  background: #ccc;
  color: #fff;
}
</style> 