define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'verification/merchant/index' + location.search,
                    add_url: 'verification/merchant/add',
                    edit_url: 'verification/merchant/edit',
                    del_url: 'verification/merchant/del',
                    table: 'merchant',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID')},
                        {field: 'name', title: __('名称')},
                        {field: 'username', title: __('登录账号'), operate: false},
                        {field: 'nickname', title: __('联系人')},
                        {field: 'mobile', title: __('联系方式')},
                        {field: 'logo', title: __('logo'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'money', title: __('余额'), operate: false},
                        {field: 'business_hours', title: __('营业时间'), operate: false},
                        {field: 'number', title: __('核销次数'), operate: false},
                        {field: 'score', title: __('总核销积分'), operate: false},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Hidden'),"1":__('Normal')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    dropdown: "更多",
                                    name: 'ps_edit',
                                    title: __('修改登录密码'),
                                    text:'修改登录密码',
                                    classname: 'btn btn-magic btn-dialog',
                                    icon: 'fa fa-key',
                                    url: function (row){
                                        return "user/user/reset_password/ids/" + row.user_id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('核销记录'),
                                    text:'核销记录',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","70%"]\'',
                                    icon: 'fa fa-list',
                                    url: function (row){
                                        return "verification/log/index?merchant_id=" + row.id;
                                    }
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        reset_password: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});