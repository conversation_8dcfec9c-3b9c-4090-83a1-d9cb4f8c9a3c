<template>
  <view class="verify-container">
    <view class="back-btn" @click="goBack"><text class="back-arrow">&lt;</text>返回</view>
    <view class="form-item">
      <text class="label">用户ID：</text>
      <text>{{ userId }}</text>
    </view>
    <view class="form-item">
      <text class="label">核销金额：</text>
      <input class="input" type="number" v-model="money" placeholder="请输入金额" @input="e => money = e.detail.value.replace(/[^\d]/g, '')" />
    </view>
    <view class="form-item">
      <text class="label">上传小票：</text>
      <view class="upload-box" @click="chooseImage">
        <image v-if="receipt" :src="receipt" class="receipt-img" />
        <view v-else class="upload-placeholder">+</view>
      </view>
    </view>
    <button class="submit-btn" @click="submitVerify">确认核销</button>
  </view>
</template>

<script>
import config from '@/config.js'
import request from '@/utils/request.js'
export default {
  data() {
    return {
      userId: '',
      ts: '',
      sign: '',
      money: '',
      receipt: ''
    }
  },
  onLoad(options) {
    // 支持扫码传递完整二维码内容
    if (options.qrText) {
      // 解析 qrText: userId=xxx&ts=xxx&sign=xxx
      const params = {};
      options.qrText.split('&').forEach(pair => {
        const [k, v] = pair.split('=');
        params[k] = v;
      });
      this.userId = params.userId || '';
      this.ts = params.ts || '';
      this.sign = params.sign || '';
    } else {
      this.userId = options.userId || '';
      this.ts = options.ts || '';
      this.sign = options.sign || '';
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    chooseImage() {
      uni.chooseImage({
        count: 1,
        success: (res) => {
          this.receipt = res.tempFilePaths[0]
        }
      })
    },
    async uploadImage(filePath) {
      // 封装上传图片为 Promise，兼容 H5
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: config.baseUrl + '/api/common/upload',
          filePath,
          name: 'file',
          success: (res) => {
            resolve(res)
          },
          fail: (err) => {
            reject(err)
          }
        })
      })
    },
    isValidLocalImage(path) {
      // 只允许本地临时路径（如 wxfile://、file://、blob:、/tmp/ 等），且非空
      return typeof path === 'string' && path.trim() !== '' &&
        !/^https?:\/\//.test(path) &&
        !/^data:image/.test(path)
    },
    async submitVerify() {
      console.log('提交核销，money:', this.money, 'receipt:', this.receipt)
      if (!this.money || !/^[1-9]\d*$/.test(this.money)) {
        uni.showToast({ title: '请输入正整数金额', icon: 'none' })
        return
      }
      // 上传小票不是必填项
      try {
        let receipt_image = ''
        const receipt = this.receipt
        if (
          typeof receipt === 'string' &&
          receipt.trim() !== '' &&
          !/^https?:\/\//.test(receipt) &&
          !/^data:image/.test(receipt)
        ) {
          // 只有本地图片才上传
          console.log('准备上传图片，receipt:', receipt)
          const uploadRes = await this.uploadImage(receipt)
          console.log('uploadRes:', uploadRes)
          const data = JSON.parse(uploadRes.data)
          if (data.code === 1 && data.data && data.data.url) {
            receipt_image = data.data.url
          } else {
            uni.showToast({ title: '小票上传失败', icon: 'none' })
            return
          }
        } else if (typeof receipt === 'string' && /^https?:\/\//.test(receipt)) {
          // 已经是线上图片
          receipt_image = receipt
        }
        const res = await request({
          url: '/api/merchant/verify_score',
          method: 'POST',
          data: {
            user_id: this.userId,
            ts: this.ts,
            sign: this.sign,
            money: parseInt(this.money),
            receipt_image
          }
        })
        if (res.code === 1) {
          uni.showToast({ title: '核销成功', icon: 'success' })
          setTimeout(() => {
            uni.switchTab({ url: '/pages/mine/mine' })
          }, 1500)
        } else {
          uni.showToast({ title: res.msg || '核销失败', icon: 'none' })
        }
      } catch (e) {
        console.log('核销异常', e)
        uni.showToast({ title: '网络错误', icon: 'none' })
      }
    }
  }
}
</script>

<style scoped>
.verify-container {
  padding: 40rpx;
  padding-top: 120rpx;
  background: #180F29;
  min-height: 100vh;
  position: relative;
}
.back-btn {
  position: absolute;
  top: 32rpx;
  left: 32rpx;
  z-index: 10;
  background: #2A1840;
  border: 1rpx solid #4C3A62;
  color: #fff;
  font-size: 28rpx;
  border-radius: 16rpx;
  width: 140rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  letter-spacing: 2rpx;
}
.back-arrow {
  font-size: 36rpx;
  margin-right: 8rpx;
  font-weight: normal;
}
.form-item {
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}
.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #fff;
}
.input {
  flex: 1;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 10rpx;
  font-size: 28rpx;
  margin: 0 10rpx;
  background: #2A1840;
  color: #fff;
}

.input::placeholder {
  color: #999;
}

.upload-box {
  width: 200rpx;
  height: 200rpx;
  border: 1rpx dashed #4C3A62;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
  background: #2A1840;
}
.upload-placeholder {
  font-size: 80rpx;
  color: #999;
}
.receipt-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.submit-btn {
  width: 100%;
  background: url('/static/mine/button.png') no-repeat center/100% 100%;
  color: #fff;
  font-size: 32rpx;
  border-radius: 8rpx;
  margin-top: 40rpx;
  height: 80rpx;
  border: none;
}
.form-item:first-of-type {
  margin-top: 32rpx;
}
</style> 