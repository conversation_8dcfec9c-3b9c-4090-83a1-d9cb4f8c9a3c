<template>
	<view class="paipai-game-control">
		<!-- 左上角金币显示框 -->
		<view class="coin-display-container">
			<view class="coin-display-box unified-coin-box">
				<image class="coin-icon" src="/static/coin.png" />
				<text class="coin-num">{{ displayCoin }}</text>
			</view>
		</view>

		<!-- 右上角头像、静音按钮和退出按钮 -->
		<view class="avatar-container right">
			<view class="avatar-group-box" v-if="gameInfo.is_full !== 2">
				<view v-for="(user, idx) in onlineAvatars" :key="user.id" class="avatar-group-item" :style="{ zIndex: 100 - idx, marginLeft: idx === 0 ? '0' : '-12px' }">
					<image class="avatar-img-group" :src="user.avatar || '/static/default_avatar.png'" />
				</view>
			</view>
			<!-- 静音按钮 -->
			<button class="mute-button" @click="toggleVideoAudio">
				<text class="mute-icon">{{ isVideoMuted ? '🔇' : '🔊' }}</text>
			</button>
			<!-- 设置按钮 -->
			<button class="setting-button" @click="$emit('show-func-dialog')">
				<img class="setting-btn-icon" src="/static/func/gear.png" alt="设置" />
			</button>
			<!-- 退出按钮 -->
			<button class="exit-button" @click="exitGame">
				<text class="exit-icon">×</text>
			</button>
		</view>

		<!-- 底部按钮区域 - 只显示一个玩家桌位 -->
		<view class="button-container" v-if="gameInfo.is_full !== 2">
			<view class="button-column" v-if="availableSeats.length > 0">
				<view v-if="availableSeats[0].status === 1" class="avatar-item" style="flex-direction: row; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.7); border-radius: 30px; padding: 5px 16px; margin-bottom: 16px; box-shadow: 0 2px 8px #0003; border: none;">
					<image class="avatar-img-group" :src="availableSeats[0].avatar || '/static/default_avatar.png'" style="margin-right: 8px;" />
					<text class="avatar-name" style="color: #fff; font-size: 18px; font-weight: bold; max-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ availableSeats[0].nickname }}</text>
				</view>
				<view v-else>
					<view class="coin-count-box unified-coin-box" :style="{ opacity: availableSeats[0].status === 0 ? 1 : 0 }">
						<image class="coin-icon" src="/static/coin.png" />
						<text class="coin-num">x{{ gameInfo.coin }}</text>
					</view>
					<view class="button-content">
						<button class="game-button" @click="startGame(availableSeats[0])" v-if="availableSeats[0].status === 0">
							<text>PLAY</text>
						</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 游戏操作区域 -->
		<view v-if="gameInfo.is_full === 2" class="game-controls">
			<!-- 左侧游戏按钮组 -->
			<view class="left-buttons">
				<!-- 比倍按钮 -->
				<button class="game-action-btn double-btn"
					@touchstart="isActiveDouble = true"
					@touchend="isActiveDouble = false"
					@mousedown="isActiveDouble = true"
					@mouseup="isActiveDouble = false"
					@mouseleave="isActiveDouble = false"
					@touchcancel="isActiveDouble = false"
					@click="handleDouble">
					<text class="btn-text" :class="{ active: isActiveDouble }">比倍</text>
				</button>
				
				<!-- 大按钮 -->
				<button class="game-action-btn big-btn"
					@touchstart="isActiveBig = true"
					@touchend="isActiveBig = false"
					@mousedown="isActiveBig = true"
					@mouseup="isActiveBig = false"
					@mouseleave="isActiveBig = false"
					@touchcancel="isActiveBig = false"
					@click="handleBig">
					<text class="btn-text" :class="{ active: isActiveBig }">大</text>
				</button>
				
				<!-- 和按钮 -->
				<button class="game-action-btn tie-btn"
					@touchstart="isActiveTie = true"
					@touchend="isActiveTie = false"
					@mousedown="isActiveTie = true"
					@mouseup="isActiveTie = false"
					@mouseleave="isActiveTie = false"
					@touchcancel="isActiveTie = false"
					@click="handleTie">
					<text class="btn-text" :class="{ active: isActiveTie }">和</text>
				</button>
				
				<!-- 小按钮 -->
				<button class="game-action-btn small-btn"
					@touchstart="isActiveSmall = true"
					@touchend="isActiveSmall = false"
					@mousedown="isActiveSmall = true"
					@mouseup="isActiveSmall = false"
					@mouseleave="isActiveSmall = false"
					@touchcancel="isActiveSmall = false"
					@click="handleSmall">
					<text class="btn-text" :class="{ active: isActiveSmall }">小</text>
				</button>
			</view>

			<!-- 右侧功能按钮 -->
			<view class="right-buttons">
				<!-- 自动按钮 -->
				<button class="game-action-btn auto-btn"
					@touchstart="isActiveAuto = true"
					@touchend="isActiveAuto = false"
					@mousedown="isActiveAuto = true"
					@mouseup="isActiveAuto = false"
					@mouseleave="isActiveAuto = false"
					@touchcancel="isActiveAuto = false"
					@click="handleAuto">
					<text class="btn-text" :class="{ active: isActiveAuto }">自动</text>
				</button>

				<!-- 切换按钮 -->
				<button class="game-action-btn switch-btn"
					@touchstart="isActiveSwitch = true"
					@touchend="isActiveSwitch = false"
					@mousedown="isActiveSwitch = true"
					@mouseup="isActiveSwitch = false"
					@mouseleave="isActiveSwitch = false"
					@touchcancel="isActiveSwitch = false"
					@click="handleSwitch">
					<text class="btn-text" :class="{ active: isActiveSwitch }">切换</text>
				</button>
				
				<!-- 拍按钮（主要操作按钮） -->
				<button class="game-action-btn pai-btn main-btn"
					@touchstart="isActivePai = true"
					@touchend="isActivePai = false"
					@mousedown="isActivePai = true"
					@mouseup="isActivePai = false"
					@mouseleave="isActivePai = false"
					@touchcancel="isActivePai = false"
					@click="handlePai">
					<text class="btn-text main-text" :class="{ active: isActivePai }">拍</text>
				</button>
				
				<!-- 投币按钮 -->
				<button class="game-action-btn coin-btn"
					@touchstart="isActiveCoin = true"
					@touchend="isActiveCoin = false"
					@mousedown="isActiveCoin = true"
					@mouseup="isActiveCoin = false"
					@mouseleave="isActiveCoin = false"
					@touchcancel="isActiveCoin = false"
					@click="showCoinDialog">
					<image src="/static/coin.png" class="coin-icon-btn" :class="{ active: isActiveCoin }" />
				</button>
			</view>
		</view>

		<!-- 投币弹窗 -->
		<view v-if="showCoinModal" class="coin-modal-overlay" @click="hideCoinDialog">
			<view class="coin-modal" @click.stop>
				<view class="coin-modal-header">
					<text class="coin-modal-title">选择投币数量</text>
					<button class="coin-modal-close" @click="hideCoinDialog">×</button>
				</view>
				<view class="coin-modal-content">
					<view v-if="coinButtons.length === 0" style="text-align: center; padding: 20rpx; color: #999;">
						暂无投币选项配置
					</view>
					<view v-else class="coin-buttons-grid">
						<button
							v-for="button in coinButtons"
							:key="button.id"
							class="coin-button"
							@click="handleCoinSelect(button)"
						>
							<text class="coin-button-text">{{ button.name }}</text>
							<text class="coin-button-value">{{ button.value }}币</text>
						</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PaiPaiLeControl',
	props: {
		gameInfo: {
			type: Object,
			default: () => ({})
		},
		userInfo: {
			type: Object,
			default: () => ({})
		},
		seats: {
			type: Array,
			default: () => []
		},
		isVideoMuted: {
			type: Boolean,
			default: false
		},
		isAuto: {
			type: Boolean,
			default: false
		},
	},
	data() {
		return {
			// 拍拍乐游戏内部状态
			onlineAvatars: [], // 在线用户头像列表
			isActiveDouble: false,
			isActiveBig: false,
			isActiveTie: false,
			isActiveSmall: false,
			isActiveSwitch: false,
			isActiveAuto: false,
			isActivePai: false,
			isActiveCoin: false,
			showCoinModal: false, // 投币弹窗显示状态
			displayCoin: 0, // 显示的金币数量
			coinButtons: [ // 投币按钮配置，先设置默认值
				{id: 51, name: "投1币", position: "center-left", value: 1},
				{id: 52, name: "投5币", position: "center", value: 5},
				{id: 53, name: "投30币", position: "center-right", value: 30}
			]
		}
	},
	watch: {
		userInfo: {
			handler(newVal) {
				if (newVal && newVal.avatar && newVal.id) {
					if (!this.onlineAvatars.some(u => u.id === newVal.id)) {
						this.onlineAvatars.push({
							id: newVal.id,
							avatar: newVal.avatar
						})
					}
				}
			},
			immediate: true,
			deep: true
		},
		// 监听用户金币变化，实时更新显示（仅在游戏开始前）
		'userInfo.money': {
			handler(newVal) {
				// 只有在游戏未开始时才从用户信息更新金币
				if (newVal !== undefined && newVal !== null &&
					(!this.gameInfo || this.gameInfo.is_full !== 2)) {
					this.displayCoin = newVal
					console.log('PaiPaiLe用户金币更新:', newVal)
				}
			},
			immediate: true
		},
		// 监听游戏状态变化
		'gameInfo.is_full': {
			handler(newVal, oldVal) {
				console.log('PaiPaiLe游戏状态变化:', oldVal, '->', newVal)
				// 游戏开始时（从其他状态变为2），设置初始金币
				if (newVal === 2 && oldVal !== 2) {
					// 优先使用用户金币
					const coinToUse = this.userInfo?.money || 0
					this.displayCoin = coinToUse
					console.log('PaiPaiLe游戏开始，设置初始金币:', coinToUse)
				}
				// 游戏结束时（从2变为其他状态），重新初始化金币显示
				else if (oldVal === 2 && newVal !== 2) {
					this.initializeCoinDisplay()
					console.log('PaiPaiLe游戏状态变为非进行中，重置金币显示')
				}
			}
		},
		gameInfo: {
			handler(newVal) {
				console.log('gameInfo watch触发:', newVal);
				// 检查奖励弹窗逻辑
				if (newVal && typeof newVal.coin !== 'undefined' && newVal.coin !== null && !isNaN(newVal.coin) && Number(newVal.coin) >= 0) {
					this.$emit('show-reward-dialog', Number(newVal.coin));
				}
				// 初始化投币按钮配置
				if (newVal) {
					this.initCoinButtons();
				}
			},
			immediate: true,
			deep: true
		}
	},
	computed: {
		gameStatusText() {
			if (!this.gameInfo) return '加载中...'
			switch (this.gameInfo.is_full) {
				case 0: return '当前空闲'
				case 1: return '座位已满'
				case 2: return this.userInfo.nickname || '正在游戏中'
				default: return '等待启动中...'
			}
		},

		// 过滤可用座位（只显示第一个座位）
		availableSeats() {
			const filtered = this.seats.filter(seat => seat.status !== undefined && seat.status !== null)
			return filtered.length > 0 ? [filtered[0]] : []
		}
	},
	mounted() {
		console.log('PaiPaiLeControl 组件已挂载')
		// 组件挂载时初始化金币显示
		this.initializeCoinDisplay()
		this.initCoinButtons();
	},
	methods: {
		// 初始化金币显示
		initializeCoinDisplay() {
			let coinValue = 0

			// 如果游戏已开始（is_full === 2），金币应该已经在游戏缓存中
			if (this.gameInfo && this.gameInfo.is_full === 2) {
				// 游戏中状态，保持当前 displayCoin 值
				console.log('PaiPaiLe游戏中状态，保持当前金币显示:', this.displayCoin)
				return
			}

			// 游戏前状态，从用户信息获取金币
			if (this.userInfo && this.userInfo.money !== undefined) {
				coinValue = this.userInfo.money
			} else if (this.gameInfo && this.gameInfo.userInfo && this.gameInfo.userInfo.money !== undefined) {
				coinValue = this.gameInfo.userInfo.money
			}

			this.displayCoin = coinValue
			console.log('PaiPaiLe初始化金币显示:', coinValue, '游戏状态:', this.gameInfo?.is_full)
		},

		// 初始化投币按钮配置
		initCoinButtons() {
			console.log('初始化投币按钮配置');
			console.log('gameInfo:', this.gameInfo);
			console.log('control_config:', this.gameInfo?.control_config);

			try {
				// 从gameInfo中获取control_config配置
				if (this.gameInfo && this.gameInfo.control_config) {
					let config;
					if (typeof this.gameInfo.control_config === 'string') {
						config = JSON.parse(this.gameInfo.control_config);
					} else {
						config = this.gameInfo.control_config;
					}

					console.log('解析后的config:', config);

					if (config && config.buttons && Array.isArray(config.buttons)) {
						this.coinButtons = config.buttons;
						console.log('使用配置的投币按钮:', this.coinButtons);
					} else {
						// 默认配置
						this.coinButtons = [
							{id: 51, name: "投1币", position: "center-left", value: 1},
							{id: 52, name: "投5币", position: "center", value: 5},
							{id: 53, name: "投30币", position: "center-right", value: 30}
						];
						console.log('使用默认投币按钮配置(config无效):', this.coinButtons);
					}
				} else {
					// 默认配置
					this.coinButtons = [
						{id: 51, name: "投1币", position: "center-left", value: 1},
						{id: 52, name: "投5币", position: "center", value: 5},
						{id: 53, name: "投30币", position: "center-right", value: 30}
					];
					console.log('使用默认投币按钮配置(无gameInfo):', this.coinButtons);
				}
				console.log('最终投币按钮配置:', this.coinButtons);
			} catch (error) {
				console.error('解析投币按钮配置失败:', error);
				// 使用默认配置
				this.coinButtons = [
					{id: 51, name: "投1币", position: "center-left", value: 1},
					{id: 52, name: "投5币", position: "center", value: 5},
					{id: 53, name: "投30币", position: "center-right", value: 30}
				];
				console.log('使用默认投币按钮配置(异常):', this.coinButtons);
			}
		},

		// 切换视频音频
		toggleVideoAudio() {
			this.$emit('toggleVideoAudio')
		},

		// 退出游戏 - 和OnekeyControl保持一致
		exitGame() {
			// 只判断是否在游戏中，emit 事件通知主页面
			if (this.gameInfo.is_full === 2) {
				this.$emit('show-exit-dialog');
			} else {
				this.$emit('exitGame');
			}
		},

		// 开始游戏
		startGame(seat) {
			if (this.gameInfo.is_full === 0) {
				this.$emit('show-wait-mask', true);
			}
			this.$emit('startGame', seat)
		},

		// 游戏操作方法
		handleDouble() {
			console.log('拍拍乐游戏操作: 比倍')
			this.sendGameCommand(10)
		},

		handleBig() {
			console.log('拍拍乐游戏操作: 大')
			this.sendGameCommand(11)
		},

		handleTie() {
			console.log('拍拍乐游戏操作: 和')
			this.sendGameCommand(12)
		},

		handleSmall() {
			console.log('拍拍乐游戏操作: 小')
			this.sendGameCommand(13)
		},

		handleAuto() {
			console.log('拍拍乐游戏操作: 自动')
			this.sendGameCommand(16)
		},

		handleSwitch() {
			console.log('拍拍乐游戏操作: 切换')
			this.sendGameCommand(14)
		},

		handlePai() {
			console.log('拍拍乐游戏操作: 拍')
			this.sendGameCommand(15)
		},

		// 发送游戏指令 - 独立的WebSocket通信
		sendGameCommand(command) {
			// 直接发送WebSocket指令，不依赖父组件
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const message = {
					type: 'command',
					number: this.gameInfo.number,
					status: command,
				}
				// GameWebSocket的send方法内部会自动调用JSON.stringify，所以直接传对象
				window.gameWebSocket.send(message)
				console.log('【LOG】拍拍乐游戏发送指令:', command, '消息:', message)
			} else {
				console.warn('【LOG】WebSocket未连接，无法发送指令', window.gameWebSocket)
			}
		},

		// 显示投币弹窗
		showCoinDialog() {
			console.log('显示投币弹窗');
			console.log('当前投币按钮配置:', this.coinButtons);
			// 调用父组件的全局投币弹窗
			if (this.$parent && this.$parent.showCoinDialog) {
				this.$parent.showCoinDialog(this.coinButtons);
			} else {
				// 兜底：使用本地弹窗
				this.showCoinModal = true;
			}
		},

		// 隐藏投币弹窗
		hideCoinDialog() {
			this.showCoinModal = false;
		},

		// 处理投币确认（由父组件调用）
		handleCoinConfirm(button) {
			console.log('处理投币确认:', button);
			this.sendPutInCoins(button.value);
		},

		// 处理投币选择
		handleCoinSelect(button) {
			console.log('选择投币:', button);
			this.hideCoinDialog();
			this.sendPutInCoins(button.value);
		},

		// 发送投币指令
		sendPutInCoins(coinAmount) {
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const requestId = 'putInCoins_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
				const message = {
					type: 'putInCoins',
					number: this.gameInfo.number,
					coin: coinAmount,
					requestId: requestId
				};

				console.log('【LOG】准备发送投币指令', message);
				console.log('【LOG】gameWebSocket类型:', typeof window.gameWebSocket);
				console.log('【LOG】gameWebSocket.send类型:', typeof window.gameWebSocket.send);

				// 直接发送对象，GameWebSocket内部会处理JSON序列化
				window.gameWebSocket.send(message);
				console.log('【LOG】投币指令已发送');

				// 显示投币提示
				uni.showToast({
					title: `投币${coinAmount}个`,
					icon: 'none',
					duration: 1500
				});
			} else {
				console.warn('WebSocket未连接，无法发送投币指令');
				console.log('gameWebSocket状态:', window.gameWebSocket?.readyState);
				uni.showToast({
					title: 'WebSocket未连接',
					icon: 'error'
				});
			}
		}
	}
}
</script>

<style scoped>
/* 拍拍乐游戏控制界面样式 */
.paipai-game-control {
	position: absolute;
	width: 100% !important;
	height: 100% !important;
	top: 0;
	left: 0;

	/* 禁用浏览器默认的选择和长按操作 */
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	/* 禁用长按上下文菜单 */
	-webkit-touch-callout: none;
	-webkit-tap-highlight-color: transparent;

	/* 禁用拖拽 */
	-webkit-user-drag: none;
	-khtml-user-drag: none;
	-moz-user-drag: none;
	-o-user-drag: none;

	/* 禁用双击缩放 */
	touch-action: manipulation;
}

/* 左上角金币显示容器 */
.coin-display-container {
	position: absolute;
	top: 5%;
	left: 5%;
	z-index: 3;
}

.coin-display-box {
	display: flex;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 30px;
	padding: 5px 16px;
	box-shadow: 0 2px 8px #0003;
	backdrop-filter: blur(4px);
}

.coin-icon {
	width: 24px;
	height: 24px;
	margin-right: 8px;
}

.coin-num {
	color: #fff;
	font-size: 18px;
	font-weight: bold;
}

/* 头像容器样式 */
.avatar-container {
	position: absolute;
	top: 5%;
	right: 5%;
	z-index: 3;
	display: flex;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 30px;
	padding: 5px 10px;
	backdrop-filter: blur(4px);
}

.avatar-item {
	margin: 0 5px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.avatar-name {
	color: white;
	font-size: 12px;
	margin-top: 2px;
	max-width: 60px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 退出按钮、静音按钮样式 */
.exit-button, .mute-button, .setting-button {
	width: 30px;
	height: 30px;
	border-radius: 20px;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 5px;
	font-size: 16px;
	font-weight: bold;
	cursor: pointer;
	backdrop-filter: blur(4px);
}

.exit-button {
	background-color: rgba(255, 0, 0, 0.8);
	color: white;
}

.mute-button {
	background-color: rgba(0, 0, 0, 0.8);
	color: white;
}

.setting-button {
	background: none;
	color: white;
}

.setting-btn-icon {
	width: 20px;
	height: 20px;
	display: block;
}

/* 头像叠加样式 */
.avatar-group-box {
	display: flex;
	align-items: center;
	position: relative;
}
.avatar-group-item {
	position: relative;
	margin-left: -12px;
}
.avatar-group-item:first-child {
	margin-left: 0;
}
.avatar-img-group {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	border: 2px solid #fff;
	background: #fff;
	box-shadow: 0 2px 8px #0003;
}

/* 底部按钮区域 */
.button-container {
	position: absolute;
	bottom: 10%;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	gap: 30px;
	z-index: 3;
	justify-content: center;
}

.button-column {
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 0;
}

.game-button {
	width: 200rpx;
	height: 80rpx;
	background: linear-gradient(90deg, #ffe066 0%, #ffb347 100%);
	color: #fff;
	font-size: 36rpx;
	font-weight: bold;
	border-radius: 40rpx;
	border: none;
	box-shadow: 0 4rpx 16rpx #ffb34755;
	letter-spacing: 2rpx;
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 8rpx;
	min-width: 120rpx;
}

.game-button:active {
	transform: scale(0.95);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

/* 游戏控制区域 */
.game-controls {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 40%;
	z-index: 2;
	pointer-events: auto;
	display: flex;
	justify-content: space-between;
	padding: 0 5vmax;
	align-items: flex-end;
	padding-bottom: 5vmax;
}

/* 左侧按钮组 */
.left-buttons {
	display: flex;
	flex-direction: column;
	gap: 1.5vmax;
	align-items: flex-start;
}

/* 右侧按钮组 */
.right-buttons {
	display: flex;
	flex-direction: column;
	gap: 1.5vmax;
	align-items: flex-end;
}

/* 游戏操作按钮通用样式 - 尺寸减半 */
.game-action-btn {
	width: 7vmax;
	height: 4vmax;
	border-radius: 1vmax;
	border: 2px solid rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.5vmax;
	font-weight: bold;
	cursor: pointer;
	transition: all 0.2s ease;
	backdrop-filter: blur(4px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.game-action-btn:active {
	transform: scale(0.95);
	filter: brightness(1.2);
}

/* 各按钮的特定颜色 */
.double-btn {
	background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
	color: white;
}

.big-btn {
	background: linear-gradient(135deg, #4ECDC4, #44A08D);
	color: white;
}

.tie-btn {
	background: linear-gradient(135deg, #45B7D1, #96C93D);
	color: white;
}

.small-btn {
	background: linear-gradient(135deg, #F093FB, #F5576C);
	color: white;
}

.auto-btn {
	background: linear-gradient(135deg, #FF9800, #F57C00);
	color: white;
}

.switch-btn {
	background: linear-gradient(135deg, #FFA726, #FF7043);
	color: white;
}

.pai-btn.main-btn {
	background: linear-gradient(135deg, #FF5722, #E91E63);
	color: white;
	width: 7.5vmax;
	height: 5vmax;
	font-size: 2vmax;
	border: 3px solid rgba(255, 255, 255, 0.9);
	box-shadow: 0 3px 10px rgba(255, 87, 34, 0.4);
}

.coin-btn {
	background: linear-gradient(135deg, #FFD700, #FFA500);
	color: white;
	width: 5vmax;
	height: 4vmax;
}

/* 按钮文字样式 */
.btn-text {
	font-weight: bold;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
	transition: all 0.2s ease;
	white-space: nowrap; /* 强制单行显示，不换行 */
	overflow: hidden; /* 隐藏溢出内容 */
	text-overflow: ellipsis; /* 超出部分显示省略号 */
}

.btn-text.active {
	transform: scale(1.1);
	text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

.main-text {
	font-size: 2vmax;
}

/* 投币按钮图标 */
.coin-icon-btn {
	width: 2.5vmax;
	height: 2.5vmax;
	transition: all 0.2s ease;
}

.coin-icon-btn.active {
	transform: scale(1.2);
	filter: brightness(1.3);
}

/* 金币框样式 */
.unified-coin-box {
	display: flex;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 30px;
	padding: 5px 16px;
	margin-bottom: 16px;
	box-shadow: 0 2px 8px #0003;
	border: none;
}
.coin-icon {
	width: 24px;
	height: 24px;
	margin-right: 8px;
}
.coin-num {
	color: #fff;
	font-size: 18px;
	font-weight: bold;
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
	.paipai-game-control {
		width: 100vw !important;
		height: 100vh !important;
		left: 0 !important;
		top: 0 !important;
	}
	.avatar-container.right {
		top: 5%;
		right: 0;
		left: auto;
		margin-right: 10%;
		margin-left: 0;
	}
	.button-container {
		bottom: 5%;
		left: 50%;
		transform: translateX(-50%);
		gap: 38px !important;
	}
	.game-button {
		width: 80rpx !important;
		height: 32rpx !important;
		font-size: 14rpx !important;
		border-radius: 16rpx !important;
		min-width: 40rpx !important;
		padding: 0 !important;
	}
}

/* 投币弹窗样式 */
.coin-modal-overlay {
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	width: 100% !important;
	height: 100% !important;
	background: rgba(0, 0, 0, 0.6) !important;
	display: flex !important;
	justify-content: center !important;
	align-items: center !important;
	z-index: 9999 !important;
	transform: none !important;
	margin: 0 !important;
	padding: 0 !important;
	contain: none !important;
	overflow: visible !important;
}

.coin-modal {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 90vh;
	box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
	position: static !important;
	margin: 0 !important;
	left: auto !important;
	right: auto !important;
	top: auto !important;
	bottom: auto !important;
	transform: none !important;
}

.coin-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.coin-modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.coin-modal-close {
	background: none;
	border: none;
	font-size: 40rpx;
	color: #999;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.coin-modal-content {
	padding: 20rpx 0;
}

.coin-buttons-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	justify-content: center;
}

.coin-button {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	border: none;
	border-radius: 15rpx;
	padding: 30rpx 40rpx;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
	box-shadow: 0 6rpx 15rpx rgba(76, 175, 80, 0.3);
	transition: all 0.3s ease;
	min-width: 150rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.coin-button:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 10rpx rgba(76, 175, 80, 0.3);
}

.coin-button-text {
	font-size: 28rpx;
	font-weight: bold;
}

.coin-button-value {
	font-size: 24rpx;
	opacity: 0.9;
}

/* 横屏下投币弹窗强制居中 */
@media screen and (orientation: landscape) {
	.coin-modal-overlay {
		position: fixed !important;
		top: 0 !important;
		left: 0 !important;
		right: 0 !important;
		bottom: 0 !important;
		width: 100vw !important;
		height: 100vh !important;
		display: flex !important;
		justify-content: center !important;
		align-items: center !important;
		transform: none !important;
		z-index: 9999 !important;
		background: rgba(0, 0, 0, 0.6) !important;
	}

	.coin-modal {
		position: static !important;
		transform: none !important;
		margin: 0 !important;
		left: auto !important;
		right: auto !important;
		top: auto !important;
		bottom: auto !important;
	}
}
</style>
