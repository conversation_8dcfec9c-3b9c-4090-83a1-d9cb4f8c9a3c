<template>
  <view class="edit-container">
    <view class="edit-title">{{ isEdit ? '编辑账户' : '添加账户' }}</view>
    <view class="form-row">
      <text class="form-label">账户类型</text>
      <picker :range="typeOptions" @change="onTypeChange">
        <view class="form-input form-picker">{{ typeOptions[typeIndex] }}</view>
      </picker>
    </view>
    <view class="form-row">
      <text class="form-label">{{ typeIndex === 0 ? '银行卡号' : '支付宝账号' }}</text>
      <input class="form-input" v-model="form.account" :placeholder="typeIndex === 0 ? '请输入银行卡号' : '请输入支付宝账号'" />
    </view>
    <view class="form-row">
      <text class="form-label">持卡人姓名</text>
      <input class="form-input" v-model="form.name" placeholder="请输入持卡人姓名" />
    </view>
    <view class="form-row" v-if="typeIndex === 0">
      <text class="form-label">银行名称</text>
      <input class="form-input" v-model="form.bank" placeholder="请输入银行名称" />
    </view>
    <view class="form-row" v-if="typeIndex === 0">
      <text class="form-label">开户行</text>
      <input class="form-input" v-model="form.bank_branch" placeholder="请输入开户行" />
    </view>
    <view class="form-row" v-if="typeIndex === 0">
      <text class="form-label">手机号</text>
      <input class="form-input" v-model="form.phone" placeholder="请输入手机号" type="number" />
    </view>
    <view class="form-row" v-if="typeIndex === 0">
      <text class="form-label">验证码</text>
      <view class="code-input-container">
        <input class="form-input code-input" v-model="form.code" placeholder="请输入验证码" type="number" />
        <button class="code-btn" @click="sendCode">获取验证码</button>
      </view>
    </view>
    <view class="save-btn-container">
      <button class="save-btn" @click="onSave">保存</button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      isEdit: false,
      typeOptions: ['银行卡', '支付宝'],
      typeIndex: 0,
      form: {
        id: '',
        type: 'bank',
        account: '',
        name: '',
        bank: '',
        bank_branch: '',
        phone: '',
        code: ''
      },
      originalPhone: ''
    }
  },
  onLoad(options) {
    if (options && options.data) {
      this.isEdit = true
      // 解析传递过来的账户数据
      try {
        const accountData = JSON.parse(decodeURIComponent(options.data))
        this.fillFormData(accountData)
      } catch (e) {
        uni.showToast({ title: '数据解析失败', icon: 'none' })
      }
    }
  },
  methods: {
    onTypeChange(e) {
      this.typeIndex = Number(e.detail.value)
      this.form.type = this.typeIndex === 0 ? 'bank' : 'alipay'
    },
    fillFormData(data) {
      // 根据账户类型设置选择器索引
      this.typeIndex = data.type === 'bank' ? 0 : 1
      
      // 填充表单数据
      this.form = {
        id: data.id,
        type: data.type,
        account: data.account,
        name: data.name,
        bank: data.bank || '',
        bank_branch: data.bank_branch || '',
        phone: data.phone || '',
        code: ''
      }
      this.originalPhone = this.form.phone
    },
    sendCode() {
      if (!this.form.phone) {
        uni.showToast({ title: '请先输入手机号', icon: 'none' })
        return
      }
      // TODO: 发送验证码接口
      uni.showToast({ title: '验证码已发送', icon: 'success' })
    },
    onSave() {
      if (!this.form.account) {
        uni.showToast({ title: this.typeIndex === 0 ? '请输入银行卡号' : '请输入支付宝账号', icon: 'none' })
        return
      }
      if (!this.form.name) {
        uni.showToast({ title: '请输入持卡人姓名', icon: 'none' })
        return
      }
      if (this.typeIndex === 0 && !this.form.bank) {
        uni.showToast({ title: '请输入银行名称', icon: 'none' })
        return
      }
      if (this.typeIndex === 0 && !this.form.bank_branch) {
        uni.showToast({ title: '请输入开户行', icon: 'none' })
        return
      }
      if (!this.form.phone) {
        uni.showToast({ title: '请输入手机号', icon: 'none' })
        return
      }
      if (this.isEdit && this.originalPhone && this.form.phone === this.originalPhone) {
        // 手机号未修改，不传递验证码
      } else if (!this.form.code) {
        uni.showToast({ title: '请输入验证码', icon: 'none' })
        return
      }
      // 构建请求参数
      const params = {
        type: this.form.type,
        account: this.form.account,
        name: this.form.name,
        bank: this.form.bank || '',
        bank_branch: this.form.bank_branch || '',
        phone: this.form.phone,
      }
      
      // 编辑时，如果手机号没有修改，则不传递验证码
      if (this.isEdit && this.originalPhone && this.form.phone === this.originalPhone) {
        // 手机号未修改，不传递验证码
      } else {
        params.code = this.form.code
      }
      
      // 如果是编辑模式，添加ID参数
      if (this.isEdit) {
        params.id = this.form.id
      }
      
      // 根据模式选择接口
      const url = this.isEdit ? '/api/merchant/withdraw_account_edit' : '/api/merchant/withdraw_account_add'
      
      request({
        url: url,
        method: 'POST',
        data: params
      }).then(res => {
        if (res.code === 1) {
          uni.showToast({ title: this.isEdit ? '编辑成功' : '添加成功', icon: 'success' })
          setTimeout(() => {
            uni.navigateBack()
          }, 800)
        } else {
          uni.showToast({ title: res.msg || (this.isEdit ? '编辑失败' : '添加失败'), icon: 'none' })
        }
      }).catch(err => {
        uni.showToast({ title: '网络错误', icon: 'none' })
      })
    }
  }
}
</script>

<style scoped>
.edit-container {
  min-height: 100vh;
  background: #180F29;
  padding: 0 24rpx 24rpx 24rpx;
}

.edit-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin: 40rpx 0 32rpx 0;
  text-align: center;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.form-label {
  width: 180rpx;
  color: #999;
  font-size: 28rpx;
}

.form-input {
  flex: 1;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 0 18rpx;
  height: 72rpx;
  font-size: 26rpx;
  background: #2A1840;
  color: #fff;
}

.form-input::placeholder {
  color: #999;
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-input-container {
  flex: 1;
  display: flex;
  gap: 16rpx;
}

.code-input {
  flex: 1;
}

.code-btn {
  background: #8F75EB;
  color: #fff;
  font-size: 24rpx;
  border-radius: 8rpx;
  height: 72rpx;
  padding: 0 24rpx;
  border: none;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn-container {
  display: flex;
  justify-content: center;
  margin-top: 48rpx;
}

.save-btn {
  width: 90%;
  background: url('/static/mine/button.png') no-repeat center/100% 100%;
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
  height: 100rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>