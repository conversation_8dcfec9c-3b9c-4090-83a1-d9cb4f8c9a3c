<form id="add-form" class="form-horizontal" role="form"  method="POST" action="" data-toggle="validator"  >
  <div class="panel-body">
    <div id="myTabContent" class="tab-content">
      <div class="tab-pane fade active in" id="t-1">
        <div class="form-group" >
        <label class="control-label col-xs-12 col-sm-2">{:__('所属代理')}:</label>
        <div class="col-xs-12 col-sm-8">
          <input data-source="agent/agent/select_list" class="form-control selectpage"  type="text" value="" readonly>
        </div>
      </div>
      <div class="form-group" >
      <label for="c-end_time" class="control-label col-xs-12 col-sm-2">{:__('到期时间')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input id="c-end_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD 23:59:59" data-use-current="true" name="row[end_time]" type="text" value="" placeholder="租用到期时间">
      </div>
    </div>
    <div class="form-group">
      <label for="c-img" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('封面')}:</label>
      <div class="col-xs-12 col-sm-8">
        <div class="input-group">
          <input id="c-img" data-rule="" class="form-control" size="50" name="row[img]" type="text" value="">
          <div class="input-group-addon no-border no-padding">
            <span><button type="button" id="faupload-img" class="btn btn-danger faupload" data-input-id="c-img" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-img"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
            <span><button type="button" id="fachoose-img" class="btn btn-primary fachoose" data-input-id="c-img" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
          </div>
          <span class="msg-box n-right" for="c-img"></span>
        </div>
        <ul class="row list-inline faupload-preview" id="p-img"></ul>
      </div>
    </div>
    <div class="form-group">
      <label for="c-sn" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('序列号')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input type="text" name="row[sn]" value=""  id="c-sn" class="form-control" required/>
      </div>
    </div>
    <div class="form-group">
      <label for="c-name" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('游戏名称')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input type="text" name="row[name]" value=""  id="c-name" class="form-control" required/>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('游戏分类')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input id="c-c_id" data-source="game/category/select_list" class="form-control selectpage" name="row[c_id]" type="text" value="" required>
      </div>
    </div>
    <div class="form-group">
      <label for="c-union_tip" class="control-label col-xs-12 col-sm-2">{:__('联机标识')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input type="number" name="row[union_tip]" value=""  id="c-union_tip" class="form-control"/>
      </div>
    </div>
    <div class="form-group">
      <label for="c-union_name" class="control-label col-xs-12 col-sm-2">{:__('联机房间名')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input type="text" name="row[union_name]" value=""  id="c-union_name" class="form-control"/>
      </div>
    </div>

    <div class="form-group">
      <label for="c-max_players" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('座位数量')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input type="number" name="row[max_players]" value=""  id="c-max_players" class="form-control" required/>
      </div>
    </div>
    <div class="form-group">
      <label for="c-level" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('最低vip等级')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input type="number" name="row[level]" value="0"  id="c-level" class="form-control" required/>
      </div>
    </div>
    <div class="form-group">
      <label for="c-m_power" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('机台倍率')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input type="number" name="row[m_power]" value="1"  id="c-m_power" class="form-control" required/>
      </div>
    </div>
    <div class="form-group">
      <label for="c-status" class="control-label col-xs-12 col-sm-2"><span style="color: red">*&nbsp;&nbsp;</span>{:__('状态')}:</label>
      <div class="col-xs-12 col-sm-8">
        {:build_radios('row[status]', ['0'=>__('下线'), '1'=>__('上线'), '2'=>__('维护中')])}
      </div>
    </div>


    <div class="form-group">
      <label for="c-sort" class="control-label col-xs-12 col-sm-2">{:__('排序')}:</label>
      <div class="col-xs-12 col-sm-8">
        <input type="number" name="row[sort]" value="0"  id="c-sort" class="form-control"/>
      </div>
    </div>
    <div class="form-group">
      <label for="c-description" class="control-label col-xs-12 col-sm-2">{:__('玩法说明')}:</label>
      <div class="col-xs-12 col-sm-8">
        <textarea id="c-description" name="row[description]" class="form-control editor" rows="5" placeholder="请输入游戏玩法说明"></textarea>
      </div>
    </div>
  </div>
  </div>
  </div>
  <div class="form-group hide layer-footer">
    <label class="control-label col-xs-12 col-sm-2"></label>
    <div class="col-xs-12 col-sm-8">
      <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
      <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
    </div>
  </div>
</form>