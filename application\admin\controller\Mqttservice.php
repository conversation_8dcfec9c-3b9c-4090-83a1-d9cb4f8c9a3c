<?php
namespace app\admin\controller;

use think\Controller;
use app\common\library\phpMQTT;
use think\Env;
use think\Log;

class Mqttservice extends Controller
{
    // 守护进程启动入口
    public function startDaemon()
    {
        // 无限重连机制
        while(true) {
            try {
                Log::info("MQTT连接....");

                $this->connectMqtt();
            } catch (\Exception $e) {
                // 记录错误日志
                Log::error("MQTT连接异常: ".$e->getMessage());
                sleep(5); // 等待5秒后重连
            }
        }
    }

    // MQTT连接核心方法
    private function connectMqtt()
    {
        $server = Env::get('mqtt.server', '');     // change if necessary
        $port = Env::get('mqtt.port', '');                     // change if necessary
        $username = Env::get('mqtt.username', '');                   // set your username
        $password =Env::get('mqtt.password', '');                   // set your password
        $client_id = Env::get('mqtt.client_id', ''); // make sure this is unique for connecting to sever - you could use uniqid()

        $mqtt = new phpMQTT($server, $port, $client_id);
        $mqtt->debug = true; // 关键！开启调试输出
        if (!$mqtt->connect(true, null, $username, $password)) {
            throw new \Exception("MQTT连接失败");
            Log::info("MQTT连接失败");

        }

        // 在订阅前添加调试日志
        Log::info("尝试订阅主题：".json_encode([
                'mqtt/+/sub/02' => ['qos' => 1],
                'mqtt/+/pub/02ack' => ['qos' => 1]
            ]));

        // 订阅所有客户端指令主题
        $success = $mqtt->subscribe([
            'mqtt/+/sub/02' => ['qos' => 1, 'function' => 'handleCommand'], // 操作指令
            'mqtt/+/pub/02ack' => ['qos' => 1, 'function' => 'handleAck'] // ACK响应
        ]);

        if (!$success) {
            // 添加详细错误信息
            Log::error("订阅失败详情：");
            throw new \Exception("订阅失败：");
        }
        Log::info("订阅成功");

        // 保持消息监听
        while ($mqtt->proc()) {
            // 此处可添加心跳检测等逻辑
            if(time() % 10 == 0) {
//                Log::info("MQTT监听服务运行中...");
            }
        }

        $mqtt->close();
    }

    // 消息处理函数
    public function handleCommand($topic, $msg)
    {
        // 提取客户端ID（使用正则匹配）
        if(preg_match('/mqtt\/(.*?)\/sub\/02/', $topic, $matches)) {
            $clientId = $matches[1] ?? 'unknown';

            // 解析消息
            $data = $this->parseMessage($msg);
            Log::info($data,'数据');

            // 记录到数据库
//            db('game_actions')->insert([
//                'client_id' => $clientId,
//                'action_data' => json_encode($data),
//                'create_time' => date('Y-m-d H:i:s')
//            ]);

            // 触发业务逻辑（如通知其他玩家等）
            $this->notifyGameServer($clientId, $data);

            // 这里可以触发业务逻辑：
            // 1. 通知其他玩家
            // 2. 更新游戏状态
            // 3. 调用其他服务接口
        }
    }

    // 处理ACK响应
    public function handleAck($topic, $msg)
    {

        // 提取客户端ID
        preg_match('/mqtt\/(.*?)\/pub\/02ack/', $topic, $matches);
        $clientId = $matches[1] ?? 'unknown';

        // 解析ACK消息
        $ackData = $this->parseMessage($msg);
        Log::info($ackData,'数据');

        // 记录ACK日志
//        db('ack_logs')->insert([
//            'client_id' => $clientId,
//            'type' => 'ack',
//            'data' => json_encode($ackData),
//            'create_time' => date('Y-m-d H:i:s')
//        ]);
//
//        // 更新游戏状态（示例）
//        db('game_status')
//            ->where('client_id', $clientId)
//            ->update([
//                'last_ack' => time(),
//                'status' => $ackData['jpall'] ?? 0
//            ]);
    }

    // 通用消息解析方法
    private function parseMessage($msg)
    {
        $params = [];
        parse_str(str_replace(';', '&', $msg), $params);
        return $params;
    }

}
