<template>
  <view v-if="show" class="simple-dialog-mask" @click="onClose">
    <view class="simple-dialog" @click.stop>
      <view class="simple-dialog-msg">{{ message }}</view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    show: <PERSON><PERSON><PERSON>,
    message: String
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.startAutoClose();
      } else {
        this.clearTimer();
      }
    }
  },
  methods: {
    onClose() {
      this.clearTimer();
      this.$emit('close');
    },
    startAutoClose() {
      this.clearTimer();
      this._timer = setTimeout(() => {
        this.onClose();
      }, 2000);
    },
    clearTimer() {
      if (this._timer) {
        clearTimeout(this._timer);
        this._timer = null;
      }
    }
  },
  beforeDestroy() {
    this.clearTimer();
  }
}
</script>

<style scoped>
.simple-dialog-mask {
  position: fixed; left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3); z-index: 9999;
  display: flex; align-items: center; justify-content: center;
}
.simple-dialog {
  background: #fff; border-radius: 20rpx; padding: 40rpx 32rpx 32rpx 32rpx;
  min-width: 300rpx; max-width: 80vw; text-align: center;
  box-shadow: 0 8rpx 32rpx #0002;
}
.simple-dialog-msg {
  font-size: 30rpx; color: #333; margin-bottom: 32rpx;
}
.simple-dialog-btn {
  width: 160rpx;
  background: #1a5cff;
  color: #fff;
  font-size: 24rpx;
  border-radius: 32rpx;
  border: none;
  padding: 8rpx 0;
  margin: 0 auto;
  display: block;
}
</style> 