{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/recharge/recharge",
			"style": {
				"navigationBarTitleText": "充值",
				"navigationStyle": "custom"
			}
		},
		// {
		// 	"path": "pages/activity/activity",
		// 	"style": {
		// 		"navigationBarTitleText": "活动"
		// 	}
		// },
		// {
		// 	"path": "pages/task/task",
		// 	"style": {
		// 		"navigationBarTitleText": "任务",
		// 		"navigationStyle": "custom"
		// 	}
		// },
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "我的",
				"disableScroll": true,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "登录/注册",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/loading/loading",
			"style": {
				"navigationBarTitleText": "加载游戏中",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/setting/setting",
			"style": {
				"navigationBarTitleText": "设置",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/game/game",
			"style" : 
			{
				"navigationBarTitleText" : "游戏中",
				"pageOrientation": "landscape" ,// 强制横屏
				"app-plus": {
				  "orientation": "landscape"
				},
				"navigationStyle": "custom",
				"h5": {
				  "meta": {
					"viewport": "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, orientation=landscape"
				  }
				}
			}
		},
		{
			"path" : "pages/game/coin",
			"style" : 
			{
				"navigationBarTitleText" : "游戏中",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/my/records",
			"style" : 
			{
				"navigationBarTitleText" : "我的账单"
			}
		}
	],
	//标签栏
	"tabBar": {
		"list": [{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "/static/tab/index-active.png",
				"selectedIconPath": "/static/tab/index.png"
			},
			{
				"pagePath": "pages/recharge/recharge",
				"text": "充值",
				"iconPath": "/static/tab/recharge-active.png",
				"selectedIconPath": "/static/tab/recharge.png"
			},
			// {
			// 	"pagePath": "pages/activity/activity",
			// 	"text": "活动",
			// 	"iconPath": "/static/tab/activity-active.png",
			// 	"selectedIconPath": "/static/tab/activity.png"
			// },
			// {
			// 	"pagePath": "pages/task/task",
			// 	"text": "任务",
			// 	"iconPath": "/static/tab/task-active.png",
			// 	"selectedIconPath": "/static/tab/task.png"
			// },
			{
				"pagePath": "pages/my/my",
				"text": "我的",
				"iconPath": "/static/tab/my-active.png",
				"selectedIconPath": "/static/tab/my.png"
			}
		]
	},
	"globalStyle": { //用于设置应用的状态栏、导航条、标题、窗口背景色等。
		//导航栏背景颜色（同状态栏背景色）
		"navigationBarBackgroundColor": "#fff",
		//导航栏标题颜色及状态栏前景颜色，仅支持 black/white
		"navigationBarTextStyle": "black",
		//	下拉显示出来的窗口的背景色
		"backgroundColor": "#8f8f94"
	},
	"easycom": {
		"autoscan": true,
		"custom": {
			"^uni-(.*)": "@/uni_modules/uni-ui/components/uni-$1/uni-$1.vue",
			"^uni-icons-(.*)": "@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue"
		}
	}

}