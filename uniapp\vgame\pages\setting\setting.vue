<template>
  <view class="settings-container">
    <!-- 隐私协议 & 用户协议 -->
    <uni-section title=" " type="line">
      <uni-list>
        <uni-list-item title="隐私协议" showArrow />
        <uni-list-item title="用户协议" showArrow />
      </uni-list>
    </uni-section>


    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <view class="logout-btn" @tap="handleLogout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>

    <!-- 备案信息 -->
    <view class="footer">
      粤ICP备2021004450号-4A
    </view>
  </view>
</template>

<script>
import config from "@/config.js";
import auth from '@/utils/auth'

export default {
  methods: {
    handleLogout() {
		if (!auth.checkLogin()) {
			uni.showToast({ title: '您已退出，请勿重复操作', icon: 'none' })
			return
		}
			  
		uni.showModal({
			title: '退出登录',
			content: '确定要退出当前账号吗？',
			success: (res) => {
			  if (res.confirm) {
				this.clearUserData()
				uni.reLaunch({ url: '/pages/login/login' }) // 跳转到登录页
			  }
			}
		})
    },
	// 新增清除用户数据方法
	clearUserData() {
	  // 清除 Token 相关
	  uni.removeStorageSync(config.tokenKey)
	  uni.removeStorageSync(config.tokenExpireKey)
	  // 清除用户信息
	  uni.removeStorageSync(config.userInfo)

	  // 如果有其他相关缓存也需要清除，例如：
	  // uni.removeStorageSync('cartData') // 清除购物车数据
	  // uni.removeStorageSync('settings') // 清除用户设置
	}
  }
}
</script>

<style scoped>
.settings-container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 危险操作文字样式 */
.danger-text ::v-deep .uni-list-item__container {
  color: #dd524d;
}

/* 退出登录区域 */
.logout-section {
  margin-top: 80rpx;
  padding: 0 32rpx 40rpx 32rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ff4757;
}

.logout-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}

/* 备案信息 */
.footer {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 30px 0;
  position: fixed;
  bottom: 0;
  width: 100%;
}
</style>