<template>
  <view
    v-if="show"
    class="universal-modal-mask"
    :class="{ landscape: landscape }"
    @click="handleMaskClick"
  >
    <view
      class="universal-modal-content"
      :class="{
        landscape: landscape,
        [size]: true,
        [type]: type !== 'default'
      }"
      @click.stop
    >
      <!-- 头部区域：标题和关闭按钮 -->
      <view class="universal-modal-header" v-if="title || showClose">
        <!-- 标题 -->
        <view class="universal-modal-title" v-if="title">{{ title }}</view>

        <!-- 关闭按钮 -->
        <view class="universal-modal-close" v-if="showClose" @click="handleClose">
          <image v-if="closeIcon" :src="closeIcon" class="close-icon" />
          <text v-else class="close-text">×</text>
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="universal-modal-body" :class="{ 'no-header': !title && !showClose }">
        <slot />
      </view>

      <!-- 底部按钮区域 -->
      <view class="universal-modal-footer" v-if="$slots.footer">
        <slot name="footer" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UniversalModal',
  emits: ['close'],
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    showClose: {
      type: Boolean,
      default: true
    },
    closeIcon: {
      type: String,
      default: ''
    },
    maskClosable: {
      type: Boolean,
      default: true
    },
    landscape: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    type: {
      type: String,
      default: 'default', // default, exit, exchange, error, reward, settle, wait, coin, setting, rule, service, function
      validator: value => ['default', 'exit', 'exchange', 'error', 'reward', 'settle', 'wait', 'coin', 'setting', 'rule', 'service', 'function'].includes(value)
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleMaskClick() {
      if (this.maskClosable) {
        this.handleClose()
      }
    }
  }
}
</script>

<style scoped>
/* =====================
   UniversalModal 统一弹窗系统
   使用固定px单位，确保在任何屏幕方向下都保持一致
   整合了GameModal和CommonModal的所有功能
   ===================== */

/* 弹窗遮罩层 */
.universal-modal-mask {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 弹窗主体容器 - 基础样式 */
.universal-modal-content {
  background: #301E46;
  border-radius: 10px;
  padding: 10px 12px 16px;
  position: relative;
  text-align: center;
  overflow-y: auto;
  font-size: 14px;
  color: #fff;
  line-height: 1.4;
  display: flex;
  flex-direction: column;
}

/* 尺寸变体 - 使用固定px单位 */
.universal-modal-content.small {
  width: 240px;
  max-width: 240px;
  max-height: 320px;
}

.universal-modal-content.medium {
  width: 280px;
  max-width: 280px;
  max-height: 400px;
}

.universal-modal-content.large {
  width: 320px;
  max-width: 320px;
  max-height: 480px;
}

/* 头部区域 */
.universal-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  width: 100%;
  flex-shrink: 0;
}

.universal-modal-title {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  flex: 1;
  text-align: center;
}

.universal-modal-close {
  width: 20px;
  height: 20px;
  font-size: 18px;
  color: #fff;
  background: #555;
  border: 1px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  line-height: 1;
  flex-shrink: 0;
}

.universal-modal-close .close-icon {
  width: 12px;
  height: 12px;
}

.universal-modal-close .close-text {
  font-size: 14px;
}

/* 内容区域 */
.universal-modal-body {
  flex: 2;
  overflow-y: visible;
}

.universal-modal-body.no-header {
  margin-top: 0;
}

/* 底部区域 */
.universal-modal-footer {
  /* margin-top: 16px; */
  flex-shrink: 0;
}



</style>
