<?php

namespace app\admin\controller\game;

use app\common\controller\Backend;
use think\Db;

/**
 * 游戏座位列表
 * @Authod JW
 * @Time 2023/02/02
 * @package app\admin\controller\agent
 */
class Seat extends Backend
{
    /**
     * Withdraw模型对象
     * @var \app\admin\model\Agent
     */
    protected $model = null;

    protected $noNeedRight = '';
    protected $multiFields = "deleted,rent_limit";
    public function _initialize()
    {
        $this->noNeedRight = array('');

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            if ($search_arr) {
                if (isset($search_arr['member_id'])) {
                    $search_arr['openid'] = Db::table('ims_cc_wifi_member')->where('id',$search_arr['member_id'])->value('openid');
                    unset($search_arr['member_id']);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('game_seat')
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->count();

            $list = Db::name('game_seat')
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            if ($list) {
                foreach ($list as &$v) {
                    $v['game_name'] = Db::name('game')->where('id',$v['game_id'])->value('name');
                }
            }

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }



}
