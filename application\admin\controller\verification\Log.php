<?php

namespace app\admin\controller\verification;

use app\common\controller\Backend;
use app\common\model\MoneyLog;
use fast\Random;
use think\Db;
use think\Exception;
use think\Validate;

/**
 * 核销记录
 * @icon fa fa-user
 */
class Log extends Backend
{
    protected $searchFields = 'username,nickname,mobile';

    public function _initialize()
    {
        $this->noNeedRight = array('select_list');

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            if ($search_arr) {
                foreach ($search_arr as $k => $v) {
                    //执行时间
                    if ($k == 'createtime') {//创建时间
                        $create_time = explode(' - ',$v);
                        $where_arr['l.createtime'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    }  else {
                        $str = 'l.'.$k;
                        $where_arr[$str] = $v;
                    }

                    unset($search_arr[$k]);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('hx_log')
                ->alias('l')
                ->where($where)
                ->where($where_arr)
                ->count();

            $list = Db::name('hx_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->join(['fa_hx_merchant m'],'l.merchant_id=m.id','left')
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->field('l.*,u.nickname,u.mobile,u.avatar,m.name merchant_name,m.logo merchant_logo,m.mobile merchant_mobile')
                ->limit($offset, $limit)
                ->select();

            // 一次SQL查询统计status=1的总核销次数、积分、金额
            $stat = Db::name('hx_log')->alias('l')
                ->where($where)
                ->where($where_arr)
                ->where('status', 1)
                ->field([ 'COUNT(*) as number', 'SUM(score) as score', 'SUM(money) as money' ])
                ->find();
            $number = $stat['number'] ?? 0;
            $score = $stat['score'] ?? 0;
            $money = $stat['money'] ?? 0;

            $result = array("total" => $total, "rows" => $list, "extend" => ['number'=>$number,'score' => $score,'money'=>$money]);

            return json($result);
        }
        return $this->view->fetch();
    }


}