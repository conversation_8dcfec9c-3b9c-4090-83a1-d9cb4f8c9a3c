<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label for="c-id" class="control-label col-xs-12 col-sm-2">{:__('ID')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[id]" value="{$row.id|htmlentities}"  id="c-id" class="form-control" readonly="readonly"/>
        </div>
    </div>
    <div class="form-group">
        <label for="c-id" class="control-label col-xs-12 col-sm-2">{:__('游戏信息')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" value="{$row.game_id} - {$row.name|htmlentities}"  id="c-id" class="form-control" readonly="readonly"/>
        </div>
    </div>
    <div class="form-group">
        <label for="c-id" class="control-label col-xs-12 col-sm-2">{:__('用户信息')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" value="{$row.user_id} - {$row.nickname|htmlentities} - {$row.mobile|htmlentities}"  id="c-id" class="form-control" readonly="readonly"/>
        </div>
    </div>

    <div class="form-group">
        <label for="c-coin" class="control-label col-xs-12 col-sm-2">{:__('返还金币')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" name="row[coin]"  id="c-coin" class="form-control" value="" required placeholder="返还给用户的金币数量"/>
        </div>
    </div>

    <div class="form-group">
        <label for="c-memo" class="control-label col-xs-12 col-sm-2">{:__('备注')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[memo]"  id="c-memo" class="form-control" value="" required placeholder="请填写备注"/>
        </div>
    </div>


    <div class="form-group hide layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed ">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
