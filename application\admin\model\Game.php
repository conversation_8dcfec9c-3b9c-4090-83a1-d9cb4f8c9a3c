<?php

namespace app\admin\model;

use think\Cache;
use think\Db;
use think\Model;

class Game extends Model
{
    // 表名
    protected $name = 'user';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [];

    public $model_field = array(
        //状态
        'status'=>array(
            '0'=>array(
                'id'=>'0',
                'text'=>'下线',
                'class_name'=>"label label_green",
            ),
            '1'=>array(
                'id'=>'1', //值
                'text'=>'上线', //名称
                'class_name'=>"label label_blue", //样式的class
            ),
            '2'=>array(
                'id'=>'2', //值
                'text'=>'维护中', //名称
                'class_name'=>"label label_red", //样式的class
            )
        ),
    );

    /**
     * 获取到游戏的SN号，每30分钟更新一次(定时任务)
     * <AUTHOR>
     * @date 2025-6-19
     * @return
     */
    public function get_game_sn(){
        $sn_arr = Db::name('game')->where('is_delete',0)->column('sn');
        Cache::set('game_sn',$sn_arr);
        return 'ok';
    }

    public function getCateGoryList()
    {
        $arr = [];
        $result = Db::name('game_category')->field('id,name')->order('sort','desc')->cache(60)->select();
        foreach ($result as $key => $value) {
            $arr[$value['id']] = $value['name'];
        }
        return $arr;
    }
}
