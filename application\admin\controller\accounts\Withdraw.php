<?php

namespace app\admin\controller\accounts;

use app\common\controller\Backend;
use app\common\library\Auth;
use think\Db;
use think\Cache;
use think\Exception;

/**
 * 提现列表
 * @Authod JW
 * @Time 2023/02/02
 * @package app\admin\controller\accounts
 */
class Withdraw extends Backend
{

    protected $noNeedRight = '';
    protected $multiFields = "deleted,rent_limit";
    public function _initialize()
    {
        $this->noNeedRight = array('index','selectpage','rejected','successed');

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            if ($search_arr) {

            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('withdraw')
                ->where($where)
                ->where($where_arr)
                ->count();

            $list = Db::name('withdraw')
                ->alias('w')
                ->join(['fa_admin a'],'w.agent_id=a.id','left')
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            if ($list) {
                foreach ($list as &$v) {

                    if ($v['status'] == 0) {
                        $v['status_name'] = '待打款';
                    }elseif ($v['status'] == 1) {
                        $v['status_name'] = '已打款';
                    }elseif ($v['status'] == 2) {
                        $v['status_name'] = '已驳回';
                    }elseif ($v['status'] == 3) {
                        $v['status_name'] = '失败';
                    }
                }
            }
            //待打款金额
            $pending_pay = Db::name('withdraw')->where($where)->where($where_arr)->where('status',0)->sum('money');
            $pending_pay = number_format($pending_pay,2);

            //已打款金额
            $paid_pay = Db::name('withdraw')->where($where)->where($where_arr)->where('status',1)->sum('real_money');
            $paid_pay = number_format($paid_pay,2);

            $result = array("total" => $total, "rows" => $list, "extend" => ['pending_pay'=>$pending_pay,'paid_pay' => $paid_pay]);
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 提现申请
     * @Authod Jw
     * @Time 2025/6/3
     * @param null $ids
     * @return string
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function add()
    {
        $admin_id = get_admin_id();
        $row = Db::name('admin')->where('id',$admin_id)->find();

        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $agent_model = new \app\common\model\Agent();
                $result = $agent_model->applyWithdraw($admin_id,$params['money'],$params['type']);
                if ($result['code'] == 1) {
                    $this->success('成功');
                }
                $this->error($result['msg']);
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * @Method 提现审核
     * <AUTHOR>
     * @Time 2021/4/16
     * @param $ids
     * @return string
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function detail($ids)
    {
        $row = Db::table('ims_cc_wifi_deposit')->where('id',$ids)->find();

        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $row['image_ali'] = Db::table('ims_cc_wifi_member')->where('id',$row['member_id'])->value('image_ali');
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * @Method 审核拒绝
     * <AUTHOR>
     * @Time 2021/5/11
     * @param null $ids
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function rejected($ids = null)
    {
        if ($this->request->isAjax()) {

            $info = Db::table('ims_cc_wifi_deposit')->where(["id" => $ids])->field('id,status,cash,member_openid')->find();
            if ($info['status'] == 2){
                $this->error('驳回失败，提现状态已经是驳回状态');
            }
            $before = Db::table('ims_cc_wifi_member')->where(["openid" => $info['member_openid']])->value('cash_remain');

            Db::startTrans();
            try {
                //余额退回
                Db::table('ims_cc_wifi_member')->where('openid',$info['member_openid'])->setInc('cash_remain',$info['cash']);
                Db::table('ims_cc_wifi_member')->where('openid',$info['member_openid'])->setDec('cash_done',$info['cash']);

                $cash_remain = floorFloat($info['cash']+$before);
                //余额日志
                $insert = [
                    'uniacid'   => 2,
                    'openid'    => $info['member_openid'],
                    'type'      => 3,
                    'money'     => $info['cash'],
                    'before'    => $before,
                    'after'     => $cash_remain,
                    'relation_id' => $info['id'],
                ];
                Db::table('ims_cc_wifi_moneylog')->insert($insert);

                //修改提现状态
                Db::table('ims_cc_wifi_deposit')->where(["id" => $ids])->update(["status" => 2, "update_time" => time()]);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error('驳回失败');
            }
        }
    }

    /**
     * @Method 审核通过
     * <AUTHOR>
     * @Time 2021/5/11
     * @param null $ids
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function successed($ids = null)
    {
        if ($this->request->isAjax()) {
            $param = $this->request->param();

            $info = Db::table('ims_cc_wifi_deposit')->where(["id" => $ids])->field('id,status,cash,member_openid')->find();
            if ($info['status'] == 1){
                $this->error('通过失败，提现状态已经是驳回状态');
            }

            $info = Db::table('ims_cc_wifi_deposit')->where(["id" => $ids])->field('type,member_openid,cash')->find();
            $depositMember = Db::table('ims_cc_wifi_member')->where('openid',$info['member_openid'])->field('active,real_name')->find();
            if (empty($depositMember)) {
                $this->error('没有查找到该代理商');
            }
            if ($depositMember['active'] == 3) {
                $this->error('该代理商已被冻结，禁止申请提现。');
            }

            $result = Db::table('ims_cc_wifi_deposit')->where('id',$ids)->update(["status" => 1, "status_timestamp" => time(),'image_deposit'=>$param['image_deposit']]);
            if ($result) {
                $this->success('通过成功');
            }
            $this->error('通过失败');
        }
    }

    public function selectpage()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'htmlspecialchars']);

        //搜索关键词,客户端输入以空格分开,这里接收为数组
        $word = (array)$this->request->request("q_word/a");

        //当前页
        $page = $this->request->request("pageNumber");
        //分页大小
        $pagesize = $this->request->request("pageSize");
        //搜索条件
        $andor = $this->request->request("andOr", "and", "strtoupper");
        //排序方式
        $orderby = (array)$this->request->request("orderBy/a");
        //显示的字段
        $field = $this->request->request("showField");
        //自定义显示的字段格式,支持多字段拼接,字段可以用{name}-{id}显示
        $field_custom = $this->request->request("showField_custom");

        $field_custom = '用户ID：{id} | 姓名：{group_name} | 手机：{contact_mobile}';

        //主键
        $primarykey = $this->request->request("keyField");
        //主键值
        $primaryvalue = $this->request->request("keyValue");
        //搜索字段
        $searchfield = (array)$this->request->request("searchField/a");
        //自定义搜索条件
        $custom = (array)$this->request->request("custom/a");
        //是否返回树形结构
        $istree = $this->request->request("isTree", 0);

        $ishtml = $this->request->request("isHtml", 0);

        //下来显示title支持模板多字段格式显示, 字段可以用{name}-{id}显示
        preg_match_all('/\{(\w*)\}/',$field_custom,$field_custom_match);

        //设置自定义搜索字段
        if(!empty($field_custom_match[1])){
            foreach ($field_custom_match[1] as $k=>$v){
                if($v!=$primarykey)$searchfield[]=$v;
            }
            $andor='or';

            $this->selectpageFields=implode(',',$field_custom_match[1]);

            $searchfield=array_flip( array_flip($searchfield) );  //去重复
        }else{
            $this->selectpageFields=$primarykey.','.$field;
        }

        //拼接自定义字段显示
        $field_custom_fun=function (&$item) use ($field_custom_match,$field_custom,$field){

            $a=[];
            if(!empty($field_custom_match[1])) {
                foreach ($field_custom_match[1] as $k => $v) {
                    $a[] = isset($item[$v]) ? $item[$v] : '';
                }
                $str = str_replace($field_custom_match[0], $a, $field_custom);
            }else{
                $str=isset($item[$field]) ? $item[$field] : '';
            }

            return $str;

        };


        if ($istree) {
            $word = [];
            $pagesize = 99999;
        }
        $order = [];
        foreach ($orderby as $k => $v) {
            $order[$v[0]] = $v[1];
        }

        //如果有primaryvalue,说明当前是初始化传值
        if ($primaryvalue !== null) {
            $where = [$primarykey => ['in', $primaryvalue]];
        } else {
            $where = function ($query) use ($word, $andor, $field, $searchfield, $custom,$primarykey) {

                $logic = $andor == 'AND' ? '&' : '|';
                $searchfield = is_array($searchfield) ? implode($logic, $searchfield) : $searchfield;
                foreach ($word as $k => $v) {
                    if(!empty($v))$query->where(str_replace(',', $logic, $searchfield), "like", "%{$v}%");
                }
                if(is_numeric($v))$query->whereOr($primarykey,'=',$v);

                if ($custom && is_array($custom)) {
                    foreach ($custom as $k => $v) {
                        if (is_array($v) && 2 == count($v)) {
                            $query->where($k, trim($v[0]), $v[1]);
                        } else {
                            $query->where($k, '=', $v);
                        }
                    }
                }
            };

        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            Db::table('oss_group')->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = [];

        $where_str = "1=1";

        $total = Db::table('oss_group')->where($where)->where($where_str)->count();

        if ($total > 0) {
            if (is_array($adminIds)) {
                Db::table('oss_group')->where($this->dataLimitField, 'in', $adminIds);
            }

            $datalist = Db::table('oss_group')->where($where)->where($where_str)
                ->order($order)
                ->page($page, $pagesize)
                ->field($this->selectpageFields)
                ->select();

            foreach ($datalist as $index => $item) {
                unset($item['password'], $item['salt']);

                $list[] = [
                    $primarykey => isset($item[$primarykey]) ? $item[$primarykey] : '',
                    $field      =>$field_custom_fun($item),
                ];
            }

            if ($istree) {
                $tree = Tree::instance();
                $tree->init(collection($list)->toArray(), 'pid');
                $list = $tree->getTreeList($tree->getTreeArray(0), $field);
                if (!$ishtml) {
                    foreach ($list as &$item) {
                        $item = str_replace('&nbsp;', ' ', $item);
                    }
                    unset($item);
                }
            }
        }
        //这里一定要返回有list这个字段,total是可选的,如果total<=list的数量,则会隐藏分页按钮
        return json(['list' => $list, 'total' => $total]);
    }
}
