// utils/websocket.js

export class GameWebSocket {
	constructor(url, options = {}) {
		this.url = url;
		this.socketTask = null;
		this.gameSn = null;
		this.heartbeatTimer = null;
		this.reconnectAttempts = 0;
		this.maxReconnectAttempts = options.maxReconnectAttempts || 3;
		this.readyState = 3;
		this.reconnectTimer = null;
		this.shouldReconnect = false; // 默认不重连
		this.messageQueue = []; // 消息队列
		this.lastHeartbeatTime = 0; // 最后一次心跳时间
		this.heartbeatTimeout = null; // 心跳超时检测
		this.isManualDisconnect = false; // 新增：标记是否是手动断开
		this.isSilentClosing = false; // 新增：标记是否是静默关闭

		// 配置选项
		this.config = {
			heartbeatInterval: options.heartbeatInterval || 30000, // 心跳间隔30秒
			heartbeatTimeout: options.heartbeatTimeout || 100000, // 心跳超时时间100秒
			reconnectInterval: options.reconnectInterval || 5000, // 重连间隔5秒
			reconnectBackoff: options.reconnectBackoff || 2, // 重连退避系数
			messageTimeout: options.messageTimeout || 5000, // 消息超时时间
			debug: options.debug || false, // 调试模式
			encryption: options.encryption || false, // 是否启用加密
			compression: options.compression || false, // 是否启用压缩
		};

		// 定义 WebSocket 状态常量
		this.states = {
			CONNECTING: 0,
			OPEN: 1,
			CLOSING: 2,
			CLOSED: 3
		};

		// 事件回调
		this.callbacks = {
			onOpen: [],
			onClose: [],
			onError: [],
			onMessage: [],
			onReconnect: [], // 新增重连回调
			onHeartbeatTimeout: [], // 新增心跳超时回调
			onMessageTimeout: [] // 新增消息超时回调
		};

		// 错误类型
		this.errorTypes = {
			CONNECTION_FAILED: 'CONNECTION_FAILED',
			HEARTBEAT_TIMEOUT: 'HEARTBEAT_TIMEOUT',
			MESSAGE_TIMEOUT: 'MESSAGE_TIMEOUT',
			NETWORK_ERROR: 'NETWORK_ERROR',
			PARSE_ERROR: 'PARSE_ERROR'
		};
	}

	/**
	 * 连接 WebSocket 服务器
	 */
	connect(gameSn) {
		// 如果已经连接，先静默关闭（不触发重连逻辑）
		if (this.socketTask) {
			this._silentClose();
		}

		this.readyState = 0;
		this.gameSn = gameSn;
		this.shouldReconnect = true; // 重置重连标志
		this.isManualDisconnect = false; // 重置手动断开标记

		try {
			this.socketTask = uni.connectSocket({
				url: this.url,
				success: () => {
					this.readyState = 0;
					this._log('WebSocket 连接初始化成功');
				},
				fail: (err) => {
					this.readyState = 3;
					this._handleError(this.errorTypes.CONNECTION_FAILED, err);
				}
			});

			this._setupEventListeners();
		} catch (err) {
			this._handleError(this.errorTypes.CONNECTION_FAILED, err);
		}
	}

	/**
	 * 设置事件监听器
	 */
	_setupEventListeners() {
		this.socketTask.onOpen(() => {
			this._log('WebSocket 连接已建立');
			this.readyState = 1;
			this._sendQueuedMessages(); // 发送队列中的消息

			// 连接成功后自动发送注册消息
			this.send({
				type: 'register',
				client_type: 'uniapp',
				gameSn: this.gameSn,
			});

			// 延迟启动心跳，确保注册完成
			setTimeout(() => {
				this._startHeartbeat();
				this._log('心跳机制已启动');
			}, 1000);

			// 延迟重置重连计数器，确保连接真正稳定
			setTimeout(() => {
				if (this.readyState === 1) {
					this._resetReconnectAttempts();
					this._log('连接稳定，重置重连计数器');
				}
			}, 3000); // 3秒后如果连接仍然稳定，才重置

			this.callbacks.onOpen.forEach(cb => cb());
		});

		this.socketTask.onMessage((res) => {
			try {
				const data = this._processIncomingMessage(res.data);
				this._log('收到消息:', data);

				// 处理服务器ping消息
				if (data.type === 'ping') {
					this.send({ type: 'pong' });
					this._log('响应服务器ping消息');
					return;
				}

				// 处理心跳响应（服务器返回heartbeat_back）
				if (data.type === 'heartbeat_back') {
					this.lastHeartbeatTime = Date.now();
					this._log('收到心跳响应');
					return;
				}

				this.callbacks.onMessage.forEach(cb => cb(data));
			} catch (e) {
				this._handleError(this.errorTypes.PARSE_ERROR, e);
			}
		});

		this.socketTask.onClose(() => {
			this._log(`WebSocket 连接关闭 - 当前重连次数: ${this.reconnectAttempts} - 静默关闭: ${this.isSilentClosing}`);
			this.readyState = 3;
			this._stopHeartbeat();
			this.callbacks.onClose.forEach(cb => cb());

			// 只有在以下情况下才尝试重连：
			// 1. 允许重连（shouldReconnect 为 true）
			// 2. 未超过最大重连次数
			// 3. 不是手动断开连接
			// 4. 不是静默关闭
			if (this.shouldReconnect &&
				this.reconnectAttempts < this.maxReconnectAttempts &&
				!this.isManualDisconnect &&
				!this.isSilentClosing) {

				const delay = this._calculateReconnectDelay();
				this._log(`将在 ${delay}ms 后尝试重连 (${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`);

				this.reconnectTimer = setTimeout(() => {
					this.reconnectAttempts++;
					this._log(`开始第 ${this.reconnectAttempts} 次重连尝试 - shouldReconnect: ${this.shouldReconnect}`);
					this.callbacks.onReconnect.forEach(cb => cb(this.reconnectAttempts));
					this.connect(this.gameSn);
				}, delay);
			} else {
				this._log('不进行重连：', {
					shouldReconnect: this.shouldReconnect,
					reconnectAttempts: this.reconnectAttempts,
					maxReconnectAttempts: this.maxReconnectAttempts,
					isManualDisconnect: this.isManualDisconnect
				});
			}
		});

		this.socketTask.onError((err) => {
			this._handleError(this.errorTypes.NETWORK_ERROR, err);
			// 网络错误时，如果连接还在，尝试重连
			if (this.readyState === 1) {
				this.socketTask.close();
			}
		});
	}

	/**
	 * 发送消息
	 */
	send(data) {
		if (this.readyState === 1) {
			const message = this._processOutgoingMessage(data);
			this.socketTask.send({
				data: JSON.stringify(message),
				success: () => {
					this._log('消息发送成功:', message);
				},
				fail: (err) => {
					this._handleError(this.errorTypes.NETWORK_ERROR, err);
					// 将消息加入队列，等待重连后发送
					this._queueMessage(message);
				}
			});
		} else {
			this._queueMessage(data);
			this._log('WebSocket 未连接，消息已加入队列');
		}
	}

	/**
	 * 发送游戏消息
	 */
	sendMessage(type, number, direction) {
		this.send({
			type: type,
			number: number,
			status: direction,
			source: 'uniapp',
			timestamp: Date.now()
		});
	}

	/**
	 * 关闭连接
	 */
	disconnect() {
		this.isManualDisconnect = true; // 标记为手动断开
		this.shouldReconnect = false; // 停止重连
		this.reconnectAttempts = 0;
		
		// 清除所有定时器
		clearTimeout(this.reconnectTimer);
		clearTimeout(this.heartbeatTimeout);
		clearInterval(this.heartbeatTimer);
		
		// 重置状态
		this.readyState = 3;
		this.lastHeartbeatTime = 0;
		this.messageQueue = [];
		
		// 关闭连接
		if (this.socketTask) {
			try {
				this.socketTask.close();
			} catch (error) {
				console.error('关闭 WebSocket 连接时出错:', error);
			}
			this.socketTask = null;
		}
	}

	/**
	 * 静默关闭连接（不触发重连逻辑）
	 */
	_silentClose() {
		// 设置静默关闭标志
		this.isSilentClosing = true;

		// 清除所有定时器
		clearTimeout(this.reconnectTimer);
		clearTimeout(this.heartbeatTimeout);
		clearInterval(this.heartbeatTimer);

		// 关闭连接
		if (this.socketTask) {
			try {
				this.socketTask.close();
			} catch (error) {
				console.error('静默关闭 WebSocket 连接时出错:', error);
			}
			this.socketTask = null;
		}

		this.readyState = 3;
		this._log('静默关闭连接完成');

		// 延迟清除静默关闭标志，确保onClose事件处理完成
		setTimeout(() => {
			this.isSilentClosing = false;
		}, 100);
	}

	// --- 私有方法 ---
	_startHeartbeat() {
		// 停止之前的心跳（如果存在）
		this._stopHeartbeat();

		// 初始化最后心跳时间
		this.lastHeartbeatTime = Date.now();
		this._log(`启动心跳机制，间隔: ${this.config.heartbeatInterval}ms`);

		// 立即发送第一次心跳
		this.send({
			type: 'heartbeat'
		});
		this._log('发送首次心跳消息');

		this.heartbeatTimer = setInterval(() => {
			// 检查连接状态
			if (this.readyState !== 1) {
				this._log('连接已断开，停止发送心跳');
				this._stopHeartbeat();
				return;
			}

			// 发送心跳
			this.send({
				type: 'heartbeat'
			});
			this._log('发送心跳消息');
		}, this.config.heartbeatInterval);

		// 单独的心跳超时检测，更宽松的处理
		this.heartbeatTimeout = setInterval(() => {
			const timeSinceLastHeartbeat = Date.now() - this.lastHeartbeatTime;
			// 只有在真正超时且连接状态正常时才断开
			if (timeSinceLastHeartbeat > this.config.heartbeatTimeout && this.readyState === 1) {
				this._log(`心跳超时: ${timeSinceLastHeartbeat}ms > ${this.config.heartbeatTimeout}ms`);
				// 先尝试发送一次心跳，如果还是不行再断开
				this.send({ type: 'heartbeat' });
				this._log('心跳超时，尝试发送额外心跳');

				// 再给5秒时间，如果还是超时才断开
				setTimeout(() => {
					const finalCheck = Date.now() - this.lastHeartbeatTime;
					if (finalCheck > this.config.heartbeatTimeout + 5000 && this.readyState === 1) {
						this._log(`最终心跳检查失败: ${finalCheck}ms，断开连接`);
						if (this.socketTask) {
							this.socketTask.close();
						}
					}
				}, 5000);
			}
		}, this.config.heartbeatTimeout / 3); // 检测频率为超时时间的三分之一
	}

	_stopHeartbeat() {
		if (this.heartbeatTimer) {
			clearInterval(this.heartbeatTimer);
			this.heartbeatTimer = null;
		}
		if (this.heartbeatTimeout) {
			clearInterval(this.heartbeatTimeout); // 改为clearInterval
			this.heartbeatTimeout = null;
		}
	}

	_resetReconnectAttempts() {
		this.reconnectAttempts = 0;
	}

	_calculateReconnectDelay() {
		return Math.min(
			this.config.reconnectInterval * Math.pow(this.config.reconnectBackoff, this.reconnectAttempts),
			30000 // 最大延迟30秒
		);
	}

	_queueMessage(message) {
		this.messageQueue.push({
			message,
			timestamp: Date.now()
		});
	}

	_sendQueuedMessages() {
		while (this.messageQueue.length > 0) {
			const { message, timestamp } = this.messageQueue.shift();
			if (Date.now() - timestamp < this.config.messageTimeout) {
				this.send(message);
			} else {
				this._handleError(this.errorTypes.MESSAGE_TIMEOUT, { message });
				this.callbacks.onMessageTimeout.forEach(cb => cb(message));
			}
		}
	}

	_processIncomingMessage(data) {
		let message = JSON.parse(data);
		if (this.config.encryption) {
			message = this._decryptMessage(message);
		}
		if (this.config.compression) {
			message = this._decompressMessage(message);
		}
		return message;
	}

	_processOutgoingMessage(data) {
		let message = { ...data };
		if (this.config.compression) {
			message = this._compressMessage(message);
		}
		if (this.config.encryption) {
			message = this._encryptMessage(message);
		}
		return message;
	}

	_encryptMessage(message) {
		// TODO: 实现消息加密
		return message;
	}

	_decryptMessage(message) {
		// TODO: 实现消息解密
		return message;
	}

	_compressMessage(message) {
		// TODO: 实现消息压缩
		return message;
	}

	_decompressMessage(message) {
		// TODO: 实现消息解压
		return message;
	}

	_handleError(type, error) {
		this._log(`错误: ${type}`, error);
		this.callbacks.onError.forEach(cb => cb({ type, error }));
	}

	_log(...args) {
		// if (this.config.debug) {
			console.log('[WebSocket]', ...args);
		// }
	}

	// --- 事件注册 ---
	on(eventName, callback) {
		if (this.callbacks[eventName]) {
			this.callbacks[eventName].push(callback);
		}
	}
}