<?php
//本地运行语句：
//wsl -d Ubuntu-22.04 --cd "/mnt/e/project/php/vgame" -e /www/server/php/74/bin/php think workerman restart
//服务器运行语句：
//php think workerman restart
namespace app\workerman\controller;
use app\common\model\Game;
use GatewayWorker\Lib\Gateway;
use think\Log;
use think\Model;
use think\Db;
use think\Cache;
use Workerman\Mqtt\Client;
use Workerman\Timer;
use think\Env;

class WorkerEvents
{
    /**
     * 实时日志写入方法
     */
    private static function realTimeLog($message, $level = 'info')
    {
        $timestamp = date('Y-m-d H:i:s.u');
        $logMessage = "[{$timestamp}][{$level}] {$message}\n";

        // 直接写入文件，立即刷新
        $logFile = LOG_PATH . date('Ymd') . '_realtime.log';
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

        // 同时使用原来的日志系统
        Log::write($message, $level);
    }
    // 客户端类型常量
    const CLIENT_TYPE_APP = 'app';
    const CLIENT_TYPE_UNIAPP = 'uniapp';

    // 通信协议常量
    const PROTOCOL_WEBSOCKET = 'websocket';
    const PROTOCOL_MQTT = 'mqtt';

    public static $sessionCache = [];

    // 游戏信息缓存
    private static $gameInfoCache = [];

    // MQTT游戏状态缓存
    private static $mqttGameStateCache = [];

    // WebSocket投币回调缓存
    private static $webSocketCoinCallbacks = [];

    // MQTT投币回调缓存
    private static $mqttCoinCallbacks = [];

    // WebSocket endGame回调缓存
    private static $webSocketEndGameCallbacks = [];

    // 游戏超时监控缓存
    private static $gameTimeoutCache = [];

    // MQTT连接实例
    private static $mqttClient = null;

    // MQTT配置
    private static $mqttConfig = [
        'host' => "**************",
        'port' => "1883",
        'username' => "game",
        'password' => "sdoifj239874fh97g34fdg34",
        'client_id' =>  "phpMQTT-subscriber", // 将在初始化时设置
        'enabled' => true, // 临时禁用MQTT功能，直到依赖问题解决
    ];

    /**
     * 检查并重连数据库
     */
    private static function checkAndReconnectDb()
    {
        $maxRetries = 3;
        $retryCount = 0;

        Log::write('开始检查数据库连接状态', 'info');

        while ($retryCount < $maxRetries) {
            try {
                // 强制关闭可能已断开的连接
                Db::close();

                // 重新建立连接
                Db::connect();

                // 测试数据库连接
                Log::write('执行数据库连接测试查询: SELECT 1', 'info');
                Db::query('SELECT 1');
                Log::write('数据库连接测试查询成功', 'info');
                Log::write('数据库连接检查完成', 'info');
                return; // 连接成功，退出

            } catch (\Exception $e) {
                $retryCount++;
                Log::write("数据库连接失败 (尝试 {$retryCount}/{$maxRetries}): " . $e->getMessage(), 'error');

                if ($retryCount >= $maxRetries) {
                    Log::write('数据库连接重试次数已达上限', 'error');
                    // 不抛出异常，让上层处理
                    break;
                }

                // 等待一段时间后重试
                sleep(1);
            }
        }

        Log::write('数据库连接检查完成', 'info');
    }

    /**
     * 获取Game模型实例
     */
    private static function getGameModel()
    {
        self::checkAndReconnectDb();
        return new Game();
    }

    /**
     * 获取游戏信息（带缓存）
     * @param string $gameSn 游戏序列号
     * @param bool $forceRefresh 是否强制刷新缓存
     * @return array|null 返回游戏信息数组，失败返回null
     */
    private static function getGameInfo($gameSn, $forceRefresh = false)
    {
        // 检查缓存
        if (!$forceRefresh && isset(self::$gameInfoCache[$gameSn])) {
            return self::$gameInfoCache[$gameSn];
        }

        try {
            self::checkAndReconnectDb();
            $game = Db::name('game')->where('sn', $gameSn)->find();

            if ($game) {
                // 缓存游戏信息
                self::$gameInfoCache[$gameSn] = $game;
                Log::write("游戏信息缓存更新: {$gameSn}", 'info');
                return $game;
            } else {
                // 游戏不存在，缓存null避免重复查询
                self::$gameInfoCache[$gameSn] = null;
                Log::write("游戏不存在，缓存null: {$gameSn}", 'info');
                return null;
            }
        } catch (\Exception $e) {
            Log::write('获取游戏信息失败: ' . $e->getMessage(), 'error');

            // 出错时使用缓存值
            if (isset(self::$gameInfoCache[$gameSn])) {
                Log::write("使用缓存的游戏信息: {$gameSn}", 'info');
                return self::$gameInfoCache[$gameSn];
            }

            return null;
        }
    }



    /**
     * 清除游戏信息缓存
     * @param string $gameSn 游戏序列号，为空时清除所有缓存
     */
    private static function clearGameInfoCache($gameSn = null)
    {
        if ($gameSn === null) {
            self::$gameInfoCache = [];
            Log::write('已清除所有游戏信息缓存', 'info');
        } else {
            unset(self::$gameInfoCache[$gameSn]);
            Log::write("已清除游戏信息缓存: {$gameSn}", 'info');
        }
    }

    /**
     * 获取游戏信息缓存统计
     * @return array
     */
    private static function getGameInfoCacheStats()
    {
        return [
            'total_cached' => count(self::$gameInfoCache),
            'cached_games' => array_keys(self::$gameInfoCache),
            'cache_data' => self::$gameInfoCache
        ];
    }

    /**
     * 批量预加载游戏信息
     * @param array $gameSnList 游戏序列号列表
     */
    private static function batchPreloadGameInfo($gameSnList)
    {
        try {
            self::checkAndReconnectDb();
            $games = Db::name('game')
                ->whereIn('sn', $gameSnList)
                ->select();

            foreach ($games as $game) {
                self::$gameInfoCache[$game['sn']] = $game;
            }

            Log::write("批量预加载游戏信息完成: " . count($games) . " 个游戏", 'info');
        } catch (\Exception $e) {
            Log::write("批量预加载游戏信息失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 初始化MQTT连接
     */
    private static function initMqttClient()
    {
        // 检查MQTT是否启用
        if (!isset(self::$mqttConfig['enabled']) || !self::$mqttConfig['enabled']) {
            Log::write('MQTT功能已禁用，无法初始化MQTT连接', 'info');
            return null;
        }

        // 检查MQTT类是否存在
        if (!class_exists('Workerman\Mqtt\Client')) {
            Log::write('MQTT客户端类不存在，无法初始化MQTT连接', 'error');
            return null;
        }

        // 如果已经有连接，先关闭
        if (self::$mqttClient !== null) {
            try {
                self::$mqttClient->close();
            } catch (\Exception $e) {
                Log::write('关闭旧MQTT连接时出错: ' . $e->getMessage(), 'error');
            }
            self::$mqttClient = null;
        }

        try {
            // 设置唯一的客户端ID
            self::$mqttConfig['client_id'] = 'workerman_server_' . uniqid();

            // 创建MQTT客户端连接
            self::$mqttClient = new Client(
                'ws://' . self::$mqttConfig['host'] . ':' . self::$mqttConfig['port'] . '/mqtt',
                [
                    'username' => self::$mqttConfig['username'],
                    'password' => self::$mqttConfig['password'],
                    'client_id' => self::$mqttConfig['client_id'].rand(0,9999),
                    'clean_session' => true,
                    'keep_alive' => 60,
                ]
            );

            // 设置连接回调
            self::$mqttClient->onConnect = function() {
                Log::write('MQTT客户端连接成功: ' . self::$mqttConfig['client_id'], 'info');
                // 不再启动时全量订阅，改为按需订阅
                Log::write('MQTT客户端已就绪，等待按需订阅游戏主题', 'info');
            };

            // 设置消息接收回调
            self::$mqttClient->onMessage = function($topic, $content) {
                self::handleMqttMessage($topic, $content);
            };

            // 设置断开连接回调
            self::$mqttClient->onClose = function() {
                Log::write('MQTT客户端连接断开', 'error');
                self::$mqttClient = null;
            };

            // 设置错误回调
            self::$mqttClient->onError = function($exception) {
                Log::write('MQTT客户端错误: ' . $exception->getMessage(), 'error');
                self::$mqttClient = null;
            };

            // 连接MQTT服务器
            self::$mqttClient->connect();

        } catch (\Exception $e) {
            Log::write('MQTT客户端连接失败: ' . $e->getMessage(), 'error');
            self::$mqttClient = null;
        }

        return self::$mqttClient;
    }

    /**
     * 发送MQTT消息
     * @param string $gameSn 游戏序列号
     * @param array $data 消息数据
     * @param string $topicType 主题类型：sub 或 pub
     */
    private static function sendMqttMessage($gameSn, $data, $topicType = 'sub')
    {
        try {
            // 检查MQTT是否启用
            if (!isset(self::$mqttConfig['enabled']) || !self::$mqttConfig['enabled']) {
                throw new \Exception('MQTT功能已禁用，无法发送MQTT消息');
            }

            // 检查MQTT类是否存在
            if (!class_exists('Workerman\Mqtt\Client')) {
                throw new \Exception('MQTT客户端类不存在，无法发送MQTT消息');
            }

            // 检查MQTT连接是否可用
            if (self::$mqttClient === null) {
                throw new \Exception('MQTT客户端未连接，请检查连接状态');
            }

            // 构建主题
            $topic = "mqtt/{$gameSn}/{$topicType}/02";
            if ($topicType === 'pub') {
                $topic = "mqtt/{$gameSn}/pub/02ack";
            }

            // 异步发送消息，避免阻塞主流程
            $message = json_encode($data);
            \Workerman\Timer::add(0.001, function() use ($topic, $message) {
                try {
                    if (self::$mqttClient !== null) {
                        self::$mqttClient->publish($topic, $message, 0); // QoS = 0
                        Log::write("MQTT消息发送成功 - 主题: {$topic}, 消息: {$message}", 'info');
                    } else {
                        Log::write("MQTT客户端已断开，无法发送消息: {$message}", 'error');
                    }
                } catch (\Exception $e) {
                    Log::write("异步发送MQTT消息失败: " . $e->getMessage(), 'error');
                }
            }, null, false);

            Log::write("MQTT消息已加入异步发送队列 - 主题: {$topic}", 'info');

        } catch (\Exception $e) {
            Log::write("发送MQTT消息失败: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * 订阅所有游戏的MQTT主题
     */
    /**
     * Subscribes to all MQTT topics for games using MQTT protocol.
     * 
     * Attempts to subscribe to the sub topic (hardware-to-server messages) for each game.
     * Logs subscription status and errors. Requires an active MQTT client connection.
     * 
     * @throws \Exception If subscription fails or database operation encounters errors
     * @return void
     */
    private static function subscribeAllGameTopics()
    {
        try {
            if (self::$mqttClient === null) {
                Log::write('MQTT客户端未连接，无法订阅主题', 'error');
                return;
            }

            // 获取所有使用MQTT协议的游戏
            self::checkAndReconnectDb();
            $games = Db::name('game')
                ->where('control_protocol', self::PROTOCOL_MQTT)
                ->column('sn');

            foreach ($games as $gameSn) {
                // 只订阅硬件响应主题，不订阅命令主题（避免处理自己发送的命令）
                $ackTopic = "mqtt/{$gameSn}/pub/02ack";
                self::$mqttClient->subscribe($ackTopic, ['qos' => 0]);
                self::$subscribedTopics[$ackTopic] = true;
                Log::write("订阅MQTT响应主题: {$ackTopic} (硬件→服务器)", 'info');
            }

            Log::write("成功订阅 " . count($games) . " 个游戏的MQTT主题和响应主题", 'info');

        } catch (\Exception $e) {
            Log::write("订阅MQTT主题失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理接收到的MQTT消息
     * @param string $topic 主题
     * @param string $content 消息内容
     */
    private static function handleMqttMessage($topic, $content)
    {
        try {
            if(!strstr($content,'heartbeat'))Log::write("收到MQTT消息 - 主题: {$topic}, 内容: {$content}", 'info');

            $gameSn = null;
            $isAckMessage = false;

            // 解析主题获取游戏序列号和消息类型
            if (preg_match('/mqtt\/(.+)\/pub\/02ack/', $topic, $matches)) {
                // 来自硬件的响应消息
                $gameSn = $matches[1];
                $isAckMessage = true;
            } else {
                Log::write("无效的MQTT主题格式或不需要处理的主题: {$topic}", 'error');
                return;
            }

            $data = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::write("MQTT消息JSON格式无效: {$content}", 'error');
                return;
            }

            // 根据消息类型处理
            if ($isAckMessage) {
                self::processMqttAckMessage($gameSn, $data, $topic);
            } else {
                self::processMqttMessage($gameSn, $data, $topic);
            }

        } catch (\Exception $e) {
            Log::write("处理MQTT消息失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理MQTT消息
     * @param string $gameSn 游戏序列号
     * @param array $data 消息数据
     * @param string $topic 原始主题
     */
    private static function processMqttMessage($gameSn, $data, $topic)
    {
        if (!isset($data['type'])) {
            Log::write("MQTT消息缺少type字段: " . json_encode($data), 'error');
            return;
        }

        $messageType = $data['type'];

        // 过滤服务器自己发送的消息（避免重复处理）
        if (isset($data['client_type']) && $data['client_type'] === 'uniapp') {
            // 这是服务器发送给硬件的消息，不需要处理
            return;
        }

        if($messageType!='heartbeat')Log::write("处理MQTT消息类型: {$messageType}, 游戏: {$gameSn}", 'info');

        try {
            // 优先处理心跳消息，避免阻塞
            if ($messageType === 'heartbeat') {
                self::handleMqttHeartbeat($gameSn, $data);
                return;
            }

            switch ($messageType) {
                case '02': // 投币命令
                    self::handleMqttCoinCommand($gameSn, $data);
                    break;

                case '05': // 查询彩金数量
                    // MQTT游戏不需要查询彩金，直接返回默认值
                    Log::write("MQTT游戏忽略彩金查询请求: {$gameSn}", 'info');
                    $response = [
                        'type' => '05',
                        'code' => 200,
                        'jp1' => '0',
                        'jp2' => '0',
                        'jp3' => '0',
                        'all' => '0'
                    ];
                    self::sendMqttResponse($gameSn, $response);
                    break;

                case '06': // 查询退币计数
                    self::handleMqttCoinOutQuery($gameSn, $data);
                    break;

                case '07': // 查询游戏结果
                    self::handleMqttGameResultQuery($gameSn, $data);
                    break;

                case '08': // 查询机器报错
                    self::handleMqttErrorQuery($gameSn, $data);
                    break;

                case 'startGame': // 硬件主动开始游戏
                    self::handleMqttStartGame($gameSn, $data);
                    break;

                case 'startGameNotify': // WebSocket转发的开始游戏通知（不处理游戏逻辑）
                    Log::write("收到WebSocket转发的开始游戏通知: {$gameSn}", 'info');
                    // 这里可以添加硬件端的UI更新逻辑，但不重复执行游戏逻辑
                    break;

                case 'endGame': // 硬件主动结束游戏
                    self::handleMqttEndGame($gameSn, $data);
                    break;

                case 'endGameNotify': // WebSocket转发的结束游戏通知（不处理游戏逻辑）
                    Log::write("收到WebSocket转发的结束游戏通知: {$gameSn}", 'info');
                    // 这里可以添加硬件端的UI更新逻辑，但不重复执行游戏逻辑
                    break;

                default:
                    Log::write("未知的MQTT消息类型: {$messageType}", 'error');
                    break;
            }
        } catch (\Exception $e) {
            Log::write("处理MQTT消息类型 {$messageType} 失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理MQTT响应消息（来自硬件的确认消息）
     * @param string $gameSn 游戏序列号
     * @param array $data 消息数据
     * @param string $topic 原始主题
     */
    private static function processMqttAckMessage($gameSn, $data, $topic)
    {
        if (!isset($data['type'])) {
            Log::write("MQTT响应消息缺少type字段: " . json_encode($data), 'error');
            return;
        }

        $messageType = $data['type'];
        if($messageType!='heartbeat_back' && $messageType!='heartbeat')Log::write("处理MQTT响应消息类型: {$messageType}, 游戏: {$gameSn}", 'info');

        try {
            // 处理心跳响应
            if ($messageType === 'heartbeat_back' || $messageType === 'heartbeat') {
                // 心跳响应不需要特殊处理，直接返回
                return;
            }

            // 处理投币命令响应
            if ($messageType === '02') {
                self::handleMqttCoinCommandResponse($gameSn, $data);
                return;
            }

            // 处理查询退币计数响应
            if ($messageType === '06') {
                self::handleMqttQueryResponse($gameSn, $data);
                return;
            }

            // 处理按键命令响应
            if ($messageType === '09') {
                self::handleMqttKeyCommandResponse($gameSn, $data);
                return;
            }

            // 其他类型的响应消息可以在这里添加处理逻辑
            Log::write("未处理的MQTT响应消息类型: {$messageType}", 'info');

        } catch (\Exception $e) {
            Log::write("处理MQTT响应消息失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理投币命令的硬件响应
     * @param string $gameSn 游戏序列号
     * @param array $data 响应数据
     */
    private static function handleMqttCoinCommandResponse($gameSn, $data)
    {
        try {
            $code = $data['code'] ?? 400;
            $requestId = $data['requestId'] ?? null;

            Log::write("收到投币命令硬件响应: 游戏={$gameSn}, 状态码={$code}, 请求ID={$requestId}", 'info');

            $foundRequestId = null;
            $requestInfo = null;
            $isNewCallback = false;

            // 优先检查新的投币回调系统（支持putInCoins）
            if ($requestId && isset(self::$mqttCoinCallbacks[$gameSn][$requestId])) {
                $foundRequestId = $requestId;
                $requestInfo = self::$mqttCoinCallbacks[$gameSn][$requestId];
                $isNewCallback = true;
                Log::write("通过请求ID匹配到新投币请求: {$requestId}", 'info');
            }
            // 如果新系统没找到，检查旧的投币回调系统
            else if ($requestId && isset(self::$mqttCoinCommandCallbacks[$gameSn][$requestId])) {
                $foundRequestId = $requestId;
                $requestInfo = self::$mqttCoinCommandCallbacks[$gameSn][$requestId];
                $isNewCallback = false;
                Log::write("通过请求ID匹配到旧投币请求: {$requestId}", 'info');
            }
            // 如果都没找到，尝试自动匹配最早的请求
            else {
                // 优先匹配新系统的请求
                if (isset(self::$mqttCoinCallbacks[$gameSn]) && !empty(self::$mqttCoinCallbacks[$gameSn])) {
                    $foundRequestId = array_key_first(self::$mqttCoinCallbacks[$gameSn]);
                    $requestInfo = self::$mqttCoinCallbacks[$gameSn][$foundRequestId];
                    $isNewCallback = true;
                    Log::write("自动匹配最早的新投币请求: {$foundRequestId}", 'info');
                }
                // 如果新系统没有，匹配旧系统的请求
                else if (isset(self::$mqttCoinCommandCallbacks[$gameSn]) && !empty(self::$mqttCoinCommandCallbacks[$gameSn])) {
                    $foundRequestId = array_key_first(self::$mqttCoinCommandCallbacks[$gameSn]);
                    $requestInfo = self::$mqttCoinCommandCallbacks[$gameSn][$foundRequestId];
                    $isNewCallback = false;
                    Log::write("自动匹配最早的旧投币请求: {$foundRequestId}", 'info');
                }
            }

            if ($foundRequestId && $requestInfo) {
                // 根据回调类型移除已处理的请求
                if ($isNewCallback) {
                    unset(self::$mqttCoinCallbacks[$gameSn][$foundRequestId]);
                    if (empty(self::$mqttCoinCallbacks[$gameSn])) {
                        unset(self::$mqttCoinCallbacks[$gameSn]);
                    }
                } else {
                    unset(self::$mqttCoinCommandCallbacks[$gameSn][$foundRequestId]);
                    if (empty(self::$mqttCoinCommandCallbacks[$gameSn])) {
                        unset(self::$mqttCoinCommandCallbacks[$gameSn]);
                    }
                }

                // 触发回调，传递成功状态
                $success = ($code == 200);
                $requestInfo['callback']($success);

                Log::write("触发投币命令回调 - 请求ID: {$foundRequestId}, 成功: " . ($success ? 'true' : 'false') . ", 类型: " . ($isNewCallback ? '新系统' : '旧系统'), 'info');
            } else {
                Log::write("未找到可处理的投币命令回调 - 游戏: {$gameSn}", 'warning');
            }

        } catch (\Exception $e) {
            Log::write("处理投币命令硬件响应失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理MQTT查询响应（如退币计数查询等）
     * @param string $gameSn 游戏序列号
     * @param array $data 响应数据
     */
    private static function handleMqttQueryResponse($gameSn, $data)
    {
        try {
            $code = $data['code'] ?? 400;
            $js = $data['js'] ?? '0';

            Log::write("收到MQTT查询响应: 游戏={$gameSn}, 状态码={$code}, js={$js}", 'info');

            // 查找对应的查询回调
            $foundrequestId = null;
            foreach (self::$mqttQueryCallbacks as $requestId => $callback) {
                if (strpos($requestId, $gameSn . '_06_') === 0) {
                    $foundrequestId = $requestId;
                    break;
                }
            }

            if ($foundrequestId && isset(self::$mqttQueryCallbacks[$foundrequestId])) {
                $callback = self::$mqttQueryCallbacks[$foundrequestId];
                unset(self::$mqttQueryCallbacks[$foundrequestId]);

                // 执行回调
                if ($code == 200) {
                    $callback(['js' => intval($js), 'code' => $code]);
                    Log::write("触发MQTT查询回调成功 - 查询ID: {$foundrequestId}, js: {$js}", 'info');
                } else {
                    $callback(['js' => 0, 'code' => $code]);
                    Log::write("触发MQTT查询回调失败 - 查询ID: {$foundrequestId}, 状态码: {$code}", 'error');
                }
            } else {
                Log::write("未找到对应的MQTT查询回调 - 游戏: {$gameSn}", 'warning');
            }

        } catch (\Exception $e) {
            Log::write("处理MQTT查询响应失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理MQTT按键命令响应 (指令ID: 09)
     * @param string $gameSn 游戏序列号
     * @param array $data 响应数据
     */
    private static function handleMqttKeyCommandResponse($gameSn, $data)
    {
        try {
            $code = $data['code'] ?? 400;
            $keyindex = $data['keyindex'] ?? 0;

            Log::write("收到MQTT按键命令响应: 游戏={$gameSn}, 状态码={$code}, keyindex={$keyindex}", 'info');

            // 检查是否是501错误码（账号没钱）
            if ($code == 501) {
                Log::write("MQTT按键命令失败 - 账号没钱: gameSn={$gameSn}, keyindex={$keyindex}", 'info');

                // 查找当前游戏的uniapp客户端
                $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                if ($uniappClientId) {
                    // 发送501错误消息给uniapp端，和WebSocket游戏保持一致
                    self::sendMessage($uniappClientId, 'GameStatus', 501, "账号已经没分了");
                    Log::write("已发送501错误通知给uniapp客户端: {$uniappClientId}", 'info');
                } else {
                    Log::write("未找到游戏{$gameSn}的uniapp客户端", 'warning');
                }

            } else if ($code == 200) {
                Log::write("MQTT按键命令执行成功: gameSn={$gameSn}, keyindex={$keyindex}", 'info');
            } else {
                Log::write("MQTT按键命令执行失败: gameSn={$gameSn}, keyindex={$keyindex}, code={$code}", 'error');
            }

        } catch (\Exception $e) {
            Log::write("处理MQTT按键命令响应失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 发送MQTT响应消息
     * @param string $gameSn 游戏序列号
     * @param array $responseData 响应数据
     */
    private static function sendMqttResponse($gameSn, $responseData)
    {
        try {
            $topic = "mqtt/{$gameSn}/pub/02ack";
            $message = json_encode($responseData);

            // 异步发送MQTT响应，避免阻塞主流程
            \Workerman\Timer::add(0.001, function() use ($topic, $message) {
                try {
                    if (self::$mqttClient !== null) {
                        self::$mqttClient->publish($topic, $message, 0);
                        if(!strstr($message, 'heartbeat'))Log::write("发送MQTT响应 - 主题: {$topic}, 消息: {$message}", 'info');
                    } else {
                        Log::write("MQTT客户端未连接，无法发送响应", 'error');
                    }
                } catch (\Exception $e) {
                    Log::write("异步发送MQTT响应失败: " . $e->getMessage(), 'error');
                }
            }, null, false);

            if(!strstr($message, 'heartbeat'))Log::write("MQTT响应已加入异步发送队列 - 主题: {$topic}", 'info');
        } catch (\Exception $e) {
            Log::write("发送MQTT响应失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理投币命令 (指令ID: 02) - MQTT专用逻辑
     */
    private static function handleMqttCoinCommand($gameSn, $data)
    {
        // 获取投币数量，默认为1
        $coinNum = isset($data['num']) ? intval($data['num']) : 1;

        // 安全检查：投币数量范围限制
        if ($coinNum < 1 || $coinNum > 100) {
            $coinNum = 1;
        }

        Log::write("处理MQTT投币命令: {$gameSn}, 投币数量: {$coinNum}", 'info');

        try {
            // 检查游戏状态缓存
            if (!isset(self::$mqttGameStateCache[$gameSn])) {

                Log::write("MQTT投币失败: 游戏未开始或状态缓存不存在", 'error');
                return;
            }

            $gameState = &self::$mqttGameStateCache[$gameSn];

            // 检查缓存金币是否足够
            if ($gameState['cachedCoin'] < $coinNum) {

                Log::write("MQTT投币失败: 缓存金币不足，需要{$coinNum}币，当前仅有{$gameState['cachedCoin']}币", 'error');
                return;
            }

            // 生成唯一的投币请求ID
            $coinRequestId = $gameSn . '_coin_' . uniqid();

            // 初始化游戏的回调数组（如果不存在）
            if (!isset(self::$mqttCoinCommandCallbacks[$gameSn])) {
                self::$mqttCoinCommandCallbacks[$gameSn] = [];
            }

            // 存储投币请求信息，等待硬件确认
            self::$mqttCoinCommandCallbacks[$gameSn][$coinRequestId] = [
                'gameSn' => $gameSn,
                'coinNum' => $coinNum,
                'callback' => function($success) use ($gameSn, $coinNum) {
                    if ($success) {
                        // 硬件确认成功，使用统一的投币处理逻辑
                        $result = self::processUnifiedCoinInput($gameSn, $coinNum, 1);

                        if ($result['code'] == 200) {
                            Log::write("MQTT投币成功: " . $result['message'], 'info');
                        } else {
                            Log::write("MQTT投币失败: " . $result['message'], 'error');
                        }
                    } else {
                        Log::write("MQTT投币失败: 硬件未确认或超时", 'error');
                    }
                }
            ];



            // 设置超时处理（15秒后如果还没收到硬件确认就认为失败）
            \Workerman\Timer::add(15, function() use ($gameSn, $coinRequestId) {
                if (isset(self::$mqttCoinCommandCallbacks[$gameSn][$coinRequestId])) {
                    Log::write("MQTT投币请求超时 - 游戏: {$gameSn}, 请求ID: {$coinRequestId}", 'error');
                    $requestInfo = self::$mqttCoinCommandCallbacks[$gameSn][$coinRequestId];
                    unset(self::$mqttCoinCommandCallbacks[$gameSn][$coinRequestId]);

                    // 如果该游戏没有其他待处理请求，清除游戏键
                    if (empty(self::$mqttCoinCommandCallbacks[$gameSn])) {
                        unset(self::$mqttCoinCommandCallbacks[$gameSn]);
                    }

                    $requestInfo['callback'](false); // 超时认为失败
                }
            }, null, false);

        } catch (\Exception $e) {

            Log::write("处理MQTT投币命令失败: " . $e->getMessage(), 'error');
        }
    }



    /**
     * 处理查询退币计数 (指令ID: 06)
     */
    private static function handleMqttCoinOutQuery($gameSn, $data)
    {
        Log::write("处理查询退币计数: {$gameSn}", 'info');

        try {
            // 从数据库获取退币计数
            $gameInfo = self::getGameInfo($gameSn);

            $response = [
                'type' => '06',
                'code' => 200,
                'js' => $gameInfo['coin_out_count'] ?? '0'
            ];

            // 如果是异步查询请求，触发回调函数
            if (isset($data['requestId'])) {
                $requestId = $data['requestId'];

                // 检查是否有对应的回调函数
                if (isset(self::$mqttQueryCallbacks[$requestId])) {
                    $callback = self::$mqttQueryCallbacks[$requestId];
                    unset(self::$mqttQueryCallbacks[$requestId]); // 清除回调

                    // 触发回调
                    $callback($response);
                    Log::write("触发MQTT查询回调 - 查询ID: {$requestId}, 响应: " . json_encode($response), 'info');
                } else {
                    // 兼容同步查询，存储到响应缓存中
                    self::$mqttQueryResponseCache[$requestId] = $response;
                    Log::write("存储MQTT查询响应到缓存 - 查询ID: {$requestId}, 响应: " . json_encode($response), 'info');
                }
            }

        } catch (\Exception $e) {
            $response = [
                'type' => '06',
                'code' => 400
            ];

            // 如果是异步查询请求，也要触发回调函数
            if (isset($data['requestId'])) {
                $requestId = $data['requestId'];

                // 检查是否有对应的回调函数
                if (isset(self::$mqttQueryCallbacks[$requestId])) {
                    $callback = self::$mqttQueryCallbacks[$requestId];
                    unset(self::$mqttQueryCallbacks[$requestId]); // 清除回调

                    // 触发回调，传递错误响应
                    $callback($response);
                } else {
                    // 兼容同步查询，存储错误响应
                    self::$mqttQueryResponseCache[$requestId] = $response;
                }
            }

            Log::write("查询退币计数失败: " . $e->getMessage(), 'error');
        }

        self::sendMqttResponse($gameSn, $response);
    }

    /**
     * 处理查询游戏结果 (指令ID: 07)
     */
    private static function handleMqttGameResultQuery($gameSn, $data)
    {
        Log::write("处理查询游戏结果: {$gameSn}", 'info');

        try {
            // 从数据库获取游戏结果
            $gameInfo = self::getGameInfo($gameSn);

            $response = [
                'type' => '07',
                'code' => 200,
                'name' => $gameInfo['last_winner'] ?? '',
                'win' => $gameInfo['last_win_amount'] ?? '0'
            ];
        } catch (\Exception $e) {
            $response = [
                'type' => '07',
                'code' => 400
            ];
            Log::write("查询游戏结果失败: " . $e->getMessage(), 'error');
        }

        self::sendMqttResponse($gameSn, $response);
    }

    /**
     * 处理查询机器报错 (指令ID: 08)
     */
    private static function handleMqttErrorQuery($gameSn, $data)
    {
        Log::write("处理查询机器报错: {$gameSn}", 'info');

        try {
            // 从数据库获取错误信息
            $gameInfo = self::getGameInfo($gameSn);

            $response = [
                'type' => '08',
                'code' => 200,
                'error' => $gameInfo['last_error'] ?? ''
            ];
        } catch (\Exception $e) {
            $response = [
                'type' => '08',
                'code' => 400
            ];
            Log::write("查询机器报错失败: " . $e->getMessage(), 'error');
        }

        self::sendMqttResponse($gameSn, $response);
    }

    /**
     * 处理硬件主动开始游戏 - MQTT专用逻辑
     */
    private static function handleMqttStartGame($gameSn, $data)
    {
        Log::write("处理MQTT硬件主动开始游戏: {$gameSn}", 'info');

        // 防止重复开始游戏 - 检查最近5秒内是否有相同的开始游戏请求
        $userId = $data['userId'] ?? 0;
        $number = $data['number'] ?? 1;
        $cacheKey = "mqtt_start_game_lock_{$gameSn}_{$userId}_{$number}";

        if (isset(self::$sessionCache[$cacheKey]) && (time() - self::$sessionCache[$cacheKey]) < 5) {
            Log::write("防止重复MQTT开始游戏: gameSn={$gameSn}, userId={$userId}, number={$number}", 'warning');

            // 发送重复请求的响应
            $mqttResponse = [
                'type' => 'startGameResult',
                'code' => 400,
                'gameSn' => $gameSn,
                'message' => '请勿重复开始游戏'
            ];
            self::sendMqttResponse($gameSn, $mqttResponse);
            return;
        }

        // 记录开始游戏时间
        self::$sessionCache[$cacheKey] = time();

        // 清理过期的缓存（超过10分钟的记录）
        $currentTime = time();
        foreach (self::$sessionCache as $key => $timestamp) {
            if (strpos($key, 'mqtt_start_game_lock_') === 0 && ($currentTime - $timestamp) > 600) {
                unset(self::$sessionCache[$key]);
            }
        }

        // 调用统一的游戏模型处理逻辑
        $gameData = [
            'gameSn' => $gameSn,
            'client_type' => 'app', // 硬件端
            'number' => $number,
            'userId' => $userId
        ];

        $game_model = self::getGameModel();
        $result = $game_model->startGame($gameData);

        Log::write('MQTT开始游戏结果：'.json_encode($result), 'info');

        // 发送MQTT响应给硬件
        $mqttResponse = [
            'type' => 'startGameResult',
            'code' => $result['code'] == 1 ? 200 : 400,
            'gameSn' => $gameSn
        ];
        self::sendMqttResponse($gameSn, $mqttResponse);

        // 获取uniapp端的client_id来发送统一格式的响应
        $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);

        Log::write('MQTT游戏开始 - 查找uniapp客户端: ' . $gameSn, 'info');
        Log::write('找到的uniapp客户端ID: ' . ($uniappClientId ?: 'null'), 'info');
        Log::write('当前会话缓存: ' . json_encode(self::$sessionCache), 'info');
        if ($uniappClientId) {
            if ($result['code'] == 1) {
                // 成功时的处理
                // 1. 初始化MQTT游戏状态缓存
                if (isset($data['userId'])) {
                    self::initGameStateCache($gameSn, $data['userId'], $result, 'mqtt', $data, $uniappClientId);
                }

                // 2. 调用startGameResult更新游戏状态为1（与WebSocket保持一致）
                $startGameResultData = [
                    'gameSn' => $gameSn,
                    'code' => 200,
                    'number' => $data['number'] ?? 1
                ];
                $startGameResult = $game_model->startGameResult($startGameResultData);
                Log::write('MQTT开始游戏状态更新结果：'.json_encode($startGameResult), 'info');

                // 3. 发送成功响应给uniapp端
                self::sendMessage($uniappClientId, 'startGame_back');

                // 4. 通知uniapp端更新游戏数据
                self::sendMessage($uniappClientId, 'update_game', 200, 'MQTT游戏开始成功');

                // 5. 发送金币更新消息
                if (isset(self::$mqttGameStateCache[$gameSn])) {
                    $gameState = self::$mqttGameStateCache[$gameSn];
                    $coinUpdateData = [
                        'cachedCoin' => $gameState['cachedCoin'],
                        'coinUsed' => $gameState['coinUsed']
                    ];
                    self::sendMessage($uniappClientId, 'coin_update', 200, '金币状态更新', $coinUpdateData);
                    Log::write("发送游戏开始金币更新: " . json_encode($coinUpdateData), 'info');
                }
            } else {
                // 失败时发送错误响应给uniapp端
                self::sendMessage($uniappClientId, 'startGame_back', 400, $result['msg']);
            }
        } else {
            Log::write("未找到游戏 {$gameSn} 对应的uniapp客户端连接", 'error');
        }
    }

    /**
     * 根据游戏序列号和客户端类型获取client_id
     * @param string $gameSn 游戏序列号
     * @param string $clientType 客户端类型
     * @return string|null 客户端ID
     */
    private static function getClientIdByGameSn($gameSn, $clientType)
    {
        // 遍历会话缓存查找匹配的客户端
        foreach (self::$sessionCache as $clientId => $session) {
            // 安全检查：确保session是数组且包含必要的键
            if (is_array($session) &&
                isset($session['gameSn']) &&
                isset($session['client_type']) &&
                $session['gameSn'] === $gameSn &&
                $session['client_type'] === $clientType) {
                return $clientId;
            }
        }
        return null;
    }





    /**
     * 检查缓存金币是否足够投币
     */
    private static function checkCachedCoinSufficient($gameSn)
    {
        if (!isset(self::$mqttGameStateCache[$gameSn])) {
            Log::write("游戏 {$gameSn} 没有MQTT缓存状态", 'error');
            return false;
        }

        $cache = self::$mqttGameStateCache[$gameSn];
        $cachedCoin = $cache['cachedCoin'] ?? 0;

        // 检查缓存金币是否大于0（至少需要1个金币才能投币）
        if ($cachedCoin <= 0) {
            Log::write("游戏 {$gameSn} 缓存金币不足: {$cachedCoin}", 'error');
            return false;
        }

        Log::write("游戏 {$gameSn} 缓存金币充足: {$cachedCoin}", 'info');
        return true;
    }

    /**
     * 统一初始化游戏状态缓存（MQTT和WebSocket共用）
     * @param string $gameSn 游戏序列号
     * @param int $userId 用户ID
     * @param array $gameResult 游戏开始结果
     * @param string $protocol 协议类型 ('mqtt' 或 'websocket')
     * @param array $originalData 原始数据
     * @param string $clientId 客户端ID（用于发送个人消息）
     */
    private static function initGameStateCache($gameSn, $userId, $gameResult, $protocol = 'websocket', $originalData = null, $clientId = null)
    {
        try {
            // 查询用户当前金币
            self::checkAndReconnectDb();
            $user = Db::name('user')->where('id', $userId)->find();
            if (!$user) {
                Log::write("初始化MQTT游戏状态失败: 用户不存在 {$userId}", 'error');
                return;
            }

            $userCoin = $user['money'] ?? 0;

            // 从原始数据或游戏结果中获取座位号
            $number = 1; // 默认值
            Log::write("【座位号调试】开始获取座位号，originalData: " . json_encode($originalData), 'info');
            Log::write("【座位号调试】gameResult: " . json_encode($gameResult), 'info');

            // 优先从原始数据获取座位号
            if ($originalData && isset($originalData['number'])) {
                $number = $originalData['number'];
                Log::write("【座位号调试】从originalData['number']获取座位号: {$number}", 'info');
            } elseif (isset($gameResult['data']['number'])) {
                $number = $gameResult['data']['number'];
                Log::write("【座位号调试】从gameResult['data']['number']获取座位号: {$number}", 'info');
            } elseif (isset($gameResult['data']['seat_id'])) {
                // 如果没有number，尝试从seat_id获取
                $seatId = $gameResult['data']['seat_id'];
                Log::write("【座位号调试】从seat_id查询座位号: seat_id={$seatId}", 'info');
                $seat = Db::name('game_seat')->where('id', $seatId)->find();
                if ($seat) {
                    $number = $seat['number'];
                    Log::write("【座位号调试】从座位表获取座位号: {$number}, 座位信息: " . json_encode($seat), 'info');
                } else {
                    Log::write("【座位号调试】座位表中未找到seat_id={$seatId}的记录", 'warning');
                }
            } else {
                Log::write("【座位号调试】originalData和gameResult中都没有座位号信息，使用默认值: {$number}", 'warning');
            }

            // 统一初始化游戏状态缓存（MQTT和WebSocket共用）
            self::$mqttGameStateCache[$gameSn] = [
                'userId' => $userId,
                'userCoin' => $userCoin,
                'cachedCoin' => $userCoin, // 缓存的金币数量
                'startTime' => time(),
                'initialCoinOut' => 0, // MQTT需要异步查询，WebSocket为0
                'coinUsed' => 0, // 已使用的金币
                'gameStarted' => true,
                'gameResult' => $gameResult,
                'protocol' => $protocol, // 标记协议类型
                'number' => $number // 保存座位号
            ];

            Log::write("【座位号调试】游戏状态缓存已保存座位号: {$number}", 'info');

            // 初始化游戏超时监控
            self::initGameTimeoutMonitor($gameSn, $userId);

            Log::write("{$protocol}游戏状态缓存初始化: " . json_encode(self::$mqttGameStateCache[$gameSn]), 'info');

            // 发送初始金币状态给前端
            if ($clientId) {
                $coinUpdateData = [
                    'cachedCoin' => $userCoin,
                    'coinUsed' => 0,
                    'userCoin' => $userCoin
                ];
                self::sendMessage($clientId, 'coin_update', 200, '初始金币状态', $coinUpdateData);
                Log::write("{$protocol}发送初始金币状态: " . json_encode($coinUpdateData), 'info');
            } else {
                // 如果没有提供clientId，尝试查找对应的uniapp客户端
                $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                if ($uniappClientId) {
                    $coinUpdateData = [
                        'cachedCoin' => $userCoin,
                        'coinUsed' => 0,
                        'userCoin' => $userCoin
                    ];
                    self::sendMessage($uniappClientId, 'coin_update', 200, '初始金币状态', $coinUpdateData);
                    Log::write("{$protocol}发送初始金币状态: " . json_encode($coinUpdateData), 'info');
                }
            }

            // MQTT协议特有：异步查询当前退币计数
            if ($protocol === 'mqtt') {
                self::queryMqttCoinOutAsync($gameSn, function($coinOutData) use ($gameSn) {
                    try {
                        if (isset(self::$mqttGameStateCache[$gameSn])) {
                            $initialCoinOut = $coinOutData['js'] ?? 0;
                            self::$mqttGameStateCache[$gameSn]['initialCoinOut'] = $initialCoinOut;
                            Log::write("MQTT游戏状态缓存更新初始退币计数: {$initialCoinOut}", 'info');
                        }
                    } catch (\Exception $e) {
                        Log::write("更新MQTT游戏状态缓存失败: " . $e->getMessage(), 'error');
                    }
                });
            }

        } catch (\Exception $e) {
            Log::write("初始化MQTT游戏状态失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理硬件主动结束游戏 - MQTT专用逻辑
     */
    private static function handleMqttEndGame($gameSn, $data)
    {
        Log::write("处理MQTT硬件主动结束游戏: {$gameSn}", 'info');

        // 防止重复结束游戏 - 检查最近3秒内是否有相同的结束游戏请求
        $userId = $data['userId'] ?? 0;
        $number = $data['number'] ?? 1;
        $cacheKey = "mqtt_end_game_lock_{$gameSn}_{$userId}_{$number}";

        if (isset(self::$sessionCache[$cacheKey]) && (time() - self::$sessionCache[$cacheKey]) < 3) {
            Log::write("防止重复MQTT结束游戏: gameSn={$gameSn}, userId={$userId}, number={$number}", 'warning');

            // 发送重复请求的响应
            $mqttResponse = [
                'type' => 'endGameResult',
                'code' => 400,
                'gameSn' => $gameSn,
                'message' => '请勿重复结束游戏'
            ];
            self::sendMqttResponse($gameSn, $mqttResponse);
            return;
        }

        // 记录结束游戏时间
        self::$sessionCache[$cacheKey] = time();

        // 调用统一的游戏模型处理逻辑
        $gameData = [
            'gameSn' => $gameSn,
            'client_type' => 'app', // 硬件端
            'number' => $number,
            'userId' => $userId
        ];

        $game_model = self::getGameModel();
        $result = $game_model->endGame($gameData);

        Log::write('MQTT结束游戏结果：'.json_encode($result), 'info');

        // 发送MQTT响应给硬件
        $mqttResponse = [
            'type' => 'endGameResult',
            'code' => $result['code'] == 1 ? 200 : 400,
            'gameSn' => $gameSn
        ];
        self::sendMqttResponse($gameSn, $mqttResponse);

        // 获取uniapp端的client_id来发送统一格式的响应
        $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);

        if ($uniappClientId) {
            if ($result['code'] == 1) {
                // 成功时的处理
                // 1. 处理MQTT游戏状态缓存和金币结算
                self::processMqttGameEnd($gameSn);

                // 2. endGame_back消息将在processMqttGameEnd的异步回调中发送

                // 3. 通知uniapp端更新游戏数据
                self::sendMessage($uniappClientId, 'update_game', 200, 'MQTT游戏结束成功');
            } else {
                // 失败时发送错误响应给uniapp端
                self::sendMessage($uniappClientId, 'endGame_back', 400, $result['msg']);
            }
        } else {
            Log::write("未找到游戏 {$gameSn} 对应的uniapp客户端连接", 'error');
        }
    }

    /**
     * 处理MQTT游戏结束时的金币结算（异步查询）
     * @param string $gameSn 游戏序列号
     */
    private static function processMqttGameEnd($gameSn)
    {
        try {
            // 检查游戏状态缓存
            if (!isset(self::$mqttGameStateCache[$gameSn])) {
                Log::write("MQTT游戏结束处理: 游戏状态缓存不存在 {$gameSn}", 'error');
                return;
            }

            $gameState = self::$mqttGameStateCache[$gameSn];

            // 获取游戏信息，确定退币方式
            $gameInfo = self::getGameInfo($gameSn);
            $returnCoinType = $gameInfo['return_coin_type'] ?? 'all'; // 默认为捕鱼机方式

            Log::write("MQTT游戏结束处理，退币方式: {$returnCoinType}", 'info');

            // 异步查询退币计数，查询完成后执行回调
            self::queryMqttCoinOutAsync($gameSn, function($coinOutData) use ($gameSn, $gameState, $returnCoinType) {
                try {
                    $finalCoinOut = $coinOutData['js'] ?? 0;

                    // 对于推币机，js值就是本次游戏赢得的金币数，不需要计算差值
                    // 因为硬件可能会重置计数器，导致差值计算错误
                    if ($returnCoinType === 'cachecoin') {
                        // 推币机：直接使用js值作为赢得的金币
                        $coinOut = $finalCoinOut;
                        Log::write("推币机MQTT退币：直接使用js值 {$finalCoinOut} 作为赢得金币", 'info');
                    } else {
                        // 捕鱼机：计算退币数量差值（保持原逻辑）
                        $coinOut = $finalCoinOut - $gameState['initialCoinOut'];
                        Log::write("捕鱼机MQTT退币：计算差值 {$finalCoinOut} - {$gameState['initialCoinOut']} = {$coinOut}", 'info');
                    }

                    // 根据游戏类型计算最终用户金币
                    if ($returnCoinType === 'all') {
                        // 捕鱼机方式：直接返回游戏机剩余金币
                        $finalUserCoin = $finalCoinOut; // 游戏机当前剩余金币
                        Log::write("捕鱼机MQTT退币：游戏机剩余金币 {$finalUserCoin}", 'info');
                    } else {
                        // 推币机方式：缓存金币 + 退币数量
                        $finalUserCoin = $gameState['cachedCoin'] + $coinOut;
                        Log::write("推币机MQTT退币：缓存金币 {$gameState['cachedCoin']} + 退币 {$coinOut} = {$finalUserCoin}", 'info');
                    }

                    // 准备订单记录数据
                    $memoData = [
                        'return_coin_type' => $returnCoinType,
                        'start_time' => date('Y-m-d H:i:s', $gameState['startTime']),
                        'end_time' => date('Y-m-d H:i:s'),
                        'initial_coin' => $gameState['userCoin'],
                        'cached_coin' => $gameState['cachedCoin'],
                        'coin_used' => $gameState['coinUsed'],
                        'initial_coin_out' => $gameState['initialCoinOut'],
                        'final_coin_out' => $finalCoinOut,
                        'coin_out_diff' => $coinOut,
                        'final_user_coin' => $finalUserCoin,
                        'note' => $returnCoinType === 'all' ? '捕鱼机退币：直接返回游戏机剩余金币' : '推币机退币：缓存金币 + 游戏机奖励金币'
                    ];

                    // 调用endGameResult方法处理倍率计算和记录更新
                    self::checkAndReconnectDb();
                    $game_model = self::getGameModel();

                    // 获取游戏记录中的真实倍率
                    $gameInfo = self::getGameInfo($gameSn);
                    $gameLogData = Db::name('game_log')
                        ->where('game_id', $gameInfo['id'])
                        ->where('number', 1)
                        ->where('status', 'in', [1, 2])
                        ->order('id desc')
                        ->find();
                    $seat_multiplier = $gameLogData['seat_multiplier'] ?? 1.00;

                    // 构建备注信息（倍率为1时不显示倍率）
                    $memo = "MQTT游戏结束，退币方式: {$returnCoinType}";
                    if ($seat_multiplier != 1.00) {
                        $memo .= " (倍率:{$seat_multiplier})";
                    }

                    Log::write("MQTT游戏结束，使用倍率: {$seat_multiplier}, 原始金币: {$coinOut}, 计算后金币: " . ($coinOut * $seat_multiplier), 'info');

                    // 获取实际花费的金币（已经包含倍率计算）
                    $usedCoins = is_array($gameState) ? ($gameState['coinUsed'] ?? 0) : 0;

                    $endGameResultData = [
                        'gameSn' => $gameSn,
                        'code' => 200,
                        'coin' => $coinOut, // 传递游戏机返回的原始金币数
                        'number' => 1,
                        'memo' => $memo,
                        'mqtt_used_coins' => $usedCoins, // 传递实际花费的金币（已包含倍率）
                        'mqtt_cached_coin_before' => is_array($gameState) ? ($gameState['userCoin'] ?? 0) : 0 // 传递游戏前金币
                    ];

                    Log::write("MQTT投币传递: 实际花费={$usedCoins}个金币（已包含倍率）", 'info');

                    Log::write("MQTT游戏调用endGameResult: " . json_encode($endGameResultData), 'info');
                    $endGameResult = $game_model->endGameResult($endGameResultData);
                    Log::write("MQTT游戏endGameResult结果: " . json_encode($endGameResult), 'info');

                    // 更新memoData记录endGameResult结果
                    $memoData['endGameResult'] = $endGameResult;

                    // 清除游戏状态缓存
                    unset(self::$mqttGameStateCache[$gameSn]);

                    // 取消订阅MQTT主题
                    self::unsubscribeGameTopic($gameSn);

                    // 清除游戏超时监控缓存
                    self::clearGameTimeoutMonitor($gameSn);

                    Log::write("MQTT游戏结束金币结算完成（异步查询退币计数）: " . json_encode($memoData), 'info');

                    // 调试信息：打印关键变量状态
                    Log::write("调试信息 - endGameResult类型: " . gettype($endGameResult), 'info');
                    Log::write("调试信息 - endGameResult内容: " . json_encode($endGameResult), 'info');
                    Log::write("调试信息 - gameState类型: " . gettype($gameState), 'info');
                    Log::write("调试信息 - gameState内容: " . json_encode($gameState), 'info');

                    // 发送endGame_back消息给uniapp端，包含金币信息
                    $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                    // 发送endGame_back消息给uniapp端，包含金币信息
                    // 注意：这里使用endGameResult的结果，它已经包含了正确的金币计算
                    $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                    if ($uniappClientId && is_array($endGameResult) && isset($endGameResult['data']) && is_array($endGameResult['data'])) {
                        $endGameData = [
                            'won_coins' => $endGameResult['data']['coin'] ?? 0,
                            'used_coins' => $endGameResult['data']['used_coins'] ?? 0
                        ];
                        self::sendMessage($uniappClientId, 'endGame_back', 200, '游戏已成功结束！赢得金币：' . $endGameData['won_coins'], $endGameData);
                        Log::write("MQTT游戏发送endGame_back消息: " . json_encode($endGameData), 'info');
                    } else {
                        Log::write("未找到uniapp客户端或endGameResult数据异常，无法发送endGame_back消息", 'warning');
                    }

                } catch (\Exception $e) {
                    Log::write("MQTT游戏结束金币结算回调失败: " . $e->getMessage() . " 文件: " . $e->getFile() . " 行号: " . $e->getLine(), 'error');
                    Log::write("错误堆栈: " . $e->getTraceAsString(), 'error');
                }
            });

        } catch (\Exception $e) {
            Log::write("MQTT游戏结束金币结算失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 处理MQTT心跳
     */
    private static function handleMqttHeartbeat($gameSn, $data)
    {
        //Log::write("处理MQTT心跳: {$gameSn}", 'info');

        // 心跳消息通常不需要响应，只需要记录即可
        // 如果需要响应，可以发送心跳响应
        $response = [
            'type' => 'heartbeat',
            'code' => 200,
            'timestamp' => time()
        ];

        self::sendMqttResponse($gameSn, $response);
    }

    /**
     * 异步查询MQTT退币计数
     * @param string $gameSn 游戏序列号
     * @param callable $callback 回调函数，参数为查询结果
     */
    private static function queryMqttCoinOutAsync($gameSn, $callback)
    {
        try {
            if (self::$mqttClient === null) {
                Log::write("MQTT客户端未连接，无法查询退币计数", 'error');
                $callback(['js' => 0]);
                return;
            }

            // 生成唯一的查询ID
            $requestId = $gameSn . '_06_' . uniqid();

            // 存储回调函数
            self::$mqttQueryCallbacks[$requestId] = $callback;

            // 发送查询消息
            $queryMessage = ['type' => '06', 'requestId' => $requestId];
            $topic = "mqtt/{$gameSn}/sub/02";
            $message = json_encode($queryMessage);

            // 异步发送查询消息
            \Workerman\Timer::add(0.001, function() use ($topic, $message, $requestId) {
                try {
                    if (self::$mqttClient !== null) {
                        self::$mqttClient->publish($topic, $message, 0);
                        Log::write("发送MQTT异步查询退币计数 - 查询ID: {$requestId}", 'info');
                    } else {
                        Log::write("MQTT客户端已断开，无法发送查询", 'error');
                        // 触发回调返回默认值
                        if (isset(self::$mqttQueryCallbacks[$requestId])) {
                            $callback = self::$mqttQueryCallbacks[$requestId];
                            unset(self::$mqttQueryCallbacks[$requestId]);
                            $callback(['js' => 0]);
                        }
                    }
                } catch (\Exception $e) {
                    Log::write("异步发送MQTT查询失败: " . $e->getMessage(), 'error');
                    // 触发回调返回默认值
                    if (isset(self::$mqttQueryCallbacks[$requestId])) {
                        $callback = self::$mqttQueryCallbacks[$requestId];
                        unset(self::$mqttQueryCallbacks[$requestId]);
                        $callback(['js' => 0]);
                    }
                }
            }, null, false);

            // 设置超时处理（5秒后如果还没收到响应就触发回调）
            \Workerman\Timer::add(5, function() use ($requestId) {
                if (isset(self::$mqttQueryCallbacks[$requestId])) {
                    Log::write("MQTT查询超时 - 查询ID: {$requestId}", 'error');
                    $callback = self::$mqttQueryCallbacks[$requestId];
                    unset(self::$mqttQueryCallbacks[$requestId]);
                    $callback(['js' => 0]); // 超时返回默认值
                }
            }, null, false);

        } catch (\Exception $e) {
            Log::write("异步查询MQTT退币计数失败: " . $e->getMessage(), 'error');
            $callback(['js' => 0]);
        }
    }



    /**
     * 临时存储MQTT查询响应的缓存
     */
    private static $mqttQueryResponseCache = [];

    /**
     * 存储MQTT异步查询的回调函数
     */
    private static $mqttQueryCallbacks = [];

    /**
     * 已订阅的MQTT主题列表
     */
    private static $subscribedTopics = [];

    /**
     * 存储MQTT投币命令的回调函数
     * 结构: [gameSn => [requestId => requestInfo, ...], ...]
     */
    private static $mqttCoinCommandCallbacks = [];



    /**
     * 获取MQTT游戏状态缓存
     * @param string $gameSn 游戏序列号
     * @return array|null 游戏状态数据
     */
    private static function getMqttGameState($gameSn)
    {
        return self::$mqttGameStateCache[$gameSn] ?? null;
    }

    /**
     * 清除MQTT游戏状态缓存
     * @param string $gameSn 游戏序列号，为空时清除所有缓存
     */
    private static function clearMqttGameState($gameSn = null)
    {
        if ($gameSn === null) {
            self::$mqttGameStateCache = [];
            Log::write('已清除所有MQTT游戏状态缓存', 'info');
        } else {
            unset(self::$mqttGameStateCache[$gameSn]);
            Log::write("已清除MQTT游戏状态缓存: {$gameSn}", 'info');
        }
    }

    /**
     * 获取MQTT游戏状态统计
     * @return array
     */
    private static function getMqttGameStateStats()
    {
        return [
            'total_games' => count(self::$mqttGameStateCache),
            'active_games' => array_keys(self::$mqttGameStateCache),
            'cache_data' => self::$mqttGameStateCache
        ];
    }

    /**
     * 初始化游戏超时监控
     * @param string $gameSn 游戏序列号
     * @param int $userId 用户ID
     */
    private static function initGameTimeoutMonitor($gameSn, $userId)
    {
        // 获取所有与该游戏关联的uniapp客户端
        $clientIds = self::getUniappClientIdsByGameSn($gameSn);

        if (empty($clientIds)) {
            Log::write("初始化游戏超时监控失败: 未找到与游戏 {$gameSn} 关联的uniapp客户端", 'error');
            return;
        }

        foreach ($clientIds as $clientId) {
            self::$gameTimeoutCache[$clientId] = [
                'gameSn' => $gameSn,
                'userId' => $userId,
                'startTime' => time(),
                'lastHeartbeatTime' => time(),
                'lastCommandTime' => time()
            ];

            Log::write("初始化游戏超时监控: client_id={$clientId}, gameSn={$gameSn}, userId={$userId}", 'info');
        }
    }

    /**
     * 获取与游戏关联的uniapp客户端ID列表
     * @param string $gameSn 游戏序列号
     * @return array 客户端ID列表
     */
    private static function getUniappClientIdsByGameSn($gameSn)
    {
        $clientIds = [];
        $uid = self::getUid($gameSn, self::CLIENT_TYPE_UNIAPP);
        $clientIds = Gateway::getClientIdByUid($uid);
        return $clientIds;
    }

    /**
     * 检查游戏超时
     * 1. 心跳超时：用户开始游戏后超过100秒没发心跳
     * 2. 操作超时：用户开始游戏后2分钟没有发送任何按键
     */
    private static function checkGameTimeouts()
    {
        $currentTime = time();
        $heartbeatTimeout = 100; // 100秒心跳超时
        $commandTimeout = 120;   // 2分钟操作超时
        $reconnectTimeout = 60;  // 60秒断线重连超时

        foreach (self::$gameTimeoutCache as $clientId => $timeoutData) {
            try {
                $gameSn = $timeoutData['gameSn'];
                $userId = $timeoutData['userId'];
                $startTime = $timeoutData['startTime'];
                $lastHeartbeatTime = $timeoutData['lastHeartbeatTime'];
                $lastCommandTime = $timeoutData['lastCommandTime'];
                $isDisconnected = $timeoutData['disconnected'] ?? false;
                $disconnectTime = $timeoutData['disconnectTime'] ?? null;

                $heartbeatElapsed = $currentTime - $lastHeartbeatTime;
                $commandElapsed = $currentTime - $lastCommandTime;
                $gameElapsed = $currentTime - $startTime;

                $shouldExit = false;
                $reason = '';

                // 如果玩家已断线，检查断线重连超时
                if ($isDisconnected && $disconnectTime) {
                    $disconnectElapsed = $currentTime - $disconnectTime;
                    if ($disconnectElapsed > $reconnectTimeout) {
                        $shouldExit = true;
                        $reason = "断线重连超时：断线{$disconnectElapsed}秒，超过{$reconnectTimeout}秒限制";
                    }
                } else {
                    // 正常连接状态下的超时检查

                    // 检查心跳超时（游戏开始后超过100秒没发心跳）
                    if ($gameElapsed > $heartbeatTimeout && $heartbeatElapsed > $heartbeatTimeout) {
                        $shouldExit = true;
                        $reason = "心跳超时：游戏开始{$gameElapsed}秒，最后心跳{$heartbeatElapsed}秒前";
                    }

                    // 检查操作超时（游戏开始后超过2分钟没有按键操作）
                    if ($gameElapsed > $commandTimeout && $commandElapsed > $commandTimeout) {
                        $shouldExit = true;
                        $reason = "操作超时：游戏开始{$gameElapsed}秒，最后操作{$commandElapsed}秒前";
                    }
                }

                if ($shouldExit) {
                    Log::write("检测到游戏超时，自动退出游戏: client_id={$clientId}, gameSn={$gameSn}, userId={$userId}, 原因={$reason}", 'info');

                    // 记录当前缓存状态用于调试
                    $mqttCacheExists = isset(self::$mqttGameStateCache[$gameSn]);
                    Log::write("超时检测时MQTT缓存状态: gameSn={$gameSn}, 缓存存在={$mqttCacheExists}", 'info');
                    if ($mqttCacheExists) {
                        Log::write("MQTT缓存内容: " . json_encode(self::$mqttGameStateCache[$gameSn]), 'info');
                    }

                    // 执行自动退出游戏逻辑
                    self::autoExitGame($clientId, $gameSn, $userId, $reason);

                    // 清除超时监控缓存
                    unset(self::$gameTimeoutCache[$clientId]);
                }

            } catch (\Exception $e) {
                Log::write("检查游戏超时异常: client_id={$clientId}, 错误=" . $e->getMessage(), 'error');
                // 清除异常的缓存项
                unset(self::$gameTimeoutCache[$clientId]);
            }
        }
    }

    /**
     * 自动退出游戏
     * @param string $clientId 客户端ID
     * @param string $gameSn 游戏序列号
     * @param int $userId 用户ID
     * @param string $reason 退出原因
     */
    private static function autoExitGame($clientId, $gameSn, $userId, $reason)
    {
        try {
            // 检查客户端是否仍然连接
            $isClientOnline = Gateway::isOnline($clientId);
            $isDisconnectTimeout = strpos($reason, '断线重连超时') !== false;

            if (!$isClientOnline && !$isDisconnectTimeout) {
                Log::write("客户端已断开连接且非断线重连超时，跳过自动退出游戏: client_id={$clientId}", 'info');
                return;
            }

            if (!$isClientOnline && $isDisconnectTimeout) {
                Log::write("客户端断线重连超时，强制执行退出游戏流程: client_id={$clientId}, 原因={$reason}", 'info');
            }

            // 检查MQTT游戏状态缓存，获取gameLogId
            $gameLogId = null;
            if (isset(self::$mqttGameStateCache[$gameSn])) {
                $gameState = self::$mqttGameStateCache[$gameSn];
                if ($gameState['userId'] == $userId && isset($gameState['gameStarted']) && $gameState['gameStarted']) {
                    // 从MQTT缓存中获取gameLogId
                    $gameLogId = $gameState['gameResult']['data']['gameLogId'] ?? null;
                    Log::write("从MQTT缓存找到活跃游戏状态: client_id={$clientId}, gameSn={$gameSn}, userId={$userId}, gameLogId={$gameLogId}", 'info');
                } else {
                    Log::write("MQTT缓存存在但游戏状态不匹配: userId={$userId}, 缓存userId={$gameState['userId']}, gameStarted=" . ($gameState['gameStarted'] ?? 'null'), 'info');
                }
            } else {
                Log::write("未找到MQTT游戏状态缓存: gameSn={$gameSn}", 'info');
            }

            // 如果从MQTT缓存中没有找到gameLogId，从数据库查询
            if (!$gameLogId) {
                self::checkAndReconnectDb();
                $gameLog = Db::name('game_log')
                    ->where('user_id', $userId)
                    ->where('sn', $gameSn)
                    ->where('status', 0) // 进行中的游戏
                    ->order('id desc')
                    ->find();

                if ($gameLog) {
                    $gameLogId = $gameLog['id'];
                    Log::write("从数据库找到活跃游戏记录: client_id={$clientId}, gameSn={$gameSn}, userId={$userId}, gameLogId={$gameLogId}", 'info');
                }
            }

            if (!$gameLogId) {
                Log::write("未找到活跃的游戏记录，跳过自动退出: client_id={$clientId}, gameSn={$gameSn}, userId={$userId}", 'info');
                return;
            }

            Log::write("开始执行自动退出游戏: client_id={$clientId}, gameSn={$gameSn}, userId={$userId}, gameLogId={$gameLogId}, 原因={$reason}", 'info');

            // 构造endGame数据
            $endGameData = [
                'type' => 'endGame',
                'gameLogId' => $gameLogId,
                'gameSn' => $gameSn,
                'userId' => $userId,
                'autoExit' => true,
                'reason' => $reason
            ];

            // 调用完整的endGame方法（包括数据库状态更新和MQTT退币处理）
            self::endGame($clientId, $endGameData, $gameSn, self::CLIENT_TYPE_UNIAPP);

            // 发送自动退出通知给客户端（仅在客户端在线时）
            if ($isClientOnline) {
                try {
                    self::sendMessage($clientId, 'autoExit', 200, '游戏已自动退出', [
                        'reason' => $reason,
                        'gameLogId' => $gameLogId
                    ]);
                    Log::write("已发送自动退出通知给客户端: client_id={$clientId}", 'info');
                } catch (\Exception $e) {
                    Log::write("发送自动退出通知失败: " . $e->getMessage(), 'error');
                }
            } else {
                Log::write("客户端已断开连接，跳过发送自动退出通知: client_id={$clientId}", 'info');
            }

            Log::write("自动退出游戏完成: client_id={$clientId}, gameSn={$gameSn}, userId={$userId}, gameLogId={$gameLogId}, 原因={$reason}", 'info');

        } catch (\Exception $e) {
            Log::write("自动退出游戏异常: client_id={$clientId}, gameSn={$gameSn}, userId={$userId}, 错误=" . $e->getMessage(), 'error');
        }
    }

    /**
     * 清除游戏超时监控缓存
     * @param string $gameSn 游戏序列号
     */
    private static function clearGameTimeoutMonitor($gameSn)
    {
        $clearedCount = 0;
        foreach (self::$gameTimeoutCache as $clientId => $timeoutData) {
            if ($timeoutData['gameSn'] === $gameSn) {
                unset(self::$gameTimeoutCache[$clientId]);
                $clearedCount++;
            }
        }

        if ($clearedCount > 0) {
            Log::write("清除游戏超时监控缓存: gameSn={$gameSn}, 清除数量={$clearedCount}", 'info');
        }
    }

    /**
     * 根据协议发送消息到游戏端
     * @param string $gameSn 游戏序列号
     * @param array $data 消息数据
     * @param string $client_type 客户端类型
     */
    private static function sendToGameByProtocol($gameSn, $data, $client_type = self::CLIENT_TYPE_APP)
    {
        Log::write("【发送硬件消息】sendToGameByProtocol开始: gameSn={$gameSn}, client_type={$client_type}", 'info');
        Log::write("【发送硬件消息】准备发送的数据: " . json_encode($data), 'info');

        $gameInfo = self::getGameInfo($gameSn);

        // 判断协议类型
        $protocol = self::PROTOCOL_WEBSOCKET; // 默认协议
        if ($gameInfo && isset($gameInfo['control_protocol']) && $gameInfo['control_protocol'] === self::PROTOCOL_MQTT) {
            $protocol = self::PROTOCOL_MQTT;
        }
        Log::write("【发送硬件消息】使用协议: {$protocol}", 'info');

        if ($protocol === self::PROTOCOL_MQTT) {
            // 使用MQTT协议发送 - 转换为MQTT格式
            Log::write("【发送硬件消息】使用MQTT协议发送消息", 'info');
            self::sendMqttCommandMessage($gameSn, $data);
        } else {
            // 使用WebSocket协议发送
            $uid = self::getUid($gameSn, $client_type);
            Log::write("【发送硬件消息】WebSocket协议，目标UID: {$uid}", 'info');

            if (!Gateway::isUidOnline($uid)) {
                Log::write("【发送硬件消息】硬件游戏未连接: {$gameSn}, UID: {$uid}", 'error');
                throw new \Exception("硬件游戏未连接: {$gameSn}");
            }

            $jsonData = json_encode($data);
            Log::write("【发送硬件消息】WebSocket发送JSON数据: {$jsonData}", 'info');
            Gateway::sendToUid($uid, $jsonData);
            Log::write("【发送硬件消息】WebSocket消息发送完成", 'info');
        }
    }

    /**
     * 发送MQTT命令消息（服务器发送给硬件）
     * @param string $gameSn 游戏序列号
     * @param array $data 消息数据
     */
    private static function sendMqttCommandMessage($gameSn, $data)
    {
        try {
            // 检查MQTT是否启用和连接
            if (!isset(self::$mqttConfig['enabled']) || !self::$mqttConfig['enabled']) {
                throw new \Exception('MQTT功能已禁用');
            }

            if (self::$mqttClient === null) {
                throw new \Exception('MQTT客户端未连接');
            }

            // 根据消息类型转换为MQTT格式
            $mqttMessage = self::convertToMqttFormat($data, $gameSn);

            // 如果转换结果为空，说明不需要发送MQTT命令
            if ($mqttMessage === null) {
                Log::write("按键 {$data['status']} 不需要发送MQTT命令，跳过", 'info');
                return;
            }

            // 如果是投币命令，检查缓存金币是否足够并存储回调
            if ($mqttMessage['type'] === '02') {
                if (!self::checkCachedCoinSufficient($gameSn)) {
                    Log::write("缓存金币不足，不发送投币命令", 'error');
                    throw new \Exception('缓存金币不足');
                }

                // 为投币命令生成请求ID并存储回调
                $coinNum = $mqttMessage['num'];
                $coinRequestId = $gameSn . '_coin_' . uniqid();

                // 添加requestId到MQTT消息中
                $mqttMessage['requestId'] = $coinRequestId;

                // 初始化游戏的回调数组（如果不存在）
                if (!isset(self::$mqttCoinCommandCallbacks[$gameSn])) {
                    self::$mqttCoinCommandCallbacks[$gameSn] = [];
                }

                // 存储投币请求信息，等待硬件确认
                self::$mqttCoinCommandCallbacks[$gameSn][$coinRequestId] = [
                    'gameSn' => $gameSn,
                    'coinNum' => $coinNum,
                    'callback' => function($success) use ($gameSn, $coinNum) {
                        if ($success) {
                            // 硬件确认成功，使用统一的投币处理逻辑
                            $result = self::processUnifiedCoinInput($gameSn, $coinNum, 1);

                            if ($result['code'] == 200) {
                                Log::write("异步MQTT投币成功: " . $result['message'], 'info');
                                $remainingCoin = $result['data']['cachedCoin'];
                                $totalUsed = $result['data']['coinUsed'];

                                // 通知uniapp更新金币显示（投币成功时才发送）
                                $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                                if ($uniappClientId) {
                                    $coinUpdateData = [
                                        'cachedCoin' => $remainingCoin,
                                        'coinUsed' => $totalUsed
                                    ];
                                    self::sendMessage($uniappClientId, 'coin_update', 200, '投币成功', $coinUpdateData);
                                    Log::write("发送投币成功金币更新: " . json_encode($coinUpdateData), 'info');
                                }
                            } else {
                                Log::write("异步MQTT投币失败: " . $result['message'], 'error');

                                // 通知uniapp投币失败
                                $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                                if ($uniappClientId) {
                                    self::sendMessage($uniappClientId, 'coin_update', 400, '投币失败，请重试');
                                }
                            }
                        } else {
                            Log::write("投币失败，硬件未确认", 'error');

                            // 通知uniapp投币失败
                            $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                            if ($uniappClientId) {
                                self::sendMessage($uniappClientId, 'coin_update', 400, '投币失败，请重试');
                            }
                        }
                    },
                    'timestamp' => time()
                ];

                Log::write("存储投币命令回调: {$coinRequestId}", 'info');
            }

            // 使用sub主题发送命令给硬件（恢复原来的配置）
            $topic = "mqtt/{$gameSn}/sub/02";
            $message = json_encode($mqttMessage);

            // 异步发送MQTT消息，避免阻塞主流程
            \Workerman\Timer::add(0.001, function() use ($topic, $message) {
                try {
                    if (self::$mqttClient !== null) {
                        self::$mqttClient->publish($topic, $message, 0);
                        Log::write("发送MQTT命令 - 主题: {$topic}, 消息: {$message}", 'info');
                    } else {
                        Log::write("MQTT客户端已断开，无法发送消息: {$message}", 'error');
                    }
                } catch (\Exception $e) {
                    Log::write("异步发送MQTT命令失败: " . $e->getMessage(), 'error');
                }
            }, null, false);

            Log::write("MQTT命令已加入异步发送队列 - 主题: {$topic}", 'info');

        } catch (\Exception $e) {
            Log::write("发送MQTT命令失败: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * 将WebSocket格式的消息转换为MQTT格式
     * @param array $data 原始消息数据
     * @param string $gameSn 游戏序列号
     * @return array MQTT格式的消息
     */
    private static function convertToMqttFormat($data, $gameSn)
    {
        $mqttMessage = [];

        // 根据消息类型进行转换
        switch ($data['type']) {
            case 'command':
                // 投币按键范围：51-54，转换为投币命令(02类型)
                if (isset($data['status']) && in_array($data['status'], [51, 52, 53, 54])) {
                    $mqttMessage['type'] = '02';
                    // 根据按键ID设置投币数量
                    $coinMap = [
                        51 => 1,    // 投币
                        52 => 5,    // 投5币
                        53 => 30,   // 投30币
                        54 => 100   // 投100币
                    ];
                    $mqttMessage['num'] = $coinMap[$data['status']] ?? 1;
                }
                // 拍拍乐游戏按键范围：10-16，转换为按键命令(09类型)
                else if (isset($data['status']) && in_array($data['status'], [10, 11, 12, 13, 14, 15, 16])) {
                    $mqttMessage['type'] = '09';
                    $mqttMessage['keyindex'] = $data['status'];
                    Log::write("转换拍拍乐按键为MQTT格式: 按键ID={$data['status']}", 'info');
                } else {
                    // 其他按键不发送MQTT命令，直接返回空
                    return null;
                }
                break;

            case 'startGame':
                // 开始游戏（硬件主动）
                $mqttMessage['type'] = 'startGame';
                $mqttMessage['client_type'] = $data['client_type'] ?? 'uniapp';
                $mqttMessage['gameSn'] = $data['gameSn'] ?? '';
                $mqttMessage['number'] = $data['number'] ?? 1;
                $mqttMessage['coin'] = $data['coin'] ?? 0;
                $mqttMessage['userId'] = $data['userId'] ?? null;
                $mqttMessage['requestId'] = $gameSn . '_startGame_' . uniqid();
                break;

            case 'startGameNotify':
                // 开始游戏通知（WebSocket转发）
                $mqttMessage['type'] = 'startGameNotify';
                $mqttMessage['client_type'] = $data['client_type'] ?? 'uniapp';
                $mqttMessage['gameSn'] = $data['gameSn'] ?? '';
                $mqttMessage['number'] = $data['number'] ?? 1;
                $mqttMessage['coin'] = $data['coin'] ?? 0;
                $mqttMessage['userId'] = $data['userId'] ?? null;
                $mqttMessage['requestId'] = $gameSn . '_startGameNotify_' . uniqid();
                break;

            case 'endGame':
                // 结束游戏（硬件主动）
                $mqttMessage['type'] = 'endGame';
                $mqttMessage['client_type'] = $data['client_type'] ?? 'uniapp';
                $mqttMessage['gameSn'] = $data['gameSn'] ?? '';
                $mqttMessage['number'] = $data['number'] ?? 1;
                $mqttMessage['userId'] = $data['userId'] ?? null;
                break;

            case 'endGameNotify':
                // 结束游戏通知（WebSocket转发）
                $mqttMessage['type'] = 'endGameNotify';
                $mqttMessage['client_type'] = $data['client_type'] ?? 'uniapp';
                $mqttMessage['gameSn'] = $data['gameSn'] ?? '';
                $mqttMessage['number'] = $data['number'] ?? 1;
                $mqttMessage['userId'] = $data['userId'] ?? null;
                $mqttMessage['requestId'] = $gameSn . '_endGameNotify_' . uniqid();
                break;

            case 'heartbeat':
                // 心跳
                $mqttMessage['type'] = 'heartbeat';
                break;

            default:
                // 其他类型保持原样
                $mqttMessage = $data;
                break;
        }

        return $mqttMessage;
    }

    /**
     * 为单个游戏订阅MQTT主题
     * @param string $gameSn 游戏序列号
     */
    private static function subscribeGameTopic($gameSn)
    {
        try {
            if (self::$mqttClient === null) {
                Log::write('MQTT客户端未连接，无法订阅游戏主题', 'error');
                return;
            }

            // 检查游戏是否使用MQTT协议
            $gameInfo = self::getGameInfo($gameSn);
            if (!$gameInfo || !isset($gameInfo['control_protocol']) || $gameInfo['control_protocol'] !== self::PROTOCOL_MQTT) {
                return; // 不是MQTT协议的游戏，不需要订阅
            }

            // 只订阅硬件响应主题，不订阅命令主题（避免处理自己发送的命令）
            $ackTopic = "mqtt/{$gameSn}/pub/02ack";
            if (!isset(self::$subscribedTopics[$ackTopic])) {
                self::$mqttClient->subscribe($ackTopic, ['qos' => 0]);
                self::$subscribedTopics[$ackTopic] = true;
                Log::write("为游戏 {$gameSn} 订阅MQTT响应主题: {$ackTopic} (硬件→服务器)", 'info');
            } else {
                Log::write("游戏 {$gameSn} 的MQTT响应主题已订阅，跳过: {$ackTopic}", 'info');
            }

        } catch (\Exception $e) {
            Log::write("为游戏 {$gameSn} 订阅MQTT主题失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 取消订阅单个游戏的MQTT主题
     * @param string $gameSn 游戏序列号
     */
    private static function unsubscribeGameTopic($gameSn)
    {
        try {
            if (self::$mqttClient === null) {
                Log::write('MQTT客户端未连接，无法取消订阅游戏主题', 'error');
                return;
            }

            // 检查游戏是否使用MQTT协议
            $gameInfo = self::getGameInfo($gameSn);
            if (!$gameInfo || !isset($gameInfo['control_protocol']) || $gameInfo['control_protocol'] !== self::PROTOCOL_MQTT) {
                return; // 不是MQTT协议的游戏，不需要取消订阅
            }

            // 取消订阅游戏的pub/02ack主题
            $ackTopic = "mqtt/{$gameSn}/pub/02ack";
            if (isset(self::$subscribedTopics[$ackTopic])) {
                self::$mqttClient->unsubscribe($ackTopic);
                unset(self::$subscribedTopics[$ackTopic]);
                Log::write("为游戏 {$gameSn} 取消订阅MQTT响应主题: {$ackTopic}", 'info');
            }

        } catch (\Exception $e) {
            Log::write("为游戏 {$gameSn} 取消订阅MQTT主题失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 当Worker启动时触发
     * 在这里初始化MQTT连接
     */
    public static function onWorkerStart()
    {
        Log::write('Worker启动，开始初始化', 'info');

        // 检查MQTT是否启用
        if (!isset(self::$mqttConfig['enabled']) || !self::$mqttConfig['enabled']) {
            Log::write('MQTT功能已禁用，跳过MQTT连接初始化', 'info');
            return;
        }

        // 检查MQTT类是否存在
        if (!class_exists('Workerman\Mqtt\Client')) {
            Log::write('MQTT客户端类不存在，跳过MQTT连接初始化', 'error');
            return;
        }

        Log::write('开始初始化MQTT连接', 'info');

        // 启动时就建立MQTT连接
        self::initMqttClient();

        // 设置定时器检查MQTT连接状态
        Timer::add(30, function() {
            try {
                if (self::$mqttConfig['enabled'] && self::$mqttClient === null && class_exists('Workerman\Mqtt\Client')) {
                    Log::write('检测到MQTT连接断开，尝试重连', 'info');
                    self::initMqttClient();
                }
            } catch (\Exception $e) {
                Log::write('MQTT连接检查异常: ' . $e->getMessage(), 'error');
            }
        });

        // 设置定时器检查游戏超时（每30秒检查一次）
        Timer::add(30, function() {
            self::checkGameTimeouts();
        });
    }

    /**
     * 当客户端连接时触发
     * 如果业务不需此回调可以删除onConnect
     *
     * @param int $client_id 连接id
     */
    public static function onConnect($client_id)
    {
        try {
            // 记录连接建立
            Log::write("新连接建立: client_id={$client_id}, IP={$_SERVER['REMOTE_ADDR']}", 'info');

            // 初始化会话数据
            Gateway::setSession($client_id, [
                'client_type'   => null,
                'gameSn'        => null,
                'uid'           => null,
                'connect_time'  => time(),
                'last_ping_time' => time(),
                'last_active'   => time()
            ]);

            // 缓存会话信息
            self::$sessionCache[$client_id] = Gateway::getSession($client_id);

            // 返回连接确认
            self::sendMessage($client_id,'connect_back');

        } catch (\Exception $e) {
            Log::write("连接建立异常: client_id={$client_id}, error=" . $e->getMessage(), 'error');
        }
    }

    /**
     * 发送消息
     * @Authod Jw
     * @Time 2025/5/15
     * @param $client_id
     * @param $type
     * @param null $message
     * @param int $code
     * @param array $data 额外数据
     */
    private static function sendMessage($client_id, $type, $code=200,$message=null, $data=null) {
        $status = $code == 200 ? 'success' : 'fail';
        $response = [
            'code'   => $code,
            'type'   => $type,
            'status' => $status,
            'message' => $message,
        ];

        // 如果有额外数据，添加到响应中
        if ($data !== null) {
            $response['data'] = $data;
        }

        Gateway::sendToClient($client_id, json_encode($response));
    }

    private static function getUid($gameSn,$client_type)
    {
        return $gameSn . ':' . $client_type;
    }

    /**
     * 当客户端发来消息时触发
     * @param int $client_id 连接id
     * @param mixed $message 具体消息
     */
    public static  function onMessage($client_id, $message)
    {
        try {
            self::cleanExpiredSessions(); // 新增会话清理
            self::monitorSystemResources(); // 监控系统资源

            // 解析 JSON 数据
            $data = json_decode($message, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("JSON格式无效");
            }

            // 校验基础字段
            if (!isset($data['type'])) {
                throw new \Exception("缺少 'type' 字段");
            }

            $gameSn = '';
            $client_type = '';
            if ($data['type'] != 'register') {//注册
                // 从 Session 中获取原始 gameSn
                $session = Gateway::getSession($client_id);

                // 安全检查：确保session是数组
                if (!is_array($session)) {
                    throw new \Exception("会话数据无效，请重新连接");
                }

                $gameSn = $session['gameSn'] ?? null;
                if (!$gameSn) {
                    throw new \Exception("请先注册游戏会话");
                }
                $client_type = $session['client_type'] ?? null;

                if(!strstr($message, 'heartbeat'))Log::write('ws 连接id='.$client_id.' 连接ip='.$_SERVER['REMOTE_ADDR'].' 机器SN='.$gameSn.' 收到数据：'.$message, 'info');
            }

            // 快速响应的消息类型（不阻塞）
            $quickTypes = ['heartbeat', 'command', 'ping', 'pong', 'GameStatus'];
            if (in_array($data['type'], $quickTypes)) {
                self::handleQuickMessage($client_id, $data, $gameSn);
                return;
            }

            // 数据库操作消息需要防重复检查后再异步处理
            $dbTypes = ['startGame', 'endGame', 'startGameResult', 'endGameResult', 'putInCoins', 'putInCoinsResult'];
            if (in_array($data['type'], $dbTypes)) {
                // 对于startGame，先进行重复检查
                if ($data['type'] === 'startGame') {
                    // Log::write("收到WebSocket开始游戏请求: client_id={$client_id}, gameSn={$gameSn}, data=" . json_encode($data), 'info');

                    $userId = $data['userId'] ?? 0;
                    $number = $data['number'] ?? 1;
                    $cacheKey = "ws_start_game_lock_{$gameSn}_{$userId}_{$number}";

                    if (isset(self::$sessionCache[$cacheKey]) && (time() - self::$sessionCache[$cacheKey]) < 3) {
                        Log::write("防止重复WebSocket开始游戏: client_id={$client_id}, gameSn={$gameSn}, userId={$userId}", 'warning');
                        self::sendMessage($client_id, 'startGame_back', 400, '请勿重复开始游戏，请等待3秒后再试');
                        return;
                    }

                    // 记录开始游戏时间
                    self::$sessionCache[$cacheKey] = time();
                    Log::write("WebSocket开始游戏防重复检查通过，准备异步处理", 'info');
                }

                // 立即返回确认
                self::sendMessage($client_id, 'message_received', 200, '消息已接收，正在处理中...');

                // 异步处理
                \Workerman\Timer::add(0.001, function() use ($client_id, $data, $gameSn, $client_type) {
                    self::handleDatabaseMessage($client_id, $data, $gameSn, $client_type);
                }, null, false);
                return;
            }

            switch ($data['type']) {
                case 'register'://注册
                    // 使用超时保护处理注册
                    self::handleRegisterWithTimeout($client_id, $data);
                    break;
                default:
                    throw new \Exception("未知消息类型: {$data['type']}");
            }
        } catch (\Exception $e) {
            Log::write('连接id='.$client_id.' 连接ip='.$_SERVER['REMOTE_ADDR'].' 错误信息：'.$e->getMessage(), 'error');

            // 返回错误信息
            self::sendMessage($client_id,null,400,$e->getMessage());
            // 主动断开连接
//            Gateway::closeClient($client_id);
        }
    }

    /**
     * 带超时保护的注册处理
     */
    private static function handleRegisterWithTimeout($client_id, array $data)
    {
        // 设置最大执行时间为5秒
        $maxExecutionTime = 5;
        $startTime = time();

        try {
            // 使用异步方式处理注册，避免阻塞
            \Workerman\Timer::add(0.001, function() use ($client_id, $data, $startTime, $maxExecutionTime) {
                try {
                    // 检查是否超时
                    if (time() - $startTime > $maxExecutionTime) {
                        Log::write("注册处理超时: client_id={$client_id}, 超时时间={$maxExecutionTime}秒", 'error');
                        self::sendMessage($client_id, 'register_back', 500, '注册处理超时，请重试');
                        return;
                    }

                    self::handleRegister($client_id, $data);
                } catch (\Exception $e) {
                    Log::write("异步注册处理异常: client_id={$client_id}, 错误=" . $e->getMessage(), 'error');
                    self::sendMessage($client_id, 'register_back', 500, $e->getMessage());
                }
            }, null, false);

        } catch (\Exception $e) {
            Log::write("注册超时保护异常: client_id={$client_id}, 错误=" . $e->getMessage(), 'error');
            self::sendMessage($client_id, 'register_back', 500, $e->getMessage());
        }
    }

    /**
     * 处理游戏注册（App 连接）
     */
    private static function handleRegister($client_id, array $data)
    {
        $startTime = microtime(true);
        Log::write("开始处理注册: client_id={$client_id}, time=" . date('H:i:s.u'), 'info');

        if (!isset($data['gameSn'])) {
            throw new \Exception("注册时缺少 'gameSn'");
        }
        Log::write('连接id='.$client_id.' 连接ip='.$_SERVER['REMOTE_ADDR'].' 请求数据：'.json_encode($data), 'info');
        if (!isset($data['client_type'])) {
            throw new \Exception("注册时缺少 'client_type'");
        }
        if (!in_array($data['client_type'], [self::CLIENT_TYPE_APP, self::CLIENT_TYPE_UNIAPP])) {
            throw new \Exception("非法的客户端类型: {$data['client_type']}");
        }

        // 绑定组合 UID（如 "12345:uniapp"）
        Log::write("步骤1: 生成UID, client_id={$client_id}", 'info');
        $uid = self::getUid($data['gameSn'],$data['client_type']);

        // 绑定 gameSn（UID）到当前连接
        Log::write("步骤2: 绑定UID, client_id={$client_id}, uid={$uid}", 'info');
        Gateway::bindUid($client_id, $uid);

        // 更新会话数据
        Log::write("步骤3: 更新会话数据, client_id={$client_id}", 'info');
        Gateway::updateSession($client_id, [
            'client_type' => $data['client_type'],
            'gameSn' => $data['gameSn'],
            'uid' => $uid,
            'last_active'    => time() // 新增活跃时间戳
        ]);

        // 缓存完整的 session 到静态数组
        Log::write("步骤4: 缓存会话数据, client_id={$client_id}", 'info');
        self::$sessionCache[$client_id] = Gateway::getSession($client_id);

        if ($data['client_type'] == self::CLIENT_TYPE_APP) {//是app的注册
            //修改设备为在线
            Log::write("步骤5: 开始更新设备在线状态, client_id={$client_id}, gameSn={$data['gameSn']}", 'info');
            try {
                // 设置较短的锁等待超时时间（3秒）
                Db::execute('SET innodb_lock_wait_timeout = 3');

                $game_model = self::getGameModel();
                $result = $game_model->updateGameOnlineStatus($data['gameSn']);

                // 恢复默认超时时间
                Db::execute('SET innodb_lock_wait_timeout = 50');

                Log::write('步骤5完成: 修改设备在线：'.json_encode($result), 'info');
            } catch (\Exception $e) {
                // 恢复默认超时时间
                Db::execute('SET innodb_lock_wait_timeout = 50');

                if (strpos($e->getMessage(), 'Lock wait timeout') !== false) {
                    Log::write("步骤5超时: 更新设备在线状态锁等待超时，跳过此操作", 'error');
                } else {
                    Log::write("步骤5失败: 更新设备在线状态异常: " . $e->getMessage(), 'error');
                }
                // 不抛出异常，继续执行
            }
        }

        // 预加载游戏信息到缓存
        Log::write("步骤6: 开始预加载游戏信息, client_id={$client_id}, gameSn={$data['gameSn']}", 'info');
        try {
            $gameInfo = self::getGameInfo($data['gameSn']);
            if ($gameInfo) {
                $protocol = isset($gameInfo['control_protocol']) ? $gameInfo['control_protocol'] : 'websocket';
                Log::write("步骤6完成: 游戏注册时预加载信息: {$data['gameSn']} -> 协议: {$protocol}", 'info');

                // MQTT主题订阅改为按需订阅（开始游戏时订阅，结束游戏时取消订阅）
                Log::write("步骤7: MQTT协议游戏，主题订阅将在开始游戏时进行", 'info');
            } else {
                Log::write("步骤6警告: 游戏注册时未找到游戏信息: {$data['gameSn']}", 'info');
            }
        } catch (\Exception $e) {
            Log::write("步骤6失败: 预加载游戏信息失败: " . $e->getMessage(), 'error');
        }

        // 返回注册成功
        Log::write("步骤8: 发送注册成功响应, client_id={$client_id}", 'info');
        self::sendMessage($client_id,'register_back');

        // 主动查询游戏状态
        if ($data['client_type'] === self::CLIENT_TYPE_UNIAPP) {
            Log::write("步骤9: 主动查询游戏状态, client_id={$client_id}, gameSn={$data['gameSn']}", 'info');
            self::handleQueryGameState($client_id, [], $data['gameSn'], $data['client_type']);

            // 检查是否有断线的超时监控缓存需要恢复
            self::checkAndRestoreDisconnectedTimeout($client_id, $data['gameSn']);
        }

        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        Log::write("注册处理完成: client_id={$client_id}, 耗时={$duration}ms", 'info');
    }

    /**
     * 检查并恢复断线的超时监控缓存
     */
    private static function checkAndRestoreDisconnectedTimeout($client_id, $gameSn)
    {
        try {
            // 查找是否有该游戏的断线超时监控缓存
            $disconnectedTimeoutData = null;
            $disconnectedClientId = null;

            foreach (self::$gameTimeoutCache as $oldClientId => $timeoutData) {
                if ($timeoutData['gameSn'] === $gameSn &&
                    isset($timeoutData['disconnected']) &&
                    $timeoutData['disconnected'] === true) {
                    $disconnectedTimeoutData = $timeoutData;
                    $disconnectedClientId = $oldClientId;
                    break;
                }
            }

            if ($disconnectedTimeoutData && $disconnectedClientId) {
                Log::write("发现断线的超时监控缓存，准备恢复: old_client_id={$disconnectedClientId}, new_client_id={$client_id}, gameSn={$gameSn}", 'info');

                // 检查断线时间是否超过重连超时限制
                $currentTime = time();
                $disconnectTime = $disconnectedTimeoutData['disconnectTime'] ?? $currentTime;
                $disconnectElapsed = $currentTime - $disconnectTime;
                $reconnectTimeout = 60; // 60秒断线重连超时

                if ($disconnectElapsed <= $reconnectTimeout) {
                    // 在重连时间限制内，恢复超时监控
                    $restoredData = $disconnectedTimeoutData;
                    $restoredData['disconnected'] = false;
                    unset($restoredData['disconnectTime']);

                    // 移除旧的缓存，添加新的缓存
                    unset(self::$gameTimeoutCache[$disconnectedClientId]);
                    self::$gameTimeoutCache[$client_id] = $restoredData;

                    Log::write("成功恢复超时监控: client_id={$client_id}, gameSn={$gameSn}, 断线时长={$disconnectElapsed}秒", 'info');
                } else {
                    // 超过重连时间限制，清除旧缓存并触发自动退出
                    Log::write("断线重连超时，触发自动退出: old_client_id={$disconnectedClientId}, gameSn={$gameSn}, 断线时长={$disconnectElapsed}秒", 'info');

                    $userId = $disconnectedTimeoutData['userId'] ?? 0;
                    $reason = "断线重连超时：断线{$disconnectElapsed}秒，超过{$reconnectTimeout}秒限制";

                    // 清除旧缓存
                    unset(self::$gameTimeoutCache[$disconnectedClientId]);

                    // 触发自动退出游戏
                    self::autoExitGame($client_id, $gameSn, $userId, $reason);
                }
            } else {
                Log::write("未发现需要恢复的断线超时监控缓存: client_id={$client_id}, gameSn={$gameSn}", 'info');
            }

        } catch (\Exception $e) {
            Log::write("检查断线超时监控缓存异常: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 监控系统资源状态
     */
    private static function monitorSystemResources()
    {
        static $last_monitor = 0;
        if (time() - $last_monitor > 300) { // 每5分钟监控一次
            $last_monitor = time();

            $memoryUsage = memory_get_usage(true);
            $memoryPeak = memory_get_peak_usage(true);
            $connectionCount = count(Gateway::getAllClientIdList());
            $sessionCacheCount = count(self::$sessionCache);

            Log::write("系统资源监控: 内存使用=" . round($memoryUsage/1024/1024, 2) . "MB, " .
                      "峰值内存=" . round($memoryPeak/1024/1024, 2) . "MB, " .
                      "连接数={$connectionCount}, " .
                      "会话缓存数={$sessionCacheCount}", 'info');

            // 如果内存使用过高，清理缓存
            if ($memoryUsage > 100 * 1024 * 1024) { // 100MB
                Log::write("内存使用过高，开始清理会话缓存", 'info');
                self::cleanExpiredSessions();
            }
        }
    }

    private static function cleanExpiredSessions()
    {
        static $last_clean = 0;
        if (time() - $last_clean > 600) {
            // 获取所有客户端会话
            $allSessions = Gateway::getAllClientSessions();
            foreach ($allSessions as $client_id => $session) {
                // 兼容旧会话：没有 last_active 则使用 connect_time
                $lastActive = $session['last_active'] ?? $session['connect_time'] ?? 0;

                if (time() - $lastActive > 3600) {
                    Gateway::closeClient($client_id);
                    unset(self::$sessionCache[$client_id]);
                }
            }
            $last_clean = time();
        }
    }


    /**
     * 处理操作指令（Uniapp 发送）
     */
    private static function sendCommand($client_id,array $data,$gameSn)
    {
        try {
            // 更新游戏超时监控中的最后操作时间
            if (isset(self::$gameTimeoutCache[$client_id])) {
                self::$gameTimeoutCache[$client_id]['lastCommandTime'] = time();
                Log::write("更新游戏操作时间: client_id={$client_id}", 'info');
            }

            // 为发送给硬件的指令添加requestId
            $data['requestId'] = $gameSn . '_command_' . uniqid();

            // 根据游戏协议发送消息
            self::sendToGameByProtocol($gameSn, $data, self::CLIENT_TYPE_APP);

            // 返回确认给 Uniapp
            self::sendMessage($client_id,'command_back');
        } catch (\Exception $e) {
            // 如果是MQTT协议且发送失败，记录日志但不抛出异常
            $gameInfo = self::getGameInfo($gameSn);
            $protocol = ($gameInfo && isset($gameInfo['control_protocol']) && $gameInfo['control_protocol'] === self::PROTOCOL_MQTT)
                       ? self::PROTOCOL_MQTT : self::PROTOCOL_WEBSOCKET;

            if ($protocol === self::PROTOCOL_MQTT) {
                Log::write("MQTT指令发送失败，但继续处理: " . $e->getMessage(), 'error');
                self::sendMessage($client_id,'command_back');
            } else {
                throw $e; // WebSocket协议失败时抛出异常
            }
        }
    }

    /**
     * 开始游戏
     * @Authod Jw
     * @Time 2025/4/27
     * @param $client_id
     * @param array $data
     */
    private static function startGame($client_id,array $data,$gameSn,$client_type)
    {
        Log::write("startGame方法开始: client_id={$client_id}, gameSn={$gameSn}, client_type={$client_type}", 'info');
        Log::write("【座位号调试】startGame接收到的原始数据: " . json_encode($data), 'info');

        // 检查数据库连接
        Log::write("开始检查数据库连接", 'info');
        self::checkAndReconnectDb();
        Log::write("数据库连接检查完成", 'info');

        // 重复检查已在异步处理前完成，这里直接处理
        Log::write("开始处理游戏开始请求", 'info');

        $data['gameSn'] = $gameSn;
        Log::write("开始获取Game模型实例", 'info');
        $game_model = self::getGameModel();
        Log::write("Game模型实例获取完成", 'info');

        Log::write("开始调用Game模型的startGame方法", 'info');
        $result = $game_model->startGame($data);
        Log::write("Game模型的startGame方法调用完成", 'info');
        Log::write('开始游戏：'.json_encode($result), 'info');

        // 检查结果是否有效
        if (!$result || !is_array($result)) {
            $result = ['code' => 0, 'msg' => '游戏开始处理失败，返回结果无效'];
            Log::write('游戏开始结果无效，设置默认错误结果', 'error');
        }

        // 确保result数组包含必要的字段
        if (!isset($result['code'])) {
            $result['code'] = 0;
        }
        if (!isset($result['msg'])) {
            $result['msg'] = '未知错误';
        }

        if ($result['code'] == 1) {//成功
            // 1. 只有MQTT协议的游戏才初始化MQTT游戏状态缓存和订阅主题
            if (isset($data['userId'])) {
                $gameInfo = self::getGameInfo($gameSn);
                $protocol = ($gameInfo && isset($gameInfo['control_protocol']) && $gameInfo['control_protocol'] === self::PROTOCOL_MQTT)
                           ? self::PROTOCOL_MQTT : self::PROTOCOL_WEBSOCKET;

                // 统一初始化游戏状态缓存（MQTT和WebSocket共用）
                self::initGameStateCache($gameSn, $data['userId'], $result, $protocol, $data, $client_id);

                if ($protocol === self::PROTOCOL_MQTT) {
                    // MQTT协议特有：订阅MQTT主题
                    self::subscribeGameTopic($gameSn);
                    Log::write('MQTT协议游戏初始化完成', 'info');
                } else {
                    Log::write('WebSocket协议游戏初始化完成', 'info');
                }
            }

            // 2. 调用startGameResult更新游戏状态为1（进行中）
            $startGameResultData = [
                'gameSn' => $gameSn,
                'code' => 200,
                'number' => $data['number'] ?? 1
            ];
            $startGameResult = $game_model->startGameResult($startGameResultData);
            Log::write('WebSocket开始游戏状态更新结果：'.json_encode($startGameResult), 'info');

            $message = [];
            if ($client_type === self::CLIENT_TYPE_UNIAPP ) {//uniapp端 发送过来的消息
                // 发送消息给硬件端（仅WebSocket协议需要）
                $gameInfo = self::getGameInfo($gameSn);
                $protocol = ($gameInfo && isset($gameInfo['control_protocol']) && $gameInfo['control_protocol'] === self::PROTOCOL_MQTT)
                           ? self::PROTOCOL_MQTT : self::PROTOCOL_WEBSOCKET;

                if ($protocol === self::PROTOCOL_WEBSOCKET) {
                    // WebSocket协议需要发送startGame消息给硬件
                    try {
                        $message['type'] = 'startGame';
                        $message['number'] = $data['number'];
                        $message['client_type'] = 'uniapp';
                        $message['userId'] = $data['userId'] ?? null;
                        $message['requestId'] = $gameSn . '_ws_startGame_' . uniqid();

                        // 根据游戏协议发送消息
                        self::sendToGameByProtocol($gameSn, $message, self::CLIENT_TYPE_APP);
                    } catch (\Exception $e) {
                        throw $e; // WebSocket协议失败时抛出异常
                    }
                } else {
                    // MQTT协议不需要发送startGame消息给硬件，避免重复处理
                    Log::write("MQTT协议游戏，跳过发送startGame消息给硬件，避免重复处理", 'info');
                }

                // 发送update_game消息给uniapp端
                if ($client_type === self::CLIENT_TYPE_UNIAPP) {
                    // uniapp端发起的游戏，直接发送给当前客户端
                    self::sendMessage($client_id, 'update_game', 200, '游戏开始成功');

                    // 发送金币更新消息
                    if (isset(self::$mqttGameStateCache[$gameSn])) {
                        $gameState = self::$mqttGameStateCache[$gameSn];
                        $coinUpdateData = [
                            'cachedCoin' => $gameState['cachedCoin'],
                            'coinUsed' => $gameState['coinUsed']
                        ];
                        self::sendMessage($client_id, 'coin_update', 200, '金币状态更新', $coinUpdateData);
                        Log::write("发送游戏开始金币更新: " . json_encode($coinUpdateData), 'info');
                    }
                } else {
                    // 硬件端发起的游戏，需要找到对应的uniapp客户端
                    $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                    if ($uniappClientId) {
                        self::sendMessage($uniappClientId, 'update_game', 200, '游戏开始成功');

                        // 发送金币更新消息
                        if (isset(self::$mqttGameStateCache[$gameSn])) {
                            $gameState = self::$mqttGameStateCache[$gameSn];
                            $coinUpdateData = [
                                'cachedCoin' => $gameState['cachedCoin'],
                                'coinUsed' => $gameState['coinUsed']
                            ];
                            self::sendMessage($uniappClientId, 'coin_update', 200, '金币状态更新', $coinUpdateData);
                            Log::write("发送游戏开始金币更新: " . json_encode($coinUpdateData), 'info');
                        }
                    }
                }

            } else { //硬件端 发送过来的消息
                //硬件端 线下有人开始游戏，则通知uniapp端更新游戏数据
                $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                if ($uniappClientId) {
                    self::sendMessage($uniappClientId, 'update_game', 200, '更新数据');
                }
            }

            self::sendMessage($client_id,'startGame_back');
        } else {//失败
            $errorMsg = isset($result['msg']) ? $result['msg'] : '游戏开始失败';
            self::sendMessage($client_id,'startGame_back',400,$errorMsg);
        }
    }

    /**
     * 开始游戏结果逻辑处理
     * @Authod Jw
     * @Time 2025/5/5
     * @param $session
     * @param $data
     * @param string $type
     */
    private static function startGameResult($client_id,array $data,$gameSn)
    {
        Log::write("处理startGameResult: gameSn={$gameSn}, code={$data['code']}, status={$data['status']}", 'info');

        // 检查状态码，如果是401表示座位被占用，需要重置并重新开始游戏
        $code = $data['code'] ?? 200;
        if ($code == 401) {
            Log::write("检测到座位被占用(401) - 上次游戏流程异常结束: gameSn={$gameSn}", 'error');
            $number = $data['number'] ?? 1;
            Log::write("座位占用详情: gameSn={$gameSn}, 座位号={$number}, 硬件端返回401错误", 'error');

            // 清理已创建的游戏记录和状态缓存
            try {

                

                $message['type'] = 'endGame';
                $message['number'] = $number;
                $message['client_type'] = 'uniapp';
                $message['userId'] = 0;
                $message['requestId'] = $gameSn . '_ws_endGame_' . uniqid();

                // 根据游戏协议发送消息
                self::sendToGameByProtocol($gameSn, $message, self::CLIENT_TYPE_APP);

                


                $message['type'] = 'startGame';
                $message['number'] = $number;
                $message['client_type'] = 'uniapp';
                $message['userId'] = 0;
                $message['requestId'] = $gameSn . '_ws_startGame_' . uniqid();

                // 根据游戏协议发送消息
                self::sendToGameByProtocol($gameSn, $message, self::CLIENT_TYPE_APP);



            } catch (\Exception $e) {
                Log::write("处理座位占用异常: " . $e->getMessage()." ".$e->getTraceAsString(), 'error');
            }

        }

        $data['gameSn'] = $gameSn;
        $game_model = self::getGameModel();
        $result = $game_model->startGameResult($data);
        Log::write('开始游戏逻辑处理结果：'.json_encode($result), 'info');

        // 检查结果是否有效
        if (!$result || !is_array($result)) {
            $result = ['code' => 0, 'msg' => '游戏开始逻辑处理失败，返回结果无效'];
            Log::write('游戏开始逻辑处理结果无效，设置默认错误结果', 'error');
        }

        // 通知uniapp更新信息
        $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
        if ($uniappClientId) {
            $code = $result['code'] == 1 ? 200 : 400;
            $msg = isset($result['msg']) ? $result['msg'] : '处理失败';
            self::sendMessage($uniappClientId, 'update_game', $code, $msg);
        }

        // 返回给app
        self::sendMessage($client_id,'startGameResult_back');
    }

    /**
     * 结束游戏
     * @Authod Jw
     * @Time 2025/4/27
     * @param $client_id
     * @param array $data
     */
    private static function endGame($client_id,array $data,$gameSn,$client_type)
    {
        Log::write("endGame方法开始: client_id={$client_id}, gameSn={$gameSn}, client_type={$client_type}, data=".json_encode($data), 'info');

        // 检查数据库连接
        self::checkAndReconnectDb();

        // 检查游戏状态缓存中的协议类型，决定处理方式
        if (isset(self::$mqttGameStateCache[$gameSn])) {
            $gameStateProtocol = self::$mqttGameStateCache[$gameSn]['protocol'] ?? 'websocket';

            if ($gameStateProtocol === 'websocket') {
                // WebSocket协议：先发送endGame给硬件，等待确认后再修改数据库
                Log::write("WebSocket协议游戏结束，先发送endGame给硬件等待确认", 'info');

                if ($client_type === self::CLIENT_TYPE_UNIAPP) {
                    try {
                        // 从游戏状态缓存中获取正确的座位号
                        $number = $data['number'] ?? 1;
                        Log::write("【座位号调试】endGame开始，原始data: " . json_encode($data), 'info');
                        Log::write("【座位号调试】endGame初始座位号: {$number}", 'info');

                        // 检查游戏状态缓存
                        if (isset(self::$mqttGameStateCache[$gameSn])) {
                            Log::write("【座位号调试】找到游戏状态缓存: " . json_encode(self::$mqttGameStateCache[$gameSn]), 'info');
                            if (isset(self::$mqttGameStateCache[$gameSn]['number'])) {
                                $number = self::$mqttGameStateCache[$gameSn]['number'];
                                Log::write("【座位号调试】从游戏状态缓存获取座位号: {$number}", 'info');
                            } else {
                                Log::write("【座位号调试】游戏状态缓存中没有number字段", 'warning');
                            }
                        } else {
                            Log::write("【座位号调试】没有找到游戏状态缓存: {$gameSn}", 'warning');
                        }

                        $requestId = $gameSn . '_endGame_' . uniqid();
                        $message = [
                            'type' => 'endGame',
                            'number' => $number,
                            'client_type' => 'uniapp',
                            'userId' => $data['userId'] ?? null,
                            'requestId' => $requestId
                        ];

                        Log::write("【发送硬件消息】准备发送endGame给硬件: " . json_encode($message), 'info');

                        // 存储endGame请求信息，包含原始数据，等待endGameResult回调时处理
                        if (!isset(self::$webSocketEndGameCallbacks)) {
                            self::$webSocketEndGameCallbacks = [];
                        }
                        self::$webSocketEndGameCallbacks[$requestId] = [
                            'gameSn' => $gameSn,
                            'originalData' => $data, // 保存原始数据，用于后续调用Game::endGame
                            'client_id' => $client_id,
                            'timestamp' => time()
                        ];

                        self::sendToGameByProtocol($gameSn, $message, self::CLIENT_TYPE_APP);
                        Log::write("WebSocket发送endGame给硬件，等待endGameResult确认: requestId={$requestId}", 'info');

                        // 直接返回，等待硬件确认
                        return;

                    } catch (\Exception $e) {
                        Log::write("WebSocket发送endGame失败: " . $e->getMessage(), 'error');
                        // 发送失败，通知客户端
                        self::sendMessage($client_id, 'endGame_back', 500, 'endGame发送失败');
                        return;
                    }
                }
            }
        }

        // 非WebSocket协议或没有游戏状态缓存，按原有流程处理
        $data['gameSn'] = $gameSn;
        $game_model = self::getGameModel();
        Log::write('开始调用 Game::endGame 方法', 'info');
        $result = $game_model->endGame($data);
        Log::write('Game::endGame 方法调用完成', 'info');
        Log::write('结束游戏：'.json_encode($result), 'info');

        // 检查结果是否有效
        if (!$result || !is_array($result)) {
            $result = ['code' => 0, 'msg' => '游戏结束处理失败，返回结果无效'];
            Log::write('游戏结束结果无效，设置默认错误结果', 'error');
        }

        Log::write("检查结果代码: {$result['code']}", 'info');

        if ($result['code'] == 1) {//成功
            Log::write("进入成功分支，开始获取游戏信息", 'info');

            // 获取游戏信息，确定退币方式
            $gameInfo = self::getGameInfo($gameSn);
            Log::write("获取到游戏信息: " . json_encode($gameInfo), 'info');

            $returnCoinType = $gameInfo['return_coin_type'] ?? 'all'; // 默认为捕鱼机方式

            Log::write("游戏退币方式: {$returnCoinType}", 'info');

            // 获取应该返还的金币数
            $refundCoin = 0;
            $bonusCoin = 0; // 奖励金币（退币计数 + 彩金）

            if (isset($result['data']) && is_array($result['data'])) {
                $gameLogCoin = $result['data']['coin'] ?? 0;

                // 检查游戏状态缓存中的协议类型
                if (isset(self::$mqttGameStateCache[$gameSn])) {
                    $gameStateProtocol = self::$mqttGameStateCache[$gameSn]['protocol'] ?? 'websocket';

                    if ($gameStateProtocol === 'mqtt') {
                        // MQTT协议：使用异步处理方式，完全由processMqttGameEnd处理
                        Log::write("MQTT协议游戏结束，调用processMqttGameEnd异步处理，跳过当前流程", 'info');
                        self::processMqttGameEnd($gameSn);

                    // 发送响应给客户端
                    if ($client_type === self::CLIENT_TYPE_UNIAPP) {
                        // endGame_back消息将在processMqttGameEnd的异步回调中发送

                        // 发送通知给硬件端
                        try {
                            $message = [
                                'type' => 'endGame',
                                'number' => $result['data']['number'] ?? 1,
                                'client_type' => 'uniapp',
                                'userId' => $data['userId'] ?? null,
                                'requestId' => $gameSn . '_endGame_' . uniqid()
                            ];
                            self::sendToGameByProtocol($gameSn, $message, self::CLIENT_TYPE_APP);
                        } catch (\Exception $e) {
                            Log::write("MQTT结束游戏通知发送失败: " . $e->getMessage(), 'error');
                        }
                    }

                        Log::write("MQTT协议游戏结束处理完成，已跳过常规endGameResult流程", 'info');
                        return; // 直接返回，不执行后续的endGameResult流程
                    } else {
                        // WebSocket协议：发送endGame给硬件，等待endGameResult回调
                        Log::write("WebSocket协议游戏结束，发送endGame给硬件等待回调", 'info');

                        if ($client_type === self::CLIENT_TYPE_UNIAPP) {
                            try {
                                $requestId = $gameSn . '_endGame_' . uniqid();
                                $message = [
                                    'type' => 'endGame',
                                    'number' => $result['data']['number'] ?? 1,
                                    'client_type' => 'uniapp',
                                    'userId' => $data['userId'] ?? null,
                                    'requestId' => $requestId
                                ];

                                // 存储endGame请求信息，等待endGameResult回调
                                if (!isset(self::$webSocketEndGameCallbacks)) {
                                    self::$webSocketEndGameCallbacks = [];
                                }
                                self::$webSocketEndGameCallbacks[$requestId] = [
                                    'gameSn' => $gameSn,
                                    'gameLogId' => $result['data']['id'] ?? 0,
                                    'number' => $result['data']['number'] ?? 1,
                                    'client_id' => $client_id,
                                    'timestamp' => time()
                                ];

                                self::sendToGameByProtocol($gameSn, $message, self::CLIENT_TYPE_APP);
                                Log::write("WebSocket发送endGame给硬件，等待endGameResult回调: requestId={$requestId}", 'info');

                            } catch (\Exception $e) {
                                Log::write("WebSocket发送endGame失败: " . $e->getMessage(), 'error');
                            }
                        }

                        Log::write("WebSocket协议游戏结束处理完成，等待endGameResult回调", 'info');
                        return; // 直接返回，等待endGameResult回调
                    }
                } else {
                    // 没有游戏状态缓存，按WebSocket协议处理
                    Log::write("没有游戏状态缓存，按WebSocket协议处理", 'info');

                    if ($client_type === self::CLIENT_TYPE_UNIAPP) {
                        try {
                            $requestId = $gameSn . '_endGame_' . uniqid();
                            $message = [
                                'type' => 'endGame',
                                'number' => $result['data']['number'] ?? 1,
                                'client_type' => 'uniapp',
                                'userId' => $data['userId'] ?? null,
                                'requestId' => $requestId
                            ];

                            // 存储endGame请求信息
                            if (!isset(self::$webSocketEndGameCallbacks)) {
                                self::$webSocketEndGameCallbacks = [];
                            }
                            self::$webSocketEndGameCallbacks[$requestId] = [
                                'gameSn' => $gameSn,
                                'gameLogId' => $result['data']['id'] ?? 0,
                                'number' => $result['data']['number'] ?? 1,
                                'client_id' => $client_id,
                                'timestamp' => time()
                            ];

                            self::sendToGameByProtocol($gameSn, $message, self::CLIENT_TYPE_APP);
                            Log::write("WebSocket发送endGame给硬件: requestId={$requestId}", 'info');

                        } catch (\Exception $e) {
                            Log::write("WebSocket发送endGame失败: " . $e->getMessage(), 'error');
                        }
                    }

                    return; // 等待endGameResult回调
                }

                // 以下代码不应该执行到，因为WebSocket协议都应该等待回调
                Log::write("警告：WebSocket协议不应该执行到这里", 'warning');
            }

            // 计算总返还金币数
            if ($returnCoinType === 'all') {
                // 捕鱼机方式：直接返回游戏机剩余金币（临时）
                $totalRefundCoin = $refundCoin;
                self::realTimeLog("捕鱼机总退币: {$totalRefundCoin} (临时，等待endGameResult调整)");
            } else {
                // 推币机方式：缓存金币 + 奖励金币
                $totalRefundCoin = $refundCoin + $bonusCoin;
                self::realTimeLog("推币机总退币: {$totalRefundCoin} (缓存:{$refundCoin} + 奖励:{$bonusCoin})");
            }

            // 调用endGameResult完成游戏状态更新为3（已完成）并返还金币
            $endGameResultData = [
                'gameSn' => $gameSn,
                'code' => 200,
                'coin' => $totalRefundCoin, // 返还总金币数（缓存 + 奖励）
                'number' => $result['data']['number'] ?? 1
            ];
            Log::write('开始调用 endGameResult 方法', 'info');
            $endGameResult = $game_model->endGameResult($endGameResultData);
            Log::write('endGameResult 方法调用完成', 'info');
            if ($returnCoinType === 'all') {
                Log::write("捕鱼机结束游戏状态更新结果：".json_encode($endGameResult)." 返还金币：{$totalRefundCoin} (临时，等待endGameResult调整)", 'info');
            } else {
                Log::write("推币机结束游戏状态更新结果：".json_encode($endGameResult)." 返还金币：{$totalRefundCoin} (缓存:{$refundCoin} + 奖励:{$bonusCoin})", 'info');
            }

            // 清除MQTT游戏状态缓存
            if (isset(self::$mqttGameStateCache[$gameSn])) {
                unset(self::$mqttGameStateCache[$gameSn]);
                // 取消订阅MQTT主题
                self::unsubscribeGameTopic($gameSn);
                Log::write('清除MQTT游戏状态缓存并取消订阅：'.$gameSn, 'info');
            }

            // 清除游戏超时监控缓存（WebSocket和MQTT游戏都需要）
            self::clearGameTimeoutMonitor($gameSn);
            Log::write('清除游戏超时监控缓存：'.$gameSn, 'info');

            $message = [];
            if ($client_type === self::CLIENT_TYPE_UNIAPP ) {//uniapp端 发送过来的消息
                //发送消息给硬件端
                try {
                    $message['type'] = 'endGame';
                    $message['number'] = $result['data']['number'];
                    $message['client_type'] = 'uniapp';
                    $message['userId'] = $data['userId'] ?? null;
                    $message['requestId'] = $gameSn . '_endGame_' . uniqid();

                    // 根据游戏协议发送消息
                    self::sendToGameByProtocol($gameSn, $message, self::CLIENT_TYPE_APP);
                } catch (\Exception $e) {
                    // MQTT协议发送失败时记录日志但不中断流程
                    $gameInfo = self::getGameInfo($gameSn);
                    $protocol = ($gameInfo && isset($gameInfo['control_protocol']) && $gameInfo['control_protocol'] === self::PROTOCOL_MQTT)
                               ? self::PROTOCOL_MQTT : self::PROTOCOL_WEBSOCKET;
                    if ($protocol === self::PROTOCOL_MQTT) {
                        Log::write("MQTT结束游戏消息发送失败，但继续处理: " . $e->getMessage(), 'error');
                    } else {
                        throw $e; // WebSocket协议失败时抛出异常
                    }
                }

                // 发送update_game消息给uniapp端
                if ($client_type === self::CLIENT_TYPE_UNIAPP) {
                    // uniapp端发起的游戏结束，直接发送给当前客户端
                    $updateData = [
                        'coin' => 0,
                        'user_id' => $result['data']['user_id'] ?? 0
                    ];
                    self::sendMessage($client_id, 'update_game', 200, '游戏结束成功', $updateData);
                } else {
                    // 硬件端发起的游戏结束，需要找到对应的uniapp客户端
                    $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                    if ($uniappClientId) {
                        $updateData = [
                            'coin' => 0,
                            'user_id' => $result['data']['user_id'] ?? 0
                        ];
                        self::sendMessage($uniappClientId, 'update_game', 200, '游戏结束成功', $updateData);
                    }
                }

            } else { //硬件端 发送过来的消息
                //硬件端 线下有人结束游戏，则通知uniapp端更新游戏数据
                $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                if ($uniappClientId) {
                    self::sendMessage($uniappClientId, 'update_game', 200, '更新数据');
                }
            }

            self::sendMessage($client_id,'endGame_back');
        } else {
            $errorMsg = isset($result['msg']) ? $result['msg'] : '游戏结束失败';
            self::sendMessage($client_id,'endGame_back',400,$errorMsg);
        }
    }

    /**
     * 结束游戏结果逻辑处理
     * @Authod Jw
     * @Time 2025/5/5
     * @param $session
     * @param $data
     * @param string $type
     */
    private static function endGameResult($client_id,array $data,$gameSn)
    {
        self::realTimeLog("处理endGameResult: gameSn={$gameSn}, coin={$data['coin']}, code={$data['code']}");
        Log::write("处理endGameResult: gameSn={$gameSn}, 游戏机返回金币={$data['coin']}, 状态码={$data['code']}", 'info');

        // 检查状态码，如果是411表示游戏正在进行，不能退出
        $code = $data['code'] ?? 200;
        if ($code == 411) {
            Log::write("游戏正在进行中，取消退出操作: gameSn={$gameSn}", 'info');

            // 检查是否有WebSocket endGame回调信息，需要清除回调
            $requestId = $data['requestId'] ?? '';
            if ($requestId && isset(self::$webSocketEndGameCallbacks[$requestId])) {
                unset(self::$webSocketEndGameCallbacks[$requestId]);
                Log::write("清除WebSocket endGame回调信息: requestId={$requestId}", 'info');
            }

            // 通知uniapp端游戏正在进行，请稍后再试，并取消结算弹窗
            $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
            if ($uniappClientId) {
                $responseData = [
                    'action' => 'cancel_settlement', // 取消结算弹窗
                    'continue_game' => true // 继续游戏
                ];
                self::sendMessage($uniappClientId, 'endGame_back', 411, '游戏正在进行中，请稍后再尝试退出', $responseData);
            }

            // 返回给硬件端
            self::sendMessage($client_id, 'endGameResult_back');
            return;
        }

        // 获取游戏信息，确定退币方式
        $gameInfo = self::getGameInfo($gameSn);
        $returnCoinType = $gameInfo['return_coin_type'] ?? 'all';

        self::realTimeLog("endGameResult 游戏退币方式: {$returnCoinType}");
        Log::write("endGameResult 游戏退币方式: {$returnCoinType}", 'info');

        // 使用统一的返分处理方法
        $actualCoin = $data['coin']; // 游戏机的实际剩余金币

        self::realTimeLog("endGameResult: 游戏机返回金币 {$actualCoin}");
        Log::write("endGameResult: 游戏机返回金币 {$actualCoin}", 'info');

        // 检查是否有WebSocket endGame回调信息
        $requestId = $data['requestId'] ?? '';
        $gameLogId = 0;

        if ($requestId && isset(self::$webSocketEndGameCallbacks[$requestId])) {
            $callbackInfo = self::$webSocketEndGameCallbacks[$requestId];
            Log::write("找到WebSocket endGame回调信息: requestId={$requestId}", 'info');

            // 现在硬件确认可以结束游戏，调用Game::endGame修改数据库
            if (isset($callbackInfo['originalData'])) {
                $originalData = $callbackInfo['originalData'];
                $originalData['gameSn'] = $gameSn;

                Log::write("硬件确认游戏结束，开始调用Game::endGame修改数据库", 'info');
                $game_model = self::getGameModel();
                $endGameResult = $game_model->endGame($originalData);
                Log::write("Game::endGame调用完成: " . json_encode($endGameResult), 'info');

                if ($endGameResult && $endGameResult['code'] == 1) {
                    $gameLogId = $endGameResult['data']['id'] ?? 0;
                    Log::write("游戏结束成功，gameLogId={$gameLogId}", 'info');
                } else {
                    Log::write("游戏结束失败: " . ($endGameResult['msg'] ?? '未知错误'), 'error');
                    // 游戏结束失败，通知客户端
                    $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
                    if ($uniappClientId) {
                        self::sendMessage($uniappClientId, 'endGame_back', 500, '游戏结束处理失败');
                    }
                    self::sendMessage($client_id, 'endGameResult_back');
                    return;
                }
            } else {
                // 兼容旧版本，使用回调信息中的gameLogId
                $gameLogId = $callbackInfo['gameLogId'] ?? 0;
                Log::write("使用回调信息中的gameLogId: {$gameLogId}", 'info');
            }

            // 清除回调信息
            unset(self::$webSocketEndGameCallbacks[$requestId]);
        } else {
            Log::write("未找到WebSocket endGame回调信息: requestId={$requestId}", 'warning');
            return;
        }

        // 调用统一的返分处理方法，传递gameLogId
        $result = self::processUnifiedEndGameResult($gameSn, $actualCoin, $data, $gameLogId);

        if ($result['success']) {
            // 发送endGame_back消息给uniapp端
            $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
            if ($uniappClientId) {
                $endGameData = [
                    'won_coins' => $result['total_coin'],
                    'used_coins' => $result['used_coins']
                ];
                self::sendMessage($uniappClientId, 'endGame_back', 200, '游戏已成功结束！赢得金币：' . $endGameData['won_coins'], $endGameData);
            }

            // 通知uniapp更新金币信息
            $uniappClientId = self::getClientIdByGameSn($gameSn, self::CLIENT_TYPE_UNIAPP);
            if ($uniappClientId) {
                $updateData = $result['game_result']['data'] ?? [];
                self::sendMessage($uniappClientId, 'update_game', 200, $result['message'], $updateData);
            }

            Log::write("endGameResult处理完成: " . $result['message'], 'info');
        } else {
            Log::write("endGameResult处理失败: " . $result['message'], 'error');
        }

        // 返回给app
        self::sendMessage($client_id, 'endGameResult_back');
    }

    /**
     * 处理服务器ping
     */
    private static function handlePing($client_id)
    {
        // 更新最后ping时间
        $session = Gateway::getSession($client_id);

        // 安全检查：确保session是数组
        if (is_array($session)) {
            $session['last_ping_time'] = time();
            Gateway::setSession($client_id, $session);

            // 计算连接持续时间
            $connectTime = $session['connect_time'] ?? time();
            $duration = time() - $connectTime;

            Log::write("响应ping消息: client_id={$client_id}, 连接时长={$duration}秒", 'info');
        } else {
            Log::write("handlePing: session数据无效, client_id={$client_id}", 'warning');
        }

        // 响应pong消息
        self::sendMessage($client_id, 'pong');
    }

    /**
     * 处理心跳
     */
    private static function handleHeartbeat($client_id)
    {
        self::sendMessage($client_id,'heartbeat_back');

        // 更新游戏超时监控中的最后心跳时间
        if (isset(self::$gameTimeoutCache[$client_id])) {
            self::$gameTimeoutCache[$client_id]['lastHeartbeatTime'] = time();
            Log::write("更新游戏心跳时间: client_id={$client_id}", 'info');
        }
    }

    /**
     * 处理客户端pong响应
     */
    private static function handlePong($client_id)
    {
        // 更新最后活跃时间
        $session = Gateway::getSession($client_id);
        if ($session) {
            $session['last_active'] = time();
            $session['last_ping_time'] = time();
            Gateway::setSession($client_id, $session);

            // 更新缓存
            self::$sessionCache[$client_id] = $session;
        }

        Log::write("收到客户端pong响应: client_id={$client_id}", 'info');
    }

    /**
     * 处理快速响应消息（不阻塞）
     */
    private static function handleQuickMessage($client_id, $data, $gameSn)
    {
        switch ($data['type']) {
            case 'heartbeat':
                self::handleHeartbeat($client_id);
                break;
            case 'command':
                self::sendCommand($client_id, $data, $gameSn);
                break;
            case 'ping':
                self::handlePing($client_id);
                break;
            case 'pong':
                self::handlePong($client_id);
                break;
            case 'GameStatus':
                self::handleGameStatus($client_id, $data, $gameSn);
                break;
        }
    }

    /**
     * 处理GameStatus消息（硬件端状态回复）
     */
    private static function handleGameStatus($client_id, $data, $gameSn)
    {
        try {
            $code = $data['code'] ?? 0;
            $number = $data['number'] ?? 0;
            $requestId = $data['requestId'] ?? '';

            Log::write("收到GameStatus消息: client_id={$client_id}, gameSn={$gameSn}, code={$code}, number={$number}, requestId={$requestId}", 'info');

            // 处理501错误码：指定座位已经没分了
            if ($code == 501) {
                Log::write("座位{$number}没分了，重新启动超时监控: gameSn={$gameSn}", 'info');

                // 重新启动该座位的超时监控
                self::initGameTimeoutMonitor($gameSn, $number, $client_id);

                // 通知前端座位没分
                self::sendMessage($client_id, 'GameStatus', $code, "座位{$number}已经没分了");
            } else {
                // 其他状态码直接转发给前端
                self::sendMessage($client_id, 'GameStatus', $code, $data['message'] ?? "游戏状态更新");
            }

        } catch (\Exception $e) {
            Log::write("处理GameStatus消息失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 异步处理数据库操作消息
     */
    private static function handleDatabaseMessage($client_id, $data, $gameSn, $client_type)
    {
        Log::write("异步处理开始: client_id={$client_id}, type={$data['type']}, gameSn={$gameSn}", 'info');

        try {
            // 检查连接是否还存在
            if (!\GatewayWorker\Lib\Gateway::isOnline($client_id)) {
                Log::write("客户端 {$client_id} 已断开，跳过消息处理", 'info');
                return;
            }

            switch ($data['type']) {
                case 'startGame':
                    self::realTimeLog("=== 开始处理startGame异步消息 === client_id={$client_id}, gameSn={$gameSn}");
                    self::startGame($client_id, $data, $gameSn, $client_type);
                    self::realTimeLog("=== startGame异步消息处理完成 === client_id={$client_id}, gameSn={$gameSn}");
                    break;
                case 'endGame':
                    Log::write("=== 开始处理endGame异步消息 === client_id={$client_id}, gameSn={$gameSn}", 'info');
                    self::endGame($client_id, $data, $gameSn, $client_type);
                    Log::write("=== endGame异步消息处理完成 === client_id={$client_id}, gameSn={$gameSn}", 'info');
                    break;
                case 'startGameResult':
                    self::startGameResult($client_id, $data, $gameSn);
                    break;
                case 'endGameResult':
                    self::endGameResult($client_id, $data, $gameSn);
                    break;
                case 'queryGameState':
                    self::handleQueryGameState($client_id, $data, $gameSn, $client_type);
                    break;
                case 'putInCoins':
                    self::handlePutInCoins($client_id, $data, $gameSn, $client_type);
                    break;
                case 'putInCoinsResult':
                    self::handlePutInCoinsResult($client_id, $data, $gameSn, $client_type);
                    break;
            }
        } catch (\Exception $e) {
            Log::write("异步消息处理异常: " . $e->getMessage(), 'error');

            // 检查连接是否还存在再发送错误消息
            if (\GatewayWorker\Lib\Gateway::isOnline($client_id)) {
                self::sendMessage($client_id, null, 400, $e->getMessage());
            }
        }
    }

    /**
     * 当用户断开连接时触发
     * @param int $client_id 连接id
     */
    public static function onClose($client_id)
    {
        // 从缓存中取 session
        if (isset(self::$sessionCache[$client_id])) {
            $session = self::$sessionCache[$client_id];

            // 计算连接持续时间
            $connectTime = $session['connect_time'] ?? time();
            $duration = time() - $connectTime;
            $lastPingTime = $session['last_ping_time'] ?? $connectTime;
            $timeSinceLastPing = time() - $lastPingTime;

            if ($session['client_type'] === self::CLIENT_TYPE_APP && $session['gameSn']) {
                //修改设备为离线
                $game_model = self::getGameModel();
                $result = $game_model->updateGameOnlineStatus($session['gameSn'],0);
                Log::write('修改设备离线：'.json_encode($result), 'info');
            }

            // 判断断开原因
            $disconnectReason = '';
            if ($duration < 10) {
                $disconnectReason = '短连接断开';
            } elseif ($timeSinceLastPing > 60) {
                $disconnectReason = '心跳超时断开';
            } else {
                $disconnectReason = '正常断开';
            }

            Log::write("连接断开: " . json_encode([
                    'client_id' => $client_id,
                    'client_type' => $session['client_type'],
                    'gameSn' => $session['gameSn'],
                    'duration' => $duration,
                    'reason' => $disconnectReason,
                    'time_since_last_ping' => $timeSinceLastPing
                ]), 'info');

            // 处理游戏超时监控缓存
            if (isset(self::$gameTimeoutCache[$client_id])) {
                // 如果是uniapp客户端且正在游戏中，不立即清除，而是标记为断线状态
                if ($session['client_type'] === self::CLIENT_TYPE_UNIAPP) {
                    self::$gameTimeoutCache[$client_id]['disconnected'] = true;
                    self::$gameTimeoutCache[$client_id]['disconnectTime'] = time();
                    Log::write("玩家断线，标记断线状态等待重连: client_id={$client_id}, gameSn={$session['gameSn']}", 'info');
                } else {
                    // 非uniapp客户端直接清除
                    unset(self::$gameTimeoutCache[$client_id]);
                    Log::write("清除游戏超时监控缓存: client_id={$client_id}", 'info');
                }
            }

            // 清除会话缓存
            unset(self::$sessionCache[$client_id]);
        } else {
            Log::write('断开连接id='.json_encode($client_id).' 断开连接ip='.$_SERVER['REMOTE_ADDR'], 'info');
        }
    }

    /**
     * 处理查询游戏状态请求
     */
    private static function handleQueryGameState($client_id, $data, $gameSn, $client_type)
    {
        try {
            Log::write("处理查询游戏状态请求: client_id={$client_id}, gameSn={$gameSn}", 'info');

            $response = [
                'type' => 'queryGameState_back',
                'code' => 200,
                'status' => 'success',
                'data' => [
                    'cachedCoin' => 0,
                    'gameLogId' => null,
                    'gameStatus' => 'idle', // idle, playing, ended
                    'coinUsed' => 0
                ]
            ];

            // 检查MQTT游戏状态缓存
            if (isset(self::$mqttGameStateCache[$gameSn])) {
                $gameState = self::$mqttGameStateCache[$gameSn];
                $response['data'] = [
                    'cachedCoin' => $gameState['cachedCoin'] ?? 0,
                    'gameLogId' => $gameState['gameLogId'] ?? null,
                    'gameStatus' => 'playing',
                    'coinUsed' => $gameState['coinUsed'] ?? 0,
                    'userId' => $gameState['userId'] ?? null
                ];
                Log::write("找到游戏状态缓存: " . json_encode($response['data']), 'info');
            } else {
                Log::write("未找到游戏状态缓存，返回默认状态", 'info');
                // 临时修复：检查并重置异常的座位状态
                self::checkAndResetSeatStatus($gameSn);
            }

            // 直接发送完整响应
            Gateway::sendToClient($client_id, json_encode([
                'code' => $response['code'],
                'type' => 'queryGameState_back',
                'status' => $response['status'],
                'data' => $response['data']
            ]));

        } catch (\Exception $e) {
            Log::write("查询游戏状态失败: " . $e->getMessage(), 'error');
            self::sendMessage($client_id, 'queryGameState_back', 400, 'error', '查询游戏状态失败');
        }
    }

    /**
     * 检查并重置异常的座位状态
     */
    private static function checkAndResetSeatStatus($gameSn)
    {
        try {
            // 检查数据库连接
            self::checkAndReconnectDb();

            // 获取游戏信息
            $game = Db::name('game')->where('sn', $gameSn)->find();
            if (!$game) {
                return;
            }

            // 检查是否有游戏状态缓存
            if (isset(self::$mqttGameStateCache[$gameSn])) {
                // 有游戏状态缓存，说明游戏正在进行中，不重置座位
                return;
            }

            // 检查是否有进行中的游戏记录
            $activeGameLog = Db::name('game_log')
                ->where('game_id', $game['id'])
                ->where('status', 1) // 进行中
                ->find();

            if ($activeGameLog) {
                // 检查游戏记录是否超时（超过30分钟认为异常）
                $currentTime = time();
                $gameStartTime = strtotime($activeGameLog['createtime']);
                $timeDiff = $currentTime - $gameStartTime;

                if ($timeDiff > 1800) { // 30分钟
                    Log::write("发现超时的游戏记录，自动结束: gameLogId={$activeGameLog['id']}, 超时时间={$timeDiff}秒", 'warning');

                    // 自动结束超时的游戏记录
                    Db::name('game_log')
                        ->where('id', $activeGameLog['id'])
                        ->update([
                            'status' => 2, // 已结束
                            'end_time' => $currentTime, // 使用时间戳而不是日期字符串
                            'updatetime' => $currentTime
                        ]);
                } else {
                    // 游戏记录正常，不重置座位
                    return;
                }
            }

            // 没有游戏状态缓存且没有进行中的游戏记录，重置所有座位状态
            $resetCount = Db::name('game_seat')
                ->where('game_id', $game['id'])
                ->where('status', 1)
                ->update([
                    'status' => 0,
                    'updatetime' => time()
                ]);

            if ($resetCount > 0) {
                Log::write("重置游戏 {$gameSn} 的异常座位状态，重置数量: {$resetCount}", 'info');
            }

        } catch (\Exception $e) {
            Log::write("检查并重置座位状态失败: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 安全的数据库操作包装器
     */
    private static function safeDbOperation($operation)
    {
        $maxRetries = 3;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                self::checkAndReconnectDb();
                return $operation();
            } catch (\Exception $e) {
                $retryCount++;
                Log::write("数据库操作失败，重试 {$retryCount}/{$maxRetries}: " . $e->getMessage(), 'error');

                if ($retryCount >= $maxRetries) {
                    throw $e;
                }

                // 等待一段时间后重试
                usleep(100000); // 100ms
            }
        }
    }

    /**
     * 统一处理投币请求（MQTT和WebSocket共用）
     */
    private static function handlePutInCoins($client_id, $data, $gameSn, $client_type)
    {
        try {
            Log::write("处理投币请求: client_id={$client_id}, gameSn={$gameSn}, data=" . json_encode($data), 'info');

            // 检查必要参数
            if (!isset($data['number']) || !isset($data['coin'])) {
                throw new \Exception('缺少必要参数: number 或 coin');
            }

            $number = $data['number'];
            $coin = $data['coin'];
            $requestId = $data['requestId'] ?? '';

            // 检查游戏状态缓存
            if (!isset(self::$mqttGameStateCache[$gameSn])) {
                throw new \Exception('游戏状态缓存不存在，请先开始游戏');
            }

            $gameState = &self::$mqttGameStateCache[$gameSn];
            $protocol = $gameState['protocol'] ?? 'websocket';

            // 获取座位倍率来计算实际花费
            $gameInfo = self::getGameInfo($gameSn);
            $gameLogData = Db::name('game_log')
                ->where('game_id', $gameInfo['id'])
                ->where('number', $number)
                ->where('status', 'in', [1, 2])
                ->order('id desc')
                ->find();
            $seat_multiplier = $gameLogData['seat_multiplier'] ?? 1.00;

            // 计算实际花费的金币（投币数 × 倍率）
            $actualCost = $coin * $seat_multiplier;

            // 检查金币是否足够
            if ($gameState['cachedCoin'] < $actualCost) {
                throw new \Exception("金币不足，需要{$actualCost}个金币，当前缓存{$gameState['cachedCoin']}个金币");
            }

            Log::write("{$protocol}投币预检查通过: 投币{$coin}个，实际花费{$actualCost}个金币(倍率{$seat_multiplier})，等待硬件确认", 'info');

            // WebSocket协议：发送投币指令给硬件，等待回调确认
            if ($protocol === 'websocket') {
                // 发送投币指令给硬件
                $hardwareMessage = [
                    'type' => 'putInCoins',
                    'number' => $number,
                    'coin' => $coin,
                    'requestId' => $requestId
                ];

                // 存储投币请求信息，等待硬件确认
                if (!isset(self::$webSocketCoinCallbacks)) {
                    self::$webSocketCoinCallbacks = [];
                }
                if (!isset(self::$webSocketCoinCallbacks[$gameSn])) {
                    self::$webSocketCoinCallbacks[$gameSn] = [];
                }

                self::$webSocketCoinCallbacks[$gameSn][$requestId] = [
                    'client_id' => $client_id,
                    'gameSn' => $gameSn,
                    'coin' => $coin,
                    'number' => $number,
                    'actualCost' => $actualCost,
                    'timestamp' => time()
                ];

                // 发送给硬件
                self::sendToGameByProtocol($gameSn, $hardwareMessage, self::CLIENT_TYPE_APP);
                Log::write("WebSocket投币指令已发送给硬件，等待确认: requestId={$requestId}", 'info');

            } else {
                // MQTT协议：发送投币指令给MQTT硬件，等待回调确认
                Log::write("MQTT协议投币处理开始: 投币{$coin}个", 'info');

                // 存储MQTT投币回调信息
                if (!isset(self::$mqttCoinCallbacks)) {
                    self::$mqttCoinCallbacks = [];
                }
                if (!isset(self::$mqttCoinCallbacks[$gameSn])) {
                    self::$mqttCoinCallbacks[$gameSn] = [];
                }

                self::$mqttCoinCallbacks[$gameSn][$requestId] = [
                    'client_id' => $client_id,
                    'gameSn' => $gameSn,
                    'coin' => $coin,
                    'number' => $number,
                    'actualCost' => $actualCost,
                    'timestamp' => time(),
                    'callback' => function($success) use ($client_id, $gameSn, $coin, $number, $actualCost, $requestId) {
                        if ($success) {
                            // 硬件确认成功，使用统一的投币处理逻辑
                            $result = self::processUnifiedCoinInput($gameSn, $coin, $number);

                            if ($result['code'] == 200) {
                                // 投币成功，发送成功响应给客户端
                                $responseData = [
                                    'type' => 'putInCoinsResult',
                                    'code' => 200,
                                    'order_id' => time() . rand(1000, 9999),
                                    'requestId' => $requestId,
                                    'coin' => $coin,
                                    'message' => '投币成功'
                                ];

                                if (\GatewayWorker\Lib\Gateway::isOnline($client_id)) {
                                    self::sendMessage($client_id, 'putInCoinsResult', 200, '投币成功', $responseData);
                                }

                                Log::write("MQTT投币成功: requestId={$requestId}, coin={$coin}, actualCost={$actualCost}", 'info');

                            } else {
                                // 投币处理失败
                                $responseData = [
                                    'type' => 'putInCoinsResult',
                                    'code' => 400,
                                    'requestId' => $requestId,
                                    'message' => $result['message']
                                ];

                                if (\GatewayWorker\Lib\Gateway::isOnline($client_id)) {
                                    self::sendMessage($client_id, 'putInCoinsResult', 400, $result['message'], $responseData);
                                }

                                Log::write("MQTT投币处理失败: requestId={$requestId}, 原因=" . $result['message'], 'error');
                            }
                        } else {
                            // 硬件拒绝投币
                            $responseData = [
                                'type' => 'putInCoinsResult',
                                'code' => 400,
                                'requestId' => $requestId,
                                'message' => 'MQTT硬件拒绝投币'
                            ];

                            if (\GatewayWorker\Lib\Gateway::isOnline($client_id)) {
                                self::sendMessage($client_id, 'putInCoinsResult', 400, 'MQTT硬件拒绝投币', $responseData);
                            }

                            Log::write("MQTT投币被硬件拒绝: requestId={$requestId}", 'error');
                        }

                        // 清除回调缓存
                        if (isset(self::$mqttCoinCallbacks[$gameSn][$requestId])) {
                            unset(self::$mqttCoinCallbacks[$gameSn][$requestId]);
                        }
                    }
                ];

                // 发送MQTT投币指令
                self::sendMqttCoinCommand($gameSn, $coin, $requestId);
                Log::write("MQTT投币指令已发送: requestId={$requestId}, coin={$coin}", 'info');
            }

        } catch (\Exception $e) {
            Log::write("WebSocket投币处理异常: " . $e->getMessage(), 'error');

            // 发送错误响应
            $responseData = [
                'type' => 'putInCoinsResult',
                'code' => 500,
                'requestId' => $data['requestId'] ?? '',
                'message' => $e->getMessage()
            ];

            if (\GatewayWorker\Lib\Gateway::isOnline($client_id)) {
                self::sendMessage($client_id, 'putInCoinsResult', 500, $e->getMessage(), $responseData);
            }
        }
    }



    /**
     * 统一的投币处理逻辑（MQTT和WebSocket共用）
     * @param string $gameSn 游戏序列号
     * @param int $coinNum 投币数量
     * @param int $number 座位号
     * @return array 处理结果
     */
    private static function processUnifiedCoinInput($gameSn, $coinNum, $number = 1)
    {
        try {
            // 检查游戏状态缓存
            if (!isset(self::$mqttGameStateCache[$gameSn])) {
                throw new \Exception('游戏状态缓存不存在，请先开始游戏');
            }

            $gameState = &self::$mqttGameStateCache[$gameSn];

            // 获取座位倍率来计算实际花费
            $gameInfo = self::getGameInfo($gameSn);
            $gameLogData = Db::name('game_log')
                ->where('game_id', $gameInfo['id'])
                ->where('number', $number)
                ->where('status', 'in', [1, 2])
                ->order('id desc')
                ->find();
            $seat_multiplier = $gameLogData['seat_multiplier'] ?? 1.00;

            // 计算实际花费的金币（投币数 × 倍率）
            $actualCost = $coinNum * $seat_multiplier;

            // 检查金币是否足够
            if ($gameState['cachedCoin'] < $actualCost) {
                throw new \Exception("金币不足，需要{$actualCost}个金币，当前缓存{$gameState['cachedCoin']}个金币");
            }

            // 更新缓存金币状态
            $gameState['cachedCoin'] -= $actualCost;
            $gameState['coinUsed'] += $actualCost;

            $protocol = $gameState['protocol'] ?? 'unknown';
            Log::write("{$protocol}投币成功: 投币{$coinNum}个，实际花费{$actualCost}个金币(倍率{$seat_multiplier})，剩余缓存金币 {$gameState['cachedCoin']}, 累计已花费 {$gameState['coinUsed']}个金币", 'info');

            // 发送金币更新消息
            $uid = self::getUid($gameSn, self::CLIENT_TYPE_UNIAPP);
            if ($uid) {
                $coinUpdateMessage = [
                    'type' => 'coin_update',
                    'code' => 200,
                    'cachedCoin' => $gameState['cachedCoin'],
                    'coinUsed' => $gameState['coinUsed'],
                    'userCoin' => $gameState['userCoin']
                ];
                Gateway::sendToUid($uid, json_encode($coinUpdateMessage));
                Log::write("{$protocol}发送金币更新: " . json_encode($coinUpdateMessage), 'info');
            }

            return [
                'code' => 200,
                'message' => '投币成功',
                'data' => [
                    'coinNum' => $coinNum,
                    'actualCost' => $actualCost,
                    'cachedCoin' => $gameState['cachedCoin'],
                    'coinUsed' => $gameState['coinUsed']
                ]
            ];

        } catch (\Exception $e) {
            Log::write("统一投币处理失败: " . $e->getMessage(), 'error');
            return [
                'code' => 400,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 处理WebSocket投币回调结果
     */
    private static function handlePutInCoinsResult($client_id, $data, $gameSn, $client_type)
    {
        try {
            Log::write("处理WebSocket投币回调: client_id={$client_id}, gameSn={$gameSn}, data=" . json_encode($data), 'info');

            $requestId = $data['requestId'] ?? '';
            $code = $data['code'] ?? 500;

            // 检查是否有对应的投币请求
            if (!isset(self::$webSocketCoinCallbacks[$gameSn][$requestId])) {
                Log::write("未找到对应的投币请求: requestId={$requestId}", 'warning');
                return;
            }

            $coinRequest = self::$webSocketCoinCallbacks[$gameSn][$requestId];
            $originalClientId = $coinRequest['client_id'];
            $coin = $coinRequest['coin'];
            $actualCost = $coinRequest['actualCost'];

            // 清除回调缓存
            unset(self::$webSocketCoinCallbacks[$gameSn][$requestId]);

            if ($code == 200) {
                // 硬件确认成功，执行实际的投币扣除
                $result = self::processUnifiedCoinInput($gameSn, $coin, $coinRequest['number']);

                if ($result['code'] == 200) {
                    // 投币成功，发送成功响应给原始客户端
                    $responseData = [
                        'type' => 'putInCoinsResult',
                        'code' => 200,
                        'order_id' => $data['order_id'] ?? (time() . rand(1000, 9999)),
                        'requestId' => $requestId,
                        'coin' => $coin,
                        'message' => '投币成功'
                    ];

                    if (\GatewayWorker\Lib\Gateway::isOnline($originalClientId)) {
                        self::sendMessage($originalClientId, 'putInCoinsResult', 200, '投币成功', $responseData);
                    }

                    Log::write("WebSocket投币成功: requestId={$requestId}, coin={$coin}, actualCost={$actualCost}", 'info');

                } else {
                    // 投币处理失败
                    $responseData = [
                        'type' => 'putInCoinsResult',
                        'code' => 400,
                        'requestId' => $requestId,
                        'message' => $result['message']
                    ];

                    if (\GatewayWorker\Lib\Gateway::isOnline($originalClientId)) {
                        self::sendMessage($originalClientId, 'putInCoinsResult', 400, $result['message'], $responseData);
                    }

                    Log::write("WebSocket投币处理失败: requestId={$requestId}, 原因=" . $result['message'], 'error');
                }

            } else {
                // 硬件拒绝投币
                $responseData = [
                    'type' => 'putInCoinsResult',
                    'code' => $code,
                    'requestId' => $requestId,
                    'message' => $data['message'] ?? '硬件拒绝投币'
                ];

                if (\GatewayWorker\Lib\Gateway::isOnline($originalClientId)) {
                    self::sendMessage($originalClientId, 'putInCoinsResult', $code, $responseData['message'], $responseData);
                }

                Log::write("WebSocket投币被硬件拒绝: requestId={$requestId}, code={$code}", 'error');
            }

        } catch (\Exception $e) {
            Log::write("WebSocket投币回调处理异常: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 统一的返分处理方法（MQTT和WebSocket共用）
     */
    private static function processUnifiedEndGameResult($gameSn, $actualCoin, $data, $gameLogId = 0)
    {
        try {
            Log::write("统一返分处理开始: gameSn={$gameSn}, 硬件返回金币={$actualCoin}, gameLogId={$gameLogId}", 'info');

            // 获取游戏信息
            $gameInfo = self::getGameInfo($gameSn);
            $returnCoinType = $gameInfo['return_coin_type'] ?? 'all';

            // 检查游戏状态缓存，确定协议类型
            $gameStateProtocol = 'websocket'; // 默认协议
            $gameStateData = null;
            $bonusCoin = 0;
            $usedCoins = 0;

            if (isset(self::$mqttGameStateCache[$gameSn])) {
                $gameStateData = self::$mqttGameStateCache[$gameSn];
                $gameStateProtocol = $gameStateData['protocol'] ?? 'websocket';
                $usedCoins = $gameStateData['coinUsed'] ?? 0;
                Log::write("检测到游戏状态缓存，协议类型: {$gameStateProtocol}, 已使用金币: {$usedCoins}", 'info');

                if ($gameStateProtocol === 'mqtt') {
                    // MQTT协议才有奖励金币
                    $bonusCoin = $gameStateData['jackpot_amount'] ?? 0;
                    Log::write("MQTT协议奖励金币: {$bonusCoin}", 'info');
                }
            }

            // 根据退币方式计算总退币
            $totalCoin = 0;
            if ($returnCoinType === 'all') {
                // 捕鱼机方式：使用硬件返回的实际金币 + 奖励金币
                $totalCoin = $actualCoin + $bonusCoin;
                Log::write("{$gameStateProtocol}协议捕鱼机退币: 硬件金币{$actualCoin} + 奖励金币{$bonusCoin} = 总计{$totalCoin}", 'info');
            } else {
                // 推币机方式：直接使用硬件返回的金币数量
                $totalCoin = $actualCoin + $bonusCoin;
                Log::write("{$gameStateProtocol}协议推币机退币: 硬件返回{$actualCoin} + 奖励金币{$bonusCoin} = 总计{$totalCoin}", 'info');
            }

            // 准备endGameResult数据
            $endGameResultData = [
                'gameSn' => $gameSn,
                'code' => 200,
                'coin' => $totalCoin,
                'number' => $data['number'] ?? 1
            ];

            // 如果有gameLogId，传递给endGameResult
            if ($gameLogId > 0) {
                $endGameResultData['gameLogId'] = $gameLogId;
                Log::write("传递gameLogId给endGameResult: {$gameLogId}", 'info');
            }

            // 传递协议特定的缓存金币信息
            if ($gameStateData) {
                if ($gameStateProtocol === 'mqtt') {
                    $endGameResultData['mqtt_used_coins'] = $usedCoins;
                    $endGameResultData['mqtt_cached_coin_before'] = $gameStateData['userCoin'] ?? 0;
                } else {
                    $endGameResultData['websocket_used_coins'] = $usedCoins;
                    $endGameResultData['websocket_cached_coin_before'] = $gameStateData['userCoin'] ?? 0;
                }
                Log::write("{$gameStateProtocol}协议传递缓存信息: 已使用={$usedCoins}, 游戏前金币={$gameStateData['userCoin']}", 'info');
            }

            // 调用游戏模型处理
            $game_model = self::getGameModel();
            $result = $game_model->endGameResult($endGameResultData);

            // 清除游戏状态缓存
            if (isset(self::$mqttGameStateCache[$gameSn])) {
                unset(self::$mqttGameStateCache[$gameSn]);

                if ($gameStateProtocol === 'mqtt') {
                    // MQTT协议需要取消订阅
                    self::unsubscribeGameTopic($gameSn);
                    Log::write('清除MQTT游戏状态缓存并取消订阅：'.$gameSn, 'info');
                } else {
                    Log::write('清除WebSocket游戏状态缓存：'.$gameSn, 'info');
                }
            }

            Log::write("{$gameStateProtocol}协议统一返分处理结果: " . json_encode($result), 'info');

            return [
                'success' => $result['code'] == 1,
                'total_coin' => $totalCoin,
                'used_coins' => $usedCoins,
                'protocol' => $gameStateProtocol,
                'return_type' => $returnCoinType,
                'message' => $result['msg'] ?? '返分处理完成',
                'game_result' => $result
            ];

        } catch (\Exception $e) {
            Log::write("统一返分处理异常: " . $e->getMessage(), 'error');
            return [
                'success' => false,
                'total_coin' => 0,
                'used_coins' => 0,
                'protocol' => 'unknown',
                'return_type' => 'unknown',
                'message' => $e->getMessage(),
                'game_result' => ['code' => 0, 'msg' => $e->getMessage()]
            ];
        }
    }

    /**
     * 发送MQTT投币指令
     * @param string $gameSn 游戏序列号
     * @param int $coin 投币数量
     * @param string $requestId 请求ID
     */
    private static function sendMqttCoinCommand($gameSn, $coin, $requestId)
    {
        try {
            // 构建MQTT投币消息
            $mqttMessage = [
                'type' => '02',
                'num' => $coin,
                'requestId' => $requestId
            ];

            // 发送MQTT消息
            self::sendMqttMessage($gameSn, $mqttMessage, 'sub');

            Log::write("MQTT投币指令发送成功: gameSn={$gameSn}, coin={$coin}, requestId={$requestId}", 'info');

        } catch (\Exception $e) {
            Log::write("MQTT投币指令发送失败: " . $e->getMessage(), 'error');

            // 如果发送失败，触发回调失败
            if (isset(self::$mqttCoinCallbacks[$gameSn][$requestId])) {
                $callback = self::$mqttCoinCallbacks[$gameSn][$requestId]['callback'];
                if (is_callable($callback)) {
                    $callback(false);
                }
            }

            throw $e;
        }
    }


}