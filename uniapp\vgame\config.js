// config.js

// 在应用启动时执行这段代码，例如在 main.js 或者 App.vue 的 onLaunch 钩子中
export const getBaseUrl = () => {
	// 获取当前页面的协议、主机（IP或域名）、端口
	let protocol = window.location.protocol;
	let host = window.location.hostname; // 这会返回类似 '*************' 或域名
	let port = window.location.port ? ':' + window.location.port : ''; // 如果没有指定端口，则为空

	// 构建 baseUrl
	return `${host}${port}`; // 根据实际情况调整路径部分
};

var mBaseUrl=getBaseUrl();
export default {
	// baseUrl: 'https://testva2.91jdcd.com',
	// baseUrl: 'http://*************:8081',
	baseUrl: 'http://'+mBaseUrl,
	tokenKey: 'auth_token', // Token存储键名
	tokenExpireKey: 'token_expire', // Token过期时间键名
	userInfo: "USER_INFO", // 用户信息键名
	// WebSocket: 'wss://testva2.91jdcd.com/websocket/',
	// WebSocket: 'ws://*************:2346/websocket'
	WebSocket: 'ws://'+window.location.hostname+':2346/websocket'
}

