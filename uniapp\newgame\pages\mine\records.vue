<template>
	<view class="container">
		<!-- 顶部选项卡 -->
		<view class="nav-bar">
			<view class="nav-item" :class="{ active: currentTab === 'currency' }" @click="switchTab('currency')">
				游戏币记录
			</view>
			<view class="nav-item" :class="{ active: currentTab === 'points' }" @click="switchTab('points')">
				积分记录
			</view>
			<view class="nav-item" :class="{ active: currentTab === 'recharge' }" @click="switchTab('recharge')">
				充值记录
			</view>
		</view>

		<!-- 记录列表 -->
		<scroll-view scroll-y class="record-list" @scrolltolower="loadMore">
			<!-- 公共记录结构 -->
			<view v-if="showNoData" class="no-data">
				{{ getNoDataText() }}
			</view>

			<view class="record-item" v-for="(item, index) in currentRecords" :key="index">
				<view class="left">
					<view class="type" v-if="currentTab !== 'recharge'">{{ item.type }}</view>
					<view class="recharge-info" v-if="currentTab === 'recharge'">
						<view class="order-no">订单号：{{ item.order_no }}</view>
						<view class="coin-info">获得金币：{{ item.coin }}</view>
					</view>
					<view class="time">{{ formatTime(item.time) }}</view>
				</view>
				<view class="right">
					<view class="amount" :class="{ 'negative': item.status == 1 }" v-if="currentTab !== 'recharge'">
						{{ item.status == 1 ? '+' : '-' }}{{ item.amount }}{{ getAmountUnit() }}
					</view>
					<view class="recharge-amount" v-if="currentTab === 'recharge'">
						<view class="payment-amount">{{ item.amount }}元</view>
						<view class="status" :class="getRechargeStatusClass(item.status)">
							{{ getRechargeStatusText(item.status) }}</view>
					</view>
				</view>
			</view>

			<view class="loading-text" v-if="currentRecords.length > 0">
				{{ loadStatus }}
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import request from '@/utils/request'
	export default {
		data() {
			return {
				currentTab: 'currency',
				// 分类型存储记录
				recordData: {
					currency: {
						list: [],
						page: 1,
						lastPage: 1,
						loading: false,
						noMore: false
					},
					points: {
						list: [],
						page: 1,
						lastPage: 1,
						loading: false,
						noMore: false
					},
					recharge: {
						list: [],
						page: 1,
						lastPage: 1,
						loading: false,
						noMore: false
					}
				}
			}
		},
		computed: {
			// 当前显示记录
			currentRecords() {
				return this.recordData[this.currentTab].list
			},
			// 是否显示空状态
			showNoData() {
				return this.currentRecords.length === 0 && !this.recordData[this.currentTab].loading
			},
			// 加载状态
			loadStatus() {
				const current = this.recordData[this.currentTab]
				if (current.noMore) return '没有更多数据了'
				return current.loading ? '加载中...' : ''
			}
		},
		methods: {
			getNoDataText() {
				switch (this.currentTab) {
					case 'currency':
						return '暂无游戏币记录'
					case 'points':
						return '暂无积分记录'
					case 'recharge':
						return '暂无充值记录'
					default:
						return '暂无记录'
				}
			},
			getAmountUnit() {
				switch (this.currentTab) {
					case 'currency':
						return '币'
					case 'points':
						return '分'
					case 'recharge':
						return '元'
					default:
						return ''
				}
			},
			switchTab(tab) {
				if (this.currentTab !== tab) {
					this.currentTab = tab
					// 首次进入时自动加载
					if (this.currentRecords.length === 0) {
						this.refreshData()
					}
				}
			},
			// 刷新数据
			async refreshData() {
				const current = this.recordData[this.currentTab]
				current.page = 1
				current.noMore = false
				await this.fetchRecords(1)
			},
			// 加载更多
			loadMore() {
				const current = this.recordData[this.currentTab]
				if (!current.noMore && !current.loading) {
					this.fetchRecords(current.page + 1)
				}
			},
			// 获取记录
			async fetchRecords(page) {
				const current = this.recordData[this.currentTab]
				if (current.loading || current.noMore) return

				current.loading = true
				try {
					let type = 'coin'

					if (this.currentTab === 'points') {
						type = 'score'
					} else if (this.currentTab === 'recharge') {
						type = 'recharge'
					}

					const res = await request({
						url: '/api/user/gold',
						method: 'POST',
						data: {
							page,
							type: type
						}
					})

					const newRecords = res.data.data.map(item => {
						if (this.currentTab === 'recharge') {
							return {
								order_no: item.order_no,
								amount: item.price,
								coin: item.coin,
								status: item.status,
								time: item.createtime
							}
						} else {
							return {
								type: item.memo,
								time: item.createtime,
								amount: item.money,
								status: item.status
							}
						}
					})

					// 更新数据
					current.list = page === 1 ? newRecords : [...current.list, ...newRecords]
					current.page = res.data.current_page
					current.lastPage = res.data.last_page
					current.noMore = res.data.current_page >= res.data.last_page
				} catch (error) {
					uni.showToast({
						title: '请求失败，请重试',
						icon: 'none'
					})
				} finally {
					current.loading = false
				}
			},
			formatTime(timestamp) {
				const date = new Date(timestamp * 1000)
				return `${date.getFullYear()}.${(date.getMonth()+1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
			},
			getRechargeStatusText(status) {
				const statusMap = {
					0: '待支付',
					1: '已支付',
					2: '已退款'
				}
				return statusMap[status] || '未知状态'
			},
			getRechargeStatusClass(status) {
				const classMap = {
					0: 'status-pending',
					1: 'status-success',
					2: 'status-refund'
				}
				return classMap[status] || ''
			}
		},
		mounted() {
			this.refreshData()
		}
	}
</script>
<style scoped>
	/* 新增加载状态样式 */
	.loading-text {
		text-align: center;
		padding: 20rpx;
		color: #999;
		font-size: 24rpx;
	}

	.container {
		background: #180F29;
		min-height: 100vh;
	}

	.nav-bar {
		display: flex;
		border-bottom: 2rpx solid #2E1E46;
	}

	.nav-item {
		flex: 1;
		text-align: center;
		padding: 20rpx;
		height: 100rpx;
		line-height: 100rpx;
		color: #fff;
		font-size: 32rpx;
		position: relative;
		background: #2E1E46;
	}

	.nav-item.active {
		color: #1D0924;
		font-weight: bold;
		background: #C2B48E;
	}

	.record-list {
		/* 添加备用高度单位 */
		height: calc(100vh - 120rpx);
		/* 微信环境 */
		height: calc(100vh - 60px);
		/* H5备用 */
		position: relative;
	}

	.record-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 20rpx;
		margin: 16rpx 20rpx;
		background: #2A1840;
		border-radius: 12rpx;
		/* border: none; */
		width: 90%;
		border: 1px solid #4C3A62;
	}

	.left {
		width: 75%;
	}

	.type {
		font-size: 34rpx;
		color: #fff;
		margin-bottom: 10rpx;
	}

	.time {
		font-size: 26rpx;
		color: #999;
	}

	.right {
		flex: 1;
		text-align: right;
	}

	.amount {
		font-size: 36rpx;
		color: #48A578;
		font-weight: bold;
	}

	.amount.negative {
		color: #905DEE;
	}

	.no-data {
		position: absolute;
		left: 0;
		right: 0;
		top: 50%;
		transform: translateY(-50%);
		text-align: center;
		color: #999;
		font-size: 28rpx;
		z-index: 10;
	}

	.recharge-info {
		margin-bottom: 10rpx;
	}

	.order-no {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 6rpx;
	}

	.coin-info {
		font-size: 26rpx;
		color: #fff;
	}

	.recharge-amount {
		text-align: right;
	}

	.payment-amount {
		font-size: 32rpx;
		color: #fff;
		font-weight: bold;
		margin-bottom: 6rpx;
	}

	.status {
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		display: inline-block;
	}

	.status-pending {
		background: #fff3e0;
		/* color: #ff9800; */
	}

	.status-success {
		background: #e8f5e9;
		color: #4caf50;
	}

	.status-refund {
		background: #ffebee;
		color: #f44336;
	}
</style>