<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">

  <div class="form-group">
    <label for="c-id" class="control-label col-xs-12 col-sm-2">{:__('ID')}:</label>
    <div class="col-xs-12 col-sm-8">
      <input type="text" name="row[agent_id]" value="{$row.id|htmlentities}"  id="c-id" class="form-control" readonly="readonly"/>
    </div>
  </div>
  <div class="form-group">
    <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('代理商')}:</label>
    <div class="col-xs-12 col-sm-8">
      <input type="text" value="{$row.nickname} - {$row.mobile}"  id="c-nickname" class="form-control" readonly="readonly"/>
    </div>
  </div>
  <div class="form-group">
    <label for="c-now_password" class="control-label col-xs-12 col-sm-2">{:__('新密码')}:</label>
    <div class="col-xs-12 col-sm-8">
      <input type="text" name="row[now_password]"  id="c-now_password" class="form-control" required placeholder="密码长度必须在6-16位之间，不能包含空格"/>
    </div>
  </div>

  <div class="form-group">
    <label for="c-now_password2" class="control-label col-xs-12 col-sm-2">{:__('再次输入密码')}:</label>
    <div class="col-xs-12 col-sm-8">
      <input type="text" name="row[now_password2]"  id="c-now_password2" class="form-control" required placeholder="密码长度必须在6-16位之间，不能包含空格"/>
    </div>
  </div>

  <div class="form-group hide layer-footer">
    <label class="control-label col-xs-12 col-sm-2"></label>
    <div class="col-xs-12 col-sm-8">
      <button type="submit" class="btn btn-success btn-embossed ">{:__('OK')}</button>
      <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
    </div>
  </div>
</form>
