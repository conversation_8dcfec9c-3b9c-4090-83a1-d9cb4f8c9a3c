<template>
	<view class="fish-game-control">
		<!-- 左上角头像区域 -->
		<view class="avatar-container left" style="left: 5%; right: auto; top: 5%;" v-if="gameInfo.is_full !== 2">
			<view class="avatar-item" v-for="(seat, index) in seats.slice(0, 4)" :key="seat.id" style="margin: 0 2px;">
				<view class="avatar-wrapper">
					<image
						:src="seat.status === 1 ? seat.avatar : `/static/dpad/p${seat.number}.png`"
						mode="aspectFill"
						class="avatar-img-group"
					/>
					<text class="avatar-name">{{ seat.status === 1 ? seat.nickname : '空闲' }}</text>
				</view>
			</view>
		</view>

		<!-- 游戏中时的左上角用户信息区域 -->
		<view class="game-user-info-container" v-if="gameInfo.is_full === 2">
			<!-- 左侧：用户头像、昵称、座位号 -->
			<view class="user-info-box">
				<image
					:src="userInfo.avatar || `/static/dpad/p${gameInfo.number || 1}.png`"
					mode="aspectFill"
					class="user-avatar"
				/>
				<view class="user-details">
					<text class="user-nickname">{{ userInfo.nickname || '玩家' }}</text>
					<text class="user-seat">{{ gameInfo.number }}P</text>
				</view>
			</view>

			<!-- 右侧：统计信息区域 -->
			<view class="user-stats-container">
				<!-- 金币框 -->
				<view class="stat-box coin-box" @click="showRechargeDialog">
					<image src="/static/coin.png" class="stat-icon" />
					<text class="stat-value">{{ userInfo.money || 0 }}</text>
					<image src="/static/add.png" class="add-icon" />
				</view>

				<!-- 积分框 -->
				<view class="stat-box score-box" @click="showScoreExchangeDialog">
					<image src="/static/score.png" class="stat-icon" />
					<text class="stat-value">{{ userInfo.score || 0 }}</text>
					<image src="/static/change.png" class="exchange-icon" />
				</view>
			</view>
		</view>

		<!-- 右上角头像、静音按钮和退出按钮 -->
		<view class="avatar-container right">
			<view class="avatar-group-box" v-if="gameInfo.is_full !== 2">
				<view v-for="(user, idx) in onlineAvatars" :key="user.id" class="avatar-group-item" :style="{ zIndex: 100 - idx, marginLeft: idx === 0 ? '0' : '-12px' }">
					<image class="avatar-img-group" :src="user.avatar || `/static/dpad/p${(idx % 4) + 1}.png`" />
				</view>
			</view>
			<!-- 静音按钮 -->
			<view class="setting-button" @click="toggleVideoAudio">
				<image class="setting-btn-icon" :src="isVideoMuted ? '/static/dpad/off.png' : '/static/dpad/on.png'" alt="静音" />
			</view>
			<!-- 设置按钮 -->
			<view class="setting-button" @click="$emit('show-func-dialog')">
				<image class="setting-btn-icon" src="/static/func/setting.png" alt="设置" />
			</view>
			<!-- 退出按钮 -->
			<button class="exit-button"
				@click="exitGame">
				<text class="exit-icon">×</text>
			</button>
		</view>

		<!-- 座位按钮区域 - 根据座位ID精确定位 -->
		<view class="seats-container" v-if="gameInfo.is_full !== 2">
			<view
				v-for="(seat, index) in availableSeats"
				:key="seat.id"
				class="seat-button-wrapper"
				:class="`seat-index-${index}`"
				:style="getSeatButtonStyle(seat.id)"
			>


				<view v-if="seat.status === 1" class="avatar-item" style="flex-direction: row; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.7); border-radius: 30px; padding: 5px 16px; margin-bottom: 16px; box-shadow: 0 2px 8px #0003; border: none;">
					<image class="avatar-img-group" :src="seat.avatar || `/static/dpad/p${seat.number}.png`" style="margin-right: 8px;" />
					<text class="avatar-name" style="color: #fff; font-size: 18px; font-weight: bold; max-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ seat.nickname }}</text>
				</view>
				<view v-else>
					<view class="seat-button-container" v-if="seat.status === 0">
						<view class="game-button" @click="startGame(seat)">
							<image src="/static/dpad/play.png" class="play-button-img" mode="aspectFit" />
						</view>
						<!-- 在开始按钮底部新增对应的座位头像 -->
						<image :src="`/static/dpad/p${seat.number}.png`" class="seat-avatar-img" mode="aspectFit" />
					</view>
				</view>
			</view>
		</view>

		<!-- 游戏操作区域 -->
		<view v-if="gameInfo.is_full === 2" class="game-controls">
			<!-- 左侧方向轮盘 -->
			<view class="directional-pad">
				<view class="dpad-circle">
					<button class="dpad-btn up"
						@touchstart="handleUpStart"
						@touchend="handleUpEnd"
						@touchcancel="handleUpEnd"
						@mousedown="handleUpStart"
						@mouseup="handleUpEnd"
						@mouseleave="handleUpEnd"
						@contextmenu.prevent>
						<image src="/static/dpad/up.png" class="dpad-arrow-img" mode="aspectFit" />
					</button>
					<button class="dpad-btn down"
						@touchstart="handleDownStart"
						@touchend="handleDownEnd"
						@touchcancel="handleDownEnd"
						@mousedown="handleDownStart"
						@mouseup="handleDownEnd"
						@mouseleave="handleDownEnd"
						@contextmenu.prevent>
						<image src="/static/dpad/down.png" class="dpad-arrow-img" mode="aspectFit" />
					</button>
					<button class="dpad-btn left"
						@touchstart="handleLeftStart"
						@touchend="handleLeftEnd"
						@touchcancel="handleLeftEnd"
						@mousedown="handleLeftStart"
						@mouseup="handleLeftEnd"
						@mouseleave="handleLeftEnd">
						<image src="/static/dpad/left.png" class="dpad-arrow-img" mode="aspectFit" />
					</button>
					<button class="dpad-btn right"
						@touchstart="handleRightStart"
						@touchend="handleRightEnd"
						@touchcancel="handleRightEnd"
						@mousedown="handleRightStart"
						@mouseup="handleRightEnd"
						@mouseleave="handleRightEnd">
						<image src="/static/dpad/right.png" class="dpad-arrow-img" mode="aspectFit" />
					</button>
				</view>
			</view>

			<!-- 右侧功能按钮 -->
			<view class="fire-group">
				<!-- 发炮按钮（圆心） -->
				<view class="ring-btn-outer fire-btn-outer">
					<view class="fire-button ring-btn-inner"
						@touchstart="onFireStart"
						@touchend="onFireEnd"
						@touchcancel="onFireEnd"
						@mousedown="onFireStart"
						@mouseup="onFireEnd"
						@mouseleave="onFireEnd"
					>
						<image src="/static/dpad/fire.png" class="action-btn-img" :class="{ active: isActiveFire }" mode="aspectFit" />
					</view>
					<view v-if="autoMode" class="auto-mode-label">托管自动中</view>
				</view>
				<!-- 加注 -->
				<view class="ring-btn-outer">
					<view class="action-btn ring-btn-inner"
						style="--angle: -40deg;"
						@touchstart="isActiveBet = true"
						@touchend="isActiveBet = false"
						@mousedown="isActiveBet = true"
						@mouseup="isActiveBet = false"
						@mouseleave="isActiveBet = false"
						@touchcancel="isActiveBet = false"
						@click="handleBet"
					>
						<image src="/static/dpad/bet.png" class="action-btn-img" :class="{ active: isActiveBet }" mode="aspectFit" />
					</view>
				</view>
				<!-- 切换 -->
				<view class="ring-btn-outer">
					<view class="action-btn ring-btn-inner"
						style="--angle: -90deg;"
						@touchstart="isActiveSwitch = true"
						@touchend="isActiveSwitch = false"
						@mousedown="isActiveSwitch = true"
						@mouseup="isActiveSwitch = false"
						@mouseleave="isActiveSwitch = false"
						@touchcancel="isActiveSwitch = false"
						@click="handleSwitch"
					>
						<image src="/static/dpad/switch.png" class="action-btn-img" :class="{ active: isActiveSwitch }" mode="aspectFit" />
					</view>
				</view>
				<!-- 托管/自动 -->
				<view class="ring-btn-outer">
					<view class="action-btn ring-btn-inner"
						style="--angle: 10deg;"
						@touchstart="isActiveAuto = true"
						@touchend="isActiveAuto = false"
						@mousedown="isActiveAuto = true"
						@mouseup="isActiveAuto = false"
						@mouseleave="isActiveAuto = false"
						@touchcancel="isActiveAuto = false"
						@click="toggleAuto"
					>
						<image
							:src="autoMode ? '/static/dpad/auto_stop.png' : '/static/dpad/auto.png'"
							class="action-btn-img"
							:class="{ active: isActiveAuto }"
							mode="aspectFit"
						/>
					</view>
				</view>
				<!-- 投币按钮（最右边） -->
				<view class="ring-btn-outer">
					<view class="action-btn ring-btn-inner"
						style="--angle: 60deg;"
						@touchstart="isActiveCoin = true"
						@touchend="isActiveCoin = false"
						@mousedown="isActiveCoin = true"
						@mouseup="isActiveCoin = false"
						@mouseleave="isActiveCoin = false"
						@touchcancel="isActiveCoin = false"
						@click="showCoinDialog"
					>
						<image src="/static/dpad/coin.png" class="action-btn-img" :class="{ active: isActiveCoin }" mode="aspectFit" />
					</view>
				</view>
			</view>
		</view>

		<!-- 投币弹窗 - 使用UniversalModal组件 -->
		<UniversalModal
			:show="showCoinModal"
			title="选择投币数量"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="coin"
			@close="hideCoinDialog"
		>
			<view v-if="coinButtons.length === 0" class="no-coin-tip">
				暂无投币选项配置
			</view>
			<view v-else class="coin-buttons-grid">
				<button
					v-for="button in coinButtons"
					:key="button.id"
					class="coin-button"
					@click="handleCoinSelect(button)"
				>
					<text class="coin-button-text">{{ button.name }}</text>
					<text class="coin-button-value">{{ button.value }}币</text>
				</button>
			</view>
		</UniversalModal>
	</view>
</template>

<script>
import UniversalModal from '@/components/UniversalModal.vue'

export default {
	name: 'FishControl',
	components: {
		UniversalModal
	},
	inheritAttrs: false, // 禁用自动继承属性，避免Vue 3的警告
	emits: [
		'show-func-dialog',
		'show-reward-dialog',
		'show-score-exchange-dialog',
		'toggleVideoAudio',
		'show-exit-dialog',
		'exitGame',
		'show-settle-mask',
		'show-wait-mask',
		'startGame',
		'showSettingDialog',
		'updateIdleCountdownStyle'
	],
	props: {
		gameInfo: {
			type: Object,
			default: () => ({})
		},
		userInfo: {
			type: Object,
			default: () => ({})
		},
		seats: {
			type: Array,
			default: () => []
		},
		isVideoMuted: {
			type: Boolean,
			default: false
		},
		isAuto: {
			type: Boolean,
			default: false
		},
		// 添加缺失的props
		showIdleCountdown: {
			type: Boolean,
			default: false
		},
		idleCountdown: {
			type: Number,
			default: 0
		},
		cachedCoin: {
			type: Number,
			default: 0
		},
		// 游戏设置状态
		gameSettings: {
			type: Object,
			default: () => ({
				gameSound: true
			})
		}
	},
	data() {
		return {
			// 捕鱼游戏内部状态
			leftPressed: false,
			rightPressed: false,
			upPressed: false,
			downPressed: false,
			firePressed: false,
			autoMode: false,
			leftInterval: null,
			rightInterval: null,
			upInterval: null,
			downInterval: null,
			fireInterval: null,
			onlineAvatars: [], // 新增：在线用户头像列表
			isActiveBet: false,
			isActiveSwitch: false,
			isActiveAuto: false,
			isActiveFire: false,
			isActiveCoin: false,
			fireDownTime: null, // 新增：记录开火按钮按下时间
			fireSent: false, // 新增：记录是否已发送过15指令
			showFuncMenu: false,
			lastFireTime: null, // 新增：记录最后发炮时间
			exitCountdown: 0, // 新增：退出倒计时
			isExitWaiting: false, // 新增：是否处于等待退出状态
			exitCountdownTimer: null, // 新增：退出倒计时定时器
			showCoinModal: false, // 投币弹窗显示状态
			fireEndTimer: null, // 新增：松开后延迟发送17的定时器
			coinButtons: [ // 投币按钮配置，先设置默认值
				{id: 51, name: "投1币", position: "center-left", value: 1},
				{id: 52, name: "投5币", position: "center", value: 5},
				{id: 53, name: "投30币", position: "center-right", value: 30}
			],
			// 静音状态
			isVideoMuted: false
		}
	},
	watch: {
		userInfo: {
			handler(newVal) {
				if (newVal && newVal.avatar && newVal.id) {
					if (!this.onlineAvatars.some(u => u.id === newVal.id)) {
						this.onlineAvatars.push({
							id: newVal.id,
							avatar: newVal.avatar
						})
					}
				}
			},
			immediate: true,
			deep: true
		},
		gameInfo: {
			handler(newVal) {
				console.log('gameInfo watch触发:', newVal);
				// 检查奖励弹窗逻辑
				if (newVal && typeof newVal.coin !== 'undefined' && newVal.coin !== null && !isNaN(newVal.coin) && Number(newVal.coin) >= 0) {
					this.$emit('show-reward-dialog', Number(newVal.coin));
				}
				// 初始化投币按钮配置
				if (newVal) {
					this.initCoinButtons();
					// 游戏信息变化时重新调整按钮位置
					this.$nextTick(() => {
						this.adjustButtonPosition();
						// 强制重新渲染组件以应用新的座位样式
						this.$forceUpdate();
					});
				}
			},
			immediate: true,
			deep: true
		}
	},
	watch: {
		// 监听游戏设置变化
		'gameSettings.gameSound': {
			handler(newVal) {
				console.log('FishControl - 游戏声音设置变化:', newVal)
				// 更新本地静音状态，注意逻辑：gameSound为true表示声音开启，isVideoMuted应该为false
				this.isVideoMuted = !newVal
			},
			immediate: true
		}
	},
	computed: {
		gameStatusText() {
			if (!this.gameInfo) return '加载中...'
			switch (this.gameInfo.is_full) {
				case 0: return '当前空闲'
				case 1: return '座位已满'
				case 2: return this.userInfo.nickname || '正在游戏中'
				default: return '等待启动中...'
			}
		},

		// 过滤可用座位（排除隐藏的座位）
		availableSeats() {
			const filtered = this.seats.filter(seat => seat.status !== undefined && seat.status !== null);
			return filtered;
		},

		// 计算当前静音状态
		currentMuteState() {
			return !this.gameSettings.gameSound
		}
	},
	mounted() {
		this.initCoinButtons();
		this.adjustButtonPosition();

		// 监听窗口大小变化
		window.addEventListener('resize', this.adjustButtonPosition);
		window.addEventListener('orientationchange', () => {
			setTimeout(this.adjustButtonPosition, 100);
		});
	},
	methods: {
		// 初始化投币按钮配置
		initCoinButtons() {
			console.log('初始化投币按钮配置');
			console.log('gameInfo:', this.gameInfo);
			console.log('control_config:', this.gameInfo?.control_config);

			try {
				// 从gameInfo中获取control_config配置
				if (this.gameInfo && this.gameInfo.control_config) {
					let config;
					if (typeof this.gameInfo.control_config === 'string') {
						config = JSON.parse(this.gameInfo.control_config);
					} else {
						config = this.gameInfo.control_config;
					}

					console.log('解析后的config:', config);

					if (config && config.buttons && Array.isArray(config.buttons)) {
						this.coinButtons = config.buttons;
						console.log('使用配置的投币按钮:', this.coinButtons);
					} else {
						// 默认配置
						this.coinButtons = [
							{id: 51, name: "投1币", position: "center-left", value: 1},
							{id: 52, name: "投5币", position: "center", value: 5},
							{id: 53, name: "投30币", position: "center-right", value: 30}
						];
						console.log('使用默认投币按钮配置(config无效):', this.coinButtons);
					}
				} else {
					// 默认配置
					this.coinButtons = [
						{id: 51, name: "投1币", position: "center-left", value: 1},
						{id: 52, name: "投5币", position: "center", value: 5},
						{id: 53, name: "投30币", position: "center-right", value: 30}
					];
					console.log('使用默认投币按钮配置(无gameInfo):', this.coinButtons);
				}
				console.log('最终投币按钮配置:', this.coinButtons);
			} catch (error) {
				console.error('解析投币按钮配置失败:', error);
				// 使用默认配置
				this.coinButtons = [
					{id: 51, name: "投1币", position: "center-left", value: 1},
					{id: 52, name: "投5币", position: "center", value: 5},
					{id: 53, name: "投30币", position: "center-right", value: 30}
				];
				console.log('使用默认投币按钮配置(异常):', this.coinButtons);
			}
		},
		// 获取玩家头像
		getPlayerAvatar(seat) {
			const player = this.gameInfo.players?.find(p => p.seat_id === seat.id)
			return player?.avatar || `/static/dpad/p${seat.number || 1}.png`
		},

		// 获取玩家名称
		getPlayerName(seat) {
			const player = this.gameInfo.players?.find(p => p.seat_id === seat.id)
			return player?.nickname || '空闲'
		},

		// 获取当前用户的座位号
		getCurrentSeatNumber() {
			if (!this.userInfo || !this.userInfo.id || !this.seats) {
				return '1'
			}
			// 查找当前用户所在的座位
			const currentSeat = this.seats.find(seat =>
				seat.status === 1 && seat.user_id === this.userInfo.id
			)
			return currentSeat ? currentSeat.id : '1'
		},

		// 显示充值对话框 - 跳转到充值页面
		showRechargeDialog() {
			console.log('点击金币，跳转到充值页面')

			// 保存游戏信息到本地存储，以便充值页面知道来源
			const gameInfo = {
				gameId: this.gameInfo?.id,
				gameName: this.gameInfo?.name,
				is_landscape: this.gameInfo?.is_landscape, // 添加横竖屏参数
				fromGame: true,
				timestamp: Date.now()
			}
			uni.setStorageSync('recharge_from_game', gameInfo)

			uni.switchTab({
				url: '/pages/pay/index'
			})
		},

		// 显示积分兑换对话框 - 显示兑换弹窗
		showScoreExchangeDialog() {
			console.log('点击积分，显示兑换弹窗')
			this.$emit('show-score-exchange-dialog')
		},

		// 切换视频音频
		toggleVideoAudio() {
			console.log('FishControl - 点击静音按钮')
			console.log('FishControl - 当前gameSound状态:', this.gameSettings.gameSound)
			console.log('FishControl - 当前isVideoMuted状态:', this.isVideoMuted)
			// 触发父组件的音频切换事件
			this.$emit('toggleVideoAudio')
		},

		// 退出游戏
		exitGame() {
			// 捕鱼特殊退出判断
			if (this.gameInfo.is_full === 2) {
				// 判断是否处于自动模式，先取消自动模式
				if (this.autoMode) {
					this.toggleAuto();
					this.lastFireTime = Date.now();
				}
				// 判断距离最后发炮时间是否小于3秒，需等待
				const now = Date.now();
				const waitTime = 3000;
				const timeSinceLastFire = now - (this.lastFireTime || 0);
				if (this.lastFireTime && timeSinceLastFire < waitTime) {
					this.exitCountdown = Math.ceil((waitTime - timeSinceLastFire) / 1000);
					this.isExitWaiting = true;
					this.$emit('show-exit-dialog');
					this.exitCountdownTimer = setInterval(() => {
						this.exitCountdown--;
						if (this.exitCountdown <= 0) {
							clearInterval(this.exitCountdownTimer);
							this.isExitWaiting = false;
						}
					}, 1000);
				} else {
					this.isExitWaiting = false;
					this.$emit('show-exit-dialog');
				}
			} else {
				this.$emit('exitGame');
			}
		},
		// 退出弹窗相关方法
		cancelExit() {
			if (this.exitCountdownTimer) clearInterval(this.exitCountdownTimer);
			this.isExitWaiting = false;
			this.$emit('show-exit-dialog');
		},
		handleSettleAndExit() {
			this.sendEndGameWS();
			this.$emit('show-exit-dialog');
			this.$emit('exitGame');
		},
		handleSettleCurrent() {
			this.sendEndGameWS();
			this.$emit('show-exit-dialog');
			this.$emit('show-settle-mask', true);
		},
		sendEndGameWS() {
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const message = {
					type: 'endGame',
					gameLogId: this.gameInfo.gameLogId
				};
				window.gameWebSocket.send(message);
				console.log('【LOG】发送endGame指令', message);
			} else {
				console.warn('WebSocket未连接，无法发送endGame指令');
			}
		},

		// 开始游戏
		startGame(seat) {
			if (this.gameInfo.is_full === 0) {
				this.$emit('show-wait-mask', true);
			}
			this.$emit('startGame', seat)
		},

		// 处理方向操作 - 独立处理
		handleDirection(direction) {
			console.log('捕鱼游戏操作:', direction)
			this.sendGameCommand(direction)
		},

		// 上键开始 - 独立处理
		handleUpStart(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (this.upPressed) return
			this.upPressed = true
			console.log('捕鱼游戏: 开始上转')
			this.sendGameCommand(10)
			this.upInterval = setInterval(() => {
				this.sendGameCommand(10) // 上方向指令
			}, 200)
		},

		// 上键结束 - 独立处理
		handleUpEnd(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (!this.upPressed) return
			this.upPressed = false
			console.log('捕鱼游戏: 结束上转')

			if (this.upInterval) {
				clearInterval(this.upInterval)
				this.upInterval = null
			}
		},

		// 下键开始 - 独立处理
		handleDownStart(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (this.downPressed) return
			this.downPressed = true
			console.log('捕鱼游戏: 开始下转')
			this.sendGameCommand(11)
			this.downInterval = setInterval(() => {
				this.sendGameCommand(11) // 下方向指令
			}, 200)
		},

		// 下键结束 - 独立处理
		handleDownEnd(event) {
			if (event) {
				event.preventDefault()
				event.stopPropagation()
			}
			if (!this.downPressed) return
			this.downPressed = false
			console.log('捕鱼游戏: 结束下转')

			if (this.downInterval) {
				clearInterval(this.downInterval)
				this.downInterval = null
			}
		},

		// 左键开始 - 独立处理
		handleLeftStart() {
			if (this.leftPressed) return;
			this.leftPressed = true;
			this.sendGameCommand(12); // 发送一次
			this.leftInterval = setInterval(() => {
				this.sendGameCommand(12);
			}, 200);
		},
		handleLeftEnd() {
			this.leftPressed = false;
			clearInterval(this.leftInterval);
		},
		handleRightStart() {
			if (this.rightPressed) return;
			this.rightPressed = true;
			this.sendGameCommand(13); // 发送一次
			this.rightInterval = setInterval(() => {
				this.sendGameCommand(13);
			}, 200);
		},
		handleRightEnd() {
			this.rightPressed = false;
			clearInterval(this.rightInterval);
		},

		// 切换自动模式 - 独立处理
		toggleAuto() {
			this.autoMode = !this.autoMode
			console.log('捕鱼游戏自动模式:', this.autoMode ? '开启' : '关闭')
			this.sendGameCommand(this.autoMode ? 18 : 17)
		},

		// 开火开始 - 独立处理
		handleFireStart() {
			if (this.firePressed) return;
			this.firePressed = true;
			this.fireDownTime = Date.now();
			console.log('捕鱼游戏: 开始发炮')

			// 如果有待发送的17指令，取消它
			if (this.fireEndTimer) {
				clearTimeout(this.fireEndTimer);
				this.fireEndTimer = null;
				console.log('取消之前的17指令延迟');
			}

			// 按下立即发一次15
			this.sendGameCommand(15);
			// 启动长按定时器
			this.fireInterval = setInterval(() => {
				this.sendGameCommand(15);
			}, 200);
		},

		// 开火结束 - 独立处理
		handleFireEnd() {
			if (!this.firePressed) return;
			this.firePressed = false;
			console.log('捕鱼游戏: 结束发炮')
			if (this.fireInterval) {
				clearInterval(this.fireInterval);
				this.fireInterval = null;
			}

			// 松开后等待600毫秒再发送17指令
			this.fireEndTimer = setTimeout(() => {
				this.sendGameCommand(17);
				this.fireEndTimer = null;
				console.log('延迟600毫秒后发送17指令');
			}, 600);

			this.fireDownTime = null;
		},

		// 发送游戏指令 - 独立的WebSocket通信
		sendGameCommand(command) {
			// 直接发送WebSocket指令，不依赖父组件
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const message = {
					type: 'command',
					number: this.gameInfo.number,
					status: command,
				}
				// GameWebSocket的send方法内部会自动调用JSON.stringify，所以直接传对象
				window.gameWebSocket.send(message)
				console.log('【LOG】捕鱼游戏发送指令:', command, '消息:', message)
			} else {
				console.warn('【LOG】WebSocket未连接，无法发送指令', window.gameWebSocket)
			}
		},
		// 显示投币弹窗
		showCoinDialog() {
			console.log('显示投币弹窗');
			console.log('当前投币按钮配置:', this.coinButtons);
			// 调用父组件的全局投币弹窗
			if (this.$parent && this.$parent.showCoinDialog) {
				this.$parent.showCoinDialog(this.coinButtons);
			} else {
				// 兜底：使用本地弹窗
				this.showCoinModal = true;
			}
		},
		// 隐藏投币弹窗
		hideCoinDialog() {
			this.showCoinModal = false;
		},
		// 处理投币确认（由父组件调用）
		handleCoinConfirm(button) {
			console.log('处理投币确认:', button);
			this.sendPutInCoins(button.value);
		},
		// 处理投币选择
		handleCoinSelect(button) {
			console.log('选择投币:', button);
			this.hideCoinDialog();
			this.sendPutInCoins(button.value);
		},
		// 发送投币指令
		sendPutInCoins(coinAmount) {
			if (window.gameWebSocket && window.gameWebSocket.readyState === 1) {
				const requestId = 'putInCoins_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
				const message = {
					type: 'putInCoins',
					number: this.gameInfo.number,
					coin: coinAmount,
					requestId: requestId
				};

				console.log('【LOG】准备发送投币指令', message);
				console.log('【LOG】gameWebSocket类型:', typeof window.gameWebSocket);
				console.log('【LOG】gameWebSocket.send类型:', typeof window.gameWebSocket.send);

				// 直接发送对象，GameWebSocket内部会处理JSON序列化
				window.gameWebSocket.send(message);
				console.log('【LOG】投币指令已发送');

				// 显示投币提示
				uni.showToast({
					title: `投币${coinAmount}个`,
					icon: 'none',
					duration: 1500
				});
			} else {
				console.warn('WebSocket未连接，无法发送投币指令');
				console.log('gameWebSocket状态:', window.gameWebSocket?.readyState);
				uni.showToast({
					title: 'WebSocket未连接',
					icon: 'error'
				});
			}
		},
		onFireStart(event) {
			if (this.autoMode) return;

			// 阻止默认行为，但保留游戏操作
			if (event) {
				event.preventDefault();
				event.stopPropagation();
			}

			this.isActiveFire = true;
			this.handleFireStart();
		},
		onFireEnd(event) {
			if (this.autoMode) return;

			// 阻止默认行为，但保留游戏操作
			if (event) {
				event.preventDefault();
				event.stopPropagation();
			}

			this.isActiveFire = false;
			this.handleFireEnd();
		},
		handleBet() {
			this.sendGameCommand(16); // 16为加注指令，如需调整请改为实际业务指令
		},
		handleSwitch() {
			this.sendGameCommand(14); // 14为切换指令，如需调整请改为实际业务指令
		},
		onClickSetting() {
			this.showFuncMenu = false;
			this.$emit('showSettingDialog');
		},
		onClickExit() {
			this.showFuncMenu = false;
			this.$emit('exitGame');
		},

		// 根据座位ID获取按钮样式定位
		getSeatButtonStyle(seatId) {
			// 检查是否为视觉横屏模式（CSS横屏）
			const isVisualLandscape = this.gameInfo && this.gameInfo.is_landscape === 1;

			// 获取座位在数组中的索引（0-3），用于确定位置
			const seatIndex = this.availableSeats.findIndex(seat => seat.id === seatId);

			// 根据捕鱼游戏的座位布局定义每个座位的精确位置
			let seatPositions;

			if (isVisualLandscape) {
				// 视觉横屏模式下的座位位置 - 底部水平排列
				seatPositions = [
					{ // 座位0 - 底部最左
						position: 'absolute',
						left: '21%',
						bottom: '15%',
						transform: 'translateX(-50%)'
					},
					{ // 座位1 - 底部左中
						position: 'absolute',
						left: '40%',
						bottom: '15%',
						transform: 'translateX(-50%)'
					},
					{ // 座位2 - 底部右中
						position: 'absolute',
						left: '59%',
						bottom: '15%',
						transform: 'translateX(-50%)'
					},
					{ // 座位3 - 底部最右
						position: 'absolute',
						left: '78%',
						bottom: '15%',
						transform: 'translateX(-50%)'
					}
				];
			} else {
				// 竖屏模式下的座位位置 - 底部水平排列
				seatPositions = [
					{ // 座位0 - 底部最左
						position: 'absolute',
						left: '15%',
						bottom: '15%',
						transform: 'translateX(-50%)'
					},
					{ // 座位1 - 底部左中
						position: 'absolute',
						left: '38%',
						bottom: '15%',
						transform: 'translateX(-50%)'
					},
					{ // 座位2 - 底部右中
						position: 'absolute',
						left: '62%',
						bottom: '15%',
						transform: 'translateX(-50%)'
					},
					{ // 座位3 - 底部最右
						position: 'absolute',
						left: '85%',
						bottom: '15%',
						transform: 'translateX(-50%)'
					}
				];
			}

			// 使用座位索引获取位置，如果索引无效则使用默认位置
			const style = (seatIndex >= 0 && seatIndex < seatPositions.length)
				? seatPositions[seatIndex]
				: {
					position: 'absolute',
					left: '50%',
					bottom: '15%',
					transform: 'translateX(-50%)'
				};
			return style;
		},

		// 调整按钮位置以适应游戏画面
		adjustButtonPosition() {
			// 由于现在使用精确定位，这个方法主要用于响应式调整
			this.$nextTick(() => {
				console.log('座位按钮位置已根据游戏画面精确定位');
			});
		},
	}, // <--- 这里加逗号

	// 组件销毁时清理定时器和事件监听器
	beforeUnmount() {
		if (this.leftInterval) {
			clearInterval(this.leftInterval)
		}
		if (this.rightInterval) {
			clearInterval(this.rightInterval)
		}
		if (this.upInterval) {
			clearInterval(this.upInterval)
		}
		if (this.downInterval) {
			clearInterval(this.downInterval)
		}
		if (this.fireInterval) {
			clearInterval(this.fireInterval)
		}
		if (this.fireEndTimer) {
			clearTimeout(this.fireEndTimer);
		}
		if (this.exitCountdownTimer) {
			clearInterval(this.exitCountdownTimer);
		}

		// 清理事件监听器
		window.removeEventListener('resize', this.adjustButtonPosition);
		window.removeEventListener('orientationchange', this.adjustButtonPosition);
	},
	mounted() {
		this.$nextTick(() => {
			setTimeout(() => {
				const avatar = this.$el.querySelector('.avatar-container.right');
				console.log('FishControl mounted, avatar:', avatar);
				if (avatar) {
					const rect = avatar.getBoundingClientRect();
					console.log('avatar rect:', rect);
					// 横竖屏自适应定位
					const isLandscape = window.innerWidth > window.innerHeight;
					let style;
					if (isLandscape) {
						// 横屏，页面整体旋转，使用fixed定位，top/left基于rect
						style = {
							position: 'fixed',
							top: rect.bottom + 18 + 'px', // 头像底部下方
							left: rect.left + 'px',
							zIndex: 20
						};
					} else {
						// 竖屏，常规绝对定位
						style = {
							position: 'absolute',
							top: rect.bottom + 18 + 'px',
							right: (window.innerWidth - rect.right) + 8 + 'px',
							zIndex: 20
						};
					}
					this.$emit('updateIdleCountdownStyle', style);
					console.log('emit updateIdleCountdownStyle', style);
				} else {
					console.warn('avatar-container.right not found');
				}
			}, 100);
		});
	}
}
</script>

<style scoped>
/* 捕鱼游戏控制界面样式 */
.fish-game-control {
	position: absolute;
	width: 100% !important;
	height: 100% !important;
	top: 0;
	left: 0;

	/* 禁用浏览器默认的选择和长按操作 */
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;

	/* 禁用长按上下文菜单 */
	-webkit-touch-callout: none;
	-webkit-tap-highlight-color: transparent;

	/* 禁用拖拽 */
	-webkit-user-drag: none;
	-khtml-user-drag: none;
	-moz-user-drag: none;
	-o-user-drag: none;
	user-drag: none;

	/* 禁用双击缩放 */
	touch-action: manipulation;
}

/* 状态信息样式 */
.status-container {
	position: absolute;
	top: 5%;
	left: 5%;
	z-index: 3;
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 10px;
	padding: 10px 15px;
	backdrop-filter: blur(4px);
}

.status-item {
	color: #fff;
	font-size: 16px;
	line-height: 1.5;
	text-shadow: 1px 1px 2px #000;
}

/* 头像容器样式 */
.avatar-container {
	position: absolute;
	top: 5%;
	right: 5%;
	z-index: 3;
	display: flex;
	align-items: center;
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 30px;
	padding: 5px 10px;
	backdrop-filter: blur(4px);
}

.avatar-item {
	margin: 0 5px;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.avatar-wrapper {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.avatar-image {
	display: none;
}

.avatar-name {
	color: white;
	font-size: 12px;
	margin-top: 2px;
	max-width: 60px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

/* 退出按钮样式 */
.exit-button {
	width: 30px;
	height: 30px;
	border-radius: 20px;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 5px;
	font-size: 16px;
	font-weight: bold;
	cursor: pointer;
	backdrop-filter: blur(4px);
	background-color: rgba(255, 0, 0, 0.8);
	color: white;
}

/* 头像叠加样式 */
.avatar-group-box {
	display: flex;
	align-items: center;
	position: relative;
}
.avatar-group-item {
	position: relative;
	margin-left: -12px;
}
.avatar-group-item:first-child {
	margin-left: 0;
}
.avatar-img-group {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	border: 2px solid #fff;
	box-shadow: 0 2px 8px #0003;
}

/* 座位按钮容器 */
.seats-container {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 3;
	pointer-events: none; /* 让容器本身不阻挡事件 */
}

.seat-button-wrapper {
	pointer-events: auto; /* 恢复按钮的事件响应 */
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

/* 座位按钮容器 - 上下两行显示 */
.seat-button-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 8px;
}

.game-button {
	background: none;
	border: none;
	outline: none;
	padding: 0;
	margin: 0 40rpx;
	margin-bottom: 5rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	box-shadow: none;
}

.game-button:focus {
	outline: none;
	border: none;
	box-shadow: none;
}

.button-text {
	white-space: nowrap; /* 防止文字换行 */
	overflow: hidden;
	text-overflow: ellipsis;
}

.game-button:active {
	transform: scale(0.95);
}

.play-button-img {
	width: 130rpx;
	height: 130rpx;
	display: block;
}

/* 座位头像样式 */
.seat-avatar-img {
	width: 70rpx;
	height: 70rpx;
	display: block;
	border-radius: 50%;
	border: 2px solid #fff;
	box-shadow: 0 2px 8px #0003;
	/* margin-bottom: 85rpx; */
}

/* 游戏控制区域 */
.game-controls {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 40%;
	z-index: 2;
	pointer-events: auto;
}

/* 方向轮盘样式 */
.directional-pad {
	position: absolute;
	left: 12vmax;
	bottom: 8vmax;
	width: 18vmax;
	height: 18vmax;
}

.dpad-circle {
	position: relative;
	width: 100%;
	height: 100%;
	background-image: url('/static/dpad/circle.png');
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
	border: none;
	background-color: transparent;
}

.dpad-btn {
	position: absolute;
	background: transparent !important;
	border: none !important;
	border-radius: 0;
	width: 6vmax;
	height: 6vmax;
	cursor: pointer;
	transition: all 0.2s ease;
	backdrop-filter: none;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: none !important;
	min-width: unset;
	min-height: unset;
	padding: 0;
	outline: none !important;

	/* 禁用长按右键菜单和选择 */
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-webkit-touch-callout: none;
	-webkit-tap-highlight-color: transparent;
	touch-action: manipulation;
}

.dpad-btn:focus {
    outline: none;
}

.dpad-btn:active {
	transform: scale(0.95);
	filter: brightness(0.9);
	opacity: 0.9;
}

.dpad-btn.up {
	top: 1vmax;
	left: 50%;
	transform: translateX(-50%);
}

.dpad-btn.down {
	bottom: 1vmax;
	left: 50%;
	transform: translateX(-50%);
}

.dpad-btn.left {
	left: 1vmax;
	top: 50%;
	transform: translateY(-50%);
}

.dpad-btn.right {
	right: 1vmax;
	top: 50%;
	transform: translateY(-50%);
}

.dpad-arrow-img {
  width: 100%;
  height: 100%;
  display: block;
  margin: auto;
}

/* 右侧功能按钮 */
.action-buttons {
	position: absolute;
	right: 5vmax;
	bottom: 5vmax;
	width: 20vmax;
	height: 20vmax;
}

.action-btn,
.fire-button {
  background: transparent;
	border: none;
	padding: 0;
	margin: 0;
	outline: none;
  border-radius: 0 !important;
  transform-origin: 50% 50%;

  /* 确保按钮可以正常响应触摸和鼠标事件 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.action-btn:active,
.fire-button:active {
	transform: scale(0.95);
	filter: brightness(0.9);
	opacity: 0.9;
}

/* 发射按钮样式 */
.fire-button {
	width: 8vmax;
	height: 8vmax;
	border: none;
	border-radius: 0;
	background: none;
	box-shadow: none;
	color: inherit;
	font-size: inherit;
	align-items: center;
	justify-content: center;
	right: 0;
	bottom: 0;
	transform: none;
	z-index: 2;
	display: flex;
	backdrop-filter: none;
	min-width: unset;
	min-height: unset;
	padding: 0;
}

/* 功能按钮位置 */
.weapon-switch-btn {
	background: rgba(0, 150, 255, 0.8);
	left: -6vmax;
	bottom: -4vmax;
	width: 8vmax;
	height: 6vmax;
	font-size: 1.5vmax;
	transform: none !important;
}

.auto-btn {
	background: rgba(0, 200, 0, 0.8);
	left: 6vmax;
	bottom: 5vmax;
	width: 8vmax;
	height: 6vmax;
	font-size: 1.5vmax;
}

.action-btn > text {
	white-space: nowrap;
}

.fire-button > text {
	white-space: nowrap;
}

/* 按钮交互效果 */
.fire-button:active {
  /* 移除 filter、opacity、transform，仅保留如需的 background 或其它样式 */
  background: rgba(255, 0, 0, 0.7);
}

.auto-btn.auto-active {
	background: rgba(255, 0, 0, 0.8);
}

/* 新增 action-btn-img 样式 */
.action-btn-img {
  width: 100%;
  height: 100%;
  display: block;
  margin: 0;
  pointer-events: none;
  /* background: #00cfff; */
}

.action-btn-img.active {
  filter: brightness(1.5);
}

/* fire-group 环形分布样式 */
.fire-group {
  position: fixed;
  right: 10vmax;
  bottom: 0;
  width: 24vmax;
  height: 24vmax;
  z-index: 1000;
}
.ring-btn-outer,
.fire-btn-outer {
  position: absolute;
  right: 12vmax;
  bottom: 12vmax;
  width: 6vmax;
  height: 6vmax;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(50%, 50%);
}
.fire-btn-outer.flex-col {
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.fire-btn-outer {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 10vmax;
  height: 10vmax;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(-50%, -50%);
  z-index: 2000;
  /* background: rgba(0,255,0,0.2);  调试用，现已去除 */
}
.fire-button.ring-btn-inner {
  position: relative;
}
.auto-mode-label {
  margin-top: -35px;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  text-shadow:
    0 2px 8px #000,
    0 0 2px #00cfff,
    2px 0 0 #7b4cff,
    -2px 0 0 #7b4cff,
    0 2px 0 #7b4cff,
    0 -2px 0 #7b4cff;
  white-space: nowrap;
  z-index: 10;
  pointer-events: none;
  line-height: 1.1;
  padding-bottom: 2px;
}
.ring-btn-inner {
  width: 100%;
  height: 100%;
  background: none;
  border: none;
  border-radius: 0;
  transform-origin: 50% 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.ring-btn-inner[style*="--angle"] {
  /* 只有功能按钮需要环形分布 */
  transform: rotate(var(--angle)) translateY(-9vmax) rotate(calc(-1 * var(--angle)));
}
.ring-btn-inner[style*="--angle"]:active {
  transform: scale(0.95) rotate(var(--angle)) translateY(-9vmax) rotate(calc(-1 * var(--angle)));
  filter: brightness(1.2);
  opacity: 0.9;
}
.fire-button:active {
  transform: scale(0.95);
  filter: brightness(1.2);
  opacity: 0.9;
}
.fire-button[disabled], .ring-btn-inner[disabled] {
  filter: grayscale(1) brightness(0.7) opacity(0.7);
  cursor: not-allowed;
}
/* 移除 .corner-dot 样式，新增 .center-dot 样式 */
.center-dot {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00cfff;
  z-index: 1001;
  box-shadow: 0 0 8px #00cfff;
}
.circle-label {
  position: absolute;
  color: #00cfff;
  font-size: 24px;
  font-weight: bold;
  z-index: 1002;
  opacity: 0.8;
}
.label-1 {
  right: 6vmax;
  bottom: 18vmax;
}
.label-2 {
  right: 18vmax;
  bottom: 18vmax;
}
.label-3 {
  right: 18vmax;
  bottom: 6vmax;
}
.label-4 {
  right: 6vmax;
  bottom: 6vmax;
}
.auto-mode-label {
  position: absolute;
  left: 50%;
  top: 100%;
  transform: translateX(-50%) translateY(8px);
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 2px 8px #000, 0 0 2px #00cfff;
  white-space: nowrap;
  pointer-events: none;
}

/* 新增 idle-countdown-bar 样式 */
.idle-countdown-bar {
	position: absolute;
	right: 70px;
	top: 75px;
	background: rgba(0,0,0,0.3);
	border-radius: 16px;
	height: 24px;
	min-width: 50px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 12px;
	z-index: 20;
	border: 2px solid #FFD600;
}
.idle-countdown-num {
	color: #FFD600;
	font-size: 16px;
	font-weight: bold;
	letter-spacing: 1px;
}
.avatar-container.left {
	position: absolute;
	top: 5%;
	left: 0;
	margin-left: 5%;
	gap: 10px;
	
}

/* 游戏中的用户信息容器 */
.game-user-info-container {
	position: absolute;
	top: 5%;
	left: 10%;
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
	z-index: 3;
}



/* 用户信息框 */
.user-info-box {
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 30px;
	padding: 5px 16px;
	display: flex;
	align-items: center;
	gap: 8px;
	backdrop-filter: blur(4px);
	box-shadow: 0 2px 8px #0003;
}

.user-avatar {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	border: 2px solid #fff;
	background: #fff;
	box-shadow: 0 2px 8px #0003;
}

.user-details {
	display: flex;
	flex-direction: column;
	gap: 2px;
	justify-content: center;
}

.user-nickname {
	color: #fff;
	font-size: 18px;
	font-weight: bold;
	max-width: 80px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.user-seat {
	color: #F17676;
	font-size: 14px;
	font-weight: bold;
}

/* 统计信息容器 */
.user-stats-container {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

/* 统计信息框 */
.stat-box {
	background-color: rgba(0, 0, 0, 0.7);
	border-radius: 30px;
	padding: 5px 10px;
	display: flex;
	align-items: center;
	gap: 8px;
	backdrop-filter: blur(4px);
	box-shadow: 0 2px 8px #0003;
	min-width: 80px;
	cursor: pointer;
}

.stat-box:active {
	transform: scale(0.95);
	opacity: 0.8;
}

.stat-icon {
	width: 20px;
	height: 20px;
	flex-shrink: 0;
}

.stat-value {
	color: #fff;
	font-size: 16px;
	font-weight: bold;
	flex: 1;
}

.add-icon,
.exchange-icon {
	width: 20px;
	height: 20px;
	flex-shrink: 0;
	opacity: 0.8;
}
.avatar-container.right {
	position: absolute;
	/* 默认：竖屏+旋转90度（视觉横屏） */
	top: 5%;
	right: 0;
	left: auto;
	margin-right: 10%;
	margin-left: 0;
}
.setting-button {
  width: 30px;
  height: 30px;
  border: none;
  background: none;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  color: white;
  box-shadow: none;
  backdrop-filter: none;
}
.setting-btn-icon {
  width: 25px;
  height: 25px;
  display: block;
}
/* 横屏适配：物理横屏时控件定位修正 */
@media screen and (orientation: landscape) {
  .fish-game-control {
    width: 100vw !important;
    height: 100vh !important;
    left: 0 !important;
    top: 0 !important;
  }
  .avatar-container.right {
    top: 5%;
    right: 0;
    left: auto;
    margin-right: 10%;
    margin-left: 0;
  }
  .avatar-container.left {
    top: 5%;
    left: 0;
    right: auto;
    margin-left: 5%;
    margin-right: 0;
  }
  /* 物理横屏模式下的座位按钮调整（仅针对物理横屏，视觉横屏由JavaScript处理） */
  .game-button {
	margin-bottom: 5rpx;
    padding: 0 !important;
  }

  .play-button-img {
    width: 60rpx !important;
    height: 60rpx !important;
  }

  .seat-avatar-img {
    width: 30rpx !important;
    height: 30rpx !important;
  }
  /* 提高优先级，横屏下倒计时大小与竖屏一致，使用px确保一致性 */
  .fish-game-control .idle-countdown-bar {
    font-size: 16px !important;
    height: 24px !important;
    min-width: 50px !important;
    padding: 0 12px !important;
    border-radius: 16px !important;
    border: 2px solid #FFD600 !important;
  }
  .fish-game-control .idle-countdown-num {
    font-size: 16px !important;
    letter-spacing: 1px !important;
  }



  /* 横屏下的用户信息区域保持相同布局，无需特殊调整 */
}

/* 投币弹窗样式 */
.coin-buttons-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  padding: 16px 0;
  width: 100%;
  max-width: 100%;
}

.coin-button {
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  color: #fff;
  border: none;
  border-radius: 12px;
  padding: 16px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
  /* 确保按钮不会换行 */
  white-space: nowrap;
  overflow: hidden;
}

.coin-button:hover {
  background: linear-gradient(135deg, #357ABD, #2968A3);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(74, 144, 226, 0.4);
}

.coin-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.coin-button-text {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 4px;
  color: #fff;
  white-space: nowrap;
}

.coin-button-value {
  font-size: 16px;
  font-weight: bold;
  color: #FFD700;
  white-space: nowrap;
}

.no-coin-tip {
  text-align: center;
  color: #fff;
  font-size: 14px;
  padding: 20px;
}

/* 横屏模式下的投币按钮适配 */
@media (orientation: landscape) {
  .coin-buttons-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 12px 0;
  }

  .coin-button {
    min-height: 60px;
    padding: 12px 6px;
  }

  .coin-button-text {
    font-size: 12px;
  }

  .coin-button-value {
    font-size: 14px;
  }
}








</style>
