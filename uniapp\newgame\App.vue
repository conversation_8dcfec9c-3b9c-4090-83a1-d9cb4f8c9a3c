<template>
	<view>
		<slot />
	</view>
</template>

<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			this.setScreenOrientation()
			this.disableContextMenu()
			this.disableZoom()
		},
		onShow: function() {
			console.log('App Show')
			this.setScreenOrientation()
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			setScreenOrientation() {
				// #ifdef APP-PLUS
				try {
					plus.screen.lockOrientation('portrait-primary')
				} catch (error) {
					console.warn('设置屏幕方向失败:', error)
				}
				// #endif
			},
			// 禁用右键菜单
			disableContextMenu() {
				// #ifdef H5
				document.addEventListener('contextmenu', function(e) {
					e.preventDefault()
					return false
				}, false)

				// 禁用选择文本
				document.addEventListener('selectstart', function(e) {
					e.preventDefault()
					return false
				}, false)

				// 禁用拖拽
				document.addEventListener('dragstart', function(e) {
					e.preventDefault()
					return false
				}, false)
				// #endif
			},
			// 禁用缩放
			disableZoom() {
				// #ifdef H5
				// 禁用双击缩放
				let lastTouchEnd = 0
				document.addEventListener('touchend', function(event) {
					const now = (new Date()).getTime()
					if (now - lastTouchEnd <= 300) {
						event.preventDefault()
					}
					lastTouchEnd = now
				}, false)

				// 禁用手势缩放
				document.addEventListener('gesturestart', function(e) {
					e.preventDefault()
				}, false)

				document.addEventListener('gesturechange', function(e) {
					e.preventDefault()
				}, false)

				document.addEventListener('gestureend', function(e) {
					e.preventDefault()
				}, false)

				// 禁用键盘缩放 (Ctrl + +/-)
				document.addEventListener('keydown', function(e) {
					if ((e.ctrlKey || e.metaKey) && (e.keyCode === 61 || e.keyCode === 107 || e.keyCode === 173 || e.keyCode === 109 || e.keyCode === 187 || e.keyCode === 189)) {
						e.preventDefault()
					}
				}, false)

				// 禁用滚轮缩放
				document.addEventListener('wheel', function(e) {
					if (e.ctrlKey || e.metaKey) {
						e.preventDefault()
					}
				}, { passive: false })
				// #endif
			}
		}
	}
</script>

<style>
	/*每个页面公共css */

	/* 全局禁用右键菜单和缩放 */
	* {
		/* 禁用文本选择 */
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;

		/* 禁用长按菜单 */
		-webkit-touch-callout: none;

		/* 禁用点击高亮 */
		-webkit-tap-highlight-color: transparent;

		/* 禁用拖拽 */
		-webkit-user-drag: none;
		-khtml-user-drag: none;
		-moz-user-drag: none;
		-o-user-drag: none;
		user-drag: none;

		/* 禁用双击缩放 */
		touch-action: manipulation;
	}

	/* 针对HTML和body的特殊设置 */
	html, body {
		/* 禁用缩放 */
		-ms-touch-action: manipulation;
		touch-action: manipulation;

		/* 禁用滚动条 */
		overflow-x: hidden;

		/* 固定视口，防止缩放 */
		width: 100%;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
	}

	/* 全局背景色 */
	page {
		background-color: #180F29;
		/* 禁用缩放 */
		-ms-touch-action: manipulation;
		touch-action: manipulation;
	}
	
	/* 通用容器样式 */
	.container {
		background: #180F29;
		/* min-height: 100vh; */
	}
	
	/* 通用顶部栏样式 */
	.top-bar {
		display: flex;
		align-items: center;
		padding-bottom: 22rpx;
		justify-content: flex-start;
		background: url('/static/home/<USER>') no-repeat center/100vw 210rpx;
		width: 100vw;
		height: 180rpx;
		z-index: 100;
	}
	
	/* 通用用户信息包装器 */
	.user-info-wrap {
		display: flex;
		margin-top: 60rpx;
	}
	
	/* 通用头像包装器 */
	.avatar-wrap {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 5rpx;
	}
	
	/* 通用头像等级包装器 */
	.avatar-level-wrap.large {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* 通用小头像 */
	.avatar.small {
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		position: absolute;
		left: 35rpx;
		top: 30rpx;
		z-index: 2;
		object-fit: cover;
	}
	
	/* 通用等级头像背景 */
	.level-avatar-bg.large {
		width: 140rpx;
		height: 140rpx;
		border-radius: 50%;
		position: absolute;
		left: 0;
		top: 0;
		z-index: 3;
		object-fit: cover;
		pointer-events: none;
	}
	
	/* 通用用户详情 */
	.user-detail {
		flex: 1;
		margin-left: 12rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	
	/* 通用昵称 */
	.nickname {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		margin-left: 10rpx;
	}
	
	/* 通用等级资产行 */
	.level-assets-row {
		display: flex;
		align-items: center;
		margin-top: 6rpx;
	}
	
	/* 通用资产项 */
	.asset-item {
		display: flex;
		align-items: center;
		margin-right: 12rpx;
		background-color: #180F2D;
		padding: 4rpx 5rpx;
		border-radius: 30rpx;
		border: 1rpx solid #7C6649;
		height: 40rpx;
		max-width: 190rpx;
		min-width: 120rpx;
	}
	
	.asset-item:last-child {
		margin-right: 0;
	}
	
	/* 通用图标 */
	.coin-icon,
	.diamond-icon,
	.asset-action-icon{
		width: 40rpx;
		height: 40rpx;
	}
	
	/* 通用资产操作图标 */
	.asset-action-icon {
		margin-left: 5rpx;
		object-fit: contain;
	}
	
	/* 通用资产数字 */
	.asset-num {
		font-size: 26rpx;
		color: #fff;
		/* font-weight: bold; */
		flex: 1;
	}
	
	.avatar-level-wrap {
		position: relative;
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.level-avatar-bg {
		width: 110rpx;
		height: 110rpx;
		border-radius: 50%;
		position: absolute;
		z-index: 3;
		object-fit: cover;
		pointer-events: none;
	}
	
	/* 弹窗样式 */
	.modal-image {
	  width: 50px !important;
	  height: 40px !important;
	  object-fit: contain;
	}
	/* 按钮变体 */
	.universal-modal-content .btn-cancel {
	  background: #ccc;
	  color: #666;
	  flex: 1;
	  /* 确保取消按钮也完美居中 */
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  padding: 0;
	}
	
	.universal-modal-content .btn-confirm {
	  background: #4A90E2;
	  color: #fff;
	  flex: 1;
	  /* 确保确认按钮也完美居中 */
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  padding: 0;
	}
	
	.universal-modal-content .btn-single {
	  background: #4A90E2;
	  color: #fff;
	  width: 100%;
	  height: 50px;
	  margin: 16px 0 0 0;
	  /* 确保单个按钮也完美居中 */
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  padding: 0;
	}
	
	/* 按钮容器 */
	.universal-modal-content .modal-buttons {
	  display: flex;
	  justify-content: center;
	  gap: 12px;
	  margin-top: 16px;
	  width: 100%;
	}
	
	/* 特殊元素样式 */
	.universal-modal-content .info-box {
	  background: #3F3055;
	  border-radius: 8px;
	  padding: 8px;
	  display: flex;
	  align-items: center;
	  justify-content: space-between;
	  margin-bottom: 12px;
	  width: 95%;
	}
	
	/* 修复 input-group 内输入框文字居中问题 */
	.universal-modal-content .input-group {
	  text-align: left;
	}
	
	.universal-modal-content .input-group input,
	.universal-modal-content .input-group .modal-input,
	.universal-modal-content .input-group .bind-input {
	  text-align: left;
	}
	
	.universal-modal-content .bind-input-group {
	  text-align: left;
	}
	
	.universal-modal-content .bind-input-group input,
	.universal-modal-content .bind-input-group .modal-input,
	.universal-modal-content .bind-input-group .bind-input {
	  text-align: left;
	}
	
	.universal-modal-content .info-box image {
	  width: 24px;
	  height: 24px;
	  margin: 0 8px;
	}
	
	.universal-modal-content .box-value {
	  color: #FFA500;
	  font-weight: bold;
	  flex: 1;
	  text-align: right;
	}
	
	.universal-modal-content .exchange-rate {
	  display: flex;
	  align-items: center;
	  margin: 8px 0;
	  flex-wrap: wrap;
	}
	
	.universal-modal-content .rate-score {
	  color: #FFA500;
	  font-weight: bold;
	}
	
	.universal-modal-content .rate-coin {
	  color: #4A90E2;
	  font-weight: bold;
	}
	
	/* 功能网格 */
	.universal-modal-content .func-grid {
	  display: grid;
	  grid-template-columns: repeat(2, 1fr);
	  gap: 12px;
	  padding: 12px 0;
	  width: 100%;
	}
	
	.universal-modal-content .func-item {
	  display: flex;
	  flex-direction: column;
	  align-items: center;
	  padding: 12px;
	  border-radius: 8px;
	  background: rgba(255, 255, 255, 0.1);
	  cursor: pointer;
	  transition: all 0.3s ease;
	  min-height: 60px;
	}
	
	.universal-modal-content .func-item:hover {
	  background: rgba(255, 255, 255, 0.2);
	}
	/* 设置项 */
	.universal-modal-content .setting-item {
	  display: flex;
	  align-items: center;
	  justify-content: space-between;
	  padding: 12px 0;
	  height: 40px;
	}
	
	/* 设置开关 */
	.universal-modal-content .setting-switch {
	  width: 50px;
	  height: 22px;
	  border-radius: 11px;
	  background: #ccc;
	  position: relative;
	  cursor: pointer;
	  transition: all 0.3s ease;
	}
	
	.universal-modal-content .setting-switch.on {
	  background: #4A90E2;
	}
	
	.universal-modal-content .switch-knob {
	  width: 16px;
	  height: 16px;
	  border-radius: 50%;
	  background: #fff;
	  position: absolute;
	  left: 3px;
	  top: 50%;
	  transform: translateY(-50%);
	  transition: left 0.3s ease;
	}
	
	.universal-modal-content .setting-switch.on .switch-knob {
	  left: 31px;
	}
	
	/* 客服二维码 */
	.universal-modal-content .service-qr {
	  width: 120px;
	  height: 120px;
	  margin-bottom: 8px;
	}
	
	/* 玩法说明 */
	.universal-modal-content .rule-dialog-content {
	  max-height: 200px;
	  overflow-y: auto;
	  padding: 12px 0;
	  line-height: 1.6;
	  text-align: left;
	  color: #fff;
	  font-size: 14px;
	}
	/* 数字显示 */
	.universal-modal-content .number {
	  font-size: 18px;
	  color: #2196F3;
	  font-weight: bold;
	  margin-bottom: 12px;
	}
	
	/* 按钮样式 */
	.universal-modal-content button {
	  border: none;
	  font-size: 14px;
	  border-radius: 20px;
	  padding: 0;
	  cursor: pointer;
	  margin: 4px;
	  min-width: 60px;
	  height: 40px;
	  background: #4A90E2;
	  color: #fff;
	  transition: all 0.2s ease;
	  /* 新增：完美的文字居中 */
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  text-align: center;
	}
	
	.universal-modal-content button:hover {
	  opacity: 0.9;
	}
	
	/* 支持背景图片的按钮 */
	.universal-modal-content .submit-btn,
	.universal-modal-content .bind-submit-btn {
	  background: url('/static/mine/button.png') no-repeat center/100% 100%;
	  height: 60px;
	  line-height: 60px;
	  border-radius: 30px;
	  font-size: 16px;
	  /* 确保背景图片按钮也完美居中 */
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  padding: 0;
	}
	
	.universal-modal-content .submit-btn:hover,
	.universal-modal-content .bind-submit-btn:hover {
	  background: url('/static/mine/button.png') no-repeat center/100% 100%;
	  opacity: 0.9;
	}
	
	.universal-modal-content .func-item image {
	  width: 24px;
	  height: 24px;
	  margin-bottom: 6px;
	}
	
	.universal-modal-content .func-item view {
	  font-size: 12px;
	  text-align: center;
	  color: #fff;
	}
</style>