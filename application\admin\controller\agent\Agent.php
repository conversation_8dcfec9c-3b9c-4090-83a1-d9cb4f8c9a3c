<?php

namespace app\admin\controller\agent;

use app\common\controller\Backend;
use app\common\model\MoneyLog;
use app\common\model\User;
use fast\Random;
use think\Db;
use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Validate;

/**
 * 代理管理
 *
 * @icon fa fa-user
 */
class Agent extends Backend
{
    protected $searchFields = 'username,nickname,mobile';

    public function _initialize()
    {
        $this->noNeedRight = array('select_list');

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            $admin = get_admin_info();
            $is_show = 1;
            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
                //代理只查看自己的
                $where_arr['a.id'] = $admin['admin_id'];
                $is_show = 0;
            }

            if ($search_arr) {
                foreach ($search_arr as $k => $v) {
                    //执行时间
                    if ($k == 'createtime'){//创建时间
                        $create_time = explode(' - ',$v);
                        $where_arr['a.createtime'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    } else {
                        $str = 'a.'.$k;
                        $where_arr[$str] = $v;
                    }

                    unset($search_arr[$k]);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('admin')
                ->alias('a')
                ->where('a.is_agent',1)
                ->where($where)
                ->where($where_arr)
                ->count();

            $list = Db::name('admin')
                ->alias('a')
                ->join('user u','a.id = u.agent_id')
                ->where('a.is_agent',1)
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->field('a.*,u.money,u.score,u.id as user_id')
                ->limit($offset, $limit)
                ->select();

            foreach ($list as $index => &$v) {
                $v['is_show'] = $is_show;
                $v['user_num'] = Db::name('user')->where('p_id',$v['id'])->count();
            }

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $time = time();

                $params['createtime'] = $time;
                $params['updatetime'] = $time;

                Db::startTrans();
                try {
                    if (!Validate::is($params['password'], '\S{6,30}')) {
                        exception(__("Please input correct password"));
                    }
                    if (Db::name('admin')->where('username',$params['mobile'])->find()) {
                        exception(__("手机号码已存在，请联系管理员"));
                    }
                    $user = Db::name('user')->where('mobile',$params['mobile'])->find();
                    if ($user) {
                        exception(__("用户已存在，请到用户管理设置代理"));
                    }

                    $params['salt'] = Random::alnum();
                    $params['password'] = $this->auth->getEncryptPassword($params['password'], $params['salt']);
                    $params['avatar'] = '/assets/img/avatar.png'; //设置新管理员默认头像。

                    $agent_id = Db::name('admin')->insertGetId([
                        'username' => $params['mobile'],
                        'nickname' => $params['nickname'],
                        'password' => $params['password'],
                        'salt' => $params['salt'],
                        'avatar' => $params['avatar'],
                        'mobile' => $params['mobile'],
                        'createtime' => $params['createtime'],
                        'updatetime' => $params['updatetime'],
                        'fee' => $params['fee'],
                        'status' => $params['status'],
                        'is_agent' => 1,
                    ]);

                    $money = config('site.gift_money');
                    $user_id = Db::name('user')->insertGetId([
                        'username' => $params['mobile'],
                        'nickname' => $params['nickname'],
                        'password' => $params['password'],
                        'salt' => $params['salt'],
                        'avatar' => $params['avatar'],
                        'mobile' => $params['mobile'],
                        'createtime' => $params['createtime'],
                        'updatetime' => $params['updatetime'],
                        'status' => 'normal',
                        'agent_id' => $agent_id,
                        'money' => $money
                    ]);

                    //写入日志
                    MoneyLog::create(['user_id' => $user_id, 'money' => $money, 'before' => 0, 'after' => $money, 'memo' => '后台添加账号赠送','status'=>1]);

                    Db::name("auth_group_access")->insert([
                        'uid' => $agent_id,
                        'group_id' => 6,//代理固定ID
                    ]);

                    Db::name('admin')->where('id',$agent_id)->update(['user_id' => $user_id]);

                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success('成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = Db::name('admin')->where('id',$ids)->find();

        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                Db::startTrans();
                try {
                    $time = time();
                    $params['updatetime'] = $time;
                    $user_update['updatetime'] = $time;

                    if ($row['mobile'] != $params['mobile']) {
                        // 添加手机格式验证
                        if (!validate_mobile($params['mobile'])) {
                            exception(__("手机号码格式不正确"));
                        }
                        if (Db::name('admin')->where('id','<>',$ids)->where('mobile',$params['mobile'])->find()) {
                            exception(__("手机号码已存在"));
                        }
                        if (Db::name('user')->where('p_id','<>',$ids)->where('mobile',$params['mobile'])->find()) {
                            exception(__("手机号码已存在"));
                        }
                        $params['username'] = $params['mobile'];
                        $user_update['mobile'] = $params['mobile'];
                    }
                    $user_update['nickname'] = $params['nickname'];
                    $user_update['status'] = $params['status'];
                    Db::name('user')->where('agent_id',$ids)->update($user_update);
                    Db::name('admin')->where('id',$ids)->update($params);

                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success('成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $row['is_show'] = 1;
        $admin = get_admin_info();
        if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
            $row['is_show'] = 0;
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if ($ids) {
            Db::startTrans();
            try {
                $agent = Db::name('admin')->where('id',$ids)->find();
                if (!$agent) {
                    exception(__("代理不存在"));
                }
                Db::name('admin')->where('id',$ids)->delete();
                Db::name('user')->where('agent_id',$ids)->delete();

                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }

            $this->success('成功');
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    /**
     * 修改密码
     * <AUTHOR>
     * @date 2025-6-13
     * @return
     */
    public function reset_password($ids = NULL)
    {
        $row = Db::name('admin')->where('id',$ids)->find();
        if (!$row)
            $this->error(__('No Results were found'));

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $user = new User();
                $result = $user->reset_password($params);
                if ($result['code'] == 1) {
                    $this->success('成功');
                }
                $this->error($result['msg']);
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public function select_list()
    {
        if($this->request->isAjax()){

            $page = $this->request->post("pageNumber/d", 1);
            $limit = $this->request->post("pageSize/d", 15);
            $keyword= $this->request->post("name", '');

            $keyValue = $this->request->post('keyValue','');
            if ($keyValue) {
                $keyword = $keyValue;
            }

            $list = Db::name('admin')
                ->field('concat(id , " - ",nickname," - ",INSERT(mobile,4,3,\'***\')) as name,id')
                ->where(['nickname|id|mobile'=>['like','%'.$keyword.'%']])
                ->where('is_agent',1)
                ->order('id', 'desc')
                ->page($page, $limit)
                ->paginate($limit,false,['page'=>$page]);

            $result = array("total" => $list->total(), "list" => $list->items());

            return json($result);
        }
    }

} 