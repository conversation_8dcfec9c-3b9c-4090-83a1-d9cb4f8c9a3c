﻿import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('http://localhost:5173/#/pages/login/index');

  await page.locator('input[type="password"]').fill('123456');

  await page.locator('input[type="text"]').fill('18111111111');
  await page.locator('uni-button').click();
  await page.getByText('街机').click();
  await page.locator('uni-view').filter({ hasText: /^一筒天下.*$/ }).getByRole('img').first().click();
  await page.locator('uni-button').filter({ hasText: 'play' }).click();
  await page.locator('uni-button').filter({ hasText: '×' }).click();
  await page.getByText('确定', { exact: true }).click();
});