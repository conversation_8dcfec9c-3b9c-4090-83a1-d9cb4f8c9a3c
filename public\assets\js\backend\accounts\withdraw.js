define(['jquery', 'bootstrap', 'backend', 'table', 'form','selectpage'], function ($, undefined, Backend, Table, Form,selectPage) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'accounts/withdraw/index'+ location.search,
                    add_url: 'accounts/withdraw/add',
                    edit_url: '',
                    del_url: '',
                    multi_url: '',
                    table: 'withdraw',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='agent_id']", form).addClass("selectpage").data("source", "agent/agent/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据

                //这里我们手动设置底部的值
                $("#pending_pay").text(data.extend.pending_pay);
                $("#paid_pay").text(data.extend.paid_pay);
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'w.id',
                sortName: 'w.id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('id'), sortable: true, operate:false},
                        {field: 'avatar', title: __('头像'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'agent_id', title: __('代理商'), operate:false,formatter: function (value, row, index) {
                                if (row.nickname) {
                                    return row.nickname+'<br>'+row.mobile+'<br>'+row.agent_id;
                                }else{
                                    return row.agent_id;
                                }
                            }},
                        {field: 'type', title: __('转账类型'),searchList: {"1":__('微信'),"2":__('支付宝')},formatter: function (value, row, index) {
                                if (row.type == 2) {
                                    return '支付宝'
                                }else{
                                    return '微信'
                                }
                            }},
                        {field: 'money', title: __('申请提现金额(元)'), operate:false},
                        {field: 'real_money', title: __('实际到账金额(元)'), operate:false},
                        // {field: 'fee_money', title: __('提现手续费金额(元)'), operate:false},
                        // {field: 'fee', title: __('提现手续费费率(%)'), operate:false},
                        {field: 'create_time', title: __('申请时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'status_timestamp', title: __('完成时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'batch_id', title: __('付款单号'), operate:false},
                        {field: 'status_name', title: __('提现状态'), operate:false,formatter:Table.api.formatter.flag,custom:{'待打款':'danger','已打款':'success','已驳回':'info','失败':'warning'}},
                        {field: 'status',visible:false, title: __('提现状态'),searchList: {"0":__('待打款'),"1":__('已打款'),"2":__('已驳回'),"3":__('失败')}},
                        {field: 'remark', title: __('备注'), operate:false},
                        {field: 'operate', title: __('Operate'), table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'detail',
                                    text: __('审核'),
                                    icon: 'fa fa-list',
                                    extend:'data-area=\'["50%","70%"]\'',
                                    classname: 'btn btn-info btn-xs btn-detail btn-dialog',
                                    url: 'agent/withdraw/detail',
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        if (row.type == 2 || row.status==0){
                                            return true;
                                        }else{
                                            return false;
                                        }
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'merchant',
                                    title: __('代理信息'),
                                    text:'代理信息',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","50%"]\'',
                                    icon: 'fa fa-user-circle-o',
                                    url: function (row){
                                        return "agent/agent/index?id=" + row.agent_id;
                                    }
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        detail: function () {
            $(document).on('click', '.btn-refuse', function () {
                var id = $("input[name='id']").val();
                var that = this;
                Layer.confirm(__('确定驳回吗?'), {
                    icon: 3,
                    title: '提示'
                }, function () {
                    $.ajax({
                        type:"post",
                        url:"agent/withdraw/rejected",
                        data:{'ids' : id},
                        success : function(ret) {
                            layer.msg(ret.msg, {
                                icon: 1,
                                time: 2000 //2秒关闭（如果不配置，默认是3秒）
                            }, function(){
                                var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                                parent.$("a.btn-refresh").trigger("click");
                                parent.layer.close(index); //再执行关闭
                            });
                        }
                    });
                });

            });

            $(document).on('click', '.btn-adopt', function () {
                var id = $("input[name='id']").val();
                var image_deposit = $("input[name='row[image_deposit]']").val();

                var that = this;
                Layer.confirm(__('确定已打款吗?'), {
                    icon: 3,
                    title: '提示'
                }, function () {
                    $.ajax({
                        type:"post",
                        url:"agent/withdraw/successed",
                        data:{'ids' : id,'image_deposit':image_deposit},
                        success : function(ret) {
                            if (ret.code == 1){
                                var icon = 1;
                                var time = 3000;
                            }else{
                                var icon = 5;
                                var time = 5000;
                            }
                            layer.msg(ret.msg, {
                                icon: icon,
                                time: time //2秒关闭（如果不配置，默认是3秒）
                            }, function(){
                                var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                                parent.$("a.btn-refresh").trigger("click");
                                parent.layer.close(index); //再执行关闭
                            });
                        }
                    });
                });

            });
            Controller.api.bindevent();
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});