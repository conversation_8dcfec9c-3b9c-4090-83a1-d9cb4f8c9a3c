<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\MoneyLog;
use app\common\model\ScoreLog;
use think\Db;

/**
 * 商户
 * @Authod Jw
 * @Time 2021/6/15
 * @package app\api\controller
 */
class Merchant extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];
    protected $user_id;
    protected $merchant_id;

    public function _initialize()
    {
        parent::_initialize();

        $user_info = $this->auth->getUserinfo();
        $this->merchant_id = $user_info['merchant_id'];
        $this->user_id = $user_info['id'];
        if (empty($this->merchant_id)) {
            $this->error('您还未注册商户，请联系客服');
        }
    }

    /**
     * 商户信息
     * <AUTHOR>
     * @date 2025-6-26
     * @return
     */
    public function detail()
    {
        // post提交
        if ($this->request->isPost()) {
            $info = Db::name('hx_merchant')
                ->where('id', $this->merchant_id)
                ->field('id,name,logo,business_hours')
                ->find();
            if (!$info) {
                $this->error('没有查找到商户信息');
            }

            // 1.今日积分和销量统计
            $todayStats = Db::name('hx_log')
                ->where('merchant_id', $this->merchant_id)
                ->where('status',1)
                ->whereTime('createtime','today')
                ->field('SUM(score) as total_score, COUNT(id) as today_count')
                ->find();

            // 昨日同期数据
            $yesterdayStats = Db::name('hx_log')
                ->where('merchant_id', $this->merchant_id)
                ->where('status',1)
                ->whereTime('createtime','yesterday')
//                ->cache(true, 86400) // 有效期24小时（86400秒）
                ->field('SUM(score) as total_score, COUNT(id) as yesterday_count')
                ->find();

            // 计算变化率
            $scoreRate = $this->calcRate($todayStats['total_score'] ?? 0, $yesterdayStats['total_score'] ?? 0);
            $countRate = $this->calcRate($todayStats['today_count'] ?? 0, $yesterdayStats['yesterday_count'] ?? 0);

            // 合并统计结果
            $info['stats'] = [
                'today_score' => $todayStats['total_score'] ?? 0,
                'score_rate'  => $scoreRate,
                'today_count' => $todayStats['today_count'] ?? 0,
                'count_rate'  => $countRate,
            ];

            $info['logo'] = config('site.domain').$info['logo'];
            $this->success('成功获取',$info);
        }else{
            $this->error('请求方式不正确');
        }
    }

    // 新增私有方法计算变化率
    private function calcRate($today, $yesterday)
    {
        if ($yesterday == 0) {
            if ($today == 0) return 0;
            return null; // 或 return '--'，前端判断
        }
        return round(($today - $yesterday) / $yesterday * 100, 2);
    }

    /**
     * 编辑
     * <AUTHOR>
     * @date 2024-07-01
     * @return
     */
    public function edit()
    {
        if ($this->request->isPost()) {
            $params = $this->request->param();

            $params['updatetime'] = time();
            $result = Db::name('hx_merchant')
                ->where('id', $this->merchant_id)
                ->update($params);
            if ($result !== false) {
                $this->success('修改成功');
            } else {
                $this->error('修改失败');
            }
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 核销记录
     * <AUTHOR>
     * @date 2025-6-25
     * @return
     */
    public function hx_log()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            $where = [];

            // 新增时间范围过滤
            if (isset($params['start_time']) && $params['start_time'] && isset($params['end_time']) && $params['end_time']) {
                $where['l.createtime'] = ['between',[$params['start_time'],$params['end_time']]];
            }

            $page = $params['page'] ?? 1;//分页页数
            $params['show_num'] = $params['show_num'] ?? 10;//默认显示10条数据

            //分页
            $pagefliter = [];
            if ($page) {  // 修正分页参数判断
                $pagefliter['page'] = $page;
            }

            $list = Db::name('hx_log')
                ->alias('l')
                ->join(['fa_hx_merchant m'],'l.merchant_id=m.id','left')
                ->where('l.merchant_id', $this->merchant_id)
                ->where($where)
                ->field('l.*,m.name as merchant_name')
                ->order('l.id desc')
                ->paginate($params['show_num'], false, $pagefliter);

            $this->success('成功获取',$list);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * 提现账号列表
     * <AUTHOR>
     * @date 2025-6-27
     * @return
     */
    public function withdraw_account_list()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();

            $where = [];

            $page = $params['page'] ?? 1;//分页页数
            $params['show_num'] = $params['show_num'] ?? 10;//默认显示10条数据

            //分页
            $pagefliter = [];
            if ($page) {  // 修正分页参数判断
                $pagefliter['page'] = $page;
            }

            $info = Db::name('withdraw_account')
                ->where('user_id', $this->user_id)
                ->where($where)  // 修改后的条件
                ->order('id desc')
                ->paginate($params['show_num'], false, $pagefliter);

            $this->success('成功获取',$info);
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 添加提现账号
     * <AUTHOR>
     * @date 2025-6-27
     * @return
     */
    public function withdraw_account_add()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            
            // 参数验证
            if (empty($params['type'])) {
                $this->error('账户类型不能为空');
            }
            if (empty($params['account'])) {
                $this->error('账号不能为空');
            }
            if (empty($params['name'])) {
                $this->error('真实姓名不能为空');
            }
            
            // 如果是银行卡类型，验证银行相关字段
            if ($params['type'] == 'bank') {
                if (empty($params['bank'])) {
                    $this->error('银行名称不能为空');
                }
                if (empty($params['bank_branch'])) {
                    $this->error('开户行不能为空');
                }
                if (empty($params['phone'])) {
                    $this->error('手机号不能为空');
                }
                if (empty($params['code'])) {
                    $this->error('验证码不能为空');
                }
                
                // TODO: 验证手机验证码
                // 这里可以添加验证码验证逻辑
            }
            
            // 检查账号是否已存在
            $existAccount = Db::name('withdraw_account')
                ->where('user_id', $this->user_id)
                ->where('account', $params['account'])
                ->where('type', $params['type'])
                ->find();
                
            if ($existAccount) {
                $this->error('该账号已存在');
            }
            
            // 构建插入数据
            $data = [
                'user_id' => $this->user_id,
                'type' => $params['type'],
                'account' => $params['account'],
                'name' => $params['name'],
                'bank' => $params['bank'] ?? '',
                'bank_branch' => $params['bank_branch'] ?? '',
                'createtime' => time(),
                'updatetime' => time(),
                'remark' => $params['remark'] ?? '',
                'phone' => $params['phone'] ?? ''
            ];
            
            // 插入数据
            $result = Db::name('withdraw_account')->insert($data);
            
            if ($result) {
                $this->success('添加成功');
            }
            $this->error('添加失败');
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 编辑提现账号
     * <AUTHOR>
     * @date 2025-6-27
     * @return
     */
    public function withdraw_account_edit()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            
            // 参数验证
            if (empty($params['id'])) {
                $this->error('账户ID不能为空');
            }
            if (empty($params['type'])) {
                $this->error('账户类型不能为空');
            }
            if (empty($params['account'])) {
                $this->error('账号不能为空');
            }
            if (empty($params['name'])) {
                $this->error('真实姓名不能为空');
            }

            // 检查账户是否存在且属于当前用户
            $existAccount = Db::name('withdraw_account')
                ->where('id', $params['id'])
                ->where('user_id', $this->user_id)
                ->find();

            if (!$existAccount) {
                $this->error('账户不存在或无权限编辑');
            }
            
            // 如果是银行卡类型，验证银行相关字段
            if ($params['type'] == 'bank') {
                if (empty($params['bank'])) {
                    $this->error('银行名称不能为空');
                }
                if (empty($params['bank_branch'])) {
                    $this->error('开户行不能为空');
                }
                if (empty($params['phone'])) {
                    $this->error('手机号不能为空');
                }
                if ($existAccount['phone'] != $params['phone']) {//有修改手机号
                    if (empty($params['code'])) {
                        $this->error('验证码不能为空');
                    }
                    // TODO: 验证手机验证码
                    // 这里可以添加验证码验证逻辑
                }
            }
            
            // 检查账号是否已被其他账户使用（排除当前编辑的账户）
            $duplicateAccount = Db::name('withdraw_account')
                ->where('user_id', $this->user_id)
                ->where('account', $params['account'])
                ->where('type', $params['type'])
                ->where('id', '<>', $params['id'])
                ->find();
                
            if ($duplicateAccount) {
                $this->error('该账号已被其他账户使用');
            }
            
            // 构建更新数据
            $data = [
                'type' => $params['type'],
                'account' => $params['account'],
                'name' => $params['name'],
                'bank' => $params['bank'] ?? '',
                'bank_branch' => $params['bank_branch'] ?? '',
                'updatetime' => time(),
                'remark' => $params['remark'] ?? '',
                'phone' => $params['phone'] ?? ''
            ];
            
            // 更新数据
            $result = Db::name('withdraw_account')
                ->where('id', $params['id'])
                ->update($data);
            
            if ($result) {
                $this->success('编辑成功');
            }
            $this->error('编辑失败');
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 提现账户删除
     * <AUTHOR>
     * @date 2025-6-27
     * @return
     */
    public function withdraw_account_del()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();

            // 参数验证
            if (empty($params['id'])) {
                $this->error('账户ID不能为空');
            }
            $result = Db::name('withdraw_account')
                ->where('id', $params['id'])
                ->where('user_id',$this->user_id)
                ->delete();
            if ($result) {
                $this->success('成功');
            }
            $this->error('失败');
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 提现申请
     * <AUTHOR>
     * @date 2025-6-27
     * @return
     */
    public function withdraw_application()
    {
        if ($this->request->isPost()) {
            $params = $this->request->param();

            if (empty($params['account_id'])) {
                $this->error('请选择提现账户');
            }
            if (empty($params['money']) || $params['money'] <= 0) {
                $this->error('请输入正确的提现金额');
            }

            if (Db::name('withdraw')->where(['user_id'=>$this->user_id,'status'=>0])->find()) {
                $this->error('已有提现申请在处理中');
            }

            $account = Db::name('withdraw_account')
                ->where('id', $params['account_id'])
                ->where('user_id', $this->user_id)
                ->find();
            if (!$account) {
                $this->error('提现账户不存在');
            }

            Db::startTrans();
            try {
                // 查询商户余额并加锁，防止并发
                $merchant = Db::name('hx_merchant')->where('id', $this->merchant_id)->lock(true)->find();
                if (!$merchant) {
                    $this->error('商户不存在');
                }
                if (!isset($merchant['money']) || $merchant['money'] < $params['money']) {
                    $this->error('余额不足');
                }

                $fee_rate = 0.00;
                $fee_money = round($params['money'] * $fee_rate / 100, 2);
                $real_money = round($params['money'] - $fee_money, 2);

                // 提现类型 1:微信 2:支付宝 3:银行卡
                $type = 3;
                if ($account['type'] == 'alipay') {
                    $type = 2;
                } elseif ($account['type'] == 'wechat') {
                    $type = 1;
                }

                $before = $merchant['money'];
                $after = $before - $params['money'];

                // 写入提现记录
                $data = [
                    'user_id' => $this->user_id,
                    'agent_id' => 0,
                    'merchant_id' => $this->merchant_id,
                    'type' => $type,
                    'money' => $params['money'],
                    'real_money' => $real_money,
                    'status' => 0,
                    'fee' => $fee_rate,
                    'fee_money' => $fee_money,
                    'remark' => '',
                    'createtime' => time(),
                    'updatetime' => time(),
                ];
                $withdraw_id = Db::name('withdraw')->insertGetId($data);

                // 扣除余额
                Db::name('hx_merchant')->where('id', $this->merchant_id)->setDec('money', $params['money']);

                // 写入余额日志
                Db::name('user_money_log')->insert([
                    'user_id'    => $this->user_id,
                    'money'      => $params['money'],
                    'before'     => $before,
                    'after'      => $after,
                    'memo'       => '商户提现申请',
                    'createtime' => time(),
                    'status'     => -1,
                ]);

                Db::commit();
                $this->success('提现申请已提交');
            } catch (\think\exception\HttpResponseException $e) {
                throw $e;
            } catch (\Exception $e) {
                Db::rollback();
                $this->error('提现申请失败: ' . $e->getMessage());
            }
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 提现信息接口：可提现余额、冻结金额、提现记录（分页）
     * <AUTHOR>
     * @date 2024-07-01
     * @return
     */
    public function withdraw_info()
    {
        if ($this->request->isPost()) {
            $params = $this->request->param();
            $page = $params['page'] ?? 1;
            $show_num = $params['show_num'] ?? 10;

            // 查询余额和冻结金额
            $merchant = Db::name('hx_merchant')->where('id', $this->merchant_id)->field('money,freeze_money')->find();
            $balance = [
                'available' => isset($merchant['money']) ? number_format($merchant['money'], 2, '.', '') : '0.00',
                'frozen'    => isset($merchant['freeze_money']) ? number_format($merchant['freeze_money'], 2, '.', '') : '0.00',
            ];

            // 查询提现记录
            $where = [
                'user_id' => $this->user_id,
                'merchant_id' => $this->merchant_id
            ];
            $list = Db::name('withdraw')
                ->where($where)
                ->order('createtime desc')
                ->paginate($show_num, false, ['page' => $page]);

            $records = [];
            foreach ($list as $item) {
                $records[] = [
                    'id' => $item['id'],
                    'apply_time' => $item['createtime'],
                    'amount' => $item['money'],
                    'real_money' => $item['real_money'],
                    'detail' => $this->getWithdrawDetail($item),
                    'status' => $item['status'],
                    'audit_time' => $item['status_timestamp'],
                    'remark' => $item['remark'] ?? ''
                ];
            }

            $data = [
                'balance' => $balance,
                'records' => $records,
                'total' => $list->total(),
                'page' => $page,
                'show_num' => $show_num
            ];
            $this->success('成功获取', $data);
        } else {
            $this->error('请求方式不正确');
        }
    }

    // 获取提现详情描述
    private function getWithdrawDetail($item)
    {
        // 可根据type返回不同描述
        $typeMap = [1 => '微信', 2 => '支付宝', 3 => '银行卡'];
        $typeStr = isset($typeMap[$item['type']]) ? $typeMap[$item['type']] : '未知';
        // 这里可以拼接更多信息，如银行卡号后四位等
        return $typeStr . ' 提现';
    }

    /**
     * 商户积分核销接口
     * <AUTHOR>
     * @date 2024-07-01
     * @return
     */
    public function verify_score()
    {
        if ($this->request->isPost()) {
            $params = $this->request->param();
            $user_id = $params['user_id'] ?? 0;
            $money = intval($params['money'] ?? 0);
            $receipt_image = $params['receipt_image'] ?? '';
            $ts = $params['ts'] ?? 0;
            $sign = $params['sign'] ?? '';
            $secret = 'your_secret_key'; // 与 makeSign 保持一致
            $test = $params['test'] ?? 0;

            if (!$user_id || $money <= 0) {
                $this->error('参数错误');
            }
            if ($test == 0) {
                if (!$ts || !$sign) {
                    $this->error('参数错误');
                }
                // 校验二维码有效期（5分钟）
                if (abs(time() - $ts) > 300) {
                    $this->error('二维码已过期');
                }
                // 校验签名
                $expectedSign = makeSignQrcode($user_id, $ts, $secret);
                if ($sign !== $expectedSign) {
                    $this->error('签名不正确');
                }
            }

            // 查询用户积分
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user) {
                $this->error('用户不存在');
            }
            $msg = '失败';
            Db::startTrans();
            try {
                $score = $money;//金额:积分 1:1

                $status = $user['score'] < $score ? 0 : 1;
                $memo = $status == 0 ? '用户积分不足' : null;
                $msg = $status == 0 ? '核销失败，用户积分不足' : '核销成功';

                // 写入核销记录
                $logData = [
                    'user_id' => $user_id,
                    'merchant_id' => $this->merchant_id,
                    'score' => $score,
                    'money' => $money,
                    'status' => $status,
                    'createtime' => time(),
                    'receipt_image' => $receipt_image,
                    'memo' => $memo
                ];
                Db::name('hx_log')->insert($logData);

                if ($status == 1) {
                    $merchant =  Db::name('hx_merchant')->where('id',$this->merchant_id)->field('money,name,number,score')->find();

                    $update['money'] = $merchant['money'] + $money;
                    $update['number'] = $merchant['number'] + 1;
                    $update['score'] = $merchant['score'] + $score;
                    // 商户余额、核销次数、总核销积分增加
                    Db::name('hx_merchant')->where('id', $this->merchant_id)->update($update);

                    $before = $merchant['money'];
                    $after = $before + $money;

                    // 写入日志
                    MoneyLog::create([
                        'user_id'       => $this->user_id,
                        'money'         => $money,
                        'before'        => $before,
                        'after'         => $after,
                        'memo'          => '核销积分',
                        'status'        => 1,
                        'createtime'    => time()
                    ]);

                    // 用户积分减少
                    Db::name('user')->where('id', $user_id)->setDec('score', $score);
                    $after = $user['score'] - $score;
                    // 写入日志
                    ScoreLog::create([
                        'user_id'       => $user_id,
                        'score'         => $score,
                        'before'        => $user['score'],
                        'after'         => $after,
                        'memo'          => "{$merchant['name']} 核销积分",
                        'status'        => -1,
                        'createtime'    => time()
                    ]);
                }

                Db::commit();

                if ($status == 1) {
                    $this->success($msg);
                }
            } catch (\think\exception\HttpResponseException $e) {
                throw $e;
            } catch (\Exception $e) {
                Db::rollback();
                $this->error('核销失败: ' . $e->getMessage());
            }
            $this->error($msg);
        } else {
            $this->error('请求方式不正确');
        }
    }


}
