<?php

namespace app\common\model;

use think\Exception;
use think\Model;
use think\Db;

class UserRechargeLog extends Model
{
    // 状态常量
    const STATUS_PENDING = 0;
    const STATUS_SUCCESS = 1;
    const STATUS_REFUNDED = 2;

    /**
     * 用户充值记录列表
     * <AUTHOR>
     * @date 2025-18-11
     * @return
     */
    public function list($params)
    {
        try {
            // 构建查询条件
            $where['l.user_id'] = $params['user_id'];

            if (isset($params['order_no'])) {
                $where['l.order_no'] = $params['order_no'];
            }
            if (isset($params['start_date']) && isset($params['end_date'])) {
                $where['l.pay_time'] = ['between', [$params['start_date'], $params['end_date']]];
            }

            // 分页配置
            $page = $params['page'] ?? 1;
            $pageSize = $params['show_num'] ?? 10;

            // 构建基础查询
            $list = Db::name('user_recharge_log')
                ->alias('l')
                ->field('l.id,l.user_id,l.game_recharge_id,l.order_no,l.price,l.coin,l.status,l.createtime,u.nickname')
                ->join(['fa_user u'],'u.id = l.user_id','left')
                ->where($where)
                ->order('l.createtime', 'desc')
                ->paginate([
                    'list_rows' => $pageSize,
                    'page' => $page
                ])->toArray();

            return ['code'=>1,'msg'=>'成功','data'=>$list];
        } catch (Exception $e){
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }
}