<?php

namespace app\admin\model;

use fast\Random;
use think\Db;
use think\Model;
use think\Session;
use think\Validate;

class Admin extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $hidden = [
        'password',
        'salt'
    ];

    public static function init()
    {
        self::beforeWrite(function ($row) {
            $changed = $row->getChangedData();
            //如果修改了用户或或密码则需要重新登录
            if (isset($changed['username']) || isset($changed['password']) || isset($changed['salt'])) {
                $row->token = '';
            }
        });
    }

    public function add($params)
    {
        $time = time();

        $params['createtime'] = $time;
        $params['updatetime'] = $time;

        Db::startTrans();
        try {
            if (!Validate::is($params['password'], '\S{6,30}')) {
                exception(__("Please input correct password"));
            }
            if (Db::name('admin')->where('username',$params['mobile'])->find()) {
                exception(__("手机号码已存在，请联系管理员"));
            }
            $user = Db::name('user')->where('mobile',$params['mobile'])->find();
            if ($user) {
                exception(__("用户已存在，请到用户管理设置代理"));
            }

            $params['salt'] = Random::alnum();
            $params['password'] = $this->auth->getEncryptPassword($params['password'], $params['salt']);
            $params['avatar'] = '/assets/img/avatar.png'; //设置新管理员默认头像。

            $agent_id = Db::name('admin')->insertGetId([
                'username' => $params['mobile'],
                'nickname' => $params['nickname'],
                'password' => $params['password'],
                'salt' => $params['salt'],
                'avatar' => $params['avatar'],
                'mobile' => $params['mobile'],
                'createtime' => $params['createtime'],
                'updatetime' => $params['updatetime'],
                'fee' => $params['fee'],
                'status' => $params['status'],
                'is_agent' => 1,
            ]);

            Db::name('user')->insert([
                'username' => $params['mobile'],
                'nickname' => $params['nickname'],
                'password' => $params['password'],
                'salt' => $params['salt'],
                'avatar' => $params['avatar'],
                'mobile' => $params['mobile'],
                'createtime' => $params['createtime'],
                'updatetime' => $params['updatetime'],
                'status' => 'normal',
                'agent_id' => $agent_id
            ]);

            Db::name("auth_group_access")->insert([
                'uid' => $agent_id,
                'group_id' => 6,//代理固定ID
            ]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

}
