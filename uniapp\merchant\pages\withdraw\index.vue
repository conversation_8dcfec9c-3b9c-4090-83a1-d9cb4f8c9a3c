<template>
  <view class="withdraw-container">
    <!-- 顶部余额信息 -->
    <view class="balance-section">
      <view class="balance-row">
        <text class="balance-label">可提现余额：</text>
        <text class="balance-value">{{ balanceInfo.available }}</text>
      </view>
      <view class="balance-row">
        <text class="balance-label">冻结金额：</text>
        <text class="balance-value">{{ balanceInfo.frozen }}</text>
      </view>
      <view class="balance-btns">
        <button class="card-btn" @click="goCard">卡管理</button>
        <button class="withdraw-btn" @click="onWithdrawClick">提现</button>
      </view>
    </view>
    <!-- 提现记录 -->
    <view class="record-section">
      <view class="record-title">提现记录</view>
      <view class="record-list">
        <view class="record-item" v-for="item in records" :key="item.id">
          <view class="record-row"><text class="record-label">申请时间：</text><text class="record-value">{{ formatTime(item.apply_time) }}</text></view>
          <view class="record-row"><text class="record-label">提现金额：</text><text class="record-value">{{ item.amount }}</text></view>
          <view class="record-row"><text class="record-label">提现详情：</text><text class="record-value">{{ item.detail }}</text></view>
          <view class="record-row"><text class="record-label">状态：</text><text class="record-value" :class="statusClass(item.status)">{{ statusText(item.status) }}</text></view>
          <view class="record-row" v-if="item.status !== 1"><text class="record-label">备注：</text><text class="record-value">{{ item.remark }}</text></view>
          <view class="record-row"><text class="record-label">审核时间：</text><text class="record-value">{{ formatTime(item.audit_time) }}</text></view>
        </view>
        <view v-if="records.length < total" class="load-more" @click="loadMore">加载更多</view>
        <view v-else-if="total > 0 && records.length >= total" class="load-more">没有更多了</view>
      </view>
    </view>
    <!-- 提现弹窗 -->
    <UniversalModal
      :show="showWithdrawDialog"
      title="提现申请"
      @close="showWithdrawDialog = false"
    >
      <view class="withdraw-form">
        <view class="form-item">
          <text class="form-label">选择账号：</text>
          <template v-if="accounts.length > 1">
            <picker :range="accounts" range-key="account_desc" @change="onAccountChange">
              <view class="form-picker">{{ selectedAccount ? selectedAccount.account_desc : '请选择账号' }}</view>
            </picker>
          </template>
          <template v-else>
            <view class="form-picker disabled">{{ selectedAccount ? selectedAccount.account_desc : '无可用账户' }}</view>
          </template>
        </view>
        <view class="form-item">
          <text class="form-label">提现金额：</text>
          <input class="form-input" v-model="withdrawAmount" type="number" placeholder="请输入提现金额" />
        </view>
        <view class="form-buttons">
          <button class="btn-cancel" @click="showWithdrawDialog = false">取消</button>
          <button class="btn-confirm" @click="submitWithdraw">确认提现</button>
        </view>
      </view>
    </UniversalModal>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import UniversalModal from '@/components/UniversalModal.vue'
function formatTime(val) {
  if (!val) return '--'
  const d = new Date(val * 1000)
  const y = d.getFullYear()
  const m = (d.getMonth() + 1).toString().padStart(2, '0')
  const day = d.getDate().toString().padStart(2, '0')
  const h = d.getHours().toString().padStart(2, '0')
  const min = d.getMinutes().toString().padStart(2, '0')
  const s = d.getSeconds().toString().padStart(2, '0')
  return `${y}.${m}.${day} ${h}:${min}:${s}`
}
export default {
  components: {
    UniversalModal
  },
  data() {
    return {
      balanceInfo: { available: '0.00', frozen: '0.00' },
      records: [],
      page: 1,
      show_num: 10,
      total: 0,
      loading: false,
      showWithdrawDialog: false,
      accounts: [],
      selectedAccount: null,
      withdrawAmount: '',
      withdrawPwd: ''
    }
  },
  onLoad() {
    this.getWithdrawInfo()
    this.getAccounts()
  },
  methods: {
    async getWithdrawInfo(loadMore = false) {
      if (this.loading) return
      this.loading = true
      try {
        const res = await request({
          url: '/api/merchant/withdraw_info',
          method: 'POST',
          data: { page: this.page, show_num: this.show_num }
        })
        if (res.code === 1 && res.data) {
          this.balanceInfo = res.data.balance || { available: '0.00', frozen: '0.00' }
          if (loadMore) {
            this.records = this.records.concat(res.data.records || [])
          } else {
            this.records = res.data.records || []
          }
          this.total = res.data.total || 0
        }
      } finally {
        this.loading = false
      }
    },
    loadMore() {
      if (this.records.length < this.total) {
        this.page++
        this.getWithdrawInfo(true)
      }
    },
    goCard() {
      uni.navigateTo({ url: '/pages/withdraw/card' })
    },
    async getAccounts() {
      // TODO: 请求账户列表接口
      // 假数据
      this.accounts = [
        { id: 1, account_desc: '建设银行 6443****2342' },
        { id: 2, account_desc: '支付宝 186****9864' }
      ]
      if (this.accounts.length > 0) {
        this.selectedAccount = this.accounts[0]
      }
    },
    onAccountChange(e) {
      this.selectedAccount = this.accounts[e.detail.value]
    },
    async submitWithdraw() {
      if (!this.selectedAccount) {
        uni.showToast({ title: '请选择提现账户', icon: 'none' })
        return
      }
      if (!this.withdrawAmount) {
        uni.showToast({ title: '请输入提现金额', icon: 'none' })
        return
      }
      // 提现接口
      try {
        const res = await request({
          url: '/api/merchant/withdraw_application',
          method: 'POST',
          data: {
            account_id: this.selectedAccount.id,
            money: this.withdrawAmount
          }
        })
        if (res.code === 1) {
          uni.showToast({ title: '提现申请已提交', icon: 'success' })
          this.showWithdrawDialog = false
          this.withdrawAmount = ''
          this.selectedAccount = null
          this.page = 1
          this.getWithdrawInfo()
        } else {
          this.showWithdrawDialog = false
          uni.showToast({ title: res.msg || '提现失败', icon: 'none' })
        }
      } catch (e) {
        uni.showToast({ title: '网络错误', icon: 'none' })
      }
    },
    formatTime,
    async onWithdrawClick() {
      // 打开弹窗前先请求账户列表
      this.loading = true
      try {
        const res = await request({
          url: '/api/merchant/withdraw_account_list',
          method: 'POST',
          data: { page: 1, show_num: 50 }
        })
        if (res.code === 1 && res.data && res.data.data) {
          this.accounts = res.data.data.map(item => ({
            ...item,
            account_desc: (item.type === 'bank' ? (item.bank || '') + ' ' : '支付宝 ') + item.account
          }))
          this.showWithdrawDialog = true
          if (this.accounts.length > 0) {
            this.selectedAccount = this.accounts[0]
          }
        } else {
          uni.showToast({ title: res.msg || '获取账户失败', icon: 'none' })
        }
      } catch (e) {
        uni.showToast({ title: '网络错误', icon: 'none' })
      } finally {
        this.loading = false
      }
    },
    statusText(status) {
      switch (status) {
        case 0: return '待审核';
        case 1: return '已打款';
        case 2: return '已驳回';
        case 3: return '失败';
        default: return '未知';
      }
    },
    statusClass(status) {
      switch (status) {
        case 1: return 'success';
        case 2:
        case 3: return 'fail';
        case 0: return 'pending';
        default: return '';
      }
    }
  }
}
</script>

<style scoped>
.withdraw-container {
  min-height: 100vh;
  background: #180F29;
  padding: 0 24rpx 24rpx 24rpx;
}

.balance-section {
  background: #2A1840;
  border: 1rpx solid #4C3A62;
  border-radius: 16rpx;
  margin: 30rpx 0 24rpx 0;
  padding: 32rpx 24rpx;
}

.balance-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.balance-label {
  color: #999;
  font-size: 28rpx;
  margin-right: 12rpx;
}

.balance-value {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
}

.balance-btns {
  display: flex;
  gap: 24rpx;
  margin-top: 18rpx;
}

.card-btn, .withdraw-btn {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-btn {
  background: #382164;
  color: #fff;
}

.withdraw-btn {
	background: #8F75EB;
  color: #fff;
}

.record-section {
  margin-top: 24rpx;
}

.record-title {
  font-size: 30rpx;
  color: #fff;
  font-weight: bold;
  margin-bottom: 18rpx;
}

.record-item {
  border: 2rpx solid #4C3A62;
  border-radius: 14rpx;
  margin-bottom: 28rpx;
  padding: 32rpx 28rpx;
  background: #2A1840;
  font-size: 24rpx;
}

.record-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.record-label {
  color: #999;
}

.record-value {
  color: #fff;
}

.success {
  color: #48A578;
}

.fail {
  color: #ff4757;
}

.load-more {
  text-align: center;
  color: #999;
  font-size: 26rpx;
  padding: 24rpx 0;
  cursor: pointer;
}

/* 提现表单样式 */
.withdraw-form {
  padding: 20rpx 0;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #fff;
  margin-bottom: 12rpx;
}

.form-picker {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 0 20rpx;
  background: #180F29;
  color: #fff;
  font-size: 28rpx;
  box-sizing: border-box;
  text-align: left;
}

.form-picker.disabled {
  color: #999;
  background: #1A1A1A;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 0 20rpx;
  background: #180F29;
  color: #fff;
  font-size: 28rpx;
  box-sizing: border-box;
  text-align: left;
}

.form-input::placeholder {
  color: #999;
}

.form-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}
</style>