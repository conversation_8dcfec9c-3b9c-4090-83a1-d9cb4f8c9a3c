<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">ClawControl 娃娃机控制组件测试</text>
    </view>
    
    <!-- 测试日志显示区域 -->
    <view class="log-container">
      <text class="log-title">操作日志：</text>
      <scroll-view class="log-scroll" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-content">{{ log.content }}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 娃娃机控制组件 -->
    <ClawControl
      :gameInfo="testGameInfo"
      :userInfo="testUserInfo"
      :seats="testSeats"
      :isVideoMuted="isVideoMuted"
      :gameSettings="gameSettings"
      @startGame="handleStartGame"
      @exitGame="handleExitGame"
      @toggleVideoAudio="handleToggleAudio"
      @show-func-dialog="handleShowFuncDialog"
      @show-reward-dialog="handleShowRewardDialog"
      @show-score-exchange-dialog="handleShowScoreDialog"
    />

    <!-- 测试控制面板 -->
    <view class="control-panel">
      <button @click="simulateGameStart" class="test-btn">模拟游戏开始</button>
      <button @click="simulateGameEnd" class="test-btn">模拟游戏结束</button>
      <button @click="clearLogs" class="test-btn">清空日志</button>
    </view>
  </view>
</template>

<script>
import ClawControl from '@/components/GameControls/ClawControl.vue'

export default {
  name: 'ClawControlTest',
  components: {
    ClawControl
  },
  data() {
    return {
      logs: [],
      isVideoMuted: false,
      gameSettings: {
        gameSound: true
      },
      // 测试用游戏信息
      testGameInfo: {
        id: 1,
        name: '测试娃娃机',
        number: 1,
        is_full: 2, // 2表示游戏中
        is_landscape: 0, // 竖屏
        control_config: {
          buttons: [
            {id: 51, name: "投1币", value: 1},
            {id: 52, name: "投5币", value: 5},
            {id: 53, name: "投30币", value: 30}
          ]
        }
      },
      // 测试用用户信息
      testUserInfo: {
        id: 1,
        nickname: '测试玩家',
        avatar: '/static/dpad/p1.png',
        money: 100,
        score: 50
      },
      // 测试用座位信息
      testSeats: [
        {id: 1, number: 1, status: 1, user_id: 1, nickname: '测试玩家', avatar: '/static/dpad/p1.png'},
        {id: 2, number: 2, status: 0},
        {id: 3, number: 3, status: 0},
        {id: 4, number: 4, status: 0}
      ]
    }
  },
  mounted() {
    this.addLog('组件测试页面加载完成');
    this.setupMockWebSocket();
  },
  methods: {
    // 添加日志
    addLog(content) {
      const now = new Date();
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
      this.logs.unshift({
        time,
        content
      });
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50);
      }
    },
    
    // 清空日志
    clearLogs() {
      this.logs = [];
      this.addLog('日志已清空');
    },
    
    // 模拟WebSocket
    setupMockWebSocket() {
      window.gameWebSocket = {
        readyState: 1, // 连接状态
        send: (message) => {
          this.addLog(`发送WebSocket消息: ${JSON.stringify(message)}`);
          
          // 模拟服务器响应
          setTimeout(() => {
            if (message.type === 'command') {
              this.addLog(`服务器响应指令: ${message.status}`);
            } else if (message.type === 'putInCoins') {
              this.addLog(`投币成功: ${message.coin}个`);
            }
          }, 100);
        }
      };
      this.addLog('模拟WebSocket连接已建立');
    },
    
    // 模拟游戏开始
    simulateGameStart() {
      this.testGameInfo.is_full = 2;
      this.addLog('模拟游戏开始');
    },
    
    // 模拟游戏结束
    simulateGameEnd() {
      this.testGameInfo.is_full = 0;
      this.addLog('模拟游戏结束');
    },
    
    // 处理开始游戏
    handleStartGame(seat) {
      this.addLog(`开始游戏 - 座位: ${seat.number}`);
    },
    
    // 处理退出游戏
    handleExitGame() {
      this.addLog('退出游戏');
    },
    
    // 处理音频切换
    handleToggleAudio() {
      this.isVideoMuted = !this.isVideoMuted;
      this.gameSettings.gameSound = !this.isVideoMuted;
      this.addLog(`音频切换: ${this.isVideoMuted ? '静音' : '开启'}`);
    },
    
    // 处理功能对话框
    handleShowFuncDialog() {
      this.addLog('显示功能对话框');
    },
    
    // 处理奖励对话框
    handleShowRewardDialog(coin) {
      this.addLog(`显示奖励对话框: ${coin}币`);
    },
    
    // 处理积分兑换对话框
    handleShowScoreDialog() {
      this.addLog('显示积分兑换对话框');
    }
  }
}
</script>

<style scoped>
.test-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.test-header {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  padding: 10px 20px;
  border-radius: 20px;
}

.test-title {
  color: white;
  font-size: 16px;
  font-weight: bold;
}

.log-container {
  position: absolute;
  top: 60px;
  right: 10px;
  width: 300px;
  height: 200px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 10px;
  padding: 10px;
  z-index: 1000;
}

.log-title {
  color: white;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 5px;
}

.log-scroll {
  height: 160px;
}

.log-item {
  margin-bottom: 5px;
  padding: 2px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.log-time {
  color: #00ff00;
  font-size: 12px;
  margin-right: 5px;
}

.log-content {
  color: white;
  font-size: 12px;
}

.control-panel {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 1000;
}

.test-btn {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
}

.test-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}
</style>
