<template>
	<view class="container">
		<!-- 顶部选项卡 -->
		<view class="nav-bar">
			<view class="nav-item" :class="{ active: currentTab === 'currency' }" @click="switchTab('currency')">
				游戏币记录
			</view>
			<view class="nav-item" :class="{ active: currentTab === 'points' }" @click="switchTab('points')">
				积分记录
			</view>
		</view>

		<!-- 记录列表 -->
		<scroll-view scroll-y class="record-list" @scrolltolower="loadMore">
			<!-- 公共记录结构 -->
			<view v-if="showNoData" class="no-data">
				{{ currentTab === 'currency' ? '暂无游戏币记录' : '暂无积分记录' }}
			</view>
			
			<view class="record-item" v-for="(item, index) in currentRecords" :key="index">
				<view class="left">
					<view class="type">{{ item.type }}</view>
					<view class="time">{{ formatTime(item.time) }}</view>
				</view>
				<view class="amount" :class="{ 'negative': item.status == 1 }">
					{{ item.status == 1 ? '+' : '-' }}{{ item.amount }}{{ currentTab === 'currency' ? '币' : '分' }}
				</view>
			</view>

			<view class="loading-text">
				{{ loadStatus }}
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import request from '@/utils/request'
	export default {
		data() {
			return {
				currentTab: 'currency',
				// 分类型存储记录
				recordData: {
					currency: {
						list: [],
						page: 1,
						lastPage: 1,
						loading: false,
						noMore: false
					},
					points: {
						list: [],
						page: 1,
						lastPage: 1,
						loading: false,
						noMore: false
					}
				}
			}
		},
		computed: {
			// 当前显示记录
			currentRecords() {
				return this.recordData[this.currentTab].list
			},
			// 是否显示空状态
			showNoData() {
				return this.currentRecords.length === 0 && !this.recordData[this.currentTab].loading
			},
			// 加载状态
			loadStatus() {
				const current = this.recordData[this.currentTab]
				if (current.noMore) return '没有更多数据了'
				return current.loading ? '加载中...' : ''
			}
		},
		methods: {
			switchTab(tab) {
				if (this.currentTab !== tab) {
					this.currentTab = tab
					// 首次进入时自动加载
					if (this.currentRecords.length === 0) {
						this.refreshData()
					}
				}
			},
			// 刷新数据
			async refreshData() {
				const current = this.recordData[this.currentTab]
				current.page = 1
				current.noMore = false
				await this.fetchRecords(1)
			},
			// 加载更多
			loadMore() {
				const current = this.recordData[this.currentTab]
				if (!current.noMore && !current.loading) {
					this.fetchRecords(current.page + 1)
				}
			},
			// 获取记录
			async fetchRecords(page) {
				const current = this.recordData[this.currentTab]
				if (current.loading || current.noMore) return
				
				current.loading = true
				try {
					const res = await request({
						url: '/api/user/gold',
						method: 'POST',
						data: {
							page,
							type: this.currentTab === 'currency' ? 'coin' : 'score'
						}
					})

					const newRecords = res.data.data.map(item => ({
						type: item.memo,
						time: item.createtime,
						amount: item.money,
						status: item.status
					}))

					// 更新数据
					current.list = page === 1 ? newRecords : [...current.list, ...newRecords]
					current.page = res.data.current_page
					current.lastPage = res.data.last_page
					current.noMore = res.data.current_page >= res.data.last_page
				} catch (error) {
					uni.showToast({
						title: '请求失败，请重试',
						icon: 'none'
					})
				} finally {
					current.loading = false
				}
			},
			formatTime(timestamp) {
				const date = new Date(timestamp * 1000)
				return `${date.getFullYear()}.${(date.getMonth()+1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
			}
		},
		mounted() {
			this.refreshData()
		}
	}
</script>
<style scoped>
	/* 新增加载状态样式 */
	.loading-text {
		text-align: center;
		padding: 20rpx;
		color: #999;
		font-size: 24rpx;
	}

	.container {
		padding: 20rpx 30rpx;
	}

	.nav-bar {
		display: flex;
		margin-bottom: 30rpx;
		border-bottom: 2rpx solid #eee;
	}

	.nav-item {
		flex: 1;
		text-align: center;
		padding: 20rpx;
		color: #666;
		font-size: 32rpx;
		position: relative;
	}

	.nav-item.active {
		color: #007AFF;
		font-weight: bold;
	}

	.nav-item.active::after {
		content: '';
		position: absolute;
		bottom: -2rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 80rpx;
		height: 4rpx;
		background: #007AFF;
	}

	.record-list {
	  /* 添加备用高度单位 */
	  height: calc(100vh - 120rpx); /* 微信环境 */
	  height: calc(100vh - 60px); /* H5备用 */
	}

	.record-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}

	.left {
		flex: 1;
	}

	.type {
		font-size: 34rpx;
		color: #333;
		margin-bottom: 10rpx;
	}

	.time {
		font-size: 26rpx;
		color: #999;
	}

	.amount {
		font-size: 36rpx;
		color: #09bb07;
		font-weight: bold;
	}

	.amount.negative {
		color: #e64340;
	}

	.no-data {
		text-align: center;
		color: #999;
		padding: 40rpx;
		font-size: 28rpx;
	}
</style>