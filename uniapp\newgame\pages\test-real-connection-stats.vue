<template>
  <view class="container">
    <view class="header">
      <text class="title">真实WebRTC连接统计测试</text>
    </view>
    
    <!-- 连接控制 -->
    <view class="control-section">
      <button @click="connect" :disabled="isConnected" class="btn connect-btn">连接</button>
      <button @click="disconnect" :disabled="!isConnected" class="btn disconnect-btn">断开</button>
      <button @click="refreshStats" class="btn refresh-btn">刷新统计</button>
    </view>
    
    <!-- 连接状态 -->
    <view class="status-section">
      <view class="status-item">
        <text class="label">连接状态:</text>
        <text :class="['value', hasError ? 'error' : 'normal']">{{ statusMessage }}</text>
      </view>
      <view class="status-item">
        <text class="label">连接类型:</text>
        <text class="value">{{ connectionType }} ({{ getConnectionTypeDesc(connectionType) }})</text>
      </view>
      <view class="status-item">
        <text class="label">可用连接数:</text>
        <text class="value">{{ availableConnections }}</text>
      </view>
    </view>
    
    <!-- 详细统计信息 -->
    <view class="stats-section">
      <view class="section-title">连接统计详情</view>
      <view v-if="connectionStats" class="stats-grid">
        <view class="stats-item">
          <text class="stats-label">发送端数量:</text>
          <text class="stats-value">{{ connectionStats.senders }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">接收端数量:</text>
          <text class="stats-value">{{ connectionStats.receivers }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">总客户端数:</text>
          <text class="stats-value">{{ connectionStats.total }}</text>
        </view>
      </view>
    </view>
    
    <!-- 客户端列表 -->
    <view class="clients-section" v-if="connectionStats && connectionStats.clients">
      <view class="section-title">在线客户端列表</view>
      <scroll-view class="clients-list" scroll-y>
        <view v-for="(client, index) in connectionStats.clients" :key="index" class="client-item">
          <view class="client-info">
            <text class="client-id">{{ client.id || client.clientId || 'Unknown' }}</text>
            <text class="client-role">{{ client.role || client.type || 'Unknown' }}</text>
          </view>
          <view class="client-status" :class="client.status || 'online'">
            {{ client.status || 'online' }}
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 视频播放器 -->
    <view class="player-section">
      <HeiqiPlayer
        ref="player"
        :senderId="senderId"
        :signalingUrl="signalingUrl"
        :showVideoStats="false"
        :enableRotation="false"
        @connected="onConnected"
        @connectionFailed="onConnectionFailed"
        @disconnected="onDisconnected"
        @statusChanged="onStatusChanged"
        @connectionStatsUpdated="onConnectionStatsUpdated"
        @realConnectionStatsUpdated="onRealConnectionStatsUpdated"
      />
    </view>
    
    <!-- 日志 -->
    <view class="log-section">
      <view class="section-title">连接日志</view>
      <scroll-view class="log-container" scroll-y :scroll-top="logScrollTop">
        <view v-for="(log, index) in logs" :key="index" :class="['log-item', log.type]">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-message">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import HeiqiPlayer from '@/components/HeiqiPlayer.vue'

export default {
  name: 'TestRealConnectionStats',
  components: {
    HeiqiPlayer
  },
  
  data() {
    return {
      // 连接配置
      senderId: 'sender-test-001',
      signalingUrl: 'ws://*************:2346',
      
      // 状态
      isConnected: false,
      statusMessage: '未连接',
      hasError: false,
      
      // 连接统计
      connectionType: 'H',
      availableConnections: 0,
      connectionStats: null,
      
      // 日志
      logs: [],
      logScrollTop: 0
    }
  },
  
  onLoad() {
    this.addLog('页面加载完成', 'info')
  },
  
  methods: {
    connect() {
      if (this.$refs.player) {
        this.addLog('开始连接WebRTC...', 'info')
        this.$refs.player.connect()
      }
    },
    
    disconnect() {
      if (this.$refs.player) {
        this.addLog('断开WebRTC连接...', 'info')
        this.$refs.player.disconnect()
      }
    },
    
    refreshStats() {
      if (this.$refs.player) {
        this.addLog('刷新连接统计...', 'info')
        this.$refs.player.getRealConnectionStats()
      }
    },
    
    onConnected() {
      this.isConnected = true
      this.addLog('WebRTC连接成功', 'success')
    },
    
    onConnectionFailed(error) {
      this.isConnected = false
      this.addLog(`连接失败: ${error}`, 'error')
    },
    
    onDisconnected() {
      this.isConnected = false
      this.addLog('连接已断开', 'info')
    },
    
    onStatusChanged({ message, isError }) {
      this.statusMessage = message
      this.hasError = isError
      this.addLog(`状态变更: ${message}`, isError ? 'error' : 'info')
    },
    
    onConnectionStatsUpdated(stats) {
      this.connectionStats = stats
      this.availableConnections = stats.senders || 0
      
      this.addLog(`连接统计更新: 发送端=${stats.senders}, 接收端=${stats.receivers}, 总数=${stats.total}`, 'info')
      
      // 更新连接类型（如果有的话）
      if (this.$refs.player) {
        this.connectionType = this.$refs.player.connectionType || 'H'
      }
    },
    
    getConnectionTypeDesc(type) {
      const types = {
        'H': '本地连接',
        'S': 'STUN穿透',
        'T': 'TURN中继'
      }
      return types[type] || '未知'
    },
    
    addLog(message, type = 'info') {
      const now = new Date()
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
      
      this.logs.push({
        time,
        message,
        type
      })
      
      // 限制日志数量
      if (this.logs.length > 100) {
        this.logs.splice(0, 10)
      }
      
      // 自动滚动到底部
      this.$nextTick(() => {
        this.logScrollTop = this.logs.length * 50
      })
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.control-section {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.btn {
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.connect-btn {
  background-color: #007aff;
  color: white;
}

.disconnect-btn {
  background-color: #ff3b30;
  color: white;
}

.refresh-btn {
  background-color: #34c759;
  color: white;
}

.btn:disabled {
  opacity: 0.5;
}

.status-section, .stats-section, .clients-section {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.status-item, .stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.status-item:last-child, .stats-item:last-child {
  border-bottom: none;
}

.label, .stats-label {
  font-size: 28rpx;
  color: #666;
}

.value, .stats-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.value.error {
  color: #ff3b30;
}

.value.normal {
  color: #34c759;
}

.stats-grid {
  display: flex;
  flex-direction: column;
}

.clients-list {
  max-height: 400rpx;
}

.client-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 10rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.client-info {
  display: flex;
  flex-direction: column;
}

.client-id {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.client-role {
  font-size: 24rpx;
  color: #666;
  margin-top: 5rpx;
}

.client-status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.client-status.online {
  background-color: #34c759;
}

.client-status.offline {
  background-color: #ff3b30;
}

.player-section {
  height: 400rpx;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
  overflow: hidden;
}

.log-section {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
}

.log-container {
  height: 400rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.log-item {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.log-time {
  color: #666;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.log-message {
  flex: 1;
  color: #333;
}

.log-item.error .log-message {
  color: #ff3b30;
}

.log-item.success .log-message {
  color: #34c759;
}

.log-item.warning .log-message {
  color: #ff9500;
}
</style>
