<?php

// 引入 Composer 自动加载文件
//require_once __DIR__ . '/vendor/autoload.php';

require_once __DIR__ . '/vendor/Workerman/Autoloader.php';
// use Workerman\Worker;

// // 设置日志路径（可选）
// Worker::$logFile = __DIR__ . '/runtime/workerman.log';

// // 创建 WebSocket 服务
// $ws_worker = new Worker("websocket://0.0.0.0:8080");
// $ws_worker->name = 'MyWebSocketServer'; // 服务名称
// $ws_worker->count = 1; // 工作进程数

// // 当客户端连接时
// $ws_worker->onConnect = function($connection) {
//     echo "New connection\n";
// };

// // 收到消息时
// $ws_worker->onMessage = function($connection, $data) {
//     echo "Received message: $data\n";
//     $connection->send("Server response: $data");
// };

// // 连接关闭时
// $ws_worker->onClose = function($connection) {
//     echo "Connection closed\n";
// };

// // 启动服务
// Worker::runAll();