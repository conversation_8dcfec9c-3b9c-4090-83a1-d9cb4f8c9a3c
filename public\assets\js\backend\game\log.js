define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'game/log/index',
                    add_url: 'game/log/add',
                    edit_url: '',
                    del_url: '',
                    multi_url: 'game/log/multi',
                    table: 'game',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='user_id']", form).addClass("selectpage").data("source", "user/user/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                $("input[name='game_id']", form).addClass("selectpage").data("source", "game/game/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'id',
                sortName: 'id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true, operate:false},
                        {field: 'avatar', title: __('用户头像'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'user_id',title: __('用户信息'),formatter: function (value, row, index) {
                                if (row.nickname) {
                                    return '昵称：'+row.nickname+'<br>'+'用户ID：'+row.user_id;
                                }else{
                                    return row.user_id;
                                }
                            }},
                        {field: 'game_id',title: __('所属游戏'),formatter: function (value, row, index) {
                                if (row.game_name) {
                                    return row.game_name+'<br>'+row.game_id;
                                }else{
                                    return row.game_id;
                                }
                            }},
                        {field: 'number',title: __('座位号'),formatter: function (value, row, index) {
                                return row.number+'P';
                            }},
                        {field: 'coin', title: __('使用金币'), operate:false},
                        {field: 'status', title: __('状态'), formatter: Table.api.formatter.status, searchList: {"-2": __('结束失败'),"-1": __('开始失败'),"0": __('开始待确认'),"1": __('进行中'), "2": __('结束待确认'),"3": __('已完成')}},
                        {field: 'createtime', title: __('创建时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('更新时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('金币记录'),
                                    text:'金币记录',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","60%"]\'',
                                    icon: 'fa fa-money',
                                    url: function (row){
                                        return "user/money/index?game_log_id=" + row.id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('用户信息'),
                                    text:'用户信息',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["50%","40%"]\'',
                                    icon: 'fa fa-user',
                                    url: function (row){
                                        return "user/user/index?id=" + row.user_id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('所属游戏'),
                                    text:'所属游戏',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["50%","40%"]\'',
                                    icon: 'fa fa-user',
                                    url: function (row){
                                        return "game/game/index?id=" + row.game_id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'end',
                                    title: __('结束游戏'),
                                    text:'结束游戏',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["50%","40%"]\'',
                                    icon: 'fa fa-gamepad',
                                    url: function (row){
                                        return "game/log/end_game?ids=" + row.id;
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        if (row.status == 1 || row.status == 2){
                                            return true;
                                        }else{
                                            return false;
                                        }
                                    }
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        end_game: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});