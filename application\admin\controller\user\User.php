<?php

namespace app\admin\controller\user;

use app\common\controller\Backend;
use app\common\library\Auth;
use think\Db;
use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;

/**
 * 会员管理
 *
 * @icon fa fa-user
 */
class User extends Backend
{

    protected $relationSearch = true;
    protected $searchFields = 'id,username,nickname';

    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        $this->noNeedRight = array('select_list');
        parent::_initialize();
        $this->model = new \app\admin\model\User;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            $where_arr = [];

            $admin = get_admin_info();
            $is_show = 1;
            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
                //代理只查看自己的用户
                $where_arr['p_id'] = $admin['admin_id'];
                $is_show = 0;
            }

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('user')
                ->alias('user')
                ->where($where)
                ->where($where_arr)
                ->where('agent_id', null)  // 使用链式where添加条件
                ->where('merchant_id', null)  // 使用链式where添加条件
                ->count();

            $list = Db::name('user')
                ->alias('user')
                ->where($where)
                ->where($where_arr)
                ->where('agent_id', null)  // 使用链式where添加条件
                ->where('merchant_id', null)  // 使用链式where添加条件
                ->order('id', $order)
                ->field('password',true)
                ->limit($offset, $limit)
                ->select();

            foreach ($list as $k => &$v) {
                $v['avatar'] = $v['avatar'] ? cdnurl($v['avatar'], true) : letter_avatar($v['nickname']);

                if ($v['p_id']) {
                    $v['agent_name'] = Db::name('admin')->where('id',$v['p_id'])->value('nickname');
                }
                $v['is_show'] = $is_show;
            }
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        return parent::add();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {
            $this->token();
        }
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign('groupList', build_select('row[group_id]', \app\admin\model\UserGroup::column('id,name'), $row['group_id'], ['class' => 'form-control selectpicker']));
        return parent::edit($ids);
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        Auth::instance()->delete($row['id']);
        $this->success();
    }

    /**
     * 设置用户为代理
     * @Authod Jw
     * @Time 2025/6/3
     * @param null $ids
     * @return string
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function set_agent($ids = null)
    {
        $row = Db::name('user')->where('id',$ids)->find();

        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($row['agent_id'] > 0) {
            $this->error(__('用户已经是代理商'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if (empty($params['fee'])) {
                    $this->error('分佣比例不能为空');
                }else{
                    if ($params['fee']>100 || $params['fee']<=0) {
                        $this->error('分佣比例范围设置错误，范围应在0~100');
                    }
                }

                if (Db::name("admin")->where('mobile',$row['mobile'])->find()) {
                    $this->error('手机号码已存在');
                }

                Db::startTrans();
                try {
                    $time = time();
                    $agent_id = Db::name('admin')->insertGetId([
                        'username'      => $row['mobile'],
                        'nickname'      => $row['nickname'],
                        'mobile'        => $row['mobile'],
                        'password'      => $row['password'],
                        'salt'          => $row['salt'],
                        'avatar'        => $row['avatar'],
                        'createtime'    => $time,
                        'updatetime'    => $time,
                        'status'        => 'normal',
                        'fee'           => $params['fee'],
                        'is_agent'      => 1
                    ]);

                    Db::name('user')->where('id',$ids)->update(['updatetime'=>$time,'agent_id'=>$agent_id]);

                    Db::commit();
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (DbException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success('成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 修改密码
     * <AUTHOR>
     * @date 2025-6-13
     * @return
     */
    public function reset_password($ids = NULL)
    {
        $row = Db::name('user')->where('id',$ids)->find();
        if (!$row)
            $this->error(__('No Results were found'));

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $user = new \app\common\model\User();
                $result = $user->reset_password($params);
                if ($result['code'] == 1) {
                    $this->success('成功');
                }
                $this->error($result['msg']);
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 修改金币
     * <AUTHOR>
     * @date 2025-6-13
     * @return
     */
    public function set_money($ids = null)
    {
        $row = Db::name('user')->find($ids);
        if (!$row) {
            $this->error(__('用户不存在'));
        }

        $admin = get_admin_info();
        if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
            $this->error(__('您没有权限'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($params['money'] < 0 || $params['money'] == $row['money']) {
                    $this->error('金币错误或没有修改');
                }

                Db::startTrans();
                try {
                    Db::name('user')->where('id', $ids)->update([
                        'money' => $params['money'],
                        'updatetime' => time()
                    ]);

                    if ($params['money'] > $row['money']) {//增加
                        $update_money = $params['money']-$row['money'];
                        $status = 1;
                    } else {//减少
                        $update_money = $row['money']-$params['money'];
                        $status = -1;
                    }

                    // 记录资金流水
                    Db::name('user_money_log')->insert([
                        'user_id'    => $ids,
                        'money'      => $update_money,
                        'before'     => $row['money'],
                        'after'      => $params['money'],
                        'memo'       => $params['memo'],
                        'createtime' => time(),
                        'status'     => $status
                    ]);

                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success('成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 修改积分
     * <AUTHOR>
     * @date 2025-6-13
     * @return
     */
    public function set_score($ids = null)
    {
        $row = Db::name('user')->find($ids);
        if (!$row) {
            $this->error(__('用户不存在'));
        }

        $admin = get_admin_info();
        if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
            $this->error(__('您没有权限'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($params['score'] < 0 || $params['score'] == $row['score']) {
                    $this->error('金币错误或没有修改');
                }

                Db::startTrans();
                try {
                    Db::name('user')->where('id', $ids)->update([
                        'score' => $params['score'],
                        'updatetime' => time()
                    ]);

                    if ($params['score'] > $row['score']) {//增加
                        $update_money = $params['score']-$row['score'];
                        $status = 1;
                    } else {//减少
                        $update_money = $row['score']-$params['score'];
                        $status = -1;
                    }

                    // 记录资金流水
                    Db::name('user_score_log')->insert([
                        'user_id'    => $ids,
                        'score'      => $update_money,
                        'before'     => $row['score'],
                        'after'      => $params['score'],
                        'memo'       => $params['memo'],
                        'createtime' => time(),
                        'status'     => $status
                    ]);

                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success('成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public function select_list()
    {
        if($this->request->isAjax()){

            $page = $this->request->post("pageNumber/d", 1);
            $limit = $this->request->post("pageSize/d", 15);
            $keyword= $this->request->post("name", '');

            $keyValue = $this->request->post('keyValue','');
            if ($keyValue) {
                $keyword = $keyValue;
            }

            $admin = get_admin_info();
            $where_arr = [];
            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
                //代理只查看自己的用户
                $where_arr['p_id'] = $admin['admin_id'];
            }

            $list = Db::name('user')
                ->field('concat(id , " - ",nickname," - ",INSERT(IFNULL(mobile, ""),4,3,\'***\')) as name,id')
                ->where(['nickname|id|mobile'=>['like','%'.$keyword.'%']])
                ->where($where_arr)
//                ->where('agent_id', null)  // 使用链式where添加条件
//                ->where('merchant_id', null)  // 使用链式where添加条件
                ->order('id', 'desc')
                ->page($page, $limit)
                ->paginate($limit,false,['page'=>$page]);

            $result = array("total" => $list->total(), "list" => $list->items());

            return json($result);
        }
    }

}
