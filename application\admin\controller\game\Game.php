<?php

namespace app\admin\controller\game;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 游戏列表
 * @Authod JW
 * @Time 2023/02/02
 * @package app\admin\controller\agent
 */
class Game extends Backend
{
    /**
     * Withdraw模型对象
     * @var \app\admin\model\Agent
     */
    protected $model = null;

    protected $noNeedRight = '';
    protected $multiFields = "deleted,rent_limit";
    public function _initialize()
    {
        $this->noNeedRight = array('select_list');

        parent::_initialize();
        $this->model = model('game');

        $this->view->assign("cateGoryList", $this->model->getCateGoryList());
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            $admin = get_admin_info();

            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
                if (Db::name('game')->where('agent_id',$admin['admin_id'])->count()) {//存在，表示代理租用了游戏
                    //只查看自己的
                    $where_arr['g.agent_id'] = $admin['admin_id'];
                }
            }
            if ($search_arr) {
                foreach ($search_arr as $k => $v) {
                    //执行时间
                    if ($k == 'createtime') {//创建时间
                        $create_time = explode(' - ',$v);
                        $where_arr['g.createtime'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    } elseif ($k == 'end_time') {
                        $create_time = explode(' - ',$v);
                        $where_arr['g.end_time'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    } else {
                        $str = 'g.'.$k;
                        $where_arr[$str] = $v;
                    }

                    unset($search_arr[$k]);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('game')
                ->alias('g')
                ->where('g.is_delete',0)
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->count();

            $list = Db::name('game')
                ->alias('g')
                ->join(['fa_game_category c'],'g.c_id=c.id','left')
                ->join(['fa_admin a'],'g.agent_id=a.id','left')
                ->where('g.is_delete',0)
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->field('g.*,c.name as category_name,a.nickname as agent_name,a.mobile')
                ->limit($offset, $limit)
                ->select();

            foreach ($list as &$v) {
                $v['status_name'] = $this->model->model_field['status'][$v['status']]['text'];

                $v['union_tip'] = $v['union_tip'] ?? '-';
                $v['union_name'] = $v['union_name'] ?? '-';
            }

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     * @Authod Jw
     * @Time 2023/2/16
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $time = time();

                $params['createtime'] = $time;
                $params['updatetime'] = $time;

                if ($params['max_players']>8) {
                    $this->error('最多8个玩家数量');
                }

                Db::startTrans();
                try {
                    if ($params['end_time']) {
                        $params['end_time'] = strtotime($params['end_time']);
                    }

                    $game_id = Db::name('game')->insert($params,false,true);

                    //生成座位数据
                    $seatData = [];
                    for ($i = 1; $i <= $params['max_players']; $i++) {
                        $seatData[] = [
                            'game_id'     => $game_id,
                            'number'      => $i,
                            'status'      => 0,
                            'createtime'  => $time,
                            'updatetime'  => $time
                        ];
                    }
                    Db::name('game_seat')->insertAll($seatData);

                    Db::commit();
                    $game_model = new \app\admin\model\Game();
                    $game_model->get_game_sn();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success('成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * @Method 编辑
     * @Authod JW
     * @Time 2020/12/8
     * @param null $ids
     * @return string
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function edit($ids = null)
    {
        $row = Db::name('game')->where('id',$ids)->find();

        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $time = time();
                $params['updatetime'] = $time;

                if (isset($params['end_time'])) {
                    $params['end_time'] = strtotime($params['end_time']);
                }

                $result = Db::name('game')->where('id',$ids)->update($params);
                if ($result) {
                    if ($params['sn'] != $row['sn']) {//修改了sn
                        $game_model = new \app\admin\model\Game();
                        $game_model->get_game_sn();
                    }
                    $this->success('成功');
                }
                $this->error('失败');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        if ($row['end_time']) {
            $row['end_time'] = date('Y-m-d H:i:s',$row['end_time']);
        }
        $row['is_show'] = 1;
        $admin = get_admin_info();
        if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
            $row['is_show'] = 0;
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * @Method 删除
     * @Authod JW
     * @Time 2021/1/28
     * @param string $ids
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function del($ids = "")
    {
        if ($ids) {
            $id = Db::name('game')->where(['id'=>$ids,'is_delete'=>0])->value('id');
            if (empty($id)) {
                $this->error('删除失败，不存在');
            }
            $result = Db::name('game')->where('id',$ids)->delete();
            if ($result) {
                $this->success('成功');
            }
            $this->error('删除失败');
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    public function select_list()
    {
        if($this->request->isAjax()){

            $page = $this->request->post("pageNumber/d", 1);
            $limit = $this->request->post("pageSize/d", 15);
            $keyword= $this->request->post("name", '');

            $keyValue = $this->request->post('keyValue','');
            if ($keyValue) {
                $keyword = $keyValue;
            }

            $where_arr = [];

//            $admin = get_admin_info();
//            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
//                //代理只查看自己的用户
//                $where_arr['p_id'] = $admin['admin_id'];
//            }

            $list = Db::name('game')
                ->field('concat(id , " - ",name," - ") as name,id')
                ->where(['name|id'=>['like','%'.$keyword.'%']])
                ->where($where_arr)
                ->order('id', 'desc')
                ->page($page, $limit)
                ->paginate($limit,false,['page'=>$page]);

            $result = array("total" => $list->total(), "list" => $list->items());

            return json($result);
        }
    }


}
