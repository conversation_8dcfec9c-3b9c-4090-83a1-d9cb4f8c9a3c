<?php

namespace app\admin\controller\verification;

use app\common\controller\Backend;
use app\common\model\MoneyLog;
use fast\Random;
use think\Db;
use think\Exception;
use think\Validate;

/**
 * 商户管理
 * @icon fa fa-user
 */
class Merchant extends Backend
{
    protected $searchFields = 'username,nickname,mobile';

    public function _initialize()
    {
        $this->noNeedRight = array('select_list');

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            if ($search_arr) {
                foreach ($search_arr as $k => $v) {
                    //执行时间
                    if ($k == 'createtime') {//创建时间
                        $create_time = explode(' - ',$v);
                        $where_arr['m.createtime'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    }  else {
                        $str = 'm.'.$k;
                        $where_arr[$str] = $v;
                    }

                    unset($search_arr[$k]);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('hx_merchant')
                ->alias('m')
                ->where($where)
                ->where($where_arr)
                ->count();

            $list = Db::name('hx_merchant')
                ->alias('m')
                ->join(['fa_user u'],'m.id=u.merchant_id','left')
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->field('m.*,u.nickname,u.username,u.id as user_id')
                ->limit($offset, $limit)
                ->select();


            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                Db::startTrans();
                try {
                    $time = time();

                    if (!Validate::is($params['password'], '\S{6,30}')) {
                        exception(__("Please input correct password"));
                    }
                    if (Db::name('hx_merchant')->where('mobile',$params['mobile'])->find()) {
                        exception(__("手机号码已存在，请联系管理员"));
                    }
                    if (Db::name('user')->where('mobile',$params['mobile'])->find()) {
                        exception(__("手机号码已存在，请联系管理员"));
                    }
                    if (validate_mobile($params['username'])) {//判断登录账号是否为手机号
                        if (Db::name('user')->where('mobile',$params['username'])->find()) {
                            exception(__("手机号码已存在，请联系管理员"));
                        }
                    }

                    $params['salt'] = Random::alnum();
                    $params['password'] = $this->auth->getEncryptPassword($params['password'], $params['salt']);

                    $merchant_id = Db::name('hx_merchant')->insertGetId([
                        'mobile' => $params['mobile'],
                        'name' => $params['name'],
                        'logo' => $params['logo'],
                        'door_photo' => $params['door_photo'],
                        'cash_photo' => $params['cash_photo'],
                        'hall_photo' => $params['hall_photo'],
                        'createtime' => $time,
                        'updatetime' => $time,
                        'status' => $params['status'],
                        'business_hours' => $params['business_hours']['startTime'].'-'.$params['business_hours']['endTime'],
                    ]);

                    $money = config('site.gift_money');
                    $user_id = Db::name('user')->insertGetId([
                        'username' => $params['username'],
                        'nickname' => $params['nickname'],
                        'password' => $params['password'],
                        'salt' => $params['salt'],
                        'avatar' => $params['logo'],
                        'mobile' => $params['mobile'],
                        'createtime' => $time,
                        'updatetime' => $time,
                        'status' => 'normal',
                        'money' => $money,
                        'merchant_id'=>$merchant_id
                    ]);

                    //写入日志
                    MoneyLog::create(['user_id' => $user_id, 'money' => $money, 'before' => 0, 'after' => $money, 'memo' => '后台添加账号赠送','status'=>1]);

                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success('成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        for ($i=0; $i <= 24; $i++){
            $time_arr[] = $i.':00';
        }

        $this->view->assign('time_arr',$time_arr);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = Db::name('hx_merchant')
            ->alias('m')
            ->join(['fa_user u'],'m.id=u.merchant_id','left')
            ->where('m.id',$ids)
            ->field('m.*,u.nickname,u.username,u.id as user_id')
            ->find();

        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                Db::startTrans();

                try {
                    $time = time();

                    $user_update = [];
                    if ($row['mobile'] != $params['mobile']) {//修改手机号码
                        // 添加手机格式验证
                        if (!validate_mobile($params['mobile'])) {
                            exception(__("手机号码格式不正确"));
                        }
                        if (Db::name('hx_merchant')->where('id','<>',$ids)->where('mobile',$params['mobile'])->find()) {
                            exception(__("手机号码已存在"));
                        }
                        if (Db::name('user')->where('merchant_id','<>',$ids)->where('mobile',$params['mobile'])->find()) {
                            exception(__("手机号码已存在"));
                        }
                        $user_update['mobile'] = $params['mobile'];
                    }

                    $user_update['status'] = $params['status'] == 1 ? 'normal' : 'hidden';
                    $user_update['nickname'] = $params['nickname'];
                    $user_update['updatetime'] = $time;
                    Db::name('user')->where('merchant_id',$ids)->update($user_update);

                    $update = [];
                    $update['name'] = $params['name'];
                    $update['mobile'] = $params['mobile'];
                    $update['business_hours'] = $params['business_hours']['startTime'].'-'.$params['business_hours']['endTime'];
                    $update['logo'] = $params['logo'];
                    $update['door_photo'] = $params['door_photo'];
                    $update['cash_photo'] = $params['cash_photo'];
                    $update['hall_photo'] = $params['hall_photo'];
                    $update['status'] = $params['status'];
                    $update['updatetime'] = $time;
                    Db::name('hx_merchant')->where('id',$ids)->update($update);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success('成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $row['business_hours'] = explode('-',$row['business_hours']);
        $row['business_hours']['startTime'] = $row['business_hours'][0];
        $row['business_hours']['endTime'] = $row['business_hours'][1];

        for ($i=0; $i <= 24; $i++){
            $time_arr[] = $i.':00';
        }

        $this->view->assign('time_arr',$time_arr);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if ($ids) {
            $result = Db::name('hx_merchant')->where('id',$ids)->delete();
            if ($result) {
                $this->success('成功');
            }
            $this->error('失败');
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    public function select_list()
    {
        if($this->request->isAjax()){

            $page = $this->request->post("pageNumber/d", 1);
            $limit = $this->request->post("pageSize/d", 15);
            $keyword= $this->request->post("name", '');

            $keyValue = $this->request->post('keyValue','');
            if ($keyValue) {
                $keyword = $keyValue;
            }

            $list = Db::name('hx_merchant')
                ->field('concat(id , " - ",name," - ",INSERT(mobile,4,3,\'***\')) as name,id')
                ->where(['name|id|mobile'=>['like','%'.$keyword.'%']])
                ->order('id', 'desc')
                ->page($page, $limit)
                ->paginate($limit,false,['page'=>$page]);

            $result = array("total" => $list->total(), "list" => $list->items());

            return json($result);
        }
    }

}