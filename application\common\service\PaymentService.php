<?php

namespace app\common\service;

use app\common\model\UserRechargeLog;
use think\Db;
use think\Log;

class PaymentService
{
    /**
     * 处理支付成功回调
     */
    public static function handlePaySuccess($orderNo,$notifyData)
    {
        Db::startTrans();
        try {
            // 1. 更新支付记录
            $payment = self::updatePayment($orderNo, $notifyData);

            // 2. 处理充值业务（包含代理分佣）
            $recharge = self::processRecharge($payment);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            Log::error("支付处理失败：{$e->getMessage()}");
            return false;
        }
    }

    /**
     * 更新支付记录
     */
    private static function updatePayment($orderNo, $notifyData)
    {
        $payment = Db::name('payment')
            ->where('order_no', $orderNo)
            ->find();

        if (!$payment) {
            throw new \Exception("支付订单不存在：{$orderNo}");
        }
        // 校验支付状态
        if ($payment['status'] != 0) {
            throw new \Exception("无效的订单状态：{$payment['status']}");
        }

        // 校验金额一致性
        $notifyAmount = number_format($notifyData['amount'], 2, '.', '');
        if ($payment['amount'] != $notifyAmount) {
            throw new \Exception("金额不一致，系统：{$payment['amount']}，通知：{$notifyAmount}");
        }

        // 更新支付记录
        Db::name('payment')
            ->where('id',$payment['id'])
            ->update([
            'status' => 1,//状态：0=待支付，1=成功，2=已关闭，3=已退款
            'trade_no' => $notifyData['trade_no'],
            'pay_time' => time(),
            'callback_data' => json_encode($notifyData)
        ]);

        return $payment;
    }

    /**
     * 处理充值业务
     */
    private static function processRecharge($payment)
    {
        // 获取关联的充值记录
        $recharge = Db::name('user_recharge_log')
            ->where('order_no', $payment['order_no'])
            ->find();

        if (!$recharge) {
            throw new \Exception("充值记录不存在：{$payment['order_no']}");
        }

        // 校验充值状态
        if ($recharge['status'] != 0) {
            throw new \Exception("无效的充值状态：{$recharge['status']}");
        }

        // 用户信息
        $user = Db::name('user')
            ->where('id',$payment['user_id'])
            ->find();
        if (!$user) {
            throw new \Exception("用户信息不存在");
        }

        $recharge['user'] = $user;

        $after = bcadd($user['money'], $recharge['total_coin'], 2);
        // 发放金币
        Db::name('user')
            ->where('id', $recharge['user_id'])
            ->inc('money', $recharge['total_coin'])
            ->update();

        // 添加记录
        Db::name('user_money_log')
            ->insert([
                'user_id'       => $payment['user_id'],
                'money'         => $recharge['total_coin'],
                'before'        => $user['money'],
                'after'         => $after,
                'memo'          => '充值',
                'createtime'    => time(),
                'status'        => 1,
                'type'          => 2,//充值类型
                'correlation_id' => $recharge['id']
            ]);

        // 更新充值状态
        Db::name('user_recharge_log')
            ->where('id',$recharge['id'])
            ->update([
                'status' => 1,
                'updatetime' => time(),
                'pay_time'=>time()
            ]);

        // 处理代理分佣
        self::processAgentCommission($user, $recharge);

        return $recharge;
    }

    /**
     * 处理代理分佣
     */
    private static function processAgentCommission($user, $recharge)
    {
        if ($recharge['commission'] <= 0) {
            Log::info("分佣金额为0，跳过分佣");
            return;
        }

        // 获取代理信息
        $agent = Db::name('admin')->where('id', $recharge['agent_id'])->find();
        if (!$agent) {
            Log::info("代理不存在");
            return;
        }

        // 更新代理余额
        $newCommission = bcadd($agent['commission'], $recharge['commission'], 2);
        Db::name('admin')
            ->where('id', $agent['id'])
            ->update([
                'commission' => $newCommission,
                'updatetime' => time()
            ]);

        // 记录分佣日志
        Db::name('admin_commission_log')->insert([
            'agent_id'      => $agent['id'],
            'commission'    => $recharge['commission'],
            'before'        => $agent['commission'],
            'after'         => $newCommission,
            'memo'          => "用户 {$user['nickname']} 充值分佣，订单号：{$recharge['order_no']}",
            'createtime'    => time(),
            'status'        => 1,// 收入
            'correlation_id' => $recharge['id'],// 关联充值记录ID
        ]);

        Log::info("代理分佣成功：代理ID {$agent['id']}，分佣金额 {$recharge['commission']}，用户 {$user['id']}，订单 {$recharge['order_no']}");
    }


}