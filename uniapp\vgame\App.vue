<script>
	export default {
		onLaunch: function() {
			// 按顺序加载所需脚本
			const loadScript = (src) => {
				return new Promise((resolve, reject) => {
					const script = document.createElement('script')
					script.src = src
					script.onload = resolve
					script.onerror = reject
					document.head.appendChild(script)
				})
			}
			
			// 按顺序加载脚本 - 使用相对路径
			Promise.resolve()
				.then(() => loadScript('./static/js/EasyPlayer-pro.js'))
				.then(() => {
					// 确保EasyPlayer-pro.js加载完成后再加载其他资源
					console.log('EasyPlayer-pro.js加载完成');
					return loadScript('./static/js/EasyPlayer-decode.js');
				})
				.catch(err => {
					console.error('脚本加载失败:', err)
				})
			
		},
		onShow: function() {
			// console.log('App Show')
		},
		onHide: function() {
			// console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url("~@/static/icon/iconfont.css");
	
	/* 全局禁用横向滚动 */
	page {
	  overflow-x: hidden !important;
	  box-sizing: border-box;
	  height: 100%;
	}
</style>
