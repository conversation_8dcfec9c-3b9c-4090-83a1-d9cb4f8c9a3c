<template>
  <view class="scan-container">
    <mumu-get-qrcode
      ref="qrcodeComponent"
      :key="qrcodeKey"
      @success="qrcodeSuccess"
      @error="qrcodeError"
      :continue="false"
      size="whole"
      definition="true"
      :show-torch="false"
    >
      <template v-slot:error>
        <view>摄像头启动失败</view>
      </template>
    </mumu-get-qrcode>
    <view class="scan-tip">请将摄像头对准客户二维码</view>
  </view>
</template>

<script>
import mumuGetQrcode from '@/uni_modules/mumu-getQrcode/components/mumu-getQrcode/mumu-getQrcode.vue'
export default {
  components: {
    mumuGetQrcode
  },
  data() {
    return {
      qrcodeKey: Date.now()
    }
  },
  onShow() {
    this.qrcodeKey = Date.now();
  },
  onHide() {
    this.closeCamera();
  },
  onUnload() {
    this.closeCamera();
  },
  methods: {
    closeCamera() {
      if (this.$refs.qrcodeComponent && typeof this.$refs.qrcodeComponent.closeCamera === 'function') {
        this.$refs.qrcodeComponent.closeCamera();
      }
    },
    qrcodeSuccess(data) {
      if (!data) {
        uni.showToast({ title: '二维码无效', icon: 'none' })
        return
      }
      uni.navigateTo({
        url: `/pages/verify/verify?qrText=${encodeURIComponent(data)}`
      })
    },
    qrcodeError(err) {
      uni.showModal({
        title: '扫码失败',
        content: '摄像头授权失败或未识别到二维码，请检查权限或重试。',
        success: () => {
          uni.navigateBack({})
        }
      })
    }
  }
}
</script>

<style scoped>
.scan-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #180F29;
}
.scan-tip {
  font-size: 32rpx;
  color: #7B68EE;
  margin-top: 40rpx;
}
</style>