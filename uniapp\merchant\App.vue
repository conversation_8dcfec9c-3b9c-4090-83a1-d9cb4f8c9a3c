<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */

	/* 全局背景色 */
	page {
		background-color: #180F29;
	}

	/* 通用容器样式 */
	.container {
		background: #180F29;
		min-height: 100vh;
	}

	/* 通用顶部栏样式 */
	.top-bar {
		display: flex;
		align-items: center;
		padding-bottom: 22rpx;
		justify-content: flex-start;
		background: url('/static/home/<USER>') no-repeat center/100vw 210rpx;
		width: 100vw;
		height: 180rpx;
		z-index: 100;
	}

	/* 通用用户信息包装器 */
	.user-info-wrap {
		display: flex;
		margin-top: 60rpx;
		position: relative;
		width: 100%;
	}

	/* 通用头像包装器 */
	.avatar-wrap {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 5rpx;
	}

	/* 通用头像等级包装器 */
	.avatar-level-wrap.large {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 通用小头像 */
	.avatar.small {
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		position: absolute;
		left: 35rpx;
		top: 30rpx;
		z-index: 2;
		object-fit: cover;
	}

	/* 通用等级头像背景 */
	.level-avatar-bg.large {
		width: 140rpx;
		height: 140rpx;
		border-radius: 50%;
		position: absolute;
		left: 0;
		top: 0;
		z-index: 3;
		object-fit: cover;
		pointer-events: none;
	}

	/* 通用用户详情 */
	.user-detail {
		flex: 1;
		margin-left: 12rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	/* 通用昵称 */
	.nickname {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		margin-left: 10rpx;
	}

	/* 通用等级资产行 */
	.level-assets-row {
		display: flex;
		align-items: center;
		margin-top: 6rpx;
	}

	/* 通用资产项 */
	.asset-item {
		display: flex;
		align-items: center;
		margin-right: 12rpx;
		background-color: #180F2D;
		padding: 4rpx 5rpx;
		border-radius: 30rpx;
		border: 1rpx solid #7C6649;
		height: 40rpx;
		max-width: 190rpx;
		min-width: 120rpx;
	}

	.asset-item:last-child {
		margin-right: 0;
	}

	/* 通用图标 */
	.coin-icon,
	.diamond-icon,
	.asset-action-icon{
		width: 40rpx;
		height: 40rpx;
	}

	/* 通用资产操作图标 */
	.asset-action-icon {
		margin-left: 5rpx;
		object-fit: contain;
	}

	/* 通用资产数字 */
	.asset-num {
		font-size: 26rpx;
		color: #fff;
		flex: 1;
	}

	/* 通用卡片样式 - 框框背景颜色 */
	.card-container {
		background: #2A1840;
		border-radius: 16rpx;
		margin: 20rpx;
		padding: 20rpx;
		border: 1rpx solid #4C3A62;
	}

	/* 通用列表项样式 */
	.list-item {
		background: #2A1840;
		border-radius: 12rpx;
		margin: 16rpx 20rpx;
		padding: 30rpx 20rpx;
		border: 1rpx solid #4C3A62;
	}

	/* 通用按钮样式 */
	.modal-button {
		border: none;
		border-radius: 30px;
		font-size: 16px;
		padding: 0 20px;
		height: 60px;
		line-height: 60px;
		text-align: center;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.modal-button.primary {
		background: url('/static/mine/button.png') no-repeat center/100% 100%;
		color: #fff;
	}

	.modal-button.secondary {
		background: #666;
		color: #999;
	}

	/* 通用输入框样式 */
	.common-input {
		width: 100%;
		height: 80rpx;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
		color: #fff;
		box-sizing: border-box;
		background-color: #2A1840;
		border: 1rpx solid #4C3A62;
		outline: none;
	}

	.common-input:focus {
		color: #fff;
		border-color: #7B68EE;
	}

	.common-input::placeholder {
		color: #999;
	}

	/* 通用文本颜色 */
	.text-primary {
		color: #fff;
	}

	.text-secondary {
		color: #999;
	}

	.text-success {
		color: #48A578;
	}

	.text-error {
		color: #ff4757;
	}

	.text-warning {
		color: #FFB366;
	}

	/* UniversalModal弹窗样式 */
	.modal-image {
		width: 50px !important;
		height: 40px !important;
		object-fit: contain;
	}

	/* 按钮变体 */
	.universal-modal-content .btn-cancel {
		background: #ccc;
		color: #666;
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0;
	}

	.universal-modal-content .btn-confirm {
		background: #4A90E2;
		color: #fff;
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0;
	}

	.universal-modal-content .btn-single {
		background: #4A90E2;
		color: #fff;
		width: 100%;
		height: 50px;
		margin: 16px 0 0 0;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0;
	}

	/* 按钮容器 */
	.universal-modal-content .modal-buttons {
		display: flex;
		justify-content: center;
		gap: 12px;
		margin-top: 16px;
		width: 100%;
	}

	/* 特殊元素样式 */
	.universal-modal-content .info-box {
		background: #3F3055;
		border-radius: 8px;
		padding: 8px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 12px;
		width: 95%;
	}

	/* 修复 input-group 内输入框文字居中问题 */
	.universal-modal-content .input-group {
		text-align: left;
	}

	.universal-modal-content .input-group input,
	.universal-modal-content .input-group .modal-input,
	.universal-modal-content .input-group .bind-input {
		text-align: left;
	}

	.universal-modal-content .bind-input-group {
		text-align: left;
	}

	.universal-modal-content .bind-input-group input,
	.universal-modal-content .bind-input-group .modal-input,
	.universal-modal-content .bind-input-group .bind-input {
		text-align: left;
	}

	/* 按钮样式 */
	.universal-modal-content button {
		border: none;
		font-size: 14px;
		border-radius: 20px;
		padding: 0;
		cursor: pointer;
		margin: 4px;
		min-width: 60px;
		height: 40px;
		background: #4A90E2;
		color: #fff;
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
	}

	.universal-modal-content button:hover {
		opacity: 0.9;
	}

	/* 支持背景图片的按钮 */
	.universal-modal-content .submit-btn,
	.universal-modal-content .bind-submit-btn {
		background: url('/static/mine/button.png') no-repeat center/100% 100%;
		height: 60px;
		line-height: 60px;
		border-radius: 30px;
		font-size: 16px;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0;
	}
</style>
