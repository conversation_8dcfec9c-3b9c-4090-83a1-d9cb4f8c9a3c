# UniversalModal 统一弹窗组件

## 🎯 概述

`UniversalModal` 是合并了 `GameModal` 和 `CommonModal` 的统一弹窗组件，使用固定px单位确保在任何屏幕方向下都保持完全一致的显示效果。

## ✨ 特性

- ✅ **完美横屏适配**：使用固定px单位，任何屏幕方向下都完全一致
- ✅ **多种尺寸**：small(240px)、medium(280px)、large(320px)
- ✅ **丰富类型**：支持9种弹窗类型，满足所有使用场景
- ✅ **灵活配置**：支持自定义标题、关闭按钮、遮罩点击等
- ✅ **插槽支持**：支持自定义内容和底部按钮区域
- ✅ **统一样式**：内置所有常用UI元素样式

## 📦 快速开始

### 1. 导入组件

```javascript
import UniversalModal from '@/components/UniversalModal.vue'

export default {
  components: {
    UniversalModal
  }
}
```

### 2. 基础用法

```vue
<UniversalModal 
  :show="showModal" 
  title="弹窗标题" 
  @close="showModal = false"
>
  <text>弹窗内容</text>
</UniversalModal>
```

## 🔧 Props 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `show` | Boolean | `false` | 是否显示弹窗 |
| `title` | String | `''` | 弹窗标题 |
| `showClose` | Boolean | `true` | 是否显示关闭按钮 |
| `closeIcon` | String | `''` | 关闭按钮图标路径（为空时显示×） |
| `maskClosable` | Boolean | `true` | 点击遮罩是否可以关闭弹窗 |
| `landscape` | Boolean | `false` | 是否为横屏模式 |
| `size` | String | `'medium'` | 弹窗尺寸：`small`、`medium`、`large` |
| `type` | String | `'default'` | 弹窗类型，用于特殊样式 |

### 尺寸说明

- **small (240px)**：简单提示、确认对话框
- **medium (280px)**：标准弹窗、表单等
- **large (320px)**：复杂内容、详情页等

### 类型说明

- `default`：默认样式
- `exit`：退出确认
- `exchange`：积分兑换
- `error`：错误提示
- `reward`：奖励展示
- `settle`：结算
- `wait`：等待/加载
- `coin`：投币
- `setting`：设置
- `rule`：规则说明
- `service`：客服
- `function`：功能菜单

## 🎨 使用示例

### 确认弹窗

```vue
<UniversalModal
  :show="showConfirm"
  title="确认操作"
  size="medium"
  @close="showConfirm = false"
>
  <text>确认要执行此操作吗？</text>
  <template #footer>
    <view class="modal-buttons">
      <button class="btn-cancel" @click="handleCancel">取消</button>
      <button class="btn-confirm" @click="handleConfirm">确认</button>
    </view>
  </template>
</UniversalModal>
```

### 奖励弹窗

```vue
<UniversalModal
  :show="showReward"
  title="恭喜获得"
  type="reward"
  size="medium"
  @close="showReward = false"
>
  <image src="/static/coin.png" />
  <view class="number">+100</view>
  <text>恭喜获得金币奖励！</text>
  <template #footer>
    <button class="btn-single" @click="showReward = false">确定</button>
  </template>
</UniversalModal>
```

### 积分兑换弹窗

```vue
<UniversalModal
  :show="showExchange"
  title="积分兑换"
  type="exchange"
  size="large"
  :mask-closable="false"
  @close="showExchange = false"
>
  <!-- 当前积分 -->
  <view class="info-box">
    <text>当前积分</text>
    <image src="/static/score.png" />
    <text class="box-value">{{ userScore }}</text>
  </view>

  <!-- 兑换比例 -->
  <view class="exchange-rate">
    <text class="rate-score">2</text>
    <text>:1</text>
    <text>积分兑换</text>
    <text class="rate-coin">1</text>
    <text>金币</text>
  </view>

  <!-- 兑换金币 -->
  <view class="info-box">
    <text>兑换金币</text>
    <image src="/static/coin.png" />
    <text class="box-value">{{ exchangeCoin }}</text>
  </view>

  <template #footer>
    <view class="modal-buttons">
      <button class="btn-cancel" @click="showExchange = false">取消</button>
      <button class="btn-confirm" @click="handleExchange">立即兑换</button>
    </view>
  </template>
</UniversalModal>
```

### 设置弹窗

```vue
<UniversalModal
  :show="showSetting"
  title="游戏设置"
  type="setting"
  size="medium"
  @close="showSetting = false"
>
  <view class="setting-item">
    <text>音效开关</text>
    <view class="setting-switch" :class="{on: settings.sound}" @click="toggleSound">
      <view class="switch-knob"></view>
    </view>
  </view>
  
  <view class="setting-item">
    <text>震动反馈</text>
    <view class="setting-switch" :class="{on: settings.vibrate}" @click="toggleVibrate">
      <view class="switch-knob"></view>
    </view>
  </view>

  <template #footer>
    <button class="btn-single" @click="showSetting = false">确定</button>
  </template>
</UniversalModal>
```

### 功能菜单弹窗

```vue
<UniversalModal
  :show="showFunction"
  title="功能菜单"
  type="function"
  size="medium"
  @close="showFunction = false"
>
  <view class="func-grid">
    <view class="func-item" @click="openSetting">
      <image src="/static/func/setting.png" />
      <view>设置</view>
    </view>
    <view class="func-item" @click="openService">
      <image src="/static/func/service.png" />
      <view>联系客服</view>
    </view>
    <view class="func-item" @click="openRule">
      <image src="/static/func/rule.png" />
      <view>玩法说明</view>
    </view>
    <view class="func-item" @click="openFeedback">
      <image src="/static/func/feedback.png" />
      <view>设备反馈</view>
    </view>
  </view>
</UniversalModal>
```

## 🎯 内置样式类

### 按钮样式

```html
<!-- 主要按钮 -->
<button class="btn-confirm">确认</button>

<!-- 次要按钮 -->
<button class="btn-cancel">取消</button>

<!-- 单按钮 -->
<button class="btn-single">确定</button>

<!-- 按钮容器 -->
<view class="modal-buttons">
  <button class="btn-cancel">取消</button>
  <button class="btn-confirm">确认</button>
</view>
```

### 信息展示

```html
<!-- 信息框 -->
<view class="info-box">
  <text>标签</text>
  <image src="/static/icon.png" />
  <text class="box-value">数值</text>
</view>

<!-- 数字显示 -->
<view class="number">+100</view>

<!-- 兑换比例 -->
<view class="exchange-rate">
  <text class="rate-score">2</text>
  <text>:1</text>
  <text>积分兑换</text>
  <text class="rate-coin">1</text>
  <text>金币</text>
</view>
```

### 设置开关

```html
<view class="setting-item">
  <text>设置项名称</text>
  <view class="setting-switch" :class="{on: isOn}" @click="toggle">
    <view class="switch-knob"></view>
  </view>
</view>
```

### 功能网格

```html
<view class="func-grid">
  <view class="func-item" @click="handleClick">
    <image src="/static/icon.png" />
    <view>功能名称</view>
  </view>
</view>
```

## 📱 横屏适配

组件会自动根据 `landscape` 属性进行适配：

```javascript
// 在游戏页面中
data() {
  return {
    isLandscape: false
  }
},
onShow() {
  // 根据游戏配置设置横屏模式
  this.isLandscape = this.gameInfo.is_landscape === 1
}
```

## 🔄 迁移指南

### 从 GameModal 迁移

```vue
<!-- 旧代码 -->
<GameModal :show="show" title="标题" :landscape="landscape" @close="close">
  内容
</GameModal>

<!-- 新代码 -->
<UniversalModal :show="show" title="标题" :landscape="landscape" @close="close">
  内容
</UniversalModal>
```

### 从 CommonModal 迁移

```vue
<!-- 旧代码 -->
<CommonModal :show="show" title="标题" size="medium" @close="close">
  内容
</CommonModal>

<!-- 新代码 -->
<UniversalModal :show="show" title="标题" size="medium" @close="close">
  内容
</UniversalModal>
```

## 🎪 展示页面

访问 `/pages/modal-showcase/index` 查看所有弹窗类型的实际效果，支持：

- 实时切换横屏/竖屏模式
- 动态调整弹窗尺寸
- 展示所有9种弹窗类型
- 交互式演示

## 🚀 优势

1. **代码统一**：一个组件替代两个，减少维护成本
2. **样式一致**：使用固定px单位，完美解决横屏适配问题
3. **功能完整**：整合了所有原有功能
4. **易于使用**：API简洁，学习成本低
5. **性能优化**：减少了重复代码和样式计算

## 📝 注意事项

1. **z-index**：组件使用 `z-index: 999999`，确保在最上层显示
2. **图片路径**：确保静态资源路径正确
3. **事件处理**：记得在父组件中处理 `close` 事件
4. **横屏模式**：根据实际需求设置 `landscape` 属性
