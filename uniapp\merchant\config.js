let host = '';
if (typeof window !== 'undefined') {
	host = window.location.host;
}

let baseUrl = '';
let WebSocket = '';
const mqttUrl = 'ws://**************:8083/mqtt'; // 固定 MQTT 地址
const mqttUsername = 'game';
const mqttPassword = 'sdoifj239874fh97g34fdg34';

// 本地开发环境
if (
	host.indexOf('localhost') !== -1 ||
	host.indexOf('127.0.0.1') !== -1 ||
	/^192\.168\./.test(host)
) {
	baseUrl = 'https://testva2.91jdcd.com';
	WebSocket = 'wss://testva2.91jdcd.com/websocket/';
}
// 生产环境1
else if (host.indexOf('testva2.91jdcd.com') !== -1) {
	baseUrl = 'https://testva2.91jdcd.com';
	WebSocket = 'wss://testva2.91jdcd.com/websocket/';
}
// 生产环境2
else if (host.indexOf('yx.yhdyc.com') !== -1) {
	baseUrl = 'https://yx.yhdyc.com';
	WebSocket = 'wss://ws.yhdyc.com/websocket/';
}
// 生产环境3
else if (host.indexOf('bsth5.yinmengkj.cn') !== -1) {
	baseUrl = 'https://bsth5.yinmengkj.cn';
	WebSocket = 'wss://bstwss.yinmengkj.cn/websocket/';
}
// 生产环境4
else if (host.indexOf('i97ypb.cn') !== -1) {
	baseUrl = 'https://i97ypb.cn';
	WebSocket = 'wss://wss.i97ypb.cn/websocket/';
}
// 默认
else {
	baseUrl = 'https://testva2.91jdcd.com';
	WebSocket = 'wss://testva2.91jdcd.com/websocket/';
}

export default {
	baseUrl,
	WebSocket,
	mqttUrl,
	mqttUsername,
	mqttPassword,
	tokenKey: 'merchant_token', // Token存储键名
	tokenExpireKey: 'merchant_token_expire', // Token过期时间键名
	userInfo: 'merchant_user_info' // 用户信息键名
} 