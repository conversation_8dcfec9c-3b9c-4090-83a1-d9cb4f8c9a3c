<template>
  <view>
    <video 
      id="h5-video"
      ref="videoRef"
      :controls="false"
      :style="{ width: '100%', height: '400rpx' }"
    ></video>
    <button @click="handleManualPlay">点击播放视频</button>
  </view>
</template>

<script>
// #ifdef H5
import flvjs from 'flv.js';
// #endif

export default {
  data() {
    return {
      flvPlayer: null,
      videoUrl: 'http://*************:10086/live/stream_1.flv' // 替换为真实FLV地址
    };
  },
  mounted() {
    // #ifdef H5
    this.$nextTick(() => {
      const videoWrapper = document.getElementById('h5-video');
      if (!videoWrapper) {
        console.error('未找到video容器');
        return;
      }
      const nativeVideo = videoWrapper.querySelector('video');
      if (nativeVideo && typeof nativeVideo.play === 'function') {
        this.initFlvPlayer(nativeVideo);
      } else {
        console.error('原生video元素无效', nativeVideo);
      }
    });
    // #endif
  },
  methods: {
    // #ifdef H5
    initFlvPlayer(videoElement) {
      if (flvjs.isSupported()) {
        this.flvPlayer = flvjs.createPlayer({
          type: 'flv',
          url: this.videoUrl,
          isLive: true
        });
        this.flvPlayer.attachMediaElement(videoElement);
        this.flvPlayer.load();
      } else {
        uni.showToast({ title: '当前环境不支持FLV播放', icon: 'none' });
      }
    },
    // #endif
    handleManualPlay() {
      // #ifdef H5
      if (this.flvPlayer) {
        this.flvPlayer.play().catch(err => {
          uni.showToast({ title: '播放失败:' + err.message, icon: 'none' });
        });
      }
      // #endif

      // #ifndef H5
      const videoContext = uni.createVideoContext('h5-video');
      videoContext.play();
      // #endif
    }
  },
  beforeDestroy() {
    // #ifdef H5
    if (this.flvPlayer) {
      this.flvPlayer.destroy();
    }
    // #endif
  }
};
</script>