<?php

namespace app\admin\controller\game;

use app\common\controller\Backend;
use think\Db;

/**
 * 游戏记录
 * @Authod JW
 * @Time 2023/02/02
 * @package app\admin\controller\agent
 */
class Log extends Backend
{
    /**
     * Withdraw模型对象
     * @var \app\admin\model\Agent
     */
    protected $model = null;

    protected $noNeedRight = '';
    protected $multiFields = "deleted,rent_limit";
    public function _initialize()
    {
        $this->noNeedRight = array('');

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            $admin = get_admin_info();
            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
                //代理只查看自己的用户
                $where_arr['u.p_id'] = $admin['admin_id'];
            }

            if ($search_arr) {

            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('game_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->where($where)
                ->where($where_arr)
                ->count();

            $list = Db::name('game_log')
                ->alias('l')
                ->join(['fa_game g'],'g.id=l.game_id','left')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->where($where)
                ->where($where_arr)
                ->order('l.id', $order)
                ->field('l.*,g.name as game_name,u.nickname,u.mobile,u.avatar')
                ->limit($offset, $limit)
                ->select();

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 结束游戏
     * <AUTHOR>
     * @date 2025-6-19
     * @return
     */
    public function end_game($ids = NULL)
    {
        $row = Db::name('game_log')
            ->alias('l')
            ->join(['fa_game g'],'g.id=l.game_id','left')
            ->join(['fa_user u'],'u.id=l.user_id','left')
            ->where('l.id',$ids)
            ->field('l.*,g.name,u.nickname,u.mobile')
            ->find();
        if (!$row)
            $this->error(__('No Results were found'));

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params['gameSn'] = $row['sn'];
                $params['code'] = 200;
                $game_model = new \app\common\model\Game();
                $result = $game_model->endGameResult($params);
                if ($result['code'] == 1) {
                    $this->success('成功');
                }
                $this->error($result['msg']);
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

}
