define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'user/invite_log/index',
                    add_url: '',
                    edit_url: '',
                    del_url: '',
                    multi_url: '',
                    table: 'user',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='invite_people_user_id']", form).addClass("selectpage").data("source", "user/user/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                $("input[name='invitee_people_user_id']", form).addClass("selectpage").data("source", "user/user/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");


                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'id',
                sortName: 'id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true, operate:false},
                        {field: 'invite_people_user_id',title: __('邀请人'),formatter: function (value, row, index) {
                                if (row.invite_people_nickname) {
                                    return '昵称：'+row.invite_people_nickname+'<br>'+'用户ID：'+row.invite_people_user_id;
                                }else{
                                    return row.invite_people_user_id;
                                }
                            }},
                        {field: 'invite_people_get_coin', title: __('邀请人获得金币'), operate:false},
                        {field: 'invitee_people_user_id',title: __('被邀请人'),formatter: function (value, row, index) {
                                if (row.invitee_people_nickname) {
                                    return '昵称：'+row.invitee_people_nickname+'<br>'+'用户ID：'+row.invitee_people_user_id;
                                }else{
                                    return '用户ID：'+row.invitee_people_user_id;
                                }
                            }},
                        {field: 'invitee_people_get_coin', title: __('被邀请人获得金币'), operate:false},
                        {field: 'createtime', title: __('创建时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});