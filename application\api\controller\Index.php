<?php

namespace app\api\controller;

use app\common\controller\Api;

/**
 * 首页接口
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 首页
     *
     */
    public function index()
    {
        $this->success('请求成功');
    }

    public function test(){
        $mes = '{"code":200,"type":"endGameResult","gameSn":"WaterMargin_LYAX3CDDOKM4HLW2HFE305A","number":1,"userId":2,"coin":100,"score":100}';
        $game = new \app\common\model\Game();
        $result = $game->gameResult($mes);
        print_r($result);die;
    }
}
