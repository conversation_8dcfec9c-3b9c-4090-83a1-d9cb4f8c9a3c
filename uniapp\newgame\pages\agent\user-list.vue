<template>
	<view class="container">
		<!-- 顶部数据显示区 -->
		<view class="data-stats-bar-wrapper">
			<view class="data-stats-bar">
				<view class="stat-item">
					<text class="stat-num">{{ totalUsers }}</text>
					<text class="stat-label">用户总数</text>
				</view>
				<view class="stat-divider"></view>
				<view class="stat-item">
					<text class="stat-num">{{ yesterdayNewUsers }}</text>
					<text class="stat-label">今日新增</text>
				</view>
			</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-bar-wrapper">
			<view class="search-bar">
				<input v-model="searchText" class="search-input" placeholder="请输入名字或手机号码搜索" />
				<button class="search-btn" @tap="searchUser">搜索</button>
			</view>
		</view>

		<!-- 用户列表 -->
		<view class="user-list">
			<view class="user-item" v-for="user in users" :key="user.id">
				<view class="user-detail-section">
					<view class="avatar-circle">
						<image :src="user.avatar || defaultAvatar" mode="aspectFill" />
					</view>
					<view class="user-info">
						<view class="user-row">昵称：{{ user.nickname }}</view>
						<view class="user-row">手机号码：{{ user.mobile }}</view>
						<view class="user-row">用户ID：{{ user.id }}</view>
						<view class="user-row">创建时间：{{ formatTime(user.createtime) }}</view>
					</view>
				</view>

				<view class="item-divider"></view>

				<!-- 用户列表项底部按钮 -->
				<view class="item-bottom-btns">
					<button class="record-btn green-btn" @tap="goTeamMembers(user)">游戏记录</button>
					<button class="record-btn blue-btn" @tap="goWifiList(user)">充值记录</button>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request.js'
	import config from '@/config.js'

	export default {
		data() {
			return {
				totalUsers: 0,
				yesterdayNewUsers: 0,
				searchText: '',
				users: [],
				defaultAvatar: '/static/avatar.png',
				page: 1,
				show_num: 10,
				hasMore: true,
				loading: false,
				currentAgentId: null, // To store the logged-in agent's ID
			}
		},
		onLoad() {
			const userInfo = uni.getStorageSync(config.userInfo)
			if (userInfo && userInfo.agent_id) {
				this.currentAgentId = userInfo.agent_id
				this.getUserList()
			} else {
				uni.showToast({
					title: '请先登录代理账户',
					icon: 'none'
				})
				// Optionally redirect to login or agent dashboard
			}
		},
		onReachBottom() {
			if (this.hasMore && !this.loading) {
				this.page++
				this.getUserList(true)
			}
		},
		methods: {
			async getUserList(loadMore = false) {
				if (this.loading || this.currentAgentId === null) return
				this.loading = true

				try {
					const params = {
						page: this.page,
						show_num: this.show_num,
					}
					if (this.searchText) {
						// Assuming 'keyword' is the parameter for name/phone search.
						// If the API expects 'nickname' or 'mobile', you'll need to adjust this.
						params.keyword = this.searchText
					}

					const res = await request({
						url: '/api/agent/user_list',
						data: params
					})

					if (res.code === 1) {
						const newUsers = res.data.list.map(user => ({
							...user,
							type: this.getUserLevelName(user
								.level) // Map level to type if needed for display
						}))

						this.totalUsers = res.data.total
						this.yesterdayNewUsers = res.data.yesterday
						this.users = loadMore ? [...this.users, ...newUsers] : newUsers
						this.hasMore = res.data.list.length === this.show_num
					} else {
						uni.showToast({
							title: res.msg || '获取用户列表失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('获取用户列表失败：', error)
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			},
			searchUser() {
				this.page = 1 // Reset page for new search
				this.users = [] // Clear existing users
				this.hasMore = true
				this.getUserList() // Fetch new search results
			},
			getUserLevelName(level) {
				// This is a placeholder. You might have a mapping for level names.
				// For example, if level 1 is "普通用户", 2 is "VIP" etc.
				// Based on the given data, level 1 is shown for 'test1' which is probably '普通用户'
				switch (level) {
					case 1:
						return '普通用户'
						// Add more cases as per your actual level definitions
					default:
						return '未知等级'
				}
			},
			formatTime(timestamp) {
				const date = new Date(timestamp * 1000) // Convert seconds to milliseconds
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				const hours = String(date.getHours()).padStart(2, '0')
				const minutes = String(date.getMinutes()).padStart(2, '0')
				const seconds = String(date.getSeconds()).padStart(2, '0')
				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
			},
			goTeamMembers(user) {
				uni.navigateTo({
					url: `/pages/agent/user-game-log?userId=${user.id}`
				})
			},
			goWifiList(user) {
				uni.navigateTo({
					url: `/pages/agent/user-recharge-log?userId=${user.id}`
				})
			},
			goHistoryRecord(user) {
				// TODO: 跳转到历史记录页面，并传入用户信息
				console.log('跳转到历史记录，用户：', user)
			}
		}
	}
</script>

<style scoped>
	.container {
		min-height: 100vh;
		padding-bottom: 32rpx;
	}

	.data-stats-bar-wrapper,
	.search-bar-wrapper {
		background: #2A1840;
		margin-bottom: 24rpx;
		border: 1px solid #4C3A62;
		border-radius: 12rpx;
		margin: 20rpx 32rpx 24rpx 32rpx;
	}

	.data-stats-bar {
		display: flex;
		justify-content: space-around;
		padding: 32rpx 0;
		border-bottom: 1rpx solid #4C3A62;
		margin: 0 32rpx;
	}

	.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.stat-num {
		font-size: 48rpx;
		font-weight: bold;
		color: #fff;
	}

	.stat-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-top: 8rpx;
	}

	.stat-divider {
		width: 2rpx;
		height: 80rpx;
		background: #4C3A62;
	}

	.search-bar {
		display: flex;
		align-items: center;
		padding: 24rpx 32rpx;
	}

	.search-input {
		flex: 1;
		height: 64rpx;
		border: 1rpx solid #4C3A62;
		padding: 0 24rpx;
		font-size: 28rpx;
		background: #3F3055;
		color: #fff;
		border-radius: 8rpx;
	}

	.search-input::placeholder {
		color: rgba(255, 255, 255, 0.5);
	}

	.search-btn {
		background: #2196F3;
		margin-left: 16rpx;
		height: 64rpx;
		padding: 0 32rpx;
		/* background: url('/static/mine/button.png') no-repeat center/100% 100%; */
		color: #fff;
		font-size: 28rpx;
		border: none;
		line-height: 64rpx;
		border-radius: 8rpx;
	}

	.user-list {
		margin: 0 32rpx;
	}

	.user-item {
		background: #2A1840;
		border-radius: 12rpx;
		border: 1px solid #4C3A62;
		padding: 24rpx;
		margin-bottom: 24rpx;
	}

	.user-detail-section {
		display: flex;
		align-items: center;
	}

	.avatar-circle {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		background: #eee;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		overflow: hidden;
	}

	.avatar-circle image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.user-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.user-row {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 8rpx;
		display: flex;
		align-items: baseline;
	}

	.user-row:last-child {
		margin-bottom: 0;
	}

	.user-type {
		font-size: 24rpx;
		color: #999;
	}

	.arrow-icon {
		width: 32rpx;
		height: 32rpx;
		margin-left: 16rpx;
	}

	.item-divider {
		height: 1rpx;
		background: #4C3A62;
		margin: 24rpx 0;
	}

	.item-bottom-btns {
		display: flex;
		justify-content: space-around;
	}

	.record-btn {
		flex: 1;
		margin: 0 8rpx;
		height: 72rpx;
		color: #fff;
		font-size: 28rpx;
		border: none;
		line-height: 72rpx;
	}

	.green-btn {
		background: #4CAF50;
	}

	.blue-btn {
		background: #2196F3;
	}

	.orange-btn {
		background: #FF9800;
	}
</style>