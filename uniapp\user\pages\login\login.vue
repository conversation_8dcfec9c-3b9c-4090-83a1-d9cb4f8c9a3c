<template>
  <view class="container">
    <!-- 登录标题 -->
    <view class="login-header">
      <view class="login-title">用户登录</view>
    </view>

    <!-- 登录表单 -->
    <view class="form-box">
      <view class="input-group">
        <input type="text" v-model="loginForm.account" placeholder="请输入登录账号" maxlength="20" />
      </view>
      <view class="input-group">
        <input type="password" v-model="loginForm.password" placeholder="请输入密码" />
      </view>
      <button class="submit-btn" @tap="handleLogin">登录</button>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import config from '@/config.js'

export default {
  data() {
    return {
      loginForm: {
        account: '',
        password: ''
      }
    }
  },
  onLoad(options) {
    // 判断是否已登录
    const token = uni.getStorageSync(config.tokenKey)
    const expire = uni.getStorageSync(config.tokenExpireKey)
    if (token && (!expire || expire > Date.now())) {
      uni.reLaunch({ url: '/pages/index/index' })
    }
  },
  methods: {
    // 登录
    async handleLogin() {
      if (!this.loginForm.account) {
        uni.showToast({
          title: '请输入登录账号',
          icon: 'none'
        })
        return
      }
      if (!this.loginForm.password) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        })
        return
      }
      try {
        const res = await request({
          url: '/api/user/login',
          data: {
            account: this.loginForm.account,
            password: this.loginForm.password
          }
        })
        // 保存token和用户信息
        uni.setStorageSync(config.tokenKey, res.data.userinfo.token)
        uni.setStorageSync(config.tokenExpireKey, (res.data.userinfo.expiretime * 1000))
        uni.setStorageSync(config.userInfo, res.data.userinfo)
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        setTimeout(() => {
          uni.reLaunch({ url: '/pages/index/index' })
        }, 1500)
      } catch (error) {
        console.error('登录失败：', error)
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 40rpx;
  background-color: #180F29;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-header {
  display: flex;
  justify-content: center;
  margin-bottom: 80rpx;
}

.login-title {
  font-size: 48rpx;
  color: #fff;
  font-weight: bold;
  text-align: center;
}

.form-box {
  padding: 0 20rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.input-group input {
  width: 100%;
  height: 80rpx;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #fff;
  box-sizing: border-box;
  background-color: #2A1840;
  border: 1rpx solid #4C3A62;
  outline: none;
}

.input-group input:focus {
  color: #fff;
  border-color: #7B68EE;
}

.input-group input::placeholder {
  color: #999;
}

.submit-btn {
  width: 100%;
  height: 120rpx;
  line-height: 120rpx;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #fff;
  background: url('/static/mine/button.png') no-repeat center/100% 100%;
  border: none;
  text-align: center;
  margin-top: 40rpx;
}
</style>