define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'recharge/recharge/index',
                    add_url: 'recharge/recharge/add',
                    edit_url: 'recharge/recharge/edit',
                    del_url: 'recharge/recharge/del',
                    multi_url: 'recharge/recharge/multi',
                    table: 'recharge',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='c_id']", form).addClass("selectpage").data("source", "game/category/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc").data("multiple","true");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'id',
                sortName: 'id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true},
                        {field: 'price', title: __('支付金额'), operate:false},
                        {field: 'coin', title: __('所得金币'), operate:false},
                        {field: 'gift_coin', title: __('赠送金币'), operate:false},
                        {field: 'type', title: __('类型'),searchList: {"1":__('普通'),"2":__('特惠')},formatter: function (value, row, index) {
                                if (row.type == 1) {
                                    return '普通';
                                }else{
                                    return '特惠';
                                }
                            }},
                        {field: 'is_package', title: __('是否套餐'),searchList: {"0":__('否'),"1":__('是')},formatter: function (value, row, index) {
                                if (row.is_package == 1) {
                                    return '是';
                                }else{
                                    return '否';
                                }
                            }},
                        {field: 'createtime', title: __('创建时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('充值记录列表'),
                                    text:'充值记录列表',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","70%"]\'',
                                    icon: 'fa fa-gamepad',
                                    url: function (row){
                                        return "game/log/index?game_id=" + row.id;
                                    }
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});