<style type="text/css">
    .layui-layer-pay .layui-layer-content {
        padding: 0;
        height: 600px !important;
    }

    .layui-layer-pay {
        border: none;
    }

    .payimg {
        position: relative;
        width: 800px;
        height: 600px;
    }

    .payimg .alipaycode {
        position: absolute;
        left: 265px;
        top: 442px;
    }

    .payimg .wechatcode {
        position: absolute;
        left: 660px;
        top: 442px;
    }

    .thumbnail img {
        width: 100%;
    }

    .fixed-table-toolbar .pull-right.search {
        min-width: 300px;
    }

    a.title {
        color: #444;
    }

    .releasetips {
        position: relative;
    }

    .releasetips i {
        display: block;
        background: #f00;
        border-radius: 50%;
        width: 0.3em;
        height: 0.3em;
        top: 0px;
        right: -8px;
        position: absolute;
        box-shadow: 0px 0px 2px #f11414;
    }

    .form-userinfo .breadcrumb {
        margin-bottom: 10px;
    }

    .btn-toggle {
        padding: 0;
    }

    .operate .dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
        bottom: inherit;
    }

</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        {if $Think.config.fastadmin.api_url}
        <ul class="nav nav-tabs nav-category">
            <li class="active"><a href="javascript:;" data-id="">{:__('All')}</a></li>
            <li><a href="javascript:;" data-id="0">{:__('Uncategoried')}</a></li>
        </ul>
        {/if}
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" data-force-refresh="false"><i class="fa fa-refresh"></i> </a>
                        {if $Think.config.fastadmin.api_url}
                        <button type="button" id="faupload-addon" class="btn btn-danger faupload btn-mini-xs" data-url="addon/local" data-chunking="false" data-mimetype="zip,fastaddon" data-multiple="false"><i class="fa fa-upload"></i>
                            {:__('Local install')}
                        </button>
                        <div class="btn-group">
                            <a href="#" class="btn btn-info btn-switch active btn-mini-xs" data-type="all"><i class="fa fa-list"></i> {:__('All')}</a>
                            <a href="#" class="btn btn-info btn-switch btn-mini-xs" data-type="free"><i class="fa fa-gift"></i> {:__('Free')}</a>
                            <a href="#" class="btn btn-info btn-switch btn-mini-xs" data-type="price"><i class="fa fa-rmb"></i> {:__('Paying')}</a>
                            <a href="#" class="btn btn-info btn-switch btn-mini-xs" data-type="local" data-url="addon/downloaded"><i class="fa fa-laptop"></i> {:__('Local addon')}</a>
                        </div>
                        <a class="btn btn-primary btn-userinfo btn-mini-xs" href="javascript:;"><i class="fa fa-user"></i> {:__('Userinfo')}</a>
                        {/if}
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover" width="100%">

                    </table>

                </div>
            </div>

        </div>
    </div>
</div>
<script id="searchformtpl" type="text/html">
    <form action="" class="form-commonsearch hide">
        <div class="well" style="box-shadow:none;border-radius:2px;margin-bottom:10px;">
            <div class="row">
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="form-group">
                        <label class="control-label">{:__('Title')}</label>
                        <input class="operate" type="hidden" data-name="title" value="like"/>
                        <input class="form-control" type="text" name="title" placeholder="" value=""/>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="form-group">
                        <label class="control-label">{:__('Type')}</label>
                        <input class="operate" type="hidden" data-name="type" value="="/>
                        <input class="form-control" type="text" name="type" placeholder="all" value=""/>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="form-group">
                        <label class="control-label">{:__('Category')}</label>
                        <input type="hidden" class="operate" data-name="category_id" value="="/>
                        <input class="form-control" name="category_id" type="text" value="">
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="form-group">
                        <label class="control-label">{:__('Version')}</label>
                        <input type="hidden" class="operate" data-name="faversion" value="="/>
                        <input class="form-control" name="faversion" type="text" value="{$Think.config.fastadmin.version|htmlentities}">
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="form-group">
                        <label class="control-label"></label>
                        <div class="row">
                            <div class="col-xs-6">
                                <input type="submit" class="btn btn-success btn-block" value="{:__('Submit')}"/>
                            </div>
                            <div class="col-xs-6">
                                <input type="reset" class="btn btn-primary btn-block" value="{:__('Reset')}"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</script>
<script id="uninstalltpl" type="text/html">
    <div class="">
        <div class=""><%=#__("Are you sure you want to unstall %s?", addon['title'])%>
            <p class="text-danger">{:__('Delete all the addon file and cannot be recovered!')} </p>
            {if config('app_debug')}
            <p class="text-danger"><input type="checkbox" name="droptables" id="droptables" data-name="<%=addon['name']%>"/> {:__('Delete all the addon database and cannot be recovered!')} </p>
            {/if}
            <p class="text-danger">{:__('Please backup important data manually before uninstall!')}</p>
        </div>
    </div>
</script>
<script id="upgradetpl" type="text/html">
    <div class="">
        <div class=""><%=#__("Upgrade tips", addon['title'])%></div>
    </div>
</script>
<script id="conflicttpl" type="text/html">
    <div class="alert alert-dismissable alert-danger">
        <button type="button" class="close" data-dismiss="alert">×</button>
        <strong>{:__('Warning')}</strong> {:__('Conflict tips')}
    </div>
    <table class="table table-striped">
        <thead>
        <tr>
            <th>#</th>
            <th>{:__('File')}</th>
        </tr>
        </thead>
        <tbody>
        <%for(var i=0;i < conflictlist.length;i++){%>
        <tr>
            <th scope="row"><%=i+1%></th>
            <td><%=conflictlist[i]%></td>
        </tr>
        <%}%>
        </tbody>
    </table>
</script>
<!--@formatter:off-->
<script id="operatetpl" type="text/html">
    <% var labelarr = ['primary', 'success', 'info', 'danger', 'warning']; %>
    <% var label = labelarr[item.id % 5]; %>
    <% var addon = item.addon; %>

    <span class="operate" data-id="<%=item.id%>" data-name="<%=item.name%>">
        <% if(!addon){ %>
            <% if(typeof item.releaselist !="undefined" && item.releaselist.length>1){%>
                <span class="btn-group">
                    <a href="javascript:;" class="btn btn-xs btn-primary btn-success btn-install"
                       data-type="<%=item.price<=0?'free':'price';%>"
                       data-version="<%=item.version%>"><i class="fa fa-cloud-download"></i> {:__('Install')}</a>
                    <a class="btn btn-xs btn-success dropdown-toggle" data-toggle="dropdown" href="javascript:;">
                        <span class="fa fa-caret-down"></span>
                    </a>
                    <ul class="dropdown-menu">
                        <% for(var j=0;j< item.releaselist.length;j++){ %>
                        <li><a href="javascript:;" class="btn-install" data-type="<%=item.price<=0?'free':'price';%>"
                               data-version="<%=item.releaselist[j].version%>"><%=item.releaselist[j].version%></a></li>
                        <% } %>
                    </ul>
                </span>
            <% }else if(typeof item.releaselist !="undefined" && item.releaselist.length>0){%>
                <a href="javascript:;" class="btn btn-xs btn-primary btn-success btn-install"
                   data-type="<%=item.price<=0?'free':'price';%>"
                   data-version="<%=item.version%>"><i class="fa fa-cloud-download"></i> {:__('Install')}</a>
            <% } %>

            <% if(item.demourl){ %>
                <a href="<%=item.demourl%>" class="btn btn-xs btn-primary btn-info btn-demo" target="_blank">
                    <i class="fa fa-flash"></i> {:__('Demo')}
                </a>
            <% } %>

            <% if(item.button){ %>
                <a href="<%=item.url%>" class="btn btn-xs btn-primary btn-info" target="_blank">
                    <%=item.button%>
                </a>
            <% } %>
        <% } else {%>
            <% if(addon.version!=item.version){%>
                <% if(typeof item.releaselist !="undefined" && item.releaselist.length>1){%>
                    <span class="btn-group">
                        <a href="javascript:;" class="btn btn-xs btn-info btn-success btn-upgrade"
                           data-version="<%=item.version%>"><i class="fa fa-cloud"></i> {:__('Upgrade')}</a>
                        <a class="btn btn-xs btn-info dropdown-toggle" data-toggle="dropdown"
                           href="javascript:;">
                            <span class="fa fa-caret-down"></span>
                        </a>
                        <ul class="dropdown-menu">
                            <% for(var j=0;j< item.releaselist.length;j++){ %>
                            <li><a href="javascript:;" class="btn-upgrade"
                                   data-version="<%=item.releaselist[j].version%>"><%=item.releaselist[j].version%></a></li>
                            <% } %>
                        </ul>
                    </span>
                <% }else{%>
                    <a href="javascript:;" class="btn btn-xs btn-info btn-upgrade" title="{:__('Upgrade')}" data-version="<%=item.version%>"><i
                        class="fa fa-cloud"></i> {:__('Upgrade')}</a>
                <% }%>
            <% }%>
            <% if(addon.config){ %>
                <a href="javascript:;" class="btn btn-xs btn-primary btn-config" title="{:__('Setting')}"><i class="fa fa-pencil"></i>
                    {:__('Setting')}</a>
            <% } %>
            <a href="javascript:;" class="btn btn-xs btn-danger btn-uninstall" title="{:__('Uninstall')}"><i class="fa fa-times"></i>
                {:__('Uninstall')}</a>
        <% } %>
    </span>
</script>
<!--@formatter:on-->
