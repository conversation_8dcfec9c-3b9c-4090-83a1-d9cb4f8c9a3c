<template>
    <view class="container">
        <!-- 视频 -->
        <view class="player-pro-live">
            <view id="easyplayer" ></view>
			<view class="video-overlay"></view>
        </view>

        <!-- 左上角状态信息 -->
        <view class="status-container">
            <view class="status-item">
                {{ gameStatusText }}
            </view>
            <view class="status-item">{{ userInfo.money || 0 }}金币</view>
        <!--   <view class="status-item">{{ userInfo.score || 0 }}积分</view> -->
        </view>

        <!-- 右上角头像和退出按钮 -->
        <view class="avatar-container">
            <view class="avatar-item" v-for="(seat, index) in seats" :key="seat.id">
                <view class="avatar-wrapper">
                    <image :src="getPlayerAvatar(seat)" mode="aspectFill" class="avatar-image"></image>
                    <text class="avatar-name">{{ getPlayerName(seat) }}</text>
                </view>
            </view>
            <button class="exit-button" @click="exitGame">
                <text class="exit-icon">×</text>
            </button>
        </view>
        <!-- 在avatar-container下方添加倒计时 -->
        <!-- <view class="countdown-container" v-if="gameInfo.is_full !== 2">
            <text class="countdown-text">{{ countdown }}秒</text>
            <text class="countdown-label">剩余围观时间</text>
        </view> -->

        <!-- 底部按钮区域 -->
        <view class="button-container" v-if="gameInfo.is_full !== 2">
            <!-- 四个按钮，每个占1/4宽度 -->
            <view class="button-column" v-for="(seat, index) in seats" :key="seat.id">
                <!-- 六宫格布局 - 简化后的结构 -->
                <view class="button-content">
                    <button class="game-button" @click="startGame(seat)" v-if="seat.status === 0">
                        <text>开始</text>
                    </button>
                    <!-- <view class="coin-display" v-if="seat.status === 0">
                        <text>{{ gameInfo.coin }}金币</text>
                    </view> -->
                </view>
            </view>
        </view>

        <!-- 游戏操作区域（仅在游戏中显示） -->
        <view v-if="gameInfo.is_full === 2" class="game-controls">
            <!-- 左侧方向轮盘 -->
            <view class="directional-pad">
                <view class="dpad-circle">
                    <!-- 上 -->
                    <button class="dpad-btn up" @touchstart="handleDirection(10)"></button>
                    <!-- 下 -->
                    <button class="dpad-btn down" @touchstart="handleDirection(11)"></button>
                    <!-- 左 -->
                    <button class="dpad-btn left" @touchstart="handleLeftStart" @touchend="handleLeftEnd"
                        @touchcancel="handleLeftEnd"></button>
                    <!-- 右 -->
                    <button class="dpad-btn right" @touchstart="handleRightStart" @touchend="handleRightEnd"
                        @touchcancel="handleRightEnd"></button>
                </view>
            </view>

            <!-- 右侧功能按钮 -->
            <view class="action-buttons">
                <!-- 加注按钮 -->
                <button class="action-btn bet-btn" @touchstart="handleDirection(16)">
                    <text>加注</text>
                </button>

                <!-- 切换武器按钮 -->
                <button class="action-btn weapon-switch-btn" @touchstart="handleDirection(14)">
                    <text>切换</text>
                </button>

                <!-- 自动按钮 -->
                <button class="action-btn auto-btn" :class="{'auto-active': isAuto}" @touchstart="toggleAuto">
                    <text>{{ isAuto ? '停止自动' : '自动' }}</text>
                </button>

                <!-- 发炮按钮 -->
                <button class="fire-button" @touchstart="handleFireStart" @touchend="handleFireEnd"
                    @touchcancel="handleFireEnd">
                    <text>发炮</text>
                </button>
            </view>
        </view>
    </view>
</template>

<script>
    import {
        GameWebSocket
    } from '@/utils/websocket.js';
    import request from '@/utils/request'
    import flvjs from 'flv.js'
    import config from '@/config.js'

    export default {
        data() {
            return {
                playerInfo: null,
                gameId: null,
                gameSn: null,
                videoUrl: '',
                gameInfo: {}, // 游戏信息
                seats: [], // 座位信息
                userInfo: {}, // 用户信息
                defaultAvatar: '/static/default_avatar.png',
                isAutoPlay: false,
                countdown: 60, // 倒计时剩余时间
                countdownTimer: null, // 倒计时定时器
                mp: null,

                ws: null, // 新增 WebSocket 实例
                hasConnected: false, // 是否已建立过连接
                number: 0, //座位号

                flvPlayer: null,

                isAuto: false, // 自动模式状态

                // 新增发炮数据项
                firePressTimer: null,
                fireInterval: null,
                isLongPress: false,

                leftPressTimer: null,
                leftInterval: null,
                rightPressTimer: null,
                rightInterval: null,
                isLeftLongPress: false,
                isRightLongPress: false
            }
        },
        onReady() {
            this.initFlvPlayer();
            this.checkPlayerAndInit()

            setTimeout(() => {
                this.play()
            }, 500); // 延迟 500ms
        },
        onHide() {
            // 页面隐藏时断开连接（包括返回首页）
            this.disconnectWebSocket()
        },
        mounted() {
			      this.fixSafariLayout()
            // 添加窗口变化监听
			      window.addEventListener('resize', this.fixSafariLayout)
            // window.addEventListener('resize', this.handleOrientationChange);
            window.addEventListener('orientationchange', this.handleOrientationChange);
        },
        watch: {
            // 监听游戏状态变化
            'gameInfo.is_full': {
                handler(newVal) {
                    if (newVal !== 2) {
                        this.startCountdown() // 启动倒计时
                    } else {
                        this.clearCountdown() // 清除倒计时

                        if (!this.hasConnected && !this.ws) {
                            this.connectWebSocket() //重连
                        }
                    }
                },
                immediate: true
            }
        },
        computed: {
            // 计算游戏状态文本
            gameStatusText() {
                if (!this.gameInfo) return '加载中...';

                switch (this.gameInfo.is_full) {
                    case 0:
                        return '当前空闲';
                    case 1:
                        return '座位已满';
                    case 2:
                        return this.userInfo.nickname || '正在游戏中';
                    default:
                        return '等待启动中...';
                }
            },
            // 获取玩家信息映射
            playersMap() {
                const map = {};
                if (this.gameInfo.players && this.gameInfo.players.length) {
                    this.gameInfo.players.forEach(player => {
                        map[player.seat_id] = player;
                    });
                }
                return map;
            }
        },
        async onLoad(options) {
            this.gameId = options.gameId
            await this.fetchGameData();

            // 延迟初始化视频确保DOM加载完成
            this.$nextTick(() => {
                this.initVideo();
            });

            //连接socket
            this.connectWebSocket()
        },

        onUnload() {
            // 组件销毁时释放资源
            if (this.flvPlayer) {
                this.flvPlayer.pause();
                this.flvPlayer.unload();
                this.flvPlayer.detachMediaElement();
                this.flvPlayer.destroy();
                this.flvPlayer = null;
            }


            this.clearCountdown(); // 清除倒计时

            // 页面卸载时确保断开
            this.disconnectWebSocket()
            this.mp.destroy()

            clearTimeout(this.firePressTimer)
            clearInterval(this.fireInterval)
            clearTimeout(this.leftPressTimer)
            clearInterval(this.leftInterval)
            clearTimeout(this.rightPressTimer)
            clearInterval(this.rightInterval)
        },
        beforeDestroy() { // 页面卸载时
            this.disconnectWebSocket()
            
              if (this.playerInfo) {
                  this.playerInfo.destroy();
                }
				        window.removeEventListener('resize', this.fixSafariLayout)
                // window.removeEventListener('resize', this.handleOrientationChange);
                window.removeEventListener('orientationchange', this.handleOrientationChange);
        },
        methods: {
			// 精准检测 Safari 浏览器（排除 iOS 上其他浏览器）
			  isSafariBrowser() {
			    const ua = navigator.userAgent
			    const isIOS = /iPad|iPhone|iPod/.test(ua)
			    const isSafari = /Safari/.test(ua) && !/Chrome|CriOS|Edg|FxiOS|OPX/.test(ua)
			    return isSafari || (isIOS && !window.MSStream)
			  },
			
			  // 修复 Safari 布局问题
			  fixSafariLayout() {
			    if (!this.isSafariBrowser()) return
			    
			    // 双重保障：直接操作 canvas 和容器
			    const fixLayout = () => {
			      try {
			        // 方案1：调整 canvas 定位
			        const canvas = document.querySelector('#easyplayer canvas')
			        if (canvas) {
			          canvas.style.cssText = `
			            top: 0 !important;
			            left: 0 !important;
						margin-left: -110px;
						margin-top: -50px;
			          `
			        }


				
			
			        // 方案2：重置容器尺寸
			        // const container = document.getElementById('easyplayer')
			        // if (container) {
			        //   container.style.transform = 'none'
			        //   container.style.width = '100vh'
			        //   container.style.height = '100vw'
			        // }
			      } catch (e) {
			        console.error('布局修复失败:', e)
			      }
			    }

            document.canvasfix=setInterval(function(){
                const canvas = document.querySelector('#easyplayer canvas')
                if(canvas){
                    canvas.style["margin-left"]="118px";
                    canvas.style["margin-top"]="-120px";
                    canvas.style.transform = 'scale(0.70)';
                    canvas.style.transformOrigin = '0 0';
                    //alert(canvas.style["margin-left"]);
                    clearInterval(document.canvasfix);
                }

                console.log(123)

            },500);


			  },
			
            handleOrientationChange() {
              const isPortrait = window.matchMedia("(orientation: portrait)").matches;
              this.$nextTick(() => {
                if(this.playerInfo) {
                  this.playerInfo.resize(); // 通知播放器重置尺寸
                }
              });
            },
            play() {
                setTimeout((url) => {
                    this.playerInfo && this.playerInfo.play(url).then(() => {}).catch((e) => {
                        console.error(e);
                    });
                }, 0, this.videoUrl)
            },
            checkPlayerAndInit() {
                let retryCount = 0
                const maxRetries = 10

                const checkAndInit = () => {
                    if (typeof EasyPlayerPro !== 'undefined') {
                        this.playCreate()
                        return
                    }

                    if (retryCount < maxRetries) {
                        retryCount++
                        setTimeout(checkAndInit, 500)
                    } else {
                        console.error('播放器加载失败')
                        uni.showToast({
                            title: '播放器加载失败',
                            icon: 'none'
                        })
                    }
                }

                checkAndInit()
            },
            playCreate() {
                try {
                    // 使用 uni.createSelectorQuery 获取元素
                    // 直接获取DOM元素
                    const container = document.getElementById('easyplayer');

                    if (!container) {
                        console.error('播放器容器未找到');
                        return;
                    }

                    console.log('容器元素:', container);

                    var easyplayer = new EasyPlayerPro({
                        container: container,
                        decoder: './static/js/EasyPlayer-decode.js', // 使用正确的解码器路径
                        videoBuffer: 1, // 视频缓存时长（单位：秒）
                        isResize: true, // 是否支持播放器大小调整
                        text: "",
                        loadingText: "加载中", // 加载视频时显示的文本
                        useMSE: true, // 是否使用 MSE（Media Source Extensions）
                        useSIMD: false, // 是否使用 SIMD（Single Instruction Multiple Data）
                        useWCS: false, // 是否使用 WCS（WebRTC通讯服务）
                        isMulti: true, // 是否支持多路视频播放
                        hasAudio: false, // 是否有音频
                        reconnection: true, // 是否支持自动重连
                        // showBandwidth: true, // 是否显示网速（注释掉，未启用）
                        showPerformance: false, // 是否显示性能信息
                        operateBtns: { // 播放器操作按钮配置
                            fullscreen: false, // 是否显示全屏按钮
                            screenshot: false, // 是否显示截图按钮
                            play: false, // 是否显示播放按钮
                            audio: false, // 是否显示音频控制按钮
                            record: false, // 是否显示录制按钮
                            quality: false, // 是否显示画质选择按钮
                            performance: false, // 是否显示性能按钮
                        },

                        watermarkConfig: { // 水印配置
                            text: { // 水印文本配置
                                content: 'easyplayer-pro' // 水印显示的文本内容
                            },
                            right: 10, // 水印距离右边的距离（单位：像素）
                            top: 10 // 水印距离顶部的距离（单位：像素）
                        },
                        quality: ['高清'],
                        playbackForwardMaxRateDecodeIFrame: 1,
                        isWebrtcForOthers: true,
                        demuxUseWorker: true,
                        supportHls265: false,
                        canvasRender: false,
    
                        aspectRatio: '16:9',// 设置视频显示区域的宽高比
                        easyStretch: true,  // 强制不同分辨率视频铺满窗口
            
                    });

                    easyplayer.on("fullscreen", function(flag) {
                        console.log('is fullscreen', flag)
                    })
                    easyplayer.on('playbackPreRateChange', (rate) => {
                        easyplayer.forward(rate);
                    })

                    easyplayer.on('playbackSeek', (data) => {
                        easyplayer.setPlaybackStartTime(data.ts);
                    })
                    this.playerInfo = easyplayer

                } catch (error) {
                    console.error('播放器初始化失败:', error);
                }
            },
            // 单独的全屏方法，由用户点击触发
            enterFullscreen() {
                if (this.playerInfo) {
                    const container = document.getElementById("easyplayer");
                    if (container) {
                        // 使用原生全屏API
                        if (container.requestFullscreen) {
                            container.requestFullscreen();
                        } else if (container.webkitRequestFullscreen) {
                            container.webkitRequestFullscreen();
                        } else if (container.mozRequestFullScreen) {
                            container.mozRequestFullScreen();
                        } else if (container.msRequestFullscreen) {
                            container.msRequestFullscreen();
                        }
                    }
                }
            },
            initFlvPlayer() {
				return;
                if (!flvjs.isSupported()) {
                    console.error('浏览器不支持MSE或flv.js');
                    // 可在此处降级到HLS（如使用hls.js）
                    return;
                }

                if (flvjs.isSupported()) {
                    const videoElement = document.getElementById('h5-video');
                    if (!videoElement || !videoElement.play) {
                        console.error('未找到有效的video元素');
                        return;
                    }
                    // const videoElement = this.$refs.videoRef.$el; // 获取原生DOM元素
                    this.flvPlayer = flvjs.createPlayer({
                        type: 'flv',
                        url: this.videoUrl,
                        isLive: true // 如果是直播流，需开启
                    });
                    this.flvPlayer.attachMediaElement(videoElement);
                    this.flvPlayer.load();
                    this.flvPlayer.play().catch(err => {
                        console.error('播放失败:', err);
                    });
                } else {
                    console.error('当前浏览器不支持flv.js');
                }
            },
            // 发炮按钮按下
            handleFireStart() {
                this.isLongPress = false
                // 启动长按检测定时器
                this.firePressTimer = setTimeout(() => {
                    this.isLongPress = true
                    // 长按时循环发炮
                    this.handleDirection(15)
                    this.fireInterval = setInterval(() => {
                        this.handleDirection(15)
                    }, 150)
                }, 150)
            },

            // 发炮按钮释放
            handleFireEnd() {
                // 清除所有定时器
                clearTimeout(this.firePressTimer)
                clearInterval(this.fireInterval)

                // 如果是短按则触发单次发炮
                if (!this.isLongPress) {
                    this.handleDirection(15)
                }

                // 无论长短按都发送停止指令
                this.handleDirection(17)
                this.isLongPress = false
            },
            // 自动模式切换
            toggleAuto() {
                const newState = !this.isAuto
                this.isAuto = newState
                // 发送对应指令：18-开始自动 17-停止自动
                this.handleDirection(newState ? 18 : 17)
            },
            initVideo() {
                return;
                const video = document.getElementById('myVideo');

                if (Hls.isSupported()) {
                    this.hls = new Hls({
                        enableWorker: false, // 禁用worker以提高兼容性
                    });
                    this.hls.loadSource(this.videoUrl);
                    this.hls.attachMedia(video);
                    this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                        video.play();
                    });
                } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                    // iOS原生支持
                    video.src = this.videoUrl;
                    video.addEventListener('loadedmetadata', () => {
                        video.play();
                    });
                }
            },

            // 新增 WebSocket 管理方法
            connectWebSocket() {
                if (!this.ws) {
                    this.ws = new GameWebSocket(config.WebSocket)
                    this.setupWebSocketEvents()
                }
                this.ws.connect(this.gameSn)
                this.hasConnected = true
            },

            disconnectWebSocket() {
                if (this.ws) {
                    this.ws.disconnect()
                    this.ws = null
                    this.hasConnected = false
                }
            },

            setupWebSocketEvents() {
                this.ws.on('onMessage', data => {
                    if (data.type == 'update_game') {
                        this.fetchGameData(); // 重新获取最新数据
                    }
                    // 处理游戏实时状态更新
                    // if (data.type === 'game_state') {
                    //  this.gameInfo = data.state
                    // }
                })

                this.ws.on('onClose', () => {
                    // 连接关闭时的处理
                    if (this.gameInfo.is_full === 2) {
                        // uni.showToast({
                        //  title: '连接断开，正在重连...',
                        //  icon: 'none'
                        // })
                    }
                })
            },
            // 启动倒计时
            startCountdown() {
				return ;
                this.clearCountdown() // 清除倒计时
                this.countdownTimer = setInterval(() => {
                    if (this.countdown <= 0) {
                        this.forceExit()
                        return
                    }
                    this.countdown--
                }, 1000)
            },
            // 清除倒计时
            clearCountdown() {
                if (this.countdownTimer) {
                    clearInterval(this.countdownTimer)
                    this.countdownTimer = null
                }
            },
            // 强制退出
            forceExit() {
                this.clearCountdown()
                uni.navigateBack()
            },

            // 获取游戏数据
            async fetchGameData() {
                try {
                    const res = await request({
                        url: '/api/game/detail',
                        data: {
                            gameId: this.gameId
                        }
                    })
                    this.gameInfo = res.data;
                    this.seats = res.data.seats || [];
                    this.userInfo = res.data.userInfo || {};
                    this.videoUrl = res.data.video_url;
                    this.gameSn = this.gameInfo.sn;
                } catch (error) {
                    console.error('获取游戏数据失败:', error);
                    uni.showToast({
                        title: '获取游戏数据失败',
                        icon: 'none'
                    });
                } finally {
                    this.loading = false;
                }
            },
            // 获取玩家头像
            getPlayerAvatar(seat) {
                const player = this.playersMap[seat.id]; // 使用座位ID匹配
                return player?.avatar || this.defaultAvatar;
            },
            // 获取玩家名称
            getPlayerName(seat) {
                const player = this.playersMap[seat.id]; // 使用座位ID匹配
                return player ? player.nickname : '空闲';
            },

            // 开始游戏
            async startGame(seat) {
                try {
					const currentMoney = this.userInfo.money || 0;
					  if (currentMoney <= 0) {
						uni.showToast({
						  title: '金币不足，请先充值',
						  icon: 'none',
						  duration: 2000
						});
						
						// 跳转充值页面（假设充值页面路径为/pages/recharge/recharge）
						setTimeout(() => {
						  uni.switchTab({
							url: '/pages/recharge/recharge'
						  });
						}, 1500);
						return;
					  }
					
                    this.number = seat.number;

                    const res = await request({
                        url: '/api/game/start',
                        method: 'POST',
                        data: { // 正确参数格式
                            sn: this.gameSn, //游戏序列号
                            number: this.number //座位号
                        }
                    });

                    await this.fetchGameData(); // 重新获取最新数据
                } catch (error) {
                    // 断开连接
                    this.disconnectWebSocket()
                    console.error('请求失败:', error);
                }
            },

            // 退出游戏
            async exitGame() {
                uni.showModal({
                    title: '提示',
                    content: '确定要退出游戏吗？',
                    success: async (res) => {
                        if (res.confirm) {
                            if (this.gameInfo.is_full === 2 || this.gameInfo.is_full === -1) { //正在游戏中
                                try {
                                    const res = await request({
                                        url: '/api/game/end',
                                        data: { // 正确参数格式
                                            sn: this.gameInfo.sn
                                        }
                                    });
                                } catch (error) {
                                    console.error('请求失败:', error);
                                }
                            }
                            if (this.hls) {
                                this.hls.destroy();
                                this.hls = null;
                            }
                            // 再断开连接
                            this.disconnectWebSocket()
                            this.number = 0;
							
                            uni.navigateBack();
                            return;
                        }
                    }
                });
            },

            // 发送操作指令
            handleDirection(direction) {
                if (this.ws && this.ws.readyState === 1) {
                    this.ws.send({
                        type: 'command',
                        number: this.number,
                        status: direction,
                        // source: 'uniapp',
                        // timestamp: Date.now()
                    })
                } else {
                    console.log('操作指令发送失败')
                    // uni.showToast({
                    //  title: '操作指令发送失败',
                    //  icon: 'none'
                    // })
                }
            },
            // 左方向处理
            handleLeftStart() {
                this.isLeftLongPress = false
                this.handleDirection(12) // 立即触发一次

                this.leftPressTimer = setTimeout(() => {
                    this.isLeftLongPress = true
                    this.handleDirection(12)
                    this.leftInterval = setInterval(() => {
                        this.handleDirection(12)
                    }, 150)
                }, 150)
            },
            handleLeftEnd() {
                clearTimeout(this.leftPressTimer)
                clearInterval(this.leftInterval)
                this.isLeftLongPress = false
            },

            // 右方向处理
            handleRightStart() {
                this.isRightLongPress = false
                this.handleDirection(13) // 立即触发一次

                this.rightPressTimer = setTimeout(() => {
                    this.isRightLongPress = true
                    this.handleDirection(13)
                    this.rightInterval = setInterval(() => {
                        this.handleDirection(13)
                    }, 150)
                }, 150)
            },
            handleRightEnd() {
                clearTimeout(this.rightPressTimer)
                clearInterval(this.rightInterval)
                this.isRightLongPress = false
            },
        },
    }
</script>

<style scoped>
    .container {
      position: fixed;
      width: 100vh; /* 竖屏时高度作为宽度 */
      height: 100vw; /* 竖屏时宽度作为高度 */
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(90deg);
      transform-origin: center;
      background: #000;
      overflow: hidden;
	  /* 添加 Safari 私有属性适配 */
	   /* transform: rotate(90deg);
	    transform-origin: 0 0; */
    }
	/* 视频容器 */
	.player-pro-live {
	  position: absolute;
	    width: 100vh;
	    height: 100vw;
		z-index: 1;
	}
	/* 播放器元素 */
	/* #easyplayer {
	  width: 100% !important;
	  height: 100% !important;
	  object-fit: cover; 
	} */
/* 	#easyplayer {
	
	  transform: translateZ(0);
	  -webkit-touch-callout: none;
	
	  touch-action: none;
	  z-index: 1 !important;
	} */
	
	/* 覆盖播放器内部样式 */
	/* #easyplayer canvas {
		margin-left: -110px;
		margin-top: -50px;
	} */
  /*  .video {
        width: 100%;
        height: 100%;
       
    } */

    /* 横屏样式 */
    @media screen and (orientation: landscape) {
        .container {
            width: 100vw;
            height: 100vh;
            transform: none;
          }
    }

    /* 竖屏样式 */
    @media screen and (orientation: portrait) {
        /* .container {
            transform: none;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
          } */
    }

    /* 视频控件隐藏 */
    video::-webkit-media-controls {
        display: none !important;
    }

    video::-webkit-media-controls-enclosure {
        display: none !important;
    }

    video::-webkit-media-controls-panel {
        display: none !important;
    }

    /* 覆盖层样式 */
    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        font-size: 24px;
    }

    /* UI层级调整 */
    .status-container,
    .avatar-container,
    .button-container,
    .game-controls {
        z-index: 2;
        position: relative;
    }

    /* 左上角状态信息 */
    .status-container {
        position: absolute;
        top: 5%;
        left: 5%;
        z-index: 3;
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 10px;
        padding: 10px 15px;
    }

    .status-item {
        color: #fff;
        font-size: 16px;
        line-height: 1.5;
        text-shadow: 1px 1px 2px #000;
    }

    /* 右上角头像和退出按钮 */
    .avatar-container {
        position: absolute;
        top: 5%;
        right: 5%;
        z-index: 3;
        display: flex;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 30px;
        padding: 5px 10px;
    }

    .avatar-item {
        margin: 0 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .avatar-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .avatar-image {
        width: 20px;
        height: 20px;
        border-radius: 20px;
        border: 2px solid gold;
    }

    .avatar-name {
        color: white;
        font-size: 12px;
        margin-top: 2px;
        max-width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .exit-button {
        width: 30px;
        height: 30px;
        border-radius: 20px;
        background-color: #ff3333;
        border: none;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 10px;
        padding: 0;
    }

    .exit-icon {
        color: white;
        font-size: 24px;
        line-height: 1;
        font-weight: bold;
    }

    .exit-button:active {
        transform: scale(0.95);
    }

    /* 按钮容器 - 占屏幕底部1/4高度 */
    .button-container {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 20%;
        /* 屏幕高度的1/4 */
        display: flex;
        flex-direction: row;
        z-index: 2;
        background-color: rgba(0, 0, 0, 0.5);
    }

    /* 每个按钮列 - 占1/4宽度 */
    .button-column {
        flex: 1;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        /* 改为顶部对齐 */
        padding-top: 5px;
        /* 添加顶部内边距 */
    }

    /* 六宫格布局 */
    .button-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        /* 顶部对齐 */
        height: 100%;
        width: 80%;
    }

    /* 游戏按钮样式 - 定位在第2个单元格 */
    .game-button {
        width: 50%;
        height: 10px;
        min-height: 25px;
        /* 设置最小高度 */
        background-color: #ffcc00;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-weight: bold;
        border: none;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        font-size: 14px;
        margin-bottom: 8px;
        /* 与金币显示的间距 */
    }

    .game-button:active {
        transform: scale(0.95);
    }

    /* 金币显示样式 - 定位在第4个单元格 */
    .coin-display {
        color: gold;
        font-size: 12px;
        font-weight: bold;
        text-align: center;
        width: 100%;
    }

    /* 新增游戏控制样式 */
    .game-controls {
        position: fixed;
        /* 改为固定定位 */
        bottom: 10vh;
        left: 0;
        right: 0;
        z-index: 4;
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
        pointer-events: none;
        /* 防止点击穿透 */
    }

    /* 方向键样式 */
    .directional-pad {
        width: 130px;
        height: 130px;
        position: relative;
        filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
        pointer-events: auto;
        /* 允许交互 */
        top: 10vh;
    }

    /* 圆形底盘 */
    .dpad-circle {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(4px);
        position: relative;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .dpad-row {
        display: flex;
        justify-content: center;
        height: 33.33%;

    }
    .dpad-btn {
        position: absolute;
        width: 40px;
        height: 40px;
        border: none;
        background: transparent;
        padding: 0;
        transform-origin: center;
    }

    /* 三角形箭头 */
    .dpad-btn::before {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        border-style: solid;
        transition: all 0.2s;
    }

    /* 上方向 */
    .up {
        top: 0%;
        left: 50%;
        transform: translateX(-50%);
    }

    .up::before {
        border-width: 0 15px 26px 15px;
        border-color: transparent transparent rgba(255, 255, 255, 0.9) transparent;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -30%);
    }

    /* 下方向 */
    .down {
        bottom: 0%;
        left: 50%;
        transform: translateX(-50%);
    }

    .down::before {
        border-width: 26px 15px 0 15px;
        border-color: rgba(255, 255, 255, 0.9) transparent transparent transparent;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -70%);
    }

    /* 左方向 */
    .left {
        left: 0%;
        top: 50%;
        transform: translateY(-50%);
    }

    .left::before {
        border-width: 15px 26px 15px 0;
        border-color: transparent rgba(255, 255, 255, 0.9) transparent transparent;
        top: 50%;
        left: 50%;
        transform: translate(-70%, -50%);
    }

    /* 右方向 */
    .right {
        right: 0%;
        top: 50%;
        transform: translateY(-50%);
    }

    .right::before {
        border-width: 15px 0 15px 26px;
        border-color: transparent transparent transparent rgba(255, 255, 255, 0.9);
        top: 50%;
        left: 50%;
        transform: translate(-30%, -50%);
    }

    /* 按压状态 */
    .dpad-btn:active::before {
        opacity: 0.7;
        transform: scale(0.9);
    }

    /* 添加中心点 */
    .dpad-circle::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .dpad-btn:active {
        background: rgba(200, 200, 200, 0.8);
        transform: none !important;
    }

    .dpad-center {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 8px;
        margin: 0 5px;
    }

    /* 自动游戏按钮 */
    .auto-play-btn {
        width: 80px;
        height: 80px;
        right: 80px;
        top: 30px;
        border-radius: 50%;
        background: rgba(0, 200, 0, 0.8);
        color: white;
        font-size: 13px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
    }

    .auto-play-btn.active {
        background: rgba(200, 0, 0, 0.8);
    }

    .auto-play-btn:active {
        transform: scale(0.95);
    }

    /* 新增倒计时样式 */
    .countdown-container {
        position: absolute;
        top: 20%;
        /* 根据头像容器高度调整 */
        right: 60px;
        z-index: 3;
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 10px;
        padding: 8px 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .countdown-text {
        color: #ff4444;
        font-size: 18px;
        font-weight: bold;
    }

    .countdown-label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
        margin-top: 2px;
    }

    /* 修改右侧按钮样式 */
    .action-buttons {
        position: relative;
        width: 180px;
        height: 180px;
        pointer-events: auto;
    }

    .action-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        color: white;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid rgba(255, 255, 255, 0.5);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        position: absolute;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .action-btn:active {
        transform: scale(0.98) !important;
        /* 缩小缩放比例 */
        filter: brightness(0.9);
        /* 改为亮度变化 */
        opacity: 0.9;
        /* 添加透明度变化 */
    }

    /* 发炮按钮样式 */
    .fire-button {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255, 0, 0, 0.8);
        position: absolute;
        color: white;
        font-size: 16px;
        align-items: center;
        justify-content: center;
        left: 50%;
        top: 80%;
        transform: translate(-50%, -50%);
        z-index: 2;
        display: flex;
    }

    /* 加注按钮 - 左上弧形 */
    .bet-btn {
        background: rgba(255, 165, 0, 0.8);
        /* 橙色 */
        left: -8% !important;
        top: 30% !important;
        transform: none !important;
    }

    /* 武器切换按钮样式 */
    .weapon-switch-btn {
        background: rgba(0, 150, 255, 0.8);
        /* 蓝色 */
        left: -15% !important;
        bottom: -5% !important;
        transform: none !important;
    }

    /* 按钮按压效果 */
    .weapon-switch-btn:active {
        transform: scale(0.95);
        opacity: 0.5;
    }

    /* 自动按钮样式 */
    .auto-btn {
        background: rgba(0, 200, 0, 0.8);
        right: 30% !important;
        top: 20% !important;
    }

    /* 发炮按钮优化 */
    .fire-button:active {
        background: rgba(255, 0, 0, 0.7);
    }

    .auto-btn.auto-active {
        background: rgba(255, 0, 0, 0.8);
    }
    
    
    .video-overlay {
         position: absolute;
         top: 0;
         left: 0;
         width: 100%;
         height: 100%;
         z-index: 3;
         pointer-events: auto; 
         background-color: transparent; 
       }

</style>