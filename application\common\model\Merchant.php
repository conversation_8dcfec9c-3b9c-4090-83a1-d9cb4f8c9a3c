<?php

namespace app\common\model;
use think\Db;
use think\Model;

class Merchant extends Model
{
    /**
     * 商户列表
     * <AUTHOR>
     * @date 2025-6-25
     * @return
     */
    public function list($params)
    {
        $lat = isset($params['lat']) && $params['lat'] ? $params['lat'] : 0;//纬度
        $lng = isset($params['lng']) && $params['lng'] ? $params['lng'] : 0;//经度
        $page = isset($params['page']) ? $params['page'] : 1;//分页页数
        $params['show_num'] = isset($params['show_num']) && $params['show_num'] ? $params['show_num'] : 10;//默认显示10条数据

        //分页
        $pagefliter = [];
        if ($page) {  // 修正分页参数判断
            $pagefliter['page'] = $page;
        }

        $list = Db::name('hx_merchant')
            ->where(['status'=>1])
            ->field('id,name,number,logo,lat,lng,(6378.138 * 2 * asin(sqrt(pow(sin((lat * pi() / 180 - '.$lat.' * pi() / 180) / 2),2) + cos(lat * pi() / 180) * cos('.$lat.' * pi() / 180) * pow(sin((lng * pi() / 180 - '.$lng.' * pi() / 180) / 2),2))) * 1000) as distance,business_hours')
            ->order('distance ASC')
            ->paginate($params['show_num'], false, $pagefliter);

        return ['code'=>1,'msg'=>'成功','data'=>$list];
    }
}