define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'agent/agent/index' + location.search,
                    add_url: 'agent/agent/add',
                    edit_url: 'agent/agent/edit',
                    del_url: 'agent/agent/del',
                    table: 'agent',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('代理ID')},
                        {field: 'nickname', title: __('Nickname')},
                        {field: 'mobile', title: __('Mobile')},
                        {field: 'avatar', title: __('头像'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'commission', title: __('佣金余额'), operate: false},
                        {field: 'fee', title: __('分佣比例(%)'), operate: false},
                        {field: 'money', title: __('金币'), operate: false},
                        {field: 'score', title: __('积分'), operate: false},
                        {field: 'user_num', title: __('直属用户'), operate: false},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Hidden'),"1":__('Normal')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    dropdown: "更多",
                                    name: 'ps_edit',
                                    title: __('修改登录密码'),
                                    text:'修改登录密码',
                                    classname: 'btn btn-magic btn-dialog',
                                    icon: 'fa fa-key',
                                    url: function (row){
                                        return "agent/agent/reset_password/ids/" + row.id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'merchant',
                                    title: __('直属用户列表'),
                                    text:'直属用户列表',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["80%","70%"]\'',
                                    icon: 'fa fa-user-circle-o',
                                    url: function (row){
                                        return "user/user/index?p_id=" + row.id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('佣金记录'),
                                    text:'佣金记录',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["80%","70%"]\'',
                                    icon: 'fa fa-list',
                                    url: function (row){
                                        return "agent/commission/index?agent_id=" + row.id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('金币记录'),
                                    text:'金币记录',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["80%","70%"]\'',
                                    icon: 'fa fa-list',
                                    url: function (row){
                                        return "user/money/index?user_id=" + row.user_id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'ps_edit',
                                    title: __('修改金币'),
                                    text:'修改金币',
                                    classname: 'btn btn-magic btn-dialog',
                                    icon: 'fa fa-money',
                                    url: function (row){
                                        return "user/user/set_money/ids/" + row.user_id;
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        if (row.is_show){
                                            return true;
                                        }else{
                                            return false;
                                        }
                                    }
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        reset_password: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
}); 