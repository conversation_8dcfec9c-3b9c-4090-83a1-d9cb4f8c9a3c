
<form id="add-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label for="name" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span> {:__('商户名称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="name" name="row[name]" autocomplete="off" value="{$row.name}" data-rule="required" />
        </div>
    </div>

    <div class="form-group">
        <label for="nickname" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('联系人')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="nickname" name="row[nickname]" value="{$row.nickname}" data-rule="nickname" placeholder="请输入联系人"/>
        </div>
    </div>
    <div class="form-group">
        <label for="mobile" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('联系方式')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" class="form-control" id="mobile" name="row[mobile]" value="{$row.mobile}" data-rule="mobile" placeholder="请输入联系方式"/>
        </div>
    </div>

    <div class="form-group">
        <label for="c-logo" class="control-label col-xs-12 col-sm-2">{:__('logo照片')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-logo" data-rule="" class="form-control" size="50" name="row[logo]" type="text" value="{$row.logo}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-logo" data-url="ajax/upload" class="btn btn-danger plupload" data-input-id="c-logo" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-logo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-logo"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-logo"></ul>
        </div>
    </div>
    <div class="form-group">
        <label for="c-door_photo" class="control-label col-xs-12 col-sm-2">{:__('门头照')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-door_photo" data-rule="" class="form-control" size="50" name="row[door_photo]" type="text" value="{$row.door_photo}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-door_photo" data-url="ajax/upload" class="btn btn-danger plupload" data-input-id="c-door_photo" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-door_photo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-door_photo"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-door_photo"></ul>
        </div>
    </div>
    <div class="form-group">
        <label for="c-cash_photo" class="control-label col-xs-12 col-sm-2">{:__('收银台')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-cash_photo" data-rule="" class="form-control" size="50" name="row[cash_photo]" type="text" value="{$row.cash_photo}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-cash_photo" data-url="ajax/upload" class="btn btn-danger plupload" data-input-id="c-cash_photo" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-cash_photo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-cash_photo"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-cash_photo"></ul>
        </div>
    </div>
    <div class="form-group">
        <label for="c-hall_photo" class="control-label col-xs-12 col-sm-2">{:__('大厅场景')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-hall_photo" data-rule="" class="form-control" size="50" name="row[hall_photo]" type="text" value="{$row.hall_photo}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-hall_photo" data-url="ajax/upload" class="btn btn-danger plupload" data-input-id="c-hall_photo" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-hall_photo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-hall_photo"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-hall_photo"></ul>
        </div>
    </div>
    <div class="form-group">
        <label for="c-business_hours" class="control-label col-xs-12 col-sm-2">{:__('营业时间')}:</label>
        <div class="form-inline col-xs-9">
            <div>
                <select data-rule="required" class="form-control first" name="row[business_hours][startTime]" style="width: 15%;margin-left: 5px;">
                    {foreach name="time_arr" item="vo"}
                    <option  value="{$vo}" {in name="vo" value="$row.business_hours.startTime"}selected{/in}>{$vo}</option>
                    {/foreach}
                </select>
                至
                <select data-rule="required" class="form-control first" name="row[business_hours][endTime]" style="width: 15%;">
                    {foreach name="time_arr" item="vo"}
                    <option  value="{$vo}" {in name="vo" value="$row.business_hours.endTime"}selected{/in}>{$vo}</option>
                    {/foreach}
                </select>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['1'=>__('正常'), '0'=>__('隐藏')])}
        </div>
    </div>
    <div class="form-group hidden layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
