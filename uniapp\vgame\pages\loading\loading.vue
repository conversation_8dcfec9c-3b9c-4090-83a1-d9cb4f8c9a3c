<template>
  <view class="game-loading">
    <view class="loading-container">
      <view class="loading-text">游戏加载中...</view>
      <progress 
        :percent="progress" 
        stroke-width="6" 
        activeColor="#4cd964" 
        backgroundColor="#ebebeb"
      />
      <view class="progress-text">{{progress}}%</view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request'
export default {
	data() {
		return {
			progress: 0,
			gameId: null,
			videoUrl: '',
			gameInfo: null,
			loadingTimer: null,
			simulationDuration: 2000, // 固定2秒模拟时间
			startTime: 0,
			apiCompleted: false
		}
	},
	onLoad(options) {
		this.gameId = options.gameId
		this.startLoading()
	},
	onUnload() {
		// 清除定时器
		if (this.loadingTimer) {
			clearInterval(this.loadingTimer)
		}
	},
	methods: {
		startLoading() {
			this.startTime = Date.now()
			this.apiCompleted = false
			this.runProgressSimulation()
			this.fetchGameVideo()
		},
		runProgressSimulation() {
		    const updateProgress = () => {
		        const elapsed = Date.now() - this.startTime
		        const progressRatio = Math.min(elapsed / this.simulationDuration, 1)
		        
		        // 使用缓动函数使进度变化更自然
		        this.progress = Math.floor(100 * this.easeOutQuad(progressRatio))
		        
		        if (progressRatio < 1 || !this.apiCompleted) {
					this.loadingTimer = setTimeout(updateProgress, 16)
		        } else {
					this.completeLoading()
		        }
		    }
		    updateProgress()
		},
		// 缓动函数 - easeOutQuad
		easeOutQuad(t) {
			return t * (2 - t)
		},
		completeLoading() {
		    this.progress = 100
		    setTimeout(() => {
		        if (this.videoUrl) {
					uni.redirectTo({
						url: `/pages/game/game?gameId=${this.gameId}&videoUrl=${encodeURIComponent(this.videoUrl)}&is_landscape=${this.gameInfo?.is_landscape || 1}`
					})
		        } else {
					this.handleLoadError('获取游戏视频失败')
		        }
		    }, 300) // 保持100%状态300ms
		},
	
		async fetchGameVideo() {
			try {
				const res = await request({
					url: '/api/game/detail',
					method: 'POST',
					data: {gameId: this.gameId}
				})
				  
				if (res.data?.video_url) {
				    this.videoUrl = res.data.video_url
				}
				this.gameInfo = res.data
				        
				this.apiCompleted = true
				
				// 如果已经超过2秒，立即完成
				if (Date.now() - this.startTime >= this.simulationDuration) {
					this.completeLoading()
				}
			} catch (error) {
				console.error('获取游戏视频出错:', error)
				this.apiCompleted = true
				this.handleLoadError('网络请求失败')
			}
		},
		handleLoadError(message) {
		    this.progress = 100
		    uni.showToast({
		        title: message,
		        icon: 'none'
		    })
		    setTimeout(() => {
		        uni.navigateBack()
		    }, 1500)
		}
	}
}
</script>

<style scoped>
.game-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  width: 80%;
  text-align: center;
}

.loading-text {
  font-size: 18px;
  color: #333;
  margin-bottom: 20px;
}

.progress-text {
  font-size: 14px;
  color: #666;
  margin-top: 10px;
}
</style>