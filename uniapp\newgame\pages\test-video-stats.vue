<template>
  <view class="container">
    <view class="header">
      <text class="title">视频统计信息测试</text>
    </view>
    
    <view class="player-container">
      <HeiqiPlayer
        ref="player"
        :defaultSignalingUrl="signalingUrl"
        :defaultSenderId="senderId"
        :showConnectionControls="true"
        :showStats="showStats"
        :autoConnect="false"
        @connected="onConnected"
        @connectionFailed="onConnectionFailed"
        @disconnected="onDisconnected"
        @statusChanged="onStatusChanged"
        @trackStateChanged="onTrackStateChanged"
        @log="onLog"
      />
    </view>
    
    <view class="controls">
      <view class="input-group">
        <text class="label">信令服务器:</text>
        <input 
          v-model="signalingUrl" 
          placeholder="wss://sling.91jdcd.com/ws/"
          class="input"
        />
      </view>
      
      <view class="input-group">
        <text class="label">发送端ID:</text>
        <input 
          v-model="senderId" 
          placeholder="sender-xxx"
          class="input"
        />
      </view>
      
      <view class="switch-group">
        <text class="label">显示统计信息:</text>
        <switch :checked="showStats" @change="onStatsToggle" />
      </view>
      
      <view class="button-group">
        <button @click="connect" class="btn primary">连接</button>
        <button @click="disconnect" class="btn secondary">断开</button>
        <button @click="toggleStats" class="btn info">切换统计</button>
      </view>
    </view>
    
    <view class="status">
      <text class="status-text" :class="{ error: hasError }">{{ statusMessage }}</text>
    </view>
    
    <view class="stats-display" v-if="videoStats">
      <text class="stats-title">当前视频统计:</text>
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-label">帧率:</text>
          <text class="stats-value">{{ videoStats.fps }} fps</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">比特率:</text>
          <text class="stats-value">{{ videoStats.bitrate }} kbps</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">分辨率:</text>
          <text class="stats-value">{{ videoStats.resolution }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">丢包数:</text>
          <text class="stats-value">{{ videoStats.packetsLost }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">接收包数:</text>
          <text class="stats-value">{{ videoStats.packetsReceived }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">接收字节:</text>
          <text class="stats-value">{{ formatBytes(videoStats.bytesReceived) }}</text>
        </view>
      </view>
    </view>
    
    <view class="logs">
      <text class="logs-title">日志:</text>
      <scroll-view class="logs-content" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <text class="log-time">{{ log.timestamp }}</text>
          <text class="log-message">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import HeiqiPlayer from '@/components/HeiqiPlayer.vue'

export default {
  name: 'TestVideoStats',
  components: {
    HeiqiPlayer
  },
  
  data() {
    return {
      signalingUrl: 'wss://sling.91jdcd.com/ws/',
      senderId: '',
      showStats: true,
      statusMessage: '未连接',
      hasError: false,
      videoStats: null,
      logs: []
    }
  },
  
  methods: {
    connect() {
      if (this.$refs.player) {
        this.$refs.player.connect()
      }
    },
    
    disconnect() {
      if (this.$refs.player) {
        this.$refs.player.disconnect()
      }
    },
    
    toggleStats() {
      if (this.$refs.player) {
        this.$refs.player.toggleVideoStats()
      }
    },
    
    onStatsToggle(event) {
      this.showStats = event.detail.value
    },
    
    onConnected() {
      this.addLog('WebRTC连接成功', 'info')
    },
    
    onConnectionFailed(error) {
      this.addLog(`连接失败: ${error}`, 'error')
    },
    
    onDisconnected() {
      this.addLog('连接已断开', 'info')
    },
    
    onStatusChanged({ message, isError }) {
      this.statusMessage = message
      this.hasError = isError
    },
    
    onTrackStateChanged(isMuted) {
      this.addLog(`视频流状态: ${isMuted ? '中断' : '正常'}`, isMuted ? 'error' : 'info')
    },
    
    onLog({ message, type, timestamp }) {
      this.addLog(message, type, timestamp)
    },
    
    addLog(message, type = 'info', timestamp = null) {
      this.logs.unshift({
        message,
        type,
        timestamp: timestamp || new Date().toLocaleTimeString()
      })
      
      // 限制日志数量
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
    },
    
    formatBytes(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.player-container {
  width: 100%;
  height: 300px;
  margin-bottom: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.controls {
  margin-bottom: 20px;
}

.input-group, .switch-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.label {
  width: 120px;
  font-size: 14px;
  color: #666;
}

.input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.btn.primary {
  background-color: #007AFF;
  color: white;
}

.btn.secondary {
  background-color: #8E8E93;
  color: white;
}

.btn.info {
  background-color: #17a2b8;
  color: white;
}

.status {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.status-text {
  font-size: 14px;
  color: #333;
}

.status-text.error {
  color: #FF3B30;
}

.stats-display {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border: 1px solid #007AFF;
}

.stats-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #007AFF;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.stats-label {
  font-size: 12px;
  color: #666;
}

.stats-value {
  font-size: 12px;
  font-weight: bold;
  color: #333;
  font-family: 'Courier New', monospace;
}

.logs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.logs-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.logs-content {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}

.log-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  width: 80px;
  color: #999;
  margin-right: 10px;
}

.log-message {
  flex: 1;
  color: #333;
}

.log-item.error .log-message {
  color: #FF3B30;
}

.log-item.warn .log-message {
  color: #FF9500;
}
</style>
