// 环境配置
const ENV = {
	development: {
		baseUrl: 'https://testva2.91jdcd.com',
		mqttUrl: 'ws://101.132.118.85:8083/mqtt',
		mqttUsername: 'game',
		mqttPassword: 'sdoifj239874fh97g34fdg34'
	},
	production: {
		baseUrl: 'https://testva2.91jdcd.com',
		mqttUrl: 'ws://101.132.118.85:8083/mqtt',
		mqttUsername: 'game',
		mqttPassword: 'sdoifj239874fh97g34fdg34'
	}
}

// 当前环境
const currentEnv = process.env.NODE_ENV || 'development'

// 导出配置
export default {
	baseUrl: ENV[currentEnv].baseUrl,
	tokenKey: 'auth_token', // Token存储键名
	tokenExpireKey: 'token_expire', // Token过期时间键名
	userInfo: "user_info", // 用户信息键名
	WebSocket:"wss://testva2.91jdcd.com/websocket/",
	mqttUrl: ENV[currentEnv].mqttUrl, // MQTT地址
	mqttUsername: ENV[currentEnv].mqttUsername, // MQTT账号
	mqttPassword: ENV[currentEnv].mqttPassword // MQTT密码
} 