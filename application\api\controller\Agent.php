<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\UserRechargeLog;
use think\Db;
use think\exception\DbException;

/**
 * 代理
 * @Authod Jw
 * @Time 2021/6/15
 * @package app\api\controller
 */
class Agent extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];
    protected $userInfo = '';
    protected $agent_id = '';

    public function _initialize()
    {
        parent::_initialize();

        // 新增代理身份验证
        $userInfo = $this->auth->getUserinfo();
        if (!isset($userInfo['agent_id']) || $userInfo['agent_id'] <= 0) {
            $this->error('无代理权限');
        }
        $this->agent_id = $userInfo['agent_id'];
    }

    /**
     * 用户列表
     * @Authod Jw
     * @Time 2021/6/21
     * @param array $params
     * @param int $page
     * @return void
     * @throws DbException
     */
    public function user_list()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();

            $where = [];
            // 修改后的关键词搜索条件
            if (isset($params['keyword']) && $params['keyword']) {
                $keyword = $params['keyword'];
                $where[] = function($query) use ($keyword) {
                    $query->where('nickname', 'like', "%{$keyword}%")
                        ->whereOr('mobile', 'like', "%{$keyword}%");
                };
            }

            $page = $params['page'] ?? 1;//分页页数
            $params['show_num'] = $params['show_num'] ?? 10;//默认显示10条数据

            //分页
            $pagefliter = [];
            if ($page) {  // 修正分页参数判断
                $pagefliter['page'] = $page;
            }

            $info = Db::name('user')
                ->where('p_id', $this->agent_id)
                ->where($where)  // 修改后的条件
                ->order('createtime desc')
                ->field('id,nickname,mobile,createtime,avatar,level')
                ->paginate($params['show_num'], false, $pagefliter);
            $result = $info->toArray()['data'];

            // 总数
            $data['total'] = $info->toArray()['total'];
            // 今日新增
            $data['yesterday'] = Db::name('user')
                ->where('p_id', $this->agent_id)
                ->where($where)  // 添加搜索条件
                ->whereTime('createtime', 'today')
                ->count();

            foreach ($result as &$v) {
                $v['avatar'] =\config('site.domain').$v['avatar'];
            }
            $data['list'] = $result;

            $this->success('成功获取',$data);
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 用户游戏记录
     * <AUTHOR>
     * @date 2025-6-10
     * @return
     */
    public function user_game_log()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();

            if (!isset($params['user_id']) || empty($params['user_id'])) {
                $this->error('用户ID不能为空');
            }

            // 验证用户是否属于当前代理
            $isSubUser = Db::name('user')
                ->where('id', $params['user_id'])
                ->where('p_id', $this->agent_id)
                ->count();
            if (!$isSubUser) {
                $this->error('该用户不属于您的下级');
            }

            $game_model = new \app\common\model\Game();
            $result = $game_model->userGameLog($params);
            if ($result['code'] == 1) {
                $this->success('成功获取',$result['data']);
            }
            $this->error($result['msg']);
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 用户充值列表
     * <AUTHOR>
     * @date 2025-6-11
     * @return
     */
    public function user_recharge_log(){
        if ($this->request->isPost()) {
            $params = $this->request->param();
            if (!isset($params['user_id']) || empty($params['user_id'])) {
                $this->error('用户ID不能为空');
            }
            $isSubUser = Db::name('user')
                ->where('id', $params['user_id'])
                ->where('p_id', $this->agent_id)
                ->count();
            if (!$isSubUser) {
                $this->error('该用户不属于您的下级');
            }
            $game_model = new UserRechargeLog();
            $result = $game_model->list($params);
            if ($result['code'] == 1) {
                $this->success('成功获取',$result['data']);
            }
            $this->error($result['msg']);
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 佣金记录
     * <AUTHOR>
     * @date 2025-6-11
     * @return
     */
    public function commission_log(){
        if ($this->request->isPost()) {
            $params = $this->request->param();
            $model = new \app\common\model\Agent();

            $result = $model->getCommissionLog($this->agent_id, $params);

            if ($result['code'] == 1) {
                $this->success('成功获取',$result['data']);
            }
            $this->error($result['msg']);
        }
        $this->error('请求方式不正确');
    }

    /**
     * 申请提现
     * <AUTHOR>
     * @date 2025-6-11
     * @return
     */
    public function applyWithdraw()
    {
        if ($this->request->isPost()) {
            $params = $this->request->param();
            $model = new \app\common\model\Agent();

            $result = $model->applyWithdraw($this->agent_id, $params);

            if ($result['code'] == 1) {
                $this->success('成功获取',$result['data']);
            }
            $this->error($result['msg']);
        }
        $this->error('请求方式不正确');
    }

    /**
     * 提现记录
     * <AUTHOR>
     * @date 2025-6-11
     * @return
     */
    public function withdraw_log()
    {
        if ($this->request->isPost()) {
            $params = $this->request->param();
            $model = new \app\common\model\Agent();

            $result = $model->getWithdrawLog($this->agent_id, $params);

            if ($result['code'] == 1) {
                $this->success('成功获取', $result['data']);
            }
            $this->error($result['msg']);
        }
        $this->error('请求方式不正确');
    }
}
