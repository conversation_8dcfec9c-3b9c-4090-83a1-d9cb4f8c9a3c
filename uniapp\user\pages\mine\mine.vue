<template>
  <view class="container">
    <!-- 顶部用户信息区 -->
    <view class="top-bar">
      <view class="user-info-wrap">
        <view class="avatar-wrap">
          <view class="avatar-level-wrap large">
            <image class="avatar small" :src="userInfo.avatar || '/static/default_avatar.png'" @error="onAvatarError" @load="onAvatarLoad" />
            <image class="level-avatar-bg large" src="/static/level_avatar.png" />
          </view>
        </view>
        <view class="user-detail">
          <view class="nickname-row">
            <view class="nickname">{{ userInfo.nickname || '未登录' }}</view>
          </view>
          <view class="level-assets-row" v-if="userInfo.nickname">
            <view class="id-bg-wrap">
              <text class="id-text">ID: {{ userInfo.id }}</text>
            </view>
            <view class="asset-item">
              <image class="coin-icon" src="/static/score.png" />
              <text class="asset-num">{{ userInfo.score }}</text>
            </view>
          </view>
        </view>
        <view class="setting-btn" @tap.stop="goSetting">
          <image src="/static/mine/setting.png" />
        </view>
      </view>
    </view>

    <!-- 订单筛选 -->
    <view class="order-filter card-container">
      <view class="filter-row">
        <view class="date-picker-wrap">
          <text class="date-label">开始时间</text>
          <picker mode="date" :value="startDate" @change="onStartDateChange">
            <view class="date-input">{{ startDate || getTodayDate() }}</view>
          </picker>
        </view>
        <view class="date-picker-wrap">
          <text class="date-label">结束时间</text>
          <picker mode="date" :value="endDate" @change="onEndDateChange">
            <view class="date-input">{{ endDate || getTodayDate() }}</view>
          </picker>
        </view>
      </view>
      <button class="search-btn" @click="onSearch">查询</button>
    </view>

    <!-- 订单列表 -->
    <view class="order-list">
      <view class="order-item list-item" v-for="(order, idx) in orders" :key="order.id">
        <view class="order-row"><text class="order-label">核销店家：</text><text class="order-value">{{ order.merchant_name }}</text></view>
        <view class="order-row"><text class="order-label">核销时间：</text><text class="order-value">{{ formatTime(order.createtime) }}</text></view>
        <view class="order-row"><text class="order-label">核销积分：</text><text class="order-value">{{ order.score }}</text></view>
        <view class="order-row"><text class="order-label">状态：</text><text class="order-value" :class="order.status === 1 ? 'success' : 'failed'">{{ order.status === 1 ? '成功' : '失败' }}</text></view>
        <view class="order-row" v-if="order.memo"><text class="order-label">备注：</text><text class="order-value">{{ order.memo }}</text></view>
      </view>
      <view v-if="orders.length === 0 && !loading" class="load-more">暂无数据</view>
      <view v-if="orders.length < total" class="load-more" @click="loadMore">加载更多</view>
      <view v-else-if="total > 0 && orders.length >= total" class="load-more">没有更多了</view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'

function dateToTimestamp(dateStr, isEnd) {
  if (!dateStr) return ''
  const time = isEnd ? '23:59:59' : '00:00:00'
  return Math.floor(new Date(dateStr + ' ' + time).getTime() / 1000)
}

function formatTime(val) {
  if (!val) return '--'
  const d = new Date(val * 1000)
  const y = d.getFullYear()
  const m = (d.getMonth() + 1).toString().padStart(2, '0')
  const day = d.getDate().toString().padStart(2, '0')
  const h = d.getHours().toString().padStart(2, '0')
  const min = d.getMinutes().toString().padStart(2, '0')
  const s = d.getSeconds().toString().padStart(2, '0')
  return `${y}.${m}.${day} ${h}:${min}:${s}`
}

export default {
  data() {
    const today = new Date()
    const year = today.getFullYear()
    const month = (today.getMonth() + 1).toString().padStart(2, '0')
    const day = today.getDate().toString().padStart(2, '0')
    const todayStr = `${year}-${month}-${day}`

    return {
      startDate: todayStr,
      endDate: todayStr,
      merchantName: '',
      userInfo: {
        nickname: '--',
        id: '--',
        score: '--',
        avatar: ''
      },
      orders: [],
      page: 1,
      show_num: 10,
      total: 0,
      loading: false
    }
  },
  onLoad() {
    this.getUserDetail()
    this.getOrderList()
  },
  onShow() {
    this.getUserDetail()
    this.getOrderList()
  },
  methods: {
    async getOrderList(loadMore = false) {
      if (this.loading) return
      this.loading = true
      try {
        const res = await request({
          url: '/api/user/hx_log',
          method: 'POST',
          data: {
            page: this.page,
            show_num: this.show_num,
            start_time: dateToTimestamp(this.startDate, false),
            end_time: dateToTimestamp(this.endDate, true),
            merchant_name: this.merchantName
          }
        })
        if (res.code === 1 && res.data && res.data.data) {
          const list = res.data.data
          if (loadMore) {
            this.orders = this.orders.concat(list)
          } else {
            this.orders = list
          }
          this.total = res.data.total
        }
      } finally {
        this.loading = false
      }
    },
    loadMore() {
      if (this.orders.length < this.total) {
        this.page++
        this.getOrderList(true)
      }
    },
    async getUserDetail() {
      try {
        const res = await request({ url: '/api/user/detail', method: 'POST' })
        if (res.code === 1 && res.data) {
          this.userInfo.nickname = res.data.nickname || '--'
          this.userInfo.id = res.data.id || '--'
          this.userInfo.score = typeof res.data.score !== 'undefined' ? res.data.score : '--'
          this.userInfo.avatar = res.data.avatar || ''
        }
      } catch (e) {}
    },
    onStartDateChange(e) {
      this.startDate = e.detail.value
    },
    onEndDateChange(e) {
      this.endDate = e.detail.value
    },
    onSearch() {
      this.page = 1
      this.getOrderList()
    },
    formatTime,
    goSetting() {
      uni.navigateTo({ url: '/pages/setting/setting' })
    },
    onAvatarError() {
      this.userInfo.avatar = ''
    },
    onAvatarLoad() {
      // 头像加载成功
    },
    getTodayDate() {
      const today = new Date()
      const year = today.getFullYear()
      const month = (today.getMonth() + 1).toString().padStart(2, '0')
      const day = today.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style scoped>
	/* 页面整体容器 */
	.container {
		background: #180F29;
		min-height: 100vh;
		padding-bottom: 100rpx;
	}

	/* 昵称行 */
	.nickname-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
	}

	/* ID背景包装器 */
	.id-bg-wrap {
		position: relative;
		width: 180rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
		margin-right: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: url('/static/mine/id.png') no-repeat center/180rpx 70rpx;
	}

	.id-text {
		position: relative;
		z-index: 2;
		color: #fff;
		font-size: 26rpx;
		font-weight: bold;
		text-align: center;
		width: 100%;
		margin-top: 5rpx;
	}

	.setting-btn {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		display: flex;
		align-items: center;
		justify-content: center;
		width: 60rpx;
		height: 60rpx;
		background: rgba(0, 0, 0, 0.3);
		border-radius: 50%;
	}

	.setting-btn image {
		width: 30rpx;
		height: 30rpx;
		object-fit: contain;
	}
	/* 订单筛选区域 */
	.order-filter {
		margin: 30rpx 20rpx;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.filter-row {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		width: 100%;
		margin-bottom: 30rpx;
	}

	.date-picker-wrap {
		width: 45%;
		display: flex;
		flex-direction: column;
	}

	.date-label {
		font-size: 24rpx;
		color: #fff;
		margin-bottom: 10rpx;
	}

	.date-input {
		width: 100%;
		height: 60rpx;
		border: 1rpx solid #4C3A62;
		border-radius: 8rpx;
		padding: 0 18rpx;
		font-size: 26rpx;
		background: #3F2F5B;
		color: #fff;
		display: flex;
		align-items: center;
		line-height: 60rpx;
		box-sizing: border-box;
	}

	.search-btn {
		width: 100%;
		font-size: 32rpx;
		padding: 0 28rpx;
		height: 80rpx;
		background: #8F75EB;
		color: #fff;
		border-radius: 12rpx;
		border: none;
	}
	/* 订单列表区域 */
	.order-list {
		margin: 0 10rpx;
	}

	.order-item {
		background: #2A1840;
		border-radius: 12rpx;
		margin: 16rpx 20rpx;
		padding: 30rpx 20rpx;
		border: 1rpx solid #4C3A62;
		font-size: 24rpx;
	}

	.order-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.order-label {
		color: #999;
		font-size: 26rpx;
	}

	.order-value {
		color: #fff;
		font-size: 26rpx;
	}

	.order-value.success {
		color: #48A578;
	}

	.order-value.failed {
		color: #ff4757;
	}

	.order-row:last-child {
		margin-bottom: 0;
	}

	.load-more {
		text-align: center;
		color: #999;
		font-size: 26rpx;
		padding: 24rpx 0;
		cursor: pointer;
	}
</style> 