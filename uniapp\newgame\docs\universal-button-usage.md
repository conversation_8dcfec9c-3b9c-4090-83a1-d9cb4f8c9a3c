# UniversalButton 通用按钮组件使用指南

## 功能特性

✅ **跨平台兼容** - 同时支持电脑鼠标和手机触摸
✅ **长按支持** - 可配置长按延迟和重复间隔
✅ **事件丰富** - 提供完整的按下/释放/长按事件
✅ **防重复点击** - 自动处理长按时的点击事件冲突
✅ **禁用状态** - 支持禁用状态和样式

## 基本用法

### 1. 导入组件
```javascript
import UniversalButton from '@/components/UniversalButton.vue'

export default {
  components: {
    UniversalButton
  }
}
```

### 2. 简单点击按钮
```html
<UniversalButton 
  customClass="game-button"
  @click="handleClick"
>
  <text>点击按钮</text>
</UniversalButton>
```

### 3. 长按按钮（如发炮按钮）
```html
<UniversalButton 
  customClass="fire-button"
  :longPress="true"
  :longPressDelay="150"
  :longPressInterval="100"
  @click="handleSingleFire"
  @longPressStart="handleFireStart"
  @longPressRepeat="handleFireRepeat"
  @longPressEnd="handleFireEnd"
>
  <text>发炮</text>
</UniversalButton>
```

### 4. 方向键按钮
```html
<UniversalButton 
  customClass="dpad-btn left"
  :longPress="true"
  @pressStart="handleLeftStart"
  @pressEnd="handleLeftEnd"
>
</UniversalButton>
```

## 事件说明

| 事件名 | 触发时机 | 参数 | 说明 |
|--------|----------|------|------|
| `click` | 单击时 | event | 普通点击事件 |
| `pressStart` | 按下时 | event | 按下开始（鼠标/触摸） |
| `pressEnd` | 释放时 | event | 按下结束 |
| `longPressStart` | 长按开始 | event | 达到长按延迟时间 |
| `longPressRepeat` | 长按重复 | event | 长按期间的重复触发 |
| `longPressEnd` | 长按结束 | event | 长按释放 |

## 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `customClass` | String | '' | 自定义CSS类名 |
| `disabled` | Boolean | false | 是否禁用 |
| `longPress` | Boolean | false | 是否启用长按 |
| `longPressDelay` | Number | 500 | 长按延迟时间(ms) |
| `longPressInterval` | Number | 150 | 长按重复间隔(ms) |

## 游戏中的实际应用

### 发炮按钮
```html
<UniversalButton 
  customClass="fire-button"
  :longPress="true"
  :longPressDelay="150"
  :longPressInterval="150"
  @click="singleFire"
  @longPressStart="startContinuousFire"
  @longPressRepeat="continuousFire"
  @longPressEnd="stopContinuousFire"
>
  <text>发炮</text>
</UniversalButton>
```

```javascript
methods: {
  singleFire() {
    console.log('单次发炮')
    this.handleDirection(15)
  },
  
  startContinuousFire() {
    console.log('开始连续发炮')
    this.isLongPress = true
    this.handleDirection(15)
  },
  
  continuousFire() {
    console.log('连续发炮中...')
    this.handleDirection(15)
  },
  
  stopContinuousFire() {
    console.log('停止连续发炮')
    this.handleDirection(17)
    this.isLongPress = false
  }
}
```

### 方向键
```html
<UniversalButton 
  customClass="dpad-btn left"
  :longPress="true"
  :longPressDelay="150"
  :longPressInterval="100"
  @pressStart="handleLeftStart"
  @pressEnd="handleLeftEnd"
>
</UniversalButton>
```

```javascript
methods: {
  handleLeftStart() {
    console.log('左方向键按下')
    this.handleDirection(12) // 左转
    this.startLeftRepeat()
  },
  
  handleLeftEnd() {
    console.log('左方向键释放')
    this.stopLeftRepeat()
  },
  
  startLeftRepeat() {
    this.leftInterval = setInterval(() => {
      this.handleDirection(12)
    }, 100)
  },
  
  stopLeftRepeat() {
    if (this.leftInterval) {
      clearInterval(this.leftInterval)
      this.leftInterval = null
    }
  }
}
```

### 功能按钮
```html
<UniversalButton 
  customClass="action-btn auto-btn"
  :class="{'auto-active': isAuto}"
  @click="toggleAuto"
>
  <text>{{ isAuto ? '停止自动' : '自动' }}</text>
</UniversalButton>
```

## CSS样式示例

```css
.game-button {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
}

.fire-button {
  background: linear-gradient(45deg, #ff4444, #cc0000);
  color: white;
  padding: 15px 30px;
  border-radius: 50%;
  font-size: 18px;
  font-weight: bold;
}

.dpad-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.5);
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.auto-active {
  background: linear-gradient(45deg, #ff9800, #f57c00) !important;
}
```

## 注意事项

1. **事件冲突处理** - 组件自动处理长按和点击的冲突
2. **性能优化** - 自动清理定时器，避免内存泄漏
3. **跨平台兼容** - 同时监听触摸和鼠标事件
4. **防抖处理** - 避免快速重复触发

## 迁移现有按钮

将现有的button替换为UniversalButton：

```html
<!-- 原来的按钮 -->
<button @touchstart="handleAction" @mousedown="handleAction">
  <text>按钮</text>
</button>

<!-- 替换为 -->
<UniversalButton @click="handleAction">
  <text>按钮</text>
</UniversalButton>
```

这样就能同时支持电脑和手机的操作了！
