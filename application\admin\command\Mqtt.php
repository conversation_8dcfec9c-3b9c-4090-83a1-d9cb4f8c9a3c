<?php

namespace app\admin\command;

use app\common\service\MqttService;
use think\console\Command;
use think\console\Input;
use think\console\Output;

class Mqtt extends Command
{
    protected function configure()
    {
        $this->setName('mqtt')->setDescription('启动MQTT客户端监听');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('正在启动MQTT监听...');
        $mqttService = new MqttService();

        // 移除外部循环，keepAlive()内部已有重连机制
        if ($mqttService->connect()) {
            $output->writeln('MQTT连接成功，开始监听...');
            try {
                $mqttService->keepAlive();
            } catch (\Exception $e) {
                $output->writeln("监听异常: " . $e->getMessage());
            }
        }
    }
}