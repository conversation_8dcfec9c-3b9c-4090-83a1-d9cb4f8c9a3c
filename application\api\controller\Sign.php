<?php
namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

class Sign extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    // 签到接口
    public function doSign()
    {
        $user_id = $this->auth->getUserId();
        $user_model = new \app\common\model\User();
        $result = $user_model->doSign($user_id);
        if ($result['code'] == 1) {
            $this->success($result['msg']);
        }
        $this->error($result['msg']);
    }

    public function info()
    {
        // 假设已通过中间件获取到用户ID
        $user_id = $this->auth->getUserId();

        $today = date('Y-m-d');

        // 1. 查询今天是否已签到
        $todaySign = Db::name('user_sign')
            ->where(['user_id' => $user_id, 'sign_date' => $today])
            ->find();

        $today_signed = $todaySign ? true : false;

        // 2. 查询最近一次未断签的连续天数
        // 先查出最近一条未断签的记录
        $lastSign = Db::name('user_sign')
            ->where(['user_id' => $user_id])
            ->order('sign_date desc')
            ->find();

        $signed_days = 0;
        if ($lastSign) {
            // 如果今天已签，直接取 continuous_days
            if ($today_signed) {
                $signed_days = $lastSign['continuous_days'];
            } else {
                // 如果今天没签，判断昨天是否签了
                $yesterday = date('Y-m-d', strtotime('-1 day'));
                $yesterdaySign = Db::name('user_sign')
                    ->where(['user_id' => $user_id, 'sign_date' => $yesterday])
                    ->find();
                if ($yesterdaySign) {
                    $signed_days = $yesterdaySign['continuous_days'];
                } else {
                    $signed_days = 0;
                }
            }
        }

        // 3. 查询签到规则
        $rules = Db::name('sign_rule')
            ->order('day asc')
            ->select();

        // 组装前端需要的规则格式
        $ruleArr = [];
        foreach ($rules as $rule) {
            $ruleArr[] = [
                'day' => intval($rule['day']),
                'reward' => intval($rule['reward'])
            ];
        }
        $data = [
            'signed_days' => intval($signed_days),
            'today_signed' => $today_signed,
            'rules' => $ruleArr
        ];
        $this->success('成功',$data);
    }

    // 获取签到状态
    public function getSignStatus()
    {
        $user_id = $this->auth->getUserId();
        $lastSign = Db::name('user_sign')
            ->where('user_id', $user_id)
            ->order('sign_date DESC')
            ->find();

        $currentDate = date('Y-m-d');
        $status = [
            'signed_today' => false,
            'continuous_days' => 0,
            'next_reward' => 10
        ];

        if ($lastSign) {
            $diffDays = (strtotime($currentDate) - strtotime($lastSign['sign_date'])) / 86400;
            $status['continuous_days'] = ($diffDays == 0) ? $lastSign['continuous_days'] : 0;
            $status['signed_today'] = $diffDays == 0;

            // 计算下次签到奖励
            $nextDay = $status['continuous_days'] >= 7 ? 1 : $status['continuous_days'] + 1;
            $nextRule = Db::name('sign_rule')->where('day', $nextDay)->find();
            $status['next_reward'] = $nextRule['reward'] ?? 10;
        }

        $this->success('', $status);
    }
}