<template>
	<view class="page-container">
		<view class="content-container">
			<view class="container">
				<!-- 用户信息区域 -->
				<view class="user-section" @click="handleUserClick">
					<view class="user-info">
						<image class="avatar" :src="userInfo.avatar || '/static/default_avatar.png'"
							mode="aspectFill" />
						<text class="nickname">{{ userInfo.nickname || '未登录' }}</text>
					</view>

					<view class="arrow-right">
						<uni-icons type="arrowright" size="20" color="#D8BFD8"></uni-icons>
					</view>

					<view class="setting-btn" @click.stop="navToSetting">
						<uni-icons type="gear" size="24" color="#333"></uni-icons>
					</view>
				</view>

				<!-- VIP进度条 -->
				<view class="vip-progress">
					<text class="vip-text">VIP{{vipInfo.level}}</text>
					<view class="progress-bar">
						<view class="progress-inner"
							:style="{width: (vipInfo.currentExp/vipInfo.requiredExp)*100 + '%'}"></view>
					</view>
					<text class="exp-text">{{vipInfo.currentExp}}/{{vipInfo.requiredExp}}</text>
				</view>

				<!-- 功能菜单 -->
				<view class="menu-section">
					<!-- 街机币 -->
					<view class="menu-item" @click="handleCoin">
						<view class="item-left">
							<uni-icons type="shop" size="24" color="#666"></uni-icons>
							<text class="item-text">街机币</text>
						</view>
						<view class="item-right">
							<text class="value">{{ userInfo.money || 0 }}</text>
							<uni-icons type="arrowright" size="20" color="#666"></uni-icons>
						</view>
					</view>

					<!-- 积分 -->
					<view class="menu-item" >
						<view class="item-left">
							<uni-icons type="star" size="24" color="#666"></uni-icons>
							<text class="item-text">积分</text>
						</view>
						<view class="item-right">
							<text class="value">{{ userInfo.score || 0 }}</text>
							<uni-icons type="arrowright" size="20" color="#666"></uni-icons>
						</view>
					</view>

					<!-- 账户记录 -->
					<view class="menu-item" @click="navToRecords">
						<view class="item-left">
							<uni-icons type="calendar" size="24" color="#666"></uni-icons>
							<text class="item-text">账户记录</text>
						</view>
						<uni-icons type="arrowright" size="20" color="#666"></uni-icons>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import config from "@/config.js";
	import auth from '@/utils/auth'
	import request from '@/utils/request'

	export default {
		data() {
			return {
				userInfo: {},
				vipInfo: {
					currentExp: 0,
					requiredExp: 30,
					level: 1
				}
			}
		},

		onShow() {
			this.checkLoginStatus()
		},

		methods: {
			checkLoginStatus() {
				if (auth.checkLogin()) {
					this.fetchUserDetail()
				}
			},

			async fetchUserDetail() {
				try {
					const res = await request({
						url: '/api/user/detail',
					})

					this.userInfo = res.data

					// this.vipInfo = {
					//   level: data.vipLevel,
					//   currentExp: data.currentExp,
					//   requiredExp: data.requiredExp
					// }
				} catch (error) {
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					})
				}
			},

			handleUserClick() {
				if (!auth.checkLogin()) {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}
			},

			navToSetting() {
				uni.navigateTo({
					url: '/pages/setting/setting'
				})
			},

			handleCoin() {
				uni.switchTab({
					url: '/pages/recharge/recharge'
				})
			},

			handlePoints() {
				uni.navigateTo({
					url: '/pages/wallet/points'
				})
			},

			navToRecords() {
				uni.navigateTo({
					url: '/pages/my/records'
				})
			}
		}
	}
</script>

<style scoped>
	.page-container {
		height: 100%;
		background: linear-gradient(180deg, #f5f7fa 0%, #c3cfe2 100%);
		overflow: hidden; /* 禁止容器滚动 */
	}

	.content-container {
		padding: 20rpx 30rpx;
		box-sizing: border-box;
	}

	.user-section {
		position: relative;
		display: flex;
		align-items: center;
		padding: 30rpx;
		background: #fff;
		/* min-width: 750rpx; */
		width: 100%;
		min-height: 200rpx;
		overflow: visible !important;
		box-sizing: border-box;
	}

	.user-info {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}

	.nickname {
		font-size: 32rpx;
		color: #333;
	}

	.arrow-right {
		position: absolute;
		right: 5%;
		top: 50%;
		transform: translateY(-50%);
	}

	.setting-btn {
		position: absolute;
		right: 5%;
		top: 15%;
		transform: translateY(-50%);
		z-index: 999;
	}

	/* VIP进度条样式 */
	.vip-progress {
		padding: 20rpx 30rpx;
		background: #fff;
		display: flex;
		align-items: center;
		margin-top: 20rpx;
		border-radius: 16rpx;
	}

	.progress-bar {
		flex: 1;
		height: 20rpx;
		background: #eee;
		border-radius: 10rpx;
		margin: 0 20rpx;
		position: relative;
	}

	.progress-inner {
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		background: #FFD700;
		border-radius: 10rpx;
		transition: width 0.3s;
	}

	.vip-text {
		font-size: 28rpx;
		color: #FFD700;
		font-weight: bold;
	}

	.exp-text {
		font-size: 24rpx;
		color: #999;
	}

	/* 菜单样式 */
	.menu-section {
		margin-top: 30rpx;
		background: #fff;
		border-radius: 16rpx;
		padding: 0 20rpx;
	}

	.menu-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #eee;
	}

	.item-left {
		display: flex;
		align-items: center;
	}

	.item-text {
		font-size: 28rpx;
		color: #333;
		margin-left: 20rpx;
	}

	.item-right {
		display: flex;
		align-items: center;
	}

	.value {
		font-size: 28rpx;
		color: #666;
		margin-right: 20rpx;
	}
</style>