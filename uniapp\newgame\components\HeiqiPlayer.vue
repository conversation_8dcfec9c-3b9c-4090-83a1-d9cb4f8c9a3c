<template>
  <view class="heiqi-player-container">
    <view class="video-container">
      <!-- WebRTC不支持时的提示（将由renderjs检查并显示） -->
      <view v-if="webrtcNotSupported" class="not-supported">
        <view class="not-supported-content">
          <text class="not-supported-title">WebRTC播放器不可用</text>
          <text class="not-supported-desc">当前App环境不支持WebRTC</text>
          <text class="not-supported-suggestion">建议切换到EasyPlayer播放器</text>
          <button class="switch-player-btn" @click="switchToEasyPlayer">切换播放器</button>
        </view>
      </view>

      <!-- 使用renderjs处理WebRTC的视频容器 -->
      <view
        v-else
        id="pageVideoWrap"
        class="video-wrap"
        :class="{ 'rotated': rotation !== 0 }"
        :rtcUrl="rtcUrl"
        :change:rtcUrl="webrtc.onChangeRtcUrl"
        :muted="videoMuted"
        :change:muted="webrtc.onChangeMuted"
        :trackState="trackState"
        :change:trackState="webrtc.onTrackStateChange"
        :webrtcSupported="webrtcSupported"
        :change:webrtcSupported="webrtc.onWebRTCSupportChanged"
        :answerData="answerData"
        :change:answerData="webrtc.onAnswerDataChanged"
        :candidateData="candidateData"
        :change:candidateData="webrtc.onCandidateDataChanged"
        :manualPlayTrigger="manualPlayTrigger"
        :change:manualPlayTrigger="webrtc.onManualPlayTrigger"
        :rotation="rotation"
        :change:rotation="webrtc.onRotationChange"
        style="width: 100%; height: 100%;"
      >
        <!-- video元素将由renderjs动态创建 -->
      </view>

      <view class="loading-indicator" :class="{ active: isLoading }">连接中...</view>

      <!-- 视频控制面板 -->
      <view class="video-controls" v-show="showControls">
        <button class="control-btn" @click="togglePlayPause">
          <svg v-if="!isPlaying" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
            <path d="M8 5v14l11-7z"/>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
          </svg>
        </button>

        <view class="seek-container">
          <input
            type="range"
            v-model="currentPosition"
            :min="0"
            :max="videoDuration"
            step="1"
            class="seek-slider"
            @input="onSeek"
          >
          <view class="time-display">
            <text>{{ formatTime(currentPosition) }} / {{ formatTime(videoDuration) }}</text>
          </view>
        </view>
      </view>

      <!-- 新增点击阻止层 -->
      <!-- <view class="video-overlay"></view -->>

      <!-- 视频统计信息显示 -->
      <view class="video-stats" v-if="showVideoStats">
        <text class="stats-text">{{ videoStats.fps }}FPS | {{ videoStats.bitrate }}kbps | {{ videoStats.packetsLost }} | {{ connectionTypeText }} | {{ this.availableConnections }}</text>
      </view>

      <!-- 新增播放按钮 -->
      <button v-if="showPlayButton" class="play-btn" @click="manualPlay">
        <view class="play-icon">▶</view>
        <text class="play-text">点击播放</text>
      </button>

    </view>

    <view class="controls" v-if="showConnectionControls">
      <view class="input-group">
        <input
          type="text"
          v-model="signalingUrl"
          placeholder="信令服务器URL"
          class="input-field"
        >
      </view>

      <view class="input-group">
        <input
          type="text"
          v-model="senderId"
          placeholder="发送端ID"
          class="input-field"
        >
      </view>

      <view class="button-group">
        <button @click="connect" :disabled="isConnected" class="connect-btn">连接</button>
        <button @click="disconnect" :disabled="!isConnected" class="disconnect-btn">断开</button>
        <button @click="reconnect" :disabled="!isConnected" class="reconnect-btn">重连</button>
      </view>
    </view>

    <view class="status" :class="{ error: hasError }">
      {{ statusMessage }}
    </view>
  </view>
</template>

<script>
export default {
  name: 'HeiqiPlayer',
  props: {
    // WebRTC信令服务器地址
    defaultSignalingUrl: {
      type: String,
      default: 'wss://sling.91jdcd.com/ws/'
    },
    // 默认发送端ID
    defaultSenderId: {
      type: String,
      default: ''  // 不设置默认值，由调用方传入
    },
    // 是否显示连接控制界面
    showConnectionControls: {
      type: Boolean,
      default: false
    },
    // 是否自动连接
    autoConnect: {
      type: Boolean,
      default: true
    },
    // 是否显示视频统计信息
    showStats: {
      type: Boolean,
      default: true
    },
    // 视频旋转角度（0, 90, 180, 270）
    rotation: {
      type: Number,
      default: 0,
      validator: function (value) {
        return [0, 90, 180, 270].includes(value)
      }
    },
    // 是否横屏游戏（0=竖屏, 1=横屏）
    isLandscape: {
      type: Number,
      default: 1
    }

  },

  data() {
    return {
      // 信令服务器WebSocket连接
      ws: null,
      receiverId: `receiver-${Math.random().toString(36).substring(2, 10)}`,
      activeSenderId: null,

      // UI状态
      isLoading: false,
      isConnected: false,
      hasError: false,
      statusMessage: '未连接',

      // 视频控制
      isPlaying: true,
      videoDuration: 0,
      currentPosition: 0,
      showControls: false,
      isLocalFile: false,
      showPlayButton: false,

      // 连接配置
      signalingUrl: '',
      senderId: '',

      // 连接管理
      connectionTimeout: null,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5, // 增加重连次数
      reconnectInterval: null, // 重连定时器
      heartbeatInterval: null, // 心跳定时器
      heartbeatCheckInterval: null, // 心跳检查定时器
      lastHeartbeatTime: 0, // 上次心跳时间
      isReconnecting: false, // 是否正在重连
      isManualDisconnect: false, // 是否手动断开
      shouldAutoConnect: true, // 是否应该自动连接

      // renderjs相关属性
      rtcUrl: '', // WebRTC连接URL，用于触发renderjs
      videoMuted: true, // 视频静音状态，默认静音
      trackState: false, // 流状态，true表示中断，false表示正常
      webrtcSupported: true, // WebRTC支持状态，由renderjs检查
      webrtcNotSupported: false, // WebRTC不支持标记
      answerData: null, // answer数据，用于传递给renderjs
      candidateData: null, // ICE候选数据，用于传递给renderjs
      manualPlayTrigger: 0, // 手动播放触发器，用于通知renderjs

      // 视频统计信息
      showVideoStats: true, // 是否显示视频统计信息
      videoStats: {
        fps: 0, // 帧率
        bitrate: 0, // 比特率(kbps)
        resolution: '0x0', // 分辨率
        packetsLost: 0, // 丢包数
        packetsReceived: 0, // 接收包数
        bytesReceived: 0, // 接收字节数
        timestamp: 0 // 时间戳
      },

      // 连接信息
      connectionType: 'H', // H=本地, S=穿透, T=中继
      availableConnections: 0, // 可用连接数量

      // 平台标记
      isH5: false
    }
  },

  computed: {
    // 连接类型文本
    connectionTypeText() {
      switch (this.connectionType) {
        case 'H': return 'H'
        case 'S': return 'S'
        case 'T': return 'T'
        default: return 'N'
      }
    }
  },

  watch: {
    // 监听gameSn变化，重新注册
    gameSn(newVal, oldVal) {
      if (newVal && newVal !== oldVal && this.isConnected) {
        this.log('gameSn变化，重新注册:', newVal)
        this.registerToSignalingServer()
      }
    },

    // 监听showStats属性变化
    showStats(newVal) {
      this.showVideoStats = newVal
    }
  },

  mounted() {
    // 平台判断
    this.isH5 = this.checkIsH5()

    // 设置视频统计信息显示状态
    this.showVideoStats = this.showStats

    // 初始化连接信息
    this.initializeConnectionInfo()

    // 监听来自 renderjs 的连接信息更新事件
    uni.$on('updateConnectionInfo', (data) => {
      this.log(`收到 renderjs 连接信息更新: type=${data.type}, count=${data.count}`)
      this.updateConnectionInfo(data.type, data.count)
    })

    this.initializeComponent()
  },
  beforeDestroy() {
    // 移除事件监听
    uni.$off('updateConnectionInfo')
    this.cleanup()
  },

  methods: {
    // 流状态变化处理（从renderjs回调）
    trackChange(isMuted) {
      this.log('流状态变化:', isMuted ? '中断' : '恢复')
      this.trackState = isMuted
      this.$emit('trackStateChanged', isMuted)

      if (isMuted) {
        this.updateStatus('视频流中断', true)
      } else {
        this.updateStatus('视频流恢复')
      }
    },

    // WebRTC支持状态变化处理（从renderjs回调）
    webrtcSupportChanged(supported) {
      this.log('WebRTC支持状态:', supported)
      this.webrtcSupported = supported
      this.webrtcNotSupported = !supported

      if (!supported) {
        const errorMsg = '当前环境不支持WebRTC，无法使用视频播放功能'
        console.warn(errorMsg)
        this.updateStatus(errorMsg, true)
        // 不在初始化时就发出connectionFailed事件，让用户可以看到提示并选择切换播放器
      } else {
        // WebRTC支持，可以正常使用
        this.updateStatus('WebRTC播放器可用')
      }
    },

    // 初始化组件
    initializeComponent() {
      this.signalingUrl = this.defaultSignalingUrl
      this.senderId = this.defaultSenderId

      this.log('HeiqiPlayer 初始化配置:', {
        signalingUrl: this.signalingUrl,
        senderId: this.senderId,
        autoConnect: this.autoConnect,
        isH5: this.isH5
      })

      // 验证配置参数
      const configValidation = this.validateConfiguration()
      if (!configValidation.isValid) {
        console.warn('配置验证失败:', configValidation.errors)
        this.updateStatus(`配置错误: ${configValidation.errors.join(', ')}`, true)

        // 如果没有发送端ID，给出更明确的提示
        if (!this.senderId) {
          this.log('警告：未提供发送端ID，可能无法自动连接到指定的发送端', 'warning')
        }
      }

      // 在renderjs模式下，不在主script中检查WebRTC支持
      // WebRTC支持检查将在renderjs中进行

      // 检测iOS设备并应用特定配置
      this.detectAndConfigureForIOS()

      if (this.autoConnect) {
        this.log('开始自动连接...')
        this.connect()
      } else {
        this.log('自动连接已禁用')
      }
    },

    // 验证配置参数
    validateConfiguration() {
      const errors = []

      // 检查信令服务器URL
      if (!this.signalingUrl) {
        errors.push('信令服务器URL未提供')
      } else if (!this.signalingUrl.startsWith('ws://') && !this.signalingUrl.startsWith('wss://')) {
        errors.push('信令服务器URL格式不正确，应以ws://或wss://开头')
      }

      // 检查发送端ID（警告级别，不阻止连接）
      if (!this.senderId) {
        console.warn('未提供发送端ID，将尝试从服务器获取可用的发送端列表')
      }

      return {
        isValid: errors.length === 0,
        errors: errors,
        warnings: this.senderId ? [] : ['未提供发送端ID']
      }
    },

    // 检测iOS设备并应用配置
    detectAndConfigureForIOS() {
      let isIOS = false

      try {
        // 安全检查 navigator 对象
        if (typeof navigator !== 'undefined' && navigator.userAgent) {
          isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream
        }

        // uni-app 环境下的iOS检测
        // #ifdef APP-PLUS
        const systemInfo = uni.getSystemInfoSync()
        isIOS = systemInfo.platform === 'ios'
        // #endif
      } catch (error) {
        console.warn('检测iOS设备时出错:', error)
        isIOS = false
      }

      if (isIOS) {
        // iOS特定配置
        //this.rtcConfig.sdpSemantics = 'unified-plan'
        //this.rtcConfig.iceCandidatePoolSize = 20
        // this.rtcConfig.iceServers.push(
        //   {
        //     urls: 'turn:turn.anyfirewall.com:443?transport=tcp',
        //     username: 'webrtc',
        //     credential: 'webrtc'
        //   },
        //   {
        //     urls: 'turn:turn.anyfirewall.com:443?transport=udp',
        //     username: 'webrtc',
        //     credential: 'webrtc'
        //   }
        // )

        // 安全检查 document 对象
        if (typeof document !== 'undefined' && document.body) {
          document.body.classList.add('ios-device')
        }
        this.log('已应用iOS特定WebRTC配置')
      }
    },

    // 连接到信令服务器
    async connect() {
      this.log('尝试连接到信令服务器:', this.signalingUrl)

      if (!this.signalingUrl) {
        const errorMsg = '信令服务器URL为空'
        console.error(errorMsg)
        this.updateStatus(errorMsg, true)
        this.$emit('connectionFailed', errorMsg)
        return
      }

      // 如果已经有连接，先关闭
      if (this.ws) {
        try {
          if (this.isH5) {
            this.ws.close()
          } else {
            uni.closeSocket()
          }
        } catch (error) {
          console.warn('关闭现有WebSocket连接时出错:', error)
        }
        this.ws = null
      }

      this.setLoading(true)
      this.updateStatus('正在连接信令服务器...')

      try {
        this.log('创建WebSocket连接:', this.signalingUrl)

        // 根据平台选择不同的WebSocket实现
        if (this.isH5) {
          // H5环境使用原生WebSocket
          this.ws = new WebSocket(this.signalingUrl)
        } else {
          // App环境使用uni-app的WebSocket
          this.ws = uni.connectSocket({
            url: this.signalingUrl,
            complete: () => {}
          })
        }

        // 设置连接超时
        this.connectionTimeout = setTimeout(() => {
          const readyState = this.isH5 ? this.ws.readyState : this.ws.readyState
          const CONNECTING = this.isH5 ? WebSocket.CONNECTING : 0

          if (this.ws && readyState === CONNECTING) {
            console.error('连接超时')
            if (this.isH5) {
              this.ws.close()
            } else {
              uni.closeSocket()
            }
            this.updateStatus('连接超时', true)
            this.setLoading(false)
            this.$emit('connectionFailed', '连接超时')
          }
        }, 10000) // 10秒超时

        // 连接打开事件
        const onOpen = () => {
          this.log('WebSocket连接已建立')
          this.log('已连接到信令服务器')
          this.reconnectAttempts = 0
          this.isConnected = true
          this.isReconnecting = false

          // 清除连接超时
          if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout)
            this.connectionTimeout = null
          }

          // 清除重连定时器
          if (this.reconnectInterval) {
            clearTimeout(this.reconnectInterval)
            this.reconnectInterval = null
          }

          // 开始心跳检测
          this.startHeartbeat()

          setTimeout(() => {
            if (this.ws && this.isConnected) {
              const registerMsg = {
                type: 'register',
                id: this.receiverId,
                role: 'viewer',
                name: this.receiverId+'接收端',
                description: this.receiverId+'来自接收端'
              }
              this.log('发送注册消息:', registerMsg)

              if (this.isH5) {
                this.ws.send(JSON.stringify(registerMsg))
              } else {
                this.ws.send({
                  data: JSON.stringify(registerMsg)
                })
              }
              this.log('已发送注册信息')
            }
          }, 100)
        }

        // 接收消息事件
        const onMessage = async (event) => {
          try {
            const data = JSON.parse(event.data)
            this.log('收到信令消息:', data)
            await this.handleSignalingMessage(data)
          } catch (error) {
            console.error('解析信令消息失败:', error)
          }
        }

        // 连接关闭事件
        const onClose = (event) => {
          this.log('WebSocket连接已关闭:', event.code, event.reason)
          this.log('信令服务器连接已关闭', 'error')
          this.isConnected = false

          // 停止心跳检测
          this.stopHeartbeat()

          // 清除连接超时
          if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout)
            this.connectionTimeout = null
          }

          // 如果不是主动断开且未达到重连上限，则自动重连
          if (event.code !== 1000 && !this.isReconnecting) { // 非正常关闭
            this.updateStatus(`连接异常关闭: ${event.code} ${event.reason}`, true)
            this.scheduleReconnect()
          } else if (event.code === 1000) {
            this.updateStatus('连接已断开')
          }

          this.setLoading(false)
        }

        // 连接错误事件
        const onError = (error) => {
          console.error('WebSocket错误:', error)
          this.log(`信令服务器错误: ${error.message || '未知错误'}`, 'error')
          this.updateStatus(`信令服务器错误: ${error.message || '未知错误'}`, true)
          this.setLoading(false)
          this.$emit('connectionFailed', error.message || '未知错误')
        }

        // 绑定事件
        if (this.isH5) {
          // H5环境直接绑定事件
          this.ws.onopen = onOpen
          this.ws.onmessage = onMessage
          this.ws.onclose = onClose
          this.ws.onerror = onError
        } else {
          // App环境使用SocketTask的事件绑定
          this.ws.onOpen(onOpen)
          this.ws.onMessage(onMessage)
          this.ws.onClose(onClose)
          this.ws.onError(onError)
        }

      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        this.log(`连接信令服务器错误: ${error.message}`, 'error')
        this.updateStatus(`连接信令服务器错误: ${error.message}`, true)
        this.setLoading(false)
        this.$emit('connectionFailed', error.message)
      }
    },

    // 断开连接
    disconnect() {
      this.cleanup()
      this.updateStatus('已断开连接')
      this.$emit('disconnected')
    },

    // 重新连接
    reconnect() {
      this.disconnect()
      setTimeout(() => this.connect(), 1000)
    },

    // 调度重连
    scheduleReconnect() {
      // 检查是否手动断开
      if (this.isManualDisconnect) {
        this.log('手动断开连接，跳过自动重连')
        return
      }

      if (this.isReconnecting) {
        this.log('已在重连中，跳过此次重连请求')
        return
      }

      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        this.log('重连次数已达上限，停止重连')
        this.updateStatus('重连次数已达上限，连接失败', true)
        this.$emit('connectionFailed', '重连次数已达上限')
        return
      }

      this.isReconnecting = true
      this.reconnectAttempts++

      // 使用指数退避算法计算重连延迟
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 30000) // 最大30秒

      this.log(`计划在 ${delay}ms 后进行第 ${this.reconnectAttempts} 次重连`)
      this.log(`尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})，${delay}ms后重连...`)
      this.updateStatus(`连接断开，${Math.round(delay/1000)}秒后重连...`, true)

      this.reconnectInterval = setTimeout(() => {
        this.log(`开始第 ${this.reconnectAttempts} 次重连`)
        this.connect()
      }, delay)
    },

    // 开始心跳检测
    startHeartbeat() {
      this.log('开始心跳检测')
      this.stopHeartbeat() // 先停止之前的心跳

      this.lastHeartbeatTime = Date.now()

      // 每30秒发送一次心跳
      this.heartbeatInterval = setInterval(() => {
        if (this.isWebSocketOpen()) {
          const heartbeatMsg = {
            type: 'heartbeat',
            timestamp: Date.now()
          }

          if (this.sendWebSocketMessage(heartbeatMsg)) {
            this.log('发送心跳消息')
            this.lastHeartbeatTime = Date.now()
          } else {
            this.log('心跳发送失败，可能连接已断开')
            this.handleHeartbeatFailure()
          }
        } else {
          this.log('WebSocket连接不可用，停止心跳并尝试重连')
          this.handleHeartbeatFailure()
        }
      }, 30000) // 30秒间隔

      // 每60秒检查心跳响应
      this.heartbeatCheckInterval = setInterval(() => {
        const now = Date.now()
        const timeSinceLastHeartbeat = now - this.lastHeartbeatTime

        // 如果超过100秒没有心跳响应，认为连接异常
        if (timeSinceLastHeartbeat > 100000) {
          this.log('心跳超时，连接可能异常')
          this.handleHeartbeatFailure()
        }
      }, 60000) // 60秒检查间隔
    },

    // 停止心跳检测
    stopHeartbeat() {
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
        this.log('停止心跳检测')
      }

      if (this.heartbeatCheckInterval) {
        clearInterval(this.heartbeatCheckInterval)
        this.heartbeatCheckInterval = null
      }
    },

    // 处理心跳失败
    handleHeartbeatFailure() {
      this.log('心跳检测失败，准备重连')
      this.stopHeartbeat()

      if (this.ws) {
        try {
          if (this.isH5) {
            this.ws.close()
          } else {
            uni.closeSocket()
          }
        } catch (error) {
          console.warn('关闭WebSocket时出错:', error)
        }
        this.ws = null
      }

      this.isConnected = false
      this.scheduleReconnect()
    },

    // 发送WebSocket消息的统一方法
    sendWebSocketMessage(message) {
      try {
        if (!this.ws) {
          console.warn('WebSocket连接不存在')
          return false
        }

        const messageStr = JSON.stringify(message)

        if (this.isH5) {
          this.ws.send(messageStr)
        } else {
          this.ws.send({
            data: messageStr
          })
        }
        return true
      } catch (error) {
        console.error('发送WebSocket消息失败:', error)
        return false
      }
    },

    // 检查WebSocket是否已连接
    isWebSocketOpen() {
      if (!this.ws) return false

      try {
        if (this.isH5) {
          return this.ws.readyState === WebSocket.OPEN
        } else {
          // App环境下，通过isConnected状态判断
          return this.isConnected
        }
      } catch (error) {
        console.warn('检查WebSocket状态时出错:', error)
        return false
      }
    },

    // 检查WebRTC支持
    checkWebRTCSupport() {
      try {
        // 检查RTCPeerConnection是否存在
        if (typeof RTCPeerConnection === 'undefined') {
          console.warn('RTCPeerConnection不可用')
          return false
        }

        // 在非H5环境下，WebRTC可能不被支持
        if (!this.isH5) {
          console.warn('非H5环境，WebRTC可能不被支持')
          // 可以尝试创建一个测试连接来验证
          try {
            const testPc = new RTCPeerConnection()
            testPc.close()
            return true
          } catch (error) {
            console.warn('WebRTC测试失败:', error)
            return false
          }
        }

        return true
      } catch (error) {
        console.warn('检查WebRTC支持时出错:', error)
        return false
      }
    },



    // 处理信令消息
    async handleSignalingMessage(data) {
      this.log(`收到消息: ${data.type}`)
      this.log('收到消息:', data)

      switch (data.type) {
        case 'registered':
          this.updateStatus(`已注册，ID: ${this.receiverId}`)
          this.log('注册成功，当前senderId:', this.senderId)

          // 注册成功后立即获取连接统计
          this.getRealConnectionStats()

          // 直接连接指定发送端
          if (this.senderId) {
            this.log(`有指定发送端ID: ${this.senderId}，直接连接`)
            this.connectToPeer(this.senderId)
          } else {
            this.log('没有指定发送端ID，请求客户端列表')
            console.warn('警告：未提供发送端ID，这可能导致连接失败')
            this.log('未提供发送端ID，尝试获取可用的发送端列表', 'warning')

            const listMessage = { type: 'list_clients' }
            this.log('发送list_clients消息:', listMessage)
            this.sendWebSocketMessage(listMessage)

            // 设置一个超时，如果5秒内没有收到客户端列表，显示错误信息
            setTimeout(() => {
              if (!this.activeSenderId) {
                this.log('5秒内未收到客户端列表，无法自动连接')
                this.updateStatus('未收到客户端列表，请检查发送端是否在线', true)
                this.log('未收到客户端列表，请确保发送端已启动并在线', 'error')
                this.$emit('connectionFailed', '未提供发送端ID且无法获取客户端列表')
              }
            }, 5000)
          }
          break

        case 'client_list':
          this.log('收到客户端列表:', data.clients)
          this.handleClientList(data.clients || [])
          break

        case 'offer':
          // 如果我们是被动接收方，处理offer
          if (data.from && !this.activeSenderId) {
            await this.handleOffer(data)
          }
          break

        case 'answer':
          // 如果我们是主动发起方，处理answer
          if (data.from === this.activeSenderId) {
            await this.handleAnswer(data)
          }
          break

        case 'candidate':
          if (data.from === this.activeSenderId) {
            await this.handleIceCandidate(data)
          }
          break

        case 'heartbeat_ack':
          this.log('收到心跳确认')
          this.lastHeartbeatTime = Date.now() // 更新心跳时间
          break

        default:
          this.log('未处理的消息类型:', data.type, '消息内容:', data)
          // 检查是否是心跳消息或其他系统消息
          if (data.type === 'heartbeat_back') {
            this.log('收到心跳回复')
            this.lastHeartbeatTime = Date.now() // 更新心跳时间
          } else {
            console.warn('未知的消息类型，可能需要添加处理逻辑')
          }
          break
      }
    },

    // 处理客户端列表
    handleClientList(clients) {
      this.log('处理客户端列表，原始数据:', clients)
      this.log('客户端数量:', clients.length)

      const senders = clients.filter(client =>
        client.role === 'source' || client.id.startsWith('sender-')
      )
      const receivers = clients.filter(client =>
        client.role === 'receiver' || client.id.startsWith('receiver-')
      )

      this.log('过滤后的发送端:', senders)
      this.log('发送端数量:', senders.length)
      this.log('接收端数量:', receivers.length)

      // 注意：这里不直接更新availableConnections
      // 可用连接数应该基于实际的ICE连接状态，而不是信令服务器的客户端列表
      this.log('信令服务器客户端统计（仅供参考）:', {
        senders: senders.length,
        receivers: receivers.length,
        total: clients.length
      })

      // 发送统计信息给父组件（包含信令服务器的统计信息）
      this.$emit('sendersUpdated', senders)
      this.$emit('connectionStatsUpdated', {
        senders: senders.length,
        receivers: receivers.length,
        total: clients.length,
        clients: clients,
        note: '此统计来自信令服务器，真实可用连接数基于ICE连接状态'
      })

      // 如果有指定的发送端ID，尝试连接
      if (this.senderId && senders.some(sender => sender.id === this.senderId)) {
        this.log(`发现目标发送端 ${this.senderId}，开始连接...`)
        this.connectToPeer(this.senderId)
      } else if (senders.length > 0) {
        // 如果没有指定发送端ID，连接第一个可用的发送端
        this.log(`没有指定发送端或指定发送端不在线，连接第一个可用发送端: ${senders[0].id}`)
        this.connectToPeer(senders[0].id)
      } else {
        this.log('没有可用的发送端')
        this.updateStatus('没有可用的发送端', true)
      }
    },

    // 主动连接到指定发送端
    async connectToPeer(senderId) {
      if (!senderId) {
        console.error('发送端ID为空')
        this.updateStatus('发送端ID为空', true)
        return
      }

      if (!this.ws || !this.isWebSocketOpen()) {
        console.error('信令服务器未连接')
        this.updateStatus('信令服务器未连接', true)
        return
      }

      this.setLoading(true)
      this.log(`开始连接到发送端: ${senderId}`)
      this.activeSenderId = senderId
      this.updateStatus(`正在连接到发送端 ${senderId}...`)

      // 通过设置rtcUrl触发renderjs中的WebRTC连接
      // 注意：这个URL需要是一个实际的WebRTC SDP交换端点
      // 目前使用测试URL，实际使用时需要替换为真实的WebRTC服务端点
      const baseUrl = this.signalingUrl.replace('ws://', 'http://').replace('wss://', 'https://').replace('/ws/', '/')
      const rtcUrl = baseUrl + 'api/webrtc/offer/' + senderId
      this.log('设置rtcUrl触发WebRTC连接:', rtcUrl)
      this.log('注意：当前使用测试URL，可能会返回404错误')
      this.rtcUrl = rtcUrl
    },

    // 处理offer - 现在只需要设置activeSenderId，实际WebRTC连接由renderjs处理
    async handleOffer(data) {
      try {
        if (!this.activeSenderId) {
          this.activeSenderId = data.from
          this.log(`设置活跃发送端为 ${this.activeSenderId}`)

          // 通过设置rtcUrl触发renderjs中的WebRTC连接
          const rtcUrl = this.signalingUrl.replace('ws://', 'http://').replace('wss://', 'https://') + 'webrtc/' + this.activeSenderId
          this.log('收到offer，设置rtcUrl触发WebRTC连接:', rtcUrl)
          this.rtcUrl = rtcUrl
          this.updateStatus(`正在连接到发送端 ${this.activeSenderId}...`)
        }
      } catch (error) {
        this.log(`处理offer错误: ${error.message}`, 'error')
        this.updateStatus(`处理offer错误: ${error.message}`, true)
      }
    },

    // 处理answer - 传递给renderjs处理
    async handleAnswer(data) {
      this.log('收到answer，传递给renderjs处理')
      // 通过修改数据属性来通知renderjs
      this.answerData = {
        sdp: data.sdp,
        from: data.from,
        timestamp: Date.now()
      }
    },

    // 处理ICE候选 - 传递给renderjs处理
    async handleIceCandidate(data) {
      this.log('收到ICE候选，传递给renderjs处理')
      // 通过修改数据属性来通知renderjs
      this.candidateData = {
        candidate: data.candidate,
        from: data.from,
        timestamp: Date.now()
      }
    },

    // 发送offer到信令服务器（从renderjs回调）
    sendOfferToSignaling({ senderId, sdp }) {
      this.log('从renderjs收到offer，发送到信令服务器:', senderId)

      if (!this.ws || !this.isWebSocketOpen()) {
        console.error('信令服务器未连接，无法发送offer')
        return
      }

      this.sendWebSocketMessage({
        type: 'offer',
        target: senderId,
        sdp: sdp
      })
      this.log(`已通过信令服务器发送offer到: ${senderId}`)
      this.log(`已发送offer到发送端: ${senderId}`)
    },



    // 发送ICE候选到信令服务器（从renderjs回调）
    sendCandidateToSignaling({ senderId, candidate }) {
      this.log('从renderjs收到ICE候选，发送到信令服务器:', senderId)

      if (!this.ws || !this.isWebSocketOpen()) {
        console.error('信令服务器未连接，无法发送ICE候选')
        return
      }

      this.sendWebSocketMessage({
        type: 'candidate',
        target: senderId,
        candidate: candidate
      })
      this.log('已发送ICE候选')
    },

    // 处理数据通道消息（从renderjs回调）
    handleDataChannelMessage(data) {
      this.log(`收到数据通道消息: ${data}`)

      try {
        if (data.startsWith('{') && data.endsWith('}')) {
          const message = JSON.parse(data)

          if (message.type === 'status') {
            this.isPlaying = message.state === 'playing'

            if (message.duration > 0) {
              this.videoDuration = message.duration
              this.currentPosition = message.position
              this.isLocalFile = message.is_local_file

              if (this.isLocalFile) {
                this.showControls = true
              }
            }
          }

          this.$emit('dataChannelMessage', message)
        }
      } catch (error) {
        this.log(`解析数据通道消息错误: ${error.message}`, 'error')
      }
    },

    // 全屏切换
    toggleFullscreen() {
      try {
        // 在非H5环境下，可能不支持全屏API
        if (!this.isH5) {
          console.warn('非H5环境，不支持全屏功能')
          return
        }

        // 安全检查 document 对象
        if (typeof document === 'undefined') {
          console.warn('document对象不存在，无法使用全屏功能')
          return
        }

        const container = document.querySelector('.video-container')
        if (!container) {
          console.warn('未找到视频容器元素')
          return
        }

        if (!document.fullscreenElement) {
          if (container.requestFullscreen) {
            container.requestFullscreen()
          } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen()
          } else if (container.mozRequestFullScreen) {
            container.mozRequestFullScreen()
          } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen()
          }
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen()
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen()
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen()
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen()
          }
        }
      } catch (error) {
        console.warn('全屏切换时出错:', error)
      }
    },

    // 播放/暂停切换（通过renderjs发送数据通道消息）
    togglePlayPause() {
      // 这里可以通过事件通知renderjs发送数据通道消息
      this.$emit('sendDataChannelMessage', {
        type: 'command',
        command: 'toggle_pause'
      })
    },

    // 拖拽进度条（通过renderjs发送数据通道消息）
    onSeek(event) {
      const position = parseInt(event.target.value)
      // 这里可以通过事件通知renderjs发送数据通道消息
      this.$emit('sendDataChannelMessage', {
        type: 'command',
        command: 'seek',
        position: position
      })
    },

    // 格式化时间
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },

    // 日志记录
    log(message, type = 'info') {
      const timestamp = new Date().toLocaleTimeString()
      console.log(`[${timestamp}] [${type}] ${message}`)
      this.$emit('log', { message, type, timestamp })
    },

    // 更新状态
    updateStatus(message, isError = false) {
      const timestamp = new Date().toLocaleTimeString()
      this.statusMessage = message
      this.hasError = isError
      this.$emit('statusChanged', { timestamp,message, isError })

      // 根据连接状态更新连接类型
      if (message.includes('连接成功') || message.includes('已连接')) {
        this.updateConnectionInfo('H', this.availableConnections) // 连接成功时设为本地
      } else if (message.includes('重连') || message.includes('连接中')) {
        this.updateConnectionInfo('S', this.availableConnections) // 重连时设为穿透
      } else if (message.includes('断开') || message.includes('失败')) {
        this.updateConnectionInfo('T', Math.max(1, this.availableConnections - 1)) // 断开时设为中继，连接数减少
      }
    },

    // 设置加载状态
    setLoading(loading) {
      this.isLoading = loading
    },

    // 设置播放按钮显示状态（从renderjs回调）
    setShowPlayButton(show) {
      this.log('设置播放按钮显示状态:', show)
      this.showPlayButton = show

      // 添加调试信息
      this.$nextTick(() => {
        const playBtn = document.querySelector('.play-btn')
        if (playBtn) {
          this.log('播放按钮元素存在，样式:', {
            display: getComputedStyle(playBtn).display,
            zIndex: getComputedStyle(playBtn).zIndex,
            position: getComputedStyle(playBtn).position,
            visibility: getComputedStyle(playBtn).visibility
          })
        } else {
          this.log('播放按钮元素不存在')
        }
      })
    },

    // 设置视频静音状态（从renderjs回调）
    setVideoMuted(muted) {
      this.log('设置视频静音状态:', muted)
      this.videoMuted = muted
    },

    // 应用iOS video层级修复（从renderjs回调）
    applyIOSVideoFix() {
      this.log('应用iOS video层级修复')
      this.$emit('iosVideoFixApplied')

      // 可以在这里通知父组件应用特殊样式
      this.$nextTick(() => {
        // 为游戏控制元素添加特殊样式类
        const gameElements = [
          '.game-controls',
          '.status-bar',
          '.direction-pad',
          '.action-buttons',
          '.game-ui',
          '.control-panel'
        ];

        gameElements.forEach(selector => {
          const elements = document.querySelectorAll(selector);
          elements.forEach(el => {
            el.classList.add('force-top-layer');
          });
        });
      });
    },

    // 手动播放（通过renderjs处理）
    manualPlay() {
      this.log('用户点击播放按钮')
      this.log('当前showPlayButton状态:', this.showPlayButton)

      // 通知renderjs执行手动播放
      this.manualPlayTrigger = Date.now() // 通过改变属性值触发renderjs

      // 隐藏播放按钮
      this.showPlayButton = false
      this.log('播放按钮已隐藏')
    },

    // 更新视频统计信息（从renderjs回调）
    updateVideoStats(stats) {
      this.videoStats = {
        fps: Math.round(stats.fps || 0),
        bitrate: Math.round(stats.bitrate || 0),
        resolution: stats.resolution || '0x0',
        packetsLost: stats.packetsLost || 0,
        packetsReceived: stats.packetsReceived || 0,
        bytesReceived: stats.bytesReceived || 0,
        timestamp: stats.timestamp || Date.now()
      }

      // 发送事件给父组件
      this.$emit('videoStatsUpdated', this.videoStats)
    },

    // 更新连接信息（基于ICE连接状态）
    updateConnectionInfo(type, availableCount) {
      const oldType = this.connectionType
      const oldCount = this.availableConnections

      this.connectionType = type || 'H'

      // 智能更新连接数，不直接覆盖
      let newCount = 0
      if (typeof availableCount === 'number') {
        // 如果传入的是有效数字
        if (availableCount === 0) {
          // 只有在连接类型为失败/关闭时才允许设置为0
          if (type === 'T' && this.connectionType === 'T') {
            // 中继连接可能会有0的情况
            newCount = 0
          } else {
            // 其他情况下，如果当前有连接，保持至少1个
            newCount = this.availableConnections > 0 ? Math.max(1, this.availableConnections) : 0
          }
        } else {
          // 传入的数量大于0，直接使用
          newCount = availableCount
        }
      } else {
        // 传入的不是数字，保持当前值
        newCount = this.availableConnections || 0
      }

      this.availableConnections = newCount

      // 记录连接状态变化
      if (oldType !== this.connectionType || oldCount !== this.availableConnections) {
        this.log(`ICE连接状态更新: 类型=${this.connectionType}, 可用连接=${this.availableConnections}个 (输入: type=${type}, count=${availableCount}, 旧值=${oldCount})`)

        // 发送真实连接状态给父组件
        this.$emit('realConnectionStatsUpdated', {
          connectionType: this.connectionType,
          availableConnections: this.availableConnections,
          isConnected: this.availableConnections > 0,
          source: 'ICE_CONNECTION_STATE'
        })
      }
    },

    // 获取真实的WebRTC连接统计信息
    async getRealConnectionStats() {
      try {
        // 1. 查询信令服务器的客户端列表
        if (this.ws && this.isWebSocketOpen()) {
          this.sendWebSocketMessage({
            type: 'list_clients'
          })
        }

        // 2. 如果有活跃的WebRTC连接，获取连接统计
        if (this.isH5) {
          // H5环境通过renderjs获取
          this.$refs.webrtc && this.$refs.webrtc.getRealConnectionStats()
        } else {
          // App环境直接调用
          this.getRealConnectionStatsFromRenderjs()
        }
      } catch (error) {
        this.log(`获取连接统计失败: ${error.message}`, 'error')
      }
    },

    // 从renderjs获取连接统计（App环境）
    getRealConnectionStatsFromRenderjs() {
      // 通过uni.$emit触发renderjs中的方法
      uni.$emit('getRealConnectionStats')
    },

    // 处理信令服务器返回的客户端列表
    handleClientListResponse(clients) {
      try {
        // 过滤出发送端（视频源）
        const senders = clients.filter(client =>
          client.role === 'source' ||
          client.id.startsWith('sender-') ||
          client.type === 'sender'
        )

        // 过滤出接收端
        const receivers = clients.filter(client =>
          client.role === 'receiver' ||
          client.id.startsWith('receiver-') ||
          client.type === 'receiver'
        )

        // 更新信令服务器统计的发送端数量（但不直接覆盖availableConnections）
        const signalingServerSenders = senders.length

        // 只有在没有ICE连接时才使用信令服务器统计，并且要通过updateConnectionInfo更新
        if (!rtc || rtc.iceConnectionState === 'new' || rtc.iceConnectionState === 'checking') {
          this.updateConnectionInfo(this.connectionType, signalingServerSenders)
        }

        this.log(`信令服务器统计: 发送端=${signalingServerSenders}个, 接收端=${receivers.length}个, 当前可用连接=${this.availableConnections}个`)

        // 发送事件给父组件
        this.$emit('connectionStatsUpdated', {
          senders: senders.length,
          receivers: receivers.length,
          total: clients.length
        })
      } catch (error) {
        this.log(`处理客户端列表失败: ${error.message}`, 'error')
      }
    },

    // 基于WebRTC连接状态判断连接类型
    detectConnectionType() {
      // 这里可以基于实际的WebRTC连接状态来判断
      // 目前先返回默认值，后续可以集成真实的检测逻辑
      if (this.isConnected) {
        // 如果连接延迟很低，可能是本地连接
        return 'H'
      } else {
        return 'T' // 连接中断时显示中继
      }
    },

    // 初始化连接信息
    initializeConnectionInfo() {
      // 立即设置初始值
      this.connectionType = '' // 默认本地连接
      this.availableConnections = 0 // 初始3个连接

      this.log(`连接信息初始化: 类型=${this.connectionTypeText}, 可用连接=${this.availableConnections}个`)

      // 强制更新视图
      this.$forceUpdate()

      // 暂时禁用定期更新连接信息，避免与ICE状态更新冲突
      // setInterval(() => {
      //   // 基于连接状态智能判断连接类型
      //   let newType = this.detectConnectionType()

      //   // 连接数量在合理范围内波动
      //   const baseCount = 3
      //   const variation = Math.floor(Math.random() * 3) - 1 // -1, 0, 1
      //   const newCount = Math.max(1, Math.min(8, baseCount + variation))

      //   this.updateConnectionInfo(newType, newCount)
      // }, 30000) // 每30秒更新一次，进一步降低频率
    },

    // 切换视频统计信息显示
    toggleVideoStats() {
      this.showVideoStats = !this.showVideoStats
    },

    // 切换音频（供父组件调用）
    toggleAudio() {
      this.videoMuted = !this.videoMuted
    },

    // 平台检测：H5
    checkIsH5() {
      try {
        // uni-app 环境下的平台检测
        // #ifdef H5
        return true
        // #endif

        // #ifdef APP-PLUS
        return false
        // #endif

        // uni-app H5 平台 window.__uniConfig 有 platform = 'h5'
        if (typeof window !== 'undefined' && window.__uniConfig && window.__uniConfig.platform === 'h5') {
          return true
        }

        // 兜底：userAgent 检查
        if (typeof navigator !== 'undefined' && navigator.userAgent) {
          return /Chrome|Safari|Firefox|Edge|Windows|Macintosh|Linux/i.test(navigator.userAgent)
        }
      } catch (error) {
        console.warn('检测H5平台时出错:', error)
      }

      return false
    },



    // 切换到EasyPlayer播放器
    switchToEasyPlayer() {
      this.$emit('switchPlayer', 'easyplayer')
    },

    // 断开WebRTC连接（保持WebSocket连接）
    disconnectWebRTC() {
      this.log('断开WebRTC连接，停止视频流传输')

      // 设置标志，禁用自动重连
      this.isManualDisconnect = true
      this.shouldAutoConnect = false

      // 通知renderjs断开WebRTC连接
      if (this.$refs.webrtc) {
        this.$refs.webrtc.disconnectWebRTC()
      }

      // 停止心跳检测
      this.stopHeartbeat()

      // 清理WebSocket连接
      if (this.ws) {
        this.ws.close(1000, '手动断开')
        this.ws = null
      }

      // 重置WebRTC相关状态
      this.rtcUrl = ''
      this.answerData = null
      this.candidateData = null
      this.trackState = false
      this.manualPlayTrigger = 0
      this.isConnected = false
      this.isReconnecting = false
      this.activeSenderId = null
      this.reconnectAttempts = 0

      // 清理所有定时器
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout)
        this.connectionTimeout = null
      }

      if (this.reconnectInterval) {
        clearTimeout(this.reconnectInterval)
        this.reconnectInterval = null
      }

      // 更新状态
      this.updateStatus('WebRTC连接已断开')
      this.setLoading(false)

      this.log('WebRTC连接已断开，自动重连已禁用，所有定时器已清理，流量传输已停止')
    },

    // 重新连接WebRTC
    reconnectWebRTC() {
      this.log('开始重新连接WebRTC')

      // 重置手动断开标志，允许重连
      this.isManualDisconnect = false
      this.shouldAutoConnect = true
      this.reconnectAttempts = 0

      // 重置状态
      this.isConnected = false
      this.isReconnecting = false

      // 更新状态
      this.updateStatus('正在重新连接...')
      this.setLoading(true)

      // 开始连接
      this.connect()

      this.log('WebRTC重新连接已启动')
    },

    // 清理资源
    cleanup() {
      this.log('开始清理资源')

      // 清理连接相关定时器
      if (this.connectionTimeout) {
        clearTimeout(this.connectionTimeout)
        this.connectionTimeout = null
      }

      if (this.reconnectInterval) {
        clearTimeout(this.reconnectInterval)
        this.reconnectInterval = null
      }

      // 停止心跳检测
      this.stopHeartbeat()

      // 清理rtcUrl，停止renderjs中的WebRTC连接
      this.rtcUrl = ''

      // 关闭WebSocket连接
      if (this.ws) {
        try {
          if (this.isH5) {
            this.ws.close(1000, '主动断开') // 正常关闭
          } else {
            uni.closeSocket()
          }
        } catch (error) {
          console.warn('关闭WebSocket时出错:', error)
        }
        this.ws = null
      }

      // 重置状态
      this.isConnected = false
      this.isReconnecting = false
      this.activeSenderId = null
      this.reconnectAttempts = 0
      this.setLoading(false)

      this.log('资源清理完成')
    }
  }
}
</script>

<script module="webrtc" lang="renderjs">
let rtc = null
let dataChannel = null

export default {
  data() {
    return {
      webrtcReqUrl: '',
      statsInterval: null, // 统计信息收集定时器
      streamMonitorInterval: null, // 流状态监控定时器
      lastBytesReceived: 0, // 上次接收的字节数
      lastTimestamp: 0, // 上次统计时间戳
      pendingIceCandidates: [], // 待处理的ICE候选
      trackMuteCount: 0, // 轨道中断次数
      lastTrackMuteTime: 0, // 上次轨道中断时间
      reconnectAttempts: 0, // 重连尝试次数
      maxReconnectAttempts: 3, // 最大重连次数
      connectionStartTime: 0, // 连接开始时间
      isManualDisconnect: false, // 是否手动断开
      lastHealthCheckTime: 0, // 上次健康检查时间
      lastConnectionState: '', // 上次连接状态
    }
  },

  methods: {
    // 断开WebRTC连接（保持WebSocket连接）
    disconnectWebRTC() {
      this.log('renderjs: 断开WebRTC连接，停止视频流传输')

      try {
        // 设置手动断开标志，禁用重连
        this.isManualDisconnect = true

        // 关闭WebRTC连接
        if (rtc) {
          // 移除所有事件监听器，避免后续事件触发
          rtc.onconnectionstatechange = null
          rtc.oniceconnectionstatechange = null
          rtc.onicecandidate = null
          rtc.ontrack = null
          rtc.ondatachannel = null
          this.log('WebRTC事件监听器已清理')

          // 关闭所有数据通道
          if (dataChannel) {
            dataChannel.close()
            dataChannel = null
            this.log('数据通道已关闭')
          }

          // 关闭PeerConnection
          rtc.close()
          rtc = null
          this.log('PeerConnection已关闭')
        }

        // 关闭WebSocket连接
        if (ws) {
          ws.close()
          ws = null
          this.log('WebSocket连接已关闭')
        }

        // 清理video元素的流
        const video = document.querySelector('#pageVideoWrap video')
        if (video) {
          video.srcObject = null
          video.pause()
          this.log('视频流已清理')
        }

        // 停止统计信息收集
        this.stopStatsCollection()

        // 停止流状态监控
        this.stopStreamMonitoring()

        // 清理所有定时器
        this.clearAllTimers()

        // 重置连接状态
        this.isConnected = false
        this.reconnectAttempts = 0

        this.log('WebRTC连接已完全断开，自动重连已禁用，所有定时器已清理，流量传输已停止')

      } catch (error) {
        console.warn('断开WebRTC连接时出错:', error)
      }
    },

    // 重新连接WebRTC（从主组件调用）
    reconnectWebRTC() {
      this.log('renderjs: 开始重新连接WebRTC')

      try {
        // 重置手动断开标志，允许重连
        this.isManualDisconnect = false
        this.reconnectAttempts = 0

        // 清理现有连接
        if (rtc) {
          rtc.close()
          rtc = null
        }

        if (ws) {
          ws.close()
          ws = null
        }

        // 重置状态
        this.isConnected = false

        // 通知主组件开始重连
        this.$ownerInstance.callMethod('updateStatus', '正在重新连接...')
        this.$ownerInstance.callMethod('setLoading', true)

        // 重新触发连接
        if (this.webrtcReqUrl) {
          this.log('使用之前的URL重新连接:', this.webrtcReqUrl)
          this.initRtc()
        } else {
          this.log('没有可用的连接URL，通知主组件重新获取')
          this.$ownerInstance.callMethod('log', '没有可用的连接URL，请重新获取', 'error')
        }

        this.log('renderjs: WebRTC重新连接已启动')

      } catch (error) {
        console.error('重新连接WebRTC时出错:', error)
        this.$ownerInstance.callMethod('log', `重新连接失败: ${error.message}`, 'error')
      }
    },

    // 清理所有定时器
    clearAllTimers() {
      this.log('清理所有定时器')

      // 清理统计收集定时器
      if (this.statsInterval) {
        clearInterval(this.statsInterval)
        this.statsInterval = null
        this.log('统计收集定时器已清理')
      }

      // 清理流监控定时器
      if (this.streamMonitorInterval) {
        clearInterval(this.streamMonitorInterval)
        this.streamMonitorInterval = null
        this.log('流监控定时器已清理')
      }

      // 清理心跳定时器
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval)
        this.heartbeatInterval = null
        this.log('心跳定时器已清理')
      }

      // 清理重连定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
        this.reconnectTimer = null
        this.log('重连定时器已清理')
      }

      // 清理连接超时定时器
      if (this.connectionTimer) {
        clearTimeout(this.connectionTimer)
        this.connectionTimer = null
        this.log('连接超时定时器已清理')
      }

      // 清理WebRTC连接超时定时器
      if (this.connectionTimeoutId) {
        clearTimeout(this.connectionTimeoutId)
        this.connectionTimeoutId = null
        this.log('WebRTC连接超时定时器已清理')
      }
    },

    // 监听静音状态变化
    onChangeMuted(muted) {
      const video = document.querySelector('#pageVideoWrap video');
      if (video) {
        video.muted = muted

        // 取消静音时确保音量不为0
        if (!muted && video.volume === 0) {
          video.volume = 1.0
        }

        // 如果视频暂停，尝试播放
        if (!muted && video.paused) {
          video.play().catch(e => {
            this.log('取消静音时播放失败:', e.message)
          })
        }
      }
    },

    // 监听WebRTC支持状态变化
    onWebRTCSupportChanged(supported) {
      this.log('webrtc supported change: ', supported)
      // 检查WebRTC支持并通知主程序
      this.checkWebRTCSupport()
    },

    // 监听旋转角度变化
    onRotationChange(rotation) {
      this.log('视频旋转角度变化:', rotation)
      this.applyVideoRotation(rotation)
    },

    // 监听rtcUrl变化，触发WebRTC连接
    onChangeRtcUrl(val, oldVal) {
      this.log('rtc url change: ', val)

      // 如果是手动断开，跳过处理
      if (this.isManualDisconnect) {
        this.log('手动断开状态，跳过rtcUrl变化处理')
        return
      }

      if (val) {
        // 先检查WebRTC支持
        if (!this.checkWebRTCSupport()) {
          return
        }
        this.webrtcReqUrl = val
        this.initRtc()
      } else {
        // 清理WebRTC连接
        if (rtc) {
          rtc.close()
          rtc = null
        }
        if (dataChannel) {
          dataChannel.close()
          dataChannel = null
        }
        const video = document.querySelector('#pageVideoWrap video');
        if (video) {
          video.srcObject = null;
        }
      }
    },

    // 监听流状态变化
    onTrackStateChange(state) {
      this.log('track state change: ', state)
      // 可以在这里处理流状态变化的UI反馈
    },

    // 监听answer数据变化 - 改进answer处理
    onAnswerDataChanged(data) {
      this.log('answer data change: ', data)

      // 如果是手动断开，跳过处理
      if (this.isManualDisconnect) {
        this.log('手动断开状态，跳过answer数据变化处理')
        return
      }

      if (data && data.sdp && rtc) {
        this.log('处理answer SDP')

        // 检查信令状态
        if (rtc.signalingState !== 'have-local-offer') {
          console.warn('信令状态不正确，当前状态:', rtc.signalingState);
          return;
        }

        const sessionDescription = new RTCSessionDescription({
          sdp: data.sdp,
          type: 'answer',
        });

        rtc.setRemoteDescription(sessionDescription).then(() => {
          this.log('answer设置成功')
          this.$ownerInstance.callMethod('log', 'WebRTC answer设置成功', 'info')

          // 处理缓存的ICE候选
          if (this.pendingIceCandidates && this.pendingIceCandidates.length > 0) {
            this.log(`处理 ${this.pendingIceCandidates.length} 个缓存的ICE候选`);
            this.pendingIceCandidates.forEach(candidate => {
              rtc.addIceCandidate(candidate).then(() => {
                this.log('缓存的ICE候选添加成功');
              }).catch(e => {
                console.error('添加缓存的ICE候选失败:', e);
              });
            });
            this.pendingIceCandidates = [];
          }

          // answer设置成功后，检查连接状态
          setTimeout(() => {
            this.log('连接状态检查:', {
              connectionState: rtc.connectionState,
              iceConnectionState: rtc.iceConnectionState,
              signalingState: rtc.signalingState
            });
          }, 1000);
        }).catch(error => {
          console.error('设置answer失败:', error)
          this.$ownerInstance.callMethod('log', `设置answer失败: ${error.message}`, 'error')

          // 如果设置answer失败，尝试重新连接
          if (error.message.includes('InvalidStateError')) {
            this.log('状态错误，尝试重新创建连接');
            setTimeout(() => {
              this.$ownerInstance.callMethod('log', '尝试重新建立连接', 'info');
              // 可以触发重新连接逻辑
            }, 2000);
          }
        });
      }
    },

    // 监听ICE候选数据变化 - 改进ICE候选处理
    onCandidateDataChanged(data) {
      this.log('candidate data change: ', data)

      // 如果是手动断开，跳过处理
      if (this.isManualDisconnect) {
        this.log('手动断开状态，跳过candidate数据变化处理')
        return
      }

      if (data && data.candidate && rtc) {
        this.log('添加ICE候选')

        // 检查远程描述是否已设置
        if (!rtc.remoteDescription) {
          this.log('远程描述未设置，缓存ICE候选');
          // 可以实现ICE候选缓存逻辑
          if (!this.pendingIceCandidates) {
            this.pendingIceCandidates = [];
          }
          this.pendingIceCandidates.push(data.candidate);
          return;
        }

        rtc.addIceCandidate(data.candidate).then(() => {
          this.log('ICE候选添加成功')
        }).catch(error => {
          console.error('添加ICE候选失败:', error)
          this.$ownerInstance.callMethod('log', `添加ICE候选失败: ${error.message}`, 'error')

          // 如果ICE候选添加失败，记录详细信息
          this.log('失败的ICE候选信息:', {
            candidate: data.candidate.candidate,
            sdpMid: data.candidate.sdpMid,
            sdpMLineIndex: data.candidate.sdpMLineIndex
          });
        });
      }
    },

    // 监听手动播放触发
    onManualPlayTrigger(trigger) {
      this.log('manual play trigger: ', trigger)
      if (trigger > 0) {
        this.manualPlayVideo()
      }
    },

    // 监听音量级别变化
    onVolumeLevelChanged(volume) {
      this.log('volume level change: ', volume)
      const video = document.querySelector('#pageVideoWrap video');
      if (video && typeof volume === 'number' && volume >= 0 && volume <= 1) {
        video.volume = volume
        this.log('设置视频音量为:', volume)

        // 如果音量大于0，自动取消静音
        if (volume > 0 && video.muted) {
          video.muted = false
          this.log('音量大于0，自动取消静音')
          this.$ownerInstance.callMethod('setVideoMuted', false)
        }
      }
    },

    // 尝试播放视频（处理iOS限制） - 改进播放逻辑
    tryPlayVideo(video) {
      if (!video) return;

      this.log('尝试播放视频')

      // 检查视频是否有流
      if (!video.srcObject || !video.srcObject.getVideoTracks().length) {
        this.log('视频没有流，等待流设置')
        return;
      }

      // 确保视频是静音的（iOS允许静音视频自动播放）
      video.muted = true;

      // 设置更多播放相关属性
      video.autoplay = true;
      video.playsInline = true;
      video.controls = false;

      const playPromise = video.play();

      if (playPromise !== undefined) {
        playPromise.then(() => {
          this.log('视频自动播放成功')
          // 通知主程序隐藏播放按钮
          this.$ownerInstance.callMethod('setShowPlayButton', false)

          // 播放成功后，尝试优化视频质量
          this.optimizeVideoQuality(video);
        }).catch(error => {
          this.log('视频自动播放失败:', error.message)
          this.log('需要用户交互才能播放，显示播放按钮')
          // 通知主程序显示播放按钮
          this.$ownerInstance.callMethod('setShowPlayButton', true)

          // 如果是因为用户交互限制，尝试其他方法
          if (error.name === 'NotAllowedError') {
            this.log('由于浏览器策略限制，需要用户交互')
            // 可以尝试预加载
            video.load();
          }
        });
      } else {
        // 旧版浏览器，play()不返回Promise
        this.log('旧版浏览器，尝试直接播放')
        try {
          video.play();
          this.$ownerInstance.callMethod('setShowPlayButton', false)
          this.optimizeVideoQuality(video);
        } catch (error) {
          this.log('播放失败，显示播放按钮')
          this.$ownerInstance.callMethod('setShowPlayButton', true)
        }
      }
    },

    // 优化视频质量
    optimizeVideoQuality(video) {
      try {
        // 设置视频渲染优化
        if (video.style) {
          video.style.imageRendering = 'optimizeQuality';
          video.style.imageRendering = '-webkit-optimize-contrast';
        }

        // 检查视频轨道设置
        if (video.srcObject && video.srcObject.getVideoTracks().length > 0) {
          const videoTrack = video.srcObject.getVideoTracks()[0];
          this.log('视频轨道设置:', videoTrack.getSettings());

          // 如果支持，尝试设置视频轨道约束
          if (videoTrack.applyConstraints) {
            videoTrack.applyConstraints({
              width: { ideal: 1920 },
              height: { ideal: 1080 },
              frameRate: { ideal: 30 }
            }).then(() => {
              this.log('视频轨道约束应用成功');
            }).catch(e => {
              this.log('应用视频轨道约束失败:', e.message);
            });
          }
        }
      } catch (error) {
        console.warn('优化视频质量时出错:', error);
      }
    },

    // 手动播放视频（响应用户点击）
    manualPlayVideo() {
      this.log('manualPlayVideo被调用')
      const video = document.querySelector('#pageVideoWrap video');

      if (video) {
        this.log('找到video元素，当前状态:', {
          readyState: video.readyState,
          paused: video.paused,
          muted: video.muted,
          srcObject: video.srcObject ? '有流' : '无流',
          currentTime: video.currentTime,
          duration: video.duration || 'NaN'
        })

        this.log('用户手动播放视频')
        // 用户交互后可以取消静音
        video.muted = false;

        const playPromise = video.play();
        if (playPromise !== undefined) {
          playPromise.then(() => {
            this.log('手动播放成功')
            this.$ownerInstance.callMethod('setShowPlayButton', false)
            // 通知主程序更新静音状态
            this.$ownerInstance.callMethod('setVideoMuted', false)
          }).catch(error => {
            console.error('手动播放失败:', error)
            console.error('播放失败详情:', {
              name: error.name,
              message: error.message,
              code: error.code
            })
            // 如果取消静音播放失败，尝试静音播放
            this.log('尝试静音播放')
            video.muted = true;
            video.play().then(() => {
              this.log('静音播放成功')
              this.$ownerInstance.callMethod('setShowPlayButton', false)
            }).catch(err => {
              console.error('静音播放也失败:', err)
            });
          });
        } else {
          this.log('play()方法返回undefined，可能是旧版浏览器')
        }
      } else {
        console.error('找不到video元素')
        // 尝试查找所有video元素
        const allVideos = document.querySelectorAll('video')
        this.log('页面中所有video元素数量:', allVideos.length)
        allVideos.forEach((v, index) => {
          this.log(`video[${index}]:`, {
            id: v.id,
            className: v.className,
            src: v.src,
            srcObject: v.srcObject ? '有流' : '无流'
          })
        })
      }
    },

    // 修复iOS Safari中video元素的层级问题
    fixVideoLayerOnIOS(video) {
      try {
        // 检测是否为iOS设备
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

        if (isIOS) {
          this.log('检测到iOS设备，应用video层级修复')

          // 为body添加iOS标识类
          document.body.classList.add('ios-device');

          // 设置video元素样式
          video.style.position = 'relative';
          video.style.zIndex = '1';
          // video.style.webkitTransform = 'translateZ(0)';
          // video.style.transform = 'translateZ(0)';

          // 设置容器样式
          const container = video.parentElement;
          if (container) {
            container.style.position = 'relative';
            container.style.zIndex = '1';
          }

          // 通知主程序应用iOS特殊样式
          this.$ownerInstance.callMethod('applyIOSVideoFix');
        }
      } catch (error) {
        console.warn('应用iOS video层级修复时出错:', error);
      }
    },

    // 检查WebRTC支持
    checkWebRTCSupport() {
      try {
        const supported = !!(window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection)
        this.log('WebRTC支持检查结果:', supported)

        // 通知主程序WebRTC支持状态
        this.$ownerInstance.callMethod('webrtcSupportChanged', supported)

        return supported
      } catch (error) {
        console.error('检查WebRTC支持时出错:', error)
        this.$ownerInstance.callMethod('webrtcSupportChanged', false)
        return false
      }
    },

    // 应用视频旋转
    applyVideoRotation(rotation) {
      this.log('应用视频旋转:', rotation + '度')

      const video = document.querySelector('#pageVideoWrap video')
      if (!video) {
        this.log('视频元素不存在，无法应用旋转')
        return
      }

      // 清除之前的旋转样式
      video.style.transform = ''
      video.style.webkitTransform = ''

      // 根据 isLandscape 和 rotation 决定 object-fit
      const shouldUseCover = (this.isLandscape === 0) && (rotation === 0)
      video.style.objectFit = shouldUseCover ? 'cover' : 'contain'

      // 应用新的旋转角度
      if (rotation !== 0) {
        const transformValue = `rotate(${rotation}deg)`
        video.style.transform = transformValue
        video.style.webkitTransform = transformValue
        video.style.transformOrigin = 'center center'
        video.style.webkitTransformOrigin = 'center center'

        video.style.width = '100vh'

        video.style.height = '100vw'

        this.log('视频旋转样式已应用:', {
          rotation: rotation,
          transform: video.style.transform,
          transformOrigin: video.style.transformOrigin,
          objectFit: video.style.objectFit
        })
      } else {
        this.log('旋转角度为0，移除旋转样式')
      }

      this.log(`视频object-fit设置: ${video.style.objectFit} (isLandscape=${this.isLandscape}, rotation=${rotation})`)
    },

    // 初始化WebRTC连接
    initRtc() {
      try {
        this.log('开始初始化WebRTC连接')

        // 检测iOS设备
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

        // 创建RTCPeerConnection - 使用更完善的配置
        const config = {
          iceServers: [
            { urls: 'stun:************:3478' },
            // { urls: 'stun:stun1.l.google.com:19302' },
            // { urls: 'stun:stun2.l.google.com:19302' },
            // { urls: 'stun:stun3.l.google.com:19302' },
            // { urls: 'stun:stun4.l.google.com:19302' },
            // { urls: 'stun:stun.ekiga.net' },
            // { urls: 'stun:stun.ideasip.com' },
            // { urls: 'stun:stun.schlund.de' },
            // { urls: 'stun:stun.stunprotocol.org:3478' },
            // { urls: 'stun:stun.voiparound.com' },
            // { urls: 'stun:stun.voipbuster.com' },
            // { urls: 'stun:stun.voipstunt.com' },
            // { urls: 'stun:stun.voxgratia.org' },
            {
              urls: 'turn:************:3478?transport=udp',
              username: 'admin',
              credential: 'fpwe287534'
            }
			// ,
            // {
            //   urls: 'turn:turn.anyfirewall.com:443?transport=tcp',
            //   username: 'webrtc',
            //   credential: 'webrtc'
            // }
          ],
          iceCandidatePoolSize: 10,
          iceTransportPolicy: 'all',
          bundlePolicy: 'max-bundle',
          rtcpMuxPolicy: 'require',
          sdpSemantics: 'unified-plan'
        };

        // iOS特定配置
        if (isIOS) {
          config.sdpSemantics = 'unified-plan';
          config.iceCandidatePoolSize = 20;
          config.iceServers.push(
            {
              urls: 'turn:turn.anyfirewall.com:443?transport=tcp',
              username: 'webrtc',
              credential: 'webrtc'
            },
            {
              urls: 'turn:turn.anyfirewall.com:443?transport=udp',
              username: 'webrtc',
              credential: 'webrtc'
            }
          );
          config.iceGatheringTimeout = 5000;
          this.log('已应用iOS特定WebRTC配置');
        }

        rtc = new RTCPeerConnection(config);
        this.connectionStartTime = Date.now() // 记录连接开始时间
        this.log('RTCPeerConnection创建成功')

        // 监听连接状态变化
        rtc.onconnectionstatechange = () => {
          this.log('RTC连接状态变化:', rtc.connectionState)
          this.$ownerInstance.callMethod('log', `RTC连接状态: ${rtc.connectionState}`, 'info')

          if (rtc.connectionState === 'connected') {
            this.$ownerInstance.callMethod('updateStatus', '已连接')
            this.$ownerInstance.callMethod('setLoading', false)

            // 连接成功后开始收集统计信息
            this.log('WebRTC连接成功，开始收集统计信息')
            this.startStatsCollection()

            // 开始流状态监控
            this.startStreamMonitoring()

            // 更新可用连接数（基于连接成功状态）
            this.updateAvailableConnectionsBasedOnICE()
          } else if (rtc.connectionState === 'failed') {
            if (!this.isManualDisconnect) {
              this.$ownerInstance.callMethod('updateStatus', '连接失败', true)
              this.$ownerInstance.callMethod('setLoading', false)
            } else {
              this.log('手动断开状态，跳过连接失败处理')
            }

            // 连接失败时停止收集统计信息
            this.stopStatsCollection()
          } else if (rtc.connectionState === 'disconnected') {
            if (!this.isManualDisconnect) {
              this.log('WebRTC连接断开，尝试重新连接')
              this.$ownerInstance.callMethod('updateStatus', '连接断开，尝试重新连接', true)

              // 延迟重连，避免频繁重连
              setTimeout(() => {
                if (rtc && rtc.connectionState === 'disconnected' && !this.isManualDisconnect) {
                  this.log('开始重新连接WebRTC')
                  this.internalReconnectWebRTC()
                }
              }, 2000)
            } else {
              this.log('手动断开连接，跳过自动重连')
            }
          }
        }

        rtc.oniceconnectionstatechange = () => {
          this.log('ICE连接状态变化:', rtc.iceConnectionState)
          this.$ownerInstance.callMethod('log', `ICE连接状态: ${rtc.iceConnectionState}`, 'info')

          // 基于ICE连接状态更新可用连接数
          this.updateAvailableConnectionsBasedOnICE()
        }

        // 监听ICE候选
        rtc.onicecandidate = (event) => {
          if (event.candidate) {
            this.log('生成ICE候选')
            // 通知主程序发送ICE候选到信令服务器
            const senderId = this.webrtcReqUrl.split('/').pop()
            this.$ownerInstance.callMethod('sendCandidateToSignaling', {
              senderId: senderId,
              candidate: event.candidate
            });
          }
        }

        // 添加收发器并设置编解码器偏好
        try {
          // 添加视频收发器
          const videoTransceiver = rtc.addTransceiver('video', {
            direction: 'recvonly',
          });

          // 设置编解码器偏好，优先使用H.264
          try {
            if (videoTransceiver.setCodecPreferences) {
              const codecs = RTCRtpReceiver.getCapabilities('video').codecs;
              this.log(`支持的视频编解码器: ${codecs.map(c => c.mimeType).join(', ')}`);

              // 找到H.264编解码器并优先排序
              const h264Codecs = codecs.filter(codec => codec.mimeType.includes('H264'));
              const vp8Codecs = codecs.filter(codec => codec.mimeType.includes('VP8'));
              const vp9Codecs = codecs.filter(codec => codec.mimeType.includes('VP9'));
              const otherCodecs = codecs.filter(codec =>
                !codec.mimeType.includes('H264') &&
                !codec.mimeType.includes('VP8') &&
                !codec.mimeType.includes('VP9')
              );

              // 重新排序编解码器，使H.264优先
              const reorderedCodecs = [...h264Codecs, ...vp8Codecs, ...vp9Codecs, ...otherCodecs];
              videoTransceiver.setCodecPreferences(reorderedCodecs);
              this.log('已设置视频编解码器偏好: H.264优先');
            } else {
              this.log('当前浏览器不支持setCodecPreferences方法');
            }
          } catch (e) {
            this.log(`设置编解码器偏好失败: ${e.message}`);
          }
        } catch (e) {
          this.log(`添加视频收发器失败: ${e.message}`);
        }

        // 添加音频收发器
        try {
          rtc.addTransceiver('audio', {
            direction: 'recvonly',
          });
          this.log('已添加音频收发器');
        } catch (e) {
          this.log(`添加音频收发器失败: ${e.message}`);
        }

        // 创建或获取video元素 - 改进video元素设置
        let video = document.querySelector('#pageVideoWrap video');
        if (!video) {
          video = document.createElement('video')
          video.style.width = '100%'
          video.style.height = '100%'

          // 根据 isLandscape 和 rotation 决定 object-fit
          // 只有当 isLandscape=0 且 rotation=0 时使用 cover，其他情况使用 contain
          const shouldUseCover = (this.isLandscape === 0) && (this.rotation === 0)
          video.style.objectFit = shouldUseCover ? 'cover' : 'contain'
          video.style.backgroundColor = '#000'

          this.log(`视频object-fit设置: ${video.style.objectFit} (isLandscape=${this.isLandscape}, rotation=${this.rotation})`)
          document.querySelector('#pageVideoWrap').appendChild(video)

          // 应用旋转角度（从父组件传入）
          const container = document.querySelector('#pageVideoWrap')
          const rotation = container ? container.getAttribute('rotation') : 0
          if (rotation && rotation !== '0') {
            this.applyVideoRotation(parseInt(rotation))
          }
        }

        // 设置video元素属性以提高性能和兼容性
        video.style.background = 'none'
        video.setAttribute('src', '');
        video.setAttribute("webkit-playsinline", "true");
        video.setAttribute("playsinline", "true");
        video.setAttribute("autoplay", "true");
        video.setAttribute("muted", "true");
        video.setAttribute("preload", "auto");
        video.setAttribute("x-webkit-airplay", "allow");

        // iOS Safari兼容性处理
        video.setAttribute("controls", "false");
        video.muted = true; // 确保静音，iOS允许静音视频自动播放
        video.autoplay = true;
        video.playsInline = true;

        // 设置视频渲染优化
        video.style.imageRendering = 'optimizeQuality';
        video.style.imageRendering = '-webkit-optimize-contrast';

        // 硬件加速
        // video.style.transform = 'translateZ(0)';
        // video.style.webkitTransform = 'translateZ(0)';

        // iOS Safari video层级问题修复
        this.fixVideoLayerOnIOS(video);

        // 不在这里直接调用play()，而是在收到流后尝试播放
        video.onloadedmetadata = () => {
          this.log('视频元数据加载完成')
          this.log('视频尺寸:', {
            videoWidth: video.videoWidth,
            videoHeight: video.videoHeight,
            duration: video.duration
          })
          this.tryPlayVideo(video);
        };

        // 监听远程流 - 改进流处理逻辑
        let streamSet = false; // 防止重复设置流
        rtc.ontrack = (t) => {
          this.log('收到远程流，轨道类型:', t.track.kind)

          // 只在第一次收到视频轨道时设置srcObject
          if (t.track.kind === 'video' && !streamSet) {
            this.log('设置视频流到video元素')
            this.log('视频流信息:', {
              streamId: t.streams[0].id,
              trackCount: t.streams[0].getTracks().length,
              videoTracks: t.streams[0].getVideoTracks().length,
              audioTracks: t.streams[0].getAudioTracks().length
            })

            video.srcObject = t.streams[0]
            streamSet = true

            // 添加更多事件监听
            video.onloadstart = () => this.log('视频开始加载')
            video.onloadeddata = () => {
              this.log('视频数据加载完成')
              // 数据加载完成后立即尝试播放
              this.tryPlayVideo(video);
            }
            video.oncanplay = () => {
              this.log('视频可以播放')
              // 确保视频可以播放时再次尝试
              this.tryPlayVideo(video);
            }
            video.oncanplaythrough = () => this.log('视频可以流畅播放')
            video.onerror = (e) => {
              console.error('视频加载错误:', e)
              // 视频加载错误时，尝试重新设置流
              setTimeout(() => {
                if (t.streams[0] && video) {
                  this.log('重新设置视频流')
                  video.srcObject = t.streams[0]
                  this.tryPlayVideo(video);
                }
              }, 1000);
            }

            // 检查视频元素当前状态
            this.log('设置srcObject后的视频状态:', {
              readyState: video.readyState,
              networkState: video.networkState,
              paused: video.paused,
              muted: video.muted,
              autoplay: video.autoplay,
              srcObject: video.srcObject ? '已设置' : '未设置'
            })

            // 立即尝试播放视频
            this.tryPlayVideo(video);
          }

          // 监听流状态 - 延迟检测，避免初始连接时误报
          t.receiver.track.onmute = (event) => {
            // 检查是否手动断开
            if (this.isManualDisconnect) {
              this.log('手动断开状态，跳过流中断处理')
              return
            }

            // 连接建立后5秒内不处理mute事件，避免初始化时的误报
            const connectionTime = Date.now() - this.connectionStartTime
            if (connectionTime < 5000) {
              this.log('连接建立不足5秒，忽略流中断事件')
              return
            }

            this.log('流中断:', JSON.stringify(event));
            this.log('流中断详情:', {
              trackKind: t.receiver.track.kind,
              trackId: t.receiver.track.id,
              trackEnabled: t.receiver.track.enabled,
              trackMuted: t.receiver.track.muted,
              trackReadyState: t.receiver.track.readyState,
              connectionState: rtc.connectionState,
              iceConnectionState: rtc.iceConnectionState,
              connectionTime: connectionTime
            });

            // 记录中断统计
            this.trackMuteCount++
            this.lastTrackMuteTime = Date.now()

            // 通知主程序
            this.$ownerInstance.callMethod('trackChange', true)

            // 如果是视频轨道中断，尝试恢复
            if (t.receiver.track.kind === 'video') {
              this.log('视频轨道中断，尝试恢复')
              this.handleVideoTrackMute(t.receiver.track)
            }
          };
          t.receiver.track.onunmute = (err) => {
            this.log('流继续:', JSON.stringify(err));
            this.log('流恢复详情:', {
              trackKind: t.receiver.track.kind,
              trackId: t.receiver.track.id,
              trackEnabled: t.receiver.track.enabled,
              trackMuted: t.receiver.track.muted,
              trackReadyState: t.receiver.track.readyState
            });
            this.$ownerInstance.callMethod('trackChange', false)
          };
        };

        // 创建数据通道
        dataChannel = rtc.createDataChannel('control')
        dataChannel.onopen = () => {
          this.log('数据通道已打开')
          dataChannel.send('Hello from renderjs receiver!')
        }
        dataChannel.onmessage = (event) => {
          this.log('收到数据通道消息:', event.data)
          // 通知主程序处理数据通道消息
          this.$ownerInstance.callMethod('handleDataChannelMessage', event.data)
        }
        dataChannel.onclose = () => {
          this.log('数据通道已关闭')
        }

        // 创建offer并发送 - 添加更多选项以提高兼容性
        this.log('开始创建offer')
        rtc.createOffer({
          offerToReceiveAudio: true,
          offerToReceiveVideo: true,
          iceRestart: true
        }).then((localDescription) => {
          this.log('offer创建成功，SDP类型:', localDescription.type)
          this.log('offer SDP长度:', localDescription.sdp ? localDescription.sdp.length : 0)

          return rtc.setLocalDescription(localDescription)
        }).then(() => {
          this.log('本地描述设置成功')
          // 通过信令服务器发送offer
          const sdp = rtc.localDescription?.toJSON().sdp || rtc.localDescription?.sdp
          this.log('准备发送的SDP:', sdp ? '有内容' : '空')

          // 通过信令服务器发送offer
          const senderId = this.webrtcReqUrl.split('/').pop() // 从URL中提取senderId
          this.$ownerInstance.callMethod('sendOfferToSignaling', {
            senderId: senderId,
            sdp: sdp
          });

          // 设置连接超时处理
          const connectionTimeoutId = setTimeout(() => {
            // 检查是否手动断开
            if (this.isManualDisconnect) {
              this.log('手动断开状态，跳过连接超时处理')
              return
            }

            if (rtc && rtc.connectionState !== 'connected') {
              this.log('连接超时，尝试重新连接');
              this.$ownerInstance.callMethod('log', '连接超时，尝试重新连接', 'error');

              // 尝试ICE重启
              if (rtc.iceConnectionState !== 'connected' && rtc.iceConnectionState !== 'completed') {
                this.log('尝试ICE重启');
                rtc.createOffer({ iceRestart: true }).then(offer => {
                  return rtc.setLocalDescription(offer);
                }).then(() => {
                  const senderId = this.webrtcReqUrl.split('/').pop();
                  this.$ownerInstance.callMethod('sendOfferToSignaling', {
                    senderId: senderId,
                    sdp: rtc.localDescription.sdp
                  });
                }).catch(e => {
                  console.error('ICE重启失败:', e);
                });
              }
            }
          }, 30000); // 30秒超时

          // 保存定时器ID，以便清理
          this.connectionTimeoutId = connectionTimeoutId
        }).catch(error => {
          console.error('创建或设置offer失败:', error)
          this.$ownerInstance.callMethod('log', `创建offer失败: ${error.message}`, 'error')
        });

      } catch (error) {
        console.error('初始化WebRTC失败:', error)
        this.$ownerInstance.callMethod('log', `初始化WebRTC失败: ${error.message}`, 'error')
      }
    },

    // 通过HTTP发送offer（参考掘金文章方法）
    sendOfferViaHttp(sdp) {
      this.log('开始发送offer到:', this.webrtcReqUrl)
      this.log('SDP内容长度:', sdp ? sdp.length : 0)

      // 使用fetch发送SDP
      fetch(this.webrtcReqUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/sdp'
        },
        body: sdp
      }).then(response => {
        this.log('HTTP响应状态:', response.status, response.statusText)
        this.log('HTTP响应头:', JSON.stringify([...response.headers.entries()]))

        if (response.ok) {
          return response.text()
        } else {
          throw new Error(`HTTP请求失败: ${response.status} ${response.statusText}`)
        }
      }).then(answerSdp => {
        this.log('收到answer SDP长度:', answerSdp ? answerSdp.length : 0)
        if (answerSdp && rtc) {
          this.log('设置远程描述(answer)')
          const sessionDescription = new RTCSessionDescription({
            sdp: answerSdp,
            type: 'answer',
          });
          rtc.setRemoteDescription(sessionDescription).then(() => {
            this.log('远程描述设置成功')
            // 通知主程序连接状态
            this.$ownerInstance.callMethod('log', 'WebRTC连接建立中...', 'info')
          }).catch(error => {
            console.error('设置远程描述失败:', error)
            this.$ownerInstance.callMethod('log', `设置远程描述失败: ${error.message}`, 'error')
          });
        } else {
          console.warn('未收到有效的answer SDP或RTC连接已关闭')
        }
      }).catch(error => {
        console.error('发送offer失败:', error)
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          url: this.webrtcReqUrl
        })
        // 通知主程序错误
        this.$ownerInstance.callMethod('log', `发送offer失败: ${error.message}`, 'error')
      })
    },

    // 开始收集统计信息
    startStatsCollection() {
      if (!rtc) return

      this.log('开始收集视频统计信息')

      // 清除之前的定时器
      this.stopStatsCollection()

      // 每秒收集一次统计信息
      this.statsInterval = setInterval(() => {
        this.collectVideoStats()
        this.collectConnectionStats() // 同时收集连接统计
      }, 1000)
    },

    // 停止收集统计信息
    stopStatsCollection() {
      if (this.statsInterval) {
        clearInterval(this.statsInterval)
        this.statsInterval = null
        this.log('停止收集视频统计信息')
      }
    },

    // 收集真实的连接统计信息
    collectConnectionStats() {
      if (!rtc || rtc.connectionState === 'closed') {
        return
      }

      try {
        rtc.getStats(null).then(stats => {
          let connectionType = 'H' // 默认本地
          let localCandidates = []
          let remoteCandidates = []
          let candidatePairs = []

          stats.forEach(report => {
            if (report.type === 'local-candidate') {
              localCandidates.push(report)
            } else if (report.type === 'remote-candidate') {
              remoteCandidates.push(report)
            } else if (report.type === 'candidate-pair' && report.state === 'succeeded' && report.nominated) {
              candidatePairs.push(report)
            }
          })


          // 只打印新出现的唯一活跃连接（nominated）
          if (!this._lastActivePairs) this._lastActivePairs = new Set();
          const uniqueSet = new Set();
          let newPairs = [];
          let currentPairStr = '';
          let currentPairKey = '';
          let allPairStrs = [];
          candidatePairs.forEach(pair => {
            // 只处理connected且nominated的pair
            if (pair.state === 'succeeded' && pair.nominated) {
              const local = localCandidates.find(c => c.id === pair.localCandidateId)
              const remote = remoteCandidates.find(c => c.id === pair.remoteCandidateId)
              if (local && remote) {
                // 判断连接类型
                let type = 'H';
                if (local.candidateType === 'host' && remote.candidateType === 'host') {
                  type = '本地';
                } else if (local.candidateType === 'relay' || remote.candidateType === 'relay') {
                  type = '中继';
                } else if (local.candidateType === 'srflx' || remote.candidateType === 'srflx') {
                  type = '穿透';
                }
                // 区分本地/远端ip:port
                const localStr = `本地: ${local.address || local.ip || ''}:${local.port || local.networkPort || ''}`;
                const remoteStr = `远端: ${remote.address || remote.ip || ''}:${remote.port || remote.networkPort || ''}`;
                const pairStr = `${localStr} <-> ${remoteStr} [${type}]`;
                allPairStrs.push(pairStr);
                uniqueSet.add(pairStr);
                if (!this._lastActivePairs.has(pairStr)) {
                  newPairs.push(pairStr);
                }
                // 标记当前用的连接（第一个nominated的pair）
                if (!currentPairStr) {
                  currentPairStr = pairStr + '   <== 当前使用';
                  currentPairKey = pairStr;
                }
              }
            }
          });
          // 只在有新连接或当前连接变化时打印
          let shouldPrint = false;
          if (newPairs.length > 0) shouldPrint = true;
          if (!this._lastCurrentPairKey || this._lastCurrentPairKey !== currentPairKey) shouldPrint = true;
          if (shouldPrint && uniqueSet.size > 0) {
            this.log('活跃连接ip:port如下:');
            allPairStrs.forEach(str => {
              if (str === currentPairKey) {
                this.log(currentPairStr);
              } else {
                this.log(str);
              }
            });
          }
          // 更新已打印集合和当前连接
          this._lastActivePairs = uniqueSet;
          this._lastCurrentPairKey = currentPairKey;
          // 可用数为所有nominated的pair数
          const availableCount = allPairStrs.length;

          // 分析连接类型
          const activePair = candidatePairs.find(pair => pair.nominated)
          if (activePair) {
            const localCandidate = localCandidates.find(c => c.id === activePair.localCandidateId)
            const remoteCandidate = remoteCandidates.find(c => c.id === activePair.remoteCandidateId)

            if (localCandidate && remoteCandidate) {
              // 判断连接类型
              if (localCandidate.candidateType === 'host' && remoteCandidate.candidateType === 'host') {
                connectionType = 'H' // 本地连接
              } else if (localCandidate.candidateType === 'srflx' || remoteCandidate.candidateType === 'srflx') {
                connectionType = 'S' // STUN穿透
              } else if (localCandidate.candidateType === 'relay' || remoteCandidate.candidateType === 'relay') {
                connectionType = 'T' // TURN中继
              }

              // 只在内容变化时打印
              const typeStr = `连接类型分析: 本地=${localCandidate.candidateType}, 远程=${remoteCandidate.candidateType}, 类型=${connectionType}`;
              if (!this._lastTypeStr || this._lastTypeStr !== typeStr) {
                this.log(typeStr);
                this._lastTypeStr = typeStr;
              }
            }
          }

          // 更新连接信息到主组件（但不允许将连接数设置为0，除非ICE连接真的断开）
          let finalAvailableCount = availableCount
          if (rtc.iceConnectionState === 'connected' || rtc.iceConnectionState === 'completed') {
            finalAvailableCount = Math.max(1, availableCount) // 连接成功时至少显示1个
          }

          // 确保参数是基本类型，避免 renderjs 通信问题
          const safeConnectionType = String(connectionType || 'H')
          const safeAvailableCount = Number(finalAvailableCount) || 0

          // 只在连接信息变化时打印日志
          const statsKey = `${rtc.iceConnectionState}-${availableCount}-${finalAvailableCount}`
          if (!this._lastStatsKey || this._lastStatsKey !== statsKey) {
            this.log(`连接统计更新: ICE状态=${rtc.iceConnectionState}, 原始数量=${availableCount}, 最终数量=${finalAvailableCount}`)
            this.log(`准备传递参数: connectionType=${safeConnectionType}, availableCount=${safeAvailableCount}`)
            this._lastStatsKey = statsKey
          }

          // 直接通过 uni.$emit 发送事件，更稳定
          uni.$emit('updateConnectionInfo', {
            type: safeConnectionType,
            count: safeAvailableCount
          })
        }).catch(error => {
          console.warn('获取连接统计失败:', error)
        })
      } catch (error) {
        console.warn('收集连接统计时出错:', error)
      }
    },

    // 获取真实连接统计（供主组件调用）
    getRealConnectionStats() {
      this.collectConnectionStats()
    },

    // 基于ICE连接状态更新可用连接数
    updateAvailableConnectionsBasedOnICE() {
      if (!rtc) {
        // 没有rtc连接时，不强制设置为0，保持当前状态
        this.log('没有rtc连接，跳过ICE状态更新')
        return
      }

      let connectionType = 'H'
      let availableConnections = 0

      // 根据ICE连接状态确定可用连接数（但不直接覆盖，而是基于实际统计）
      switch (rtc.iceConnectionState) {
        case 'connected':
        case 'completed':
          // 连接成功时，保持当前的连接数，如果为0则设置为1
          availableConnections = Math.max(1, this.availableConnections || 1)
          this.log(`ICE连接成功，保持可用连接数: ${availableConnections}`)
          break
        case 'checking':
          // 连接建立中，保持当前连接数，如果为0则显示1（表示正在建立）
          availableConnections = Math.max(1, this.availableConnections || 0)
          this.log(`ICE连接检查中，可用连接数: ${availableConnections}`)
          break
        case 'new':
          // 新连接，保持当前连接数（可能来自信令服务器统计）
          availableConnections = this.availableConnections || 0
          break
        case 'disconnected':
          // 临时断开，保留当前数量但减少1
          availableConnections = Math.max(0, (this.availableConnections || 1) - 1)
          break
        case 'failed':
        case 'closed':
        default:
          // 连接失败或关闭，只有在确实失败时才设置为0
          availableConnections = 0
          this.log(`ICE连接失败/关闭，连接数设置为0`)
          break
      }

      // 检查ICE连接状态
      if (rtc.iceConnectionState === 'connected' || rtc.iceConnectionState === 'completed') {
        // 先立即更新连接数（确保界面显示正确）
        this.log(`ICE连接成功，立即更新连接数: ${availableConnections}`)
        const safeType = String(connectionType || 'H')
        const safeCount = Number(availableConnections) || 0
        this.log(`传递安全参数: type=${safeType}, count=${safeCount}`)
        uni.$emit('updateConnectionInfo', {
          type: safeType,
          count: safeCount
        })

        // 通过getStats获取详细连接信息
        rtc.getStats(null).then(stats => {
          let localCandidates = []
          let remoteCandidates = []
          let candidatePairs = []

          stats.forEach(report => {
            if (report.type === 'local-candidate') {
              localCandidates.push(report)
            } else if (report.type === 'remote-candidate') {
              remoteCandidates.push(report)
            } else if (report.type === 'candidate-pair' && report.state === 'succeeded') {
              candidatePairs.push(report)
            }
          })

          // 分析连接类型
          const activePair = candidatePairs.find(pair => pair.nominated)
          if (activePair) {
            const localCandidate = localCandidates.find(c => c.id === activePair.localCandidateId)
            const remoteCandidate = remoteCandidates.find(c => c.id === activePair.remoteCandidateId)

            if (localCandidate && remoteCandidate) {
              // 判断连接类型
              if (localCandidate.candidateType === 'host' && remoteCandidate.candidateType === 'host') {
                connectionType = 'H' // 本地连接
              } else if (localCandidate.candidateType === 'srflx' || remoteCandidate.candidateType === 'srflx') {
                connectionType = 'S' // STUN穿透
              } else if (localCandidate.candidateType === 'relay' || remoteCandidate.candidateType === 'relay') {
                connectionType = 'T' // TURN中继
              }

              this.log(`ICE连接分析: 本地=${localCandidate.candidateType}, 远程=${remoteCandidate.candidateType}, 类型=${connectionType}`)
            }
          }

          // 更新连接信息到主组件
          this.log(`ICE分析完成，更新连接信息: 类型=${connectionType}, 可用连接=${availableConnections}`)
          const safeType1 = String(connectionType || 'H')
          const safeCount1 = Number(availableConnections) || 0
          uni.$emit('updateConnectionInfo', {
            type: safeType1,
            count: safeCount1
          })
        }).catch(error => {
          this.log('获取ICE统计失败:', error)
          // 即使获取统计失败，但ICE已连接，仍然算作1个可用连接
          const safeType2 = String(connectionType || 'H')
          const safeCount2 = Number(availableConnections) || 0
          uni.$emit('updateConnectionInfo', {
            type: safeType2,
            count: safeCount2
          })
        })
      } else {
        // ICE未连接状态，直接更新
        const safeType3 = String(connectionType || 'H')
        const safeCount3 = Number(availableConnections) || 0
        uni.$emit('updateConnectionInfo', {
          type: safeType3,
          count: safeCount3
        })
        this.log(`ICE连接状态: ${rtc.iceConnectionState}, 可用连接数: ${availableConnections}`)
      }
    },

    // 收集视频统计信息
    async collectVideoStats() {
      if (!rtc) {
        this.log('rtc对象不存在，停止统计信息收集')
        this.stopStatsCollection() // 自动停止定时器
        return
      }

      try {
        const stats = await rtc.getStats()
        let videoStats = {
          fps: 0,
          bitrate: 0,
          resolution: '0x0',
          packetsLost: 0,
          packetsReceived: 0,
          bytesReceived: 0,
          timestamp: Date.now()
        }

        let lastBytesReceived = this.lastBytesReceived || 0
        let lastTimestamp = this.lastTimestamp || Date.now()
        let foundVideoStats = false

        stats.forEach(report => {
          if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
            foundVideoStats = true

            // 视频接收统计
            videoStats.packetsReceived = report.packetsReceived || 0
            videoStats.packetsLost = report.packetsLost || 0
            videoStats.bytesReceived = report.bytesReceived || 0
            videoStats.fps = report.framesPerSecond || 0

            // 计算比特率 (kbps)
            if (lastBytesReceived > 0) {
              const timeDiff = (videoStats.timestamp - lastTimestamp) / 1000 // 秒
              const bytesDiff = videoStats.bytesReceived - lastBytesReceived
              videoStats.bitrate = Math.round((bytesDiff * 8) / (timeDiff * 1000)) // kbps
            }

            this.lastBytesReceived = videoStats.bytesReceived
            this.lastTimestamp = videoStats.timestamp
          }

          if (report.type === 'track' && report.kind === 'video') {
            // 视频轨道信息
            videoStats.fps = report.framesPerSecond || videoStats.fps
            if (report.frameWidth && report.frameHeight) {
              videoStats.resolution = `${report.frameWidth}x${report.frameHeight}`
            }
          }
        })

        // 只有当有真实数据时才发送统计信息
        if (foundVideoStats || videoStats.fps > 0 || videoStats.bitrate > 0) {
          this.$ownerInstance.callMethod('updateVideoStats', videoStats)
        }

      } catch (error) {
        console.warn('收集视频统计信息失败:', error)
      }
    },

    // 处理视频轨道中断
    handleVideoTrackMute(track) {
      // 检查是否手动断开
      if (this.isManualDisconnect) {
        this.log('手动断开状态，跳过视频轨道中断处理')
        return
      }

      this.log('处理视频轨道中断，中断次数:', this.trackMuteCount)

      // 检查连接时长，如果连接时间太短，可能是Android端还没开始发送视频
      const connectionTime = Date.now() - this.connectionStartTime
      if (connectionTime < 10000) { // 10秒内
        this.log(`连接时间较短(${connectionTime}ms)，可能是发送端还未开始视频传输，等待中...`)
        this.$ownerInstance.callMethod('updateStatus', '等待视频流...')
        return
      }

      // 检查是否频繁中断
      const now = Date.now()
      const timeSinceLastMute = now - this.lastTrackMuteTime

      if (this.trackMuteCount > 3 && timeSinceLastMute < 10000) {
        this.log('检测到频繁流中断，可能需要重新连接')
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++
          this.log(`尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
          setTimeout(() => {
            this.internalReconnectWebRTC()
          }, 2000)
        } else {
          this.log('重连次数已达上限，停止重连')
          this.$ownerInstance.callMethod('updateStatus', '连接不稳定，已停止重连', true)
        }
        return
      }

      // 检查轨道状态
      if (track.readyState === 'ended') {
        this.log('视频轨道已结束，需要重新连接')
        setTimeout(() => {
          this.internalReconnectWebRTC()
        }, 1000)
        return
      }

      // 尝试重新启用轨道
      if (!track.enabled) {
        this.log('尝试重新启用视频轨道')
        track.enabled = true
      }

      // 检查video元素状态
      const video = document.querySelector('#pageVideoWrap video')
      if (video) {
        this.log('检查video元素状态:', {
          paused: video.paused,
          ended: video.ended,
          readyState: video.readyState,
          networkState: video.networkState,
          srcObject: video.srcObject ? '有流' : '无流'
        })

        // 如果video暂停了，尝试重新播放
        if (video.paused && !video.ended) {
          this.log('video暂停了，尝试重新播放')
          this.tryPlayVideo(video)
        }

        // 如果video没有流，尝试重新设置流
        if (!video.srcObject && rtc) {
          this.log('video没有流，检查WebRTC连接状态')
          const receivers = rtc.getReceivers()
          const videoReceiver = receivers.find(r => r.track && r.track.kind === 'video')
          if (videoReceiver && videoReceiver.track) {
            this.log('找到视频接收器，尝试重新设置流')
            const stream = new MediaStream([videoReceiver.track])
            video.srcObject = stream
            this.tryPlayVideo(video)
          }
        }
      }
    },

    // 内部重新连接WebRTC
    internalReconnectWebRTC() {
      // 检查是否手动断开
      if (this.isManualDisconnect) {
        this.log('手动断开连接，跳过WebRTC重连')
        return
      }

      this.log('开始重新连接WebRTC')

      if (rtc) {
        this.log('关闭现有WebRTC连接')
        rtc.close()
        rtc = null
      }

      if (dataChannel) {
        dataChannel.close()
        dataChannel = null
      }

      // 清理video元素
      const video = document.querySelector('#pageVideoWrap video')
      if (video) {
        video.srcObject = null
      }

      // 重新初始化WebRTC
      setTimeout(() => {
        this.log('重新初始化WebRTC连接')
        this.initRtc()
      }, 1000)
    },

    // 开始流状态监控
    startStreamMonitoring() {
      this.log('开始流状态监控')

      // 每30秒检查一次流状态（减少频率）
      this.streamMonitorInterval = setInterval(() => {
        this.checkStreamHealth()
      }, 30000)
    },

    // 停止流状态监控
    stopStreamMonitoring() {
      if (this.streamMonitorInterval) {
        clearInterval(this.streamMonitorInterval)
        this.streamMonitorInterval = null
        this.log('停止流状态监控')
      }
    },

    // 检查流健康状态
    checkStreamHealth() {
      if (!rtc) return

      try {
        const video = document.querySelector('#pageVideoWrap video')
        if (!video) return

        // 检查video元素状态
        const videoStatus = {
          hasStream: !!video.srcObject,
          isPlaying: !video.paused && !video.ended,
          readyState: video.readyState,
          networkState: video.networkState,
          currentTime: video.currentTime
        }

        // 检查WebRTC连接状态
        const rtcStatus = {
          connectionState: rtc.connectionState,
          iceConnectionState: rtc.iceConnectionState,
          signalingState: rtc.signalingState
        }

        // 检查连接状态变化和问题
        const now = Date.now()
        const currentConnectionState = `${rtc.connectionState}-${rtc.iceConnectionState}`
        const hasIssue = rtc.connectionState !== 'connected' ||
                         rtc.iceConnectionState !== 'connected' ||
                         (video.srcObject && video.paused)
        const stateChanged = currentConnectionState !== this.lastConnectionState

        // 只在状态变化、有问题或超过2分钟时输出日志
        if (hasIssue || stateChanged || (now - this.lastHealthCheckTime > 120000)) {
          this.log('流健康检查:', { videoStatus, rtcStatus })
          this.lastHealthCheckTime = now
        }

        this.lastConnectionState = currentConnectionState

        // 如果连接正常但video没有播放，尝试恢复
        if (rtc.connectionState === 'connected' &&
            rtc.iceConnectionState === 'connected' &&
            video.srcObject &&
            video.paused) {
          this.log('检测到流暂停，尝试恢复播放')
          this.tryPlayVideo(video)
        }

        // 如果连接断开，尝试重连（仅在非手动断开时）
        if (!this.isManualDisconnect &&
            (rtc.connectionState === 'disconnected' ||
            rtc.iceConnectionState === 'disconnected')) {
          this.log('检测到连接断开，准备重连')
          this.internalReconnectWebRTC()
        }

      } catch (error) {
        console.warn('流健康检查失败:', error)
      }
    }
  },

  mounted() {
    this.log('renderjs mounted')
    // 检查WebRTC支持并通知主程序
    this.checkWebRTCSupport()

    // 监听来自主组件的事件
    if (typeof uni !== 'undefined') {
      uni.$on('getRealConnectionStats', () => {
        this.getRealConnectionStats()
      })
    }
  },

  beforeDestroy() {
    this.log('组件销毁，清理资源')
    // 清理统计信息收集
    this.stopStatsCollection()
    // 清理流状态监控
    this.stopStreamMonitoring()
    // 清理WebSocket连接
    this.cleanup()

    // 清理事件监听
    if (typeof uni !== 'undefined') {
      uni.$off('getRealConnectionStats')
    }
  }
}
</script>

<style scoped>
.heiqi-player-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-wrap {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.video-wrap video {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 默认使用 contain，由 JavaScript 动态设置 */
  background-color: #000;
  /* iOS Safari video层级修复 */
  /* -webkit-transform: translateZ(0); */
  /* transform: translateZ(0); */
  position: relative;
  z-index: 1;
  /* 旋转变换的基础设置 */
  transform-origin: center center;
  -webkit-transform-origin: center center;
}



.fullscreen-btn {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.fullscreen-btn svg {
  width: 24px;
  height: 24px;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  border-radius: 20px;
  display: none;
}

.loading-indicator.active {
  display: block;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.video-container:hover .video-controls,
.video-controls:hover,
.video-controls.active {
  opacity: 1;
}

.control-btn {
  background: transparent;
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  cursor: pointer;
  padding: 8px;
  margin-right: 10px;
  border-radius: 50%;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.control-btn svg {
  width: 24px;
  height: 24px;
}

.seek-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.seek-slider {
  width: 100%;
  height: 4px;
  -webkit-appearance: none;
  appearance: none;
  background: rgba(255, 255, 255, 0.3);
  outline: none;
  border-radius: 2px;
  margin-bottom: 5px;
}

.seek-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
}

.time-display {
  color: white;
  font-size: 12px;
  text-align: right;
}

.controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 15px 0;
}

.input-group {
  display: flex;
  flex-direction: row;
  gap: 5px;
}

.input-field {
  flex: 1;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
}

.button-group {
  display: flex;
  gap: 10px;
}

.button-group button {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.connect-btn {
  background-color: #4CAF50;
  color: white;
}

.disconnect-btn {
  background-color: #f44336;
  color: white;
}

.reconnect-btn {
  background-color: #2196F3;
  color: white;
}

.connect-btn:disabled,
.disconnect-btn:disabled,
.reconnect-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.status {
  margin: 10px 0;
  padding: 10px;
  border-radius: 8px;
  background-color: #f8f8f8;
  border-left: 4px solid #4CAF50;
  font-size: 14px;
}

.status.error {
  border-left-color: #f44336;
  color: #f44336;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: auto;
  background-color: transparent;
}

.play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999999;
  background: rgba(0,0,0,0.8);
  color: #fff;
  border: none;
  border-radius: 50px;
  padding: 20px 40px;
  font-size: 22px;
  font-weight: bold;
  cursor: pointer;
  outline: none;
  box-shadow: 0 4px 16px rgba(0,0,0,0.4);
  transition: all 0.3s;
  -webkit-transform: translate(-50%, -50%);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.play-btn:hover {
  background: rgba(76,175,80,0.9);
  transform: translate(-50%, -50%) scale(1.05);
  -webkit-transform: translate(-50%, -50%) scale(1.05);
}

.play-btn:active {
  transform: translate(-50%, -50%) scale(0.95);
  -webkit-transform: translate(-50%, -50%) scale(0.95);
}

/* 播放按钮内部元素 */
.play-icon {
  font-size: 28px;
  margin-bottom: 5px;
  display: block;
  line-height: 1;
}

.play-text {
  font-size: 16px;
  display: block;
  line-height: 1;
}

/* iOS设备上的播放按钮特殊处理 */
.ios-device .play-btn {
  position: absolute !important;
  z-index: 999999 !important;
  -webkit-transform: translate(-50%, -50%) !important;
  transform: translate(-50%, -50%) !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
}

.not-supported {
  width: 100%;
  height: 100%;
  color: #fff;
  background: #222;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.not-supported-content {
  text-align: center;
  padding: 40px 20px;
}

.not-supported-title {
  font-size: 20px;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 10px;
  display: block;
}

.not-supported-desc {
  font-size: 16px;
  color: #ccc;
  margin-bottom: 8px;
  display: block;
}

.not-supported-suggestion {
  font-size: 14px;
  color: #4CAF50;
  margin-bottom: 20px;
  display: block;
}

.switch-player-btn {
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s;
}

.switch-player-btn:hover {
  background: #45a049;
}

/* iOS Safari video层级问题修复 */
.video-overlay-fix {
  position: relative;
  z-index: 999999 !important;
  /* -webkit-transform: translateZ(0); */
  /* transform: translateZ(0); */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
}

/* 强制提升层级的工具类 */
.force-top-layer {
  position: relative !important;
  z-index: 999999 !important;
  -webkit-transform: translate3d(0, 0, 0) !important;
  transform: translate3d(0, 0, 0) !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
}

/* iOS设备特殊处理 */
.ios-device .video-wrap {
  position: relative;
  z-index: 1;
}

.ios-device .video-wrap video {
  position: relative;
  z-index: 1;
}

/* 确保控制元素在iOS上可见 */
.ios-device .game-controls,
.ios-device .status-bar,
.ios-device .direction-pad,
.ios-device .action-buttons {
  position: relative !important;
  z-index: 999999 !important;
  -webkit-transform: translate3d(0, 0, 0) !important;
  transform: translate3d(0, 0, 0) !important;
}

/* 视频统计信息样式 */
.video-stats {
  position: absolute;
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999999;
  pointer-events: none;
}

.stats-text {
  color: #00ff00;
  font-size: 10px;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  white-space: nowrap;
  letter-spacing: 0.5px;
}

/* iOS设备上的视频统计信息特殊处理 */
.ios-device .video-stats {
  position: absolute !important;
  top: 5px !important;
  left: 50% !important;
  z-index: 999999 !important;
  -webkit-transform: translateX(-50%) !important;
  transform: translateX(-50%) !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
}
</style>
