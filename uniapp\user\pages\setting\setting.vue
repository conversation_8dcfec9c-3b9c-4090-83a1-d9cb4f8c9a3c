<template>
  <view class="setting-container">
    <!-- 头像区域 -->
    <view class="avatar-section">
      <view class="avatar-wrapper">
        <view class="user-avatar">
          <image class="avatar-img" :src="userInfo.avatar || '/static/default_avatar.png'" />
        </view>
        <view class="change-avatar-btn" @tap="editAvatar">
          <image class="camera-icon" src="/static/camera.png" />
          <text>更换</text>
        </view>
      </view>
      <view class="avatar-tip">点击更换头像</view>
    </view>

    <!-- 信息列表 -->
    <view class="info-list">
      <!-- 昵称 -->
      <view class="info-item" @tap="showNicknameModal = true">
        <view class="item-label">昵称</view>
        <view class="item-value">{{ userInfo.nickname || '未设置' }}</view>
        <image class="arrow-icon" src="/static/arrow-right.png" />
      </view>
      <!-- 手机号 -->
      <view class="info-item" @tap="showPhoneModal = true">
        <view class="item-label">手机号</view>
        <view class="item-value">{{ userInfo.mobile || '未绑定' }}</view>
        <image class="arrow-icon" src="/static/arrow-right.png" />
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <view class="logout-btn" @tap="showLogoutModal = true">
        <text class="logout-text">退出登录</text>
      </view>
    </view>

    <!-- 更改昵称弹窗 -->
    <UniversalModal
      :show="showNicknameModal"
      title="更改昵称"
      @close="closeNicknameModal"
      size="medium"
    >
      <view class="input-group">
        <view class="input-label">昵称</view>
        <input class="modal-input" :class="{error: nicknameError}" v-model="newNickname" placeholder="请输入昵称" maxlength="12" @focus="clearNicknameError" />
        <view class="error-tip" :class="{visible: nicknameError}">{{ nicknameErrorMsg || '　' }}</view>
      </view>
      <view class="modal-tip">
        温馨提示：请使用正面、积极的符合平台规范名称，名称中不得包含任何广告、反动、涉黄、侮辱性或攻击性词语。
      </view>
      <template #footer>
        <button class="submit-btn modal-button primary" @tap="submitNickname" :disabled="nicknameLoading">提交</button>
      </template>
    </UniversalModal>

    <!-- 手机绑定弹窗 -->
    <UniversalModal
      :show="showPhoneModal"
      title="绑定手机号"
      @close="closePhoneModal"
      size="medium"
    >
      <view class="bind-input-group">
        <input class="bind-input modal-input" :class="{error: phoneError}" v-model="bindPhone" placeholder="请输入手机号" maxlength="11" @focus="clearPhoneError" />
        <view class="error-tip" :class="{visible: phoneError}">{{ phoneErrorMsg || '　' }}</view>
      </view>
      <view class="bind-input-group bind-code-group">
        <input class="bind-input modal-input" :class="{error: codeError}" v-model="bindCode" placeholder="请输入验证码" maxlength="6" @focus="clearCodeError" />
        <view class="get-code-btn" :class="{disabled: codeTimer>0}" @tap="getBindCode">
          {{ codeTimer>0 ? codeTimer+'s' : '获取验证码' }}
        </view>
      </view>
      <view class="error-tip" :class="{visible: codeError}">{{ codeErrorMsg || '　' }}</view>

      <template #footer>
        <button class="bind-submit-btn modal-button primary" @tap="submitBindPhone">提交</button>
      </template>
    </UniversalModal>

    <!-- 退出登录确认弹窗 -->
    <UniversalModal
      :show="showLogoutModal"
      title="退出登录"
      @close="closeLogoutModal"
      size="small"
    >
      <view class="logout-message">您确认退出账号吗？</view>

      <template #footer>
        <view class="logout-buttons">
          <button class="logout-cancel-btn modal-button secondary" @tap="closeLogoutModal">取消</button>
          <button class="logout-confirm-btn modal-button primary" @tap="confirmLogout">确定</button>
        </view>
      </template>
    </UniversalModal>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import config from '@/config.js'
import UniversalModal from '@/components/UniversalModal.vue'

export default {
  components: {
    UniversalModal
  },
  data() {
    return {
      userInfo: {
        nickname: '',
        avatar: '',
        mobile: ''
      },
      showNicknameModal: false,
      showPhoneModal: false,
      showLogoutModal: false,
      newNickname: '',
      nicknameLoading: false,
      bindPhone: '',
      bindCode: '',
      codeTimer: 0,
      codeTimerId: null,
      // 错误状态
      nicknameError: false,
      nicknameErrorMsg: '',
      phoneError: false,
      phoneErrorMsg: '',
      codeError: false,
      codeErrorMsg: ''
    }
  },
  onLoad() {
    this.getUserInfo()
  },
  onShow() {
    this.getUserInfo()
  },
  methods: {
    async getUserInfo() {
      try {
        // 先从接口获取最新数据
        const res = await request({
          url: '/api/user/detail',
          method: 'POST'
        })
        if (res.code === 1 && res.data) {
          this.userInfo = res.data
          // 更新本地存储
          uni.setStorageSync('user_info', res.data)
        } else {
          // 接口失败时从本地存储获取
          const userInfo = uni.getStorageSync('user_info')
          if (userInfo) {
            this.userInfo = userInfo
          }
        }
      } catch (error) {
        console.error('获取用户信息失败：', error)
        // 网络错误时从本地存储获取
        const userInfo = uni.getStorageSync('user_info')
        if (userInfo) {
          this.userInfo = userInfo
        }
      }
    },
    editAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          // 这里可以添加上传头像的逻辑
          uni.showToast({ title: '头像上传功能待开发', icon: 'none' })
        },
        fail: (error) => {
          console.error('选择图片失败：', error)
          uni.showToast({ title: '选择图片失败', icon: 'none' })
        }
      })
    },
    closeNicknameModal() {
      this.showNicknameModal = false
      this.newNickname = ''
      this.nicknameError = false
      this.nicknameErrorMsg = ''
    },
    clearNicknameError() {
      this.nicknameError = false
      this.nicknameErrorMsg = ''
    },
    async submitNickname() {
      // 清除之前的错误状态
      this.nicknameError = false
      this.nicknameErrorMsg = ''

      if (!this.newNickname.trim()) {
        this.nicknameError = true
        this.nicknameErrorMsg = '请输入昵称'
        return
      }
      this.nicknameLoading = true
      try {
        // 这里应该调用后端接口修改昵称
        // const res = await request({
        //   url: '/api/user/changeInfo',
        //   method: 'POST',
        //   data: { nickname: this.newNickname.trim() }
        // })

        // 临时本地修改
        this.userInfo.nickname = this.newNickname.trim()
        uni.setStorageSync('user_info', this.userInfo)

        this.closeNicknameModal()
        uni.showToast({ title: '修改成功', icon: 'success' })
      } catch (e) {
        this.nicknameError = true
        this.nicknameErrorMsg = '请求失败'
      } finally {
        this.nicknameLoading = false
      }
    },
    closePhoneModal() {
      this.showPhoneModal = false
      this.bindPhone = ''
      this.bindCode = ''
      if (this.codeTimerId) {
        clearInterval(this.codeTimerId)
        this.codeTimerId = null
      }
      this.codeTimer = 0
      // 清除错误状态
      this.phoneError = false
      this.phoneErrorMsg = ''
      this.codeError = false
      this.codeErrorMsg = ''
    },
    clearPhoneError() {
      this.phoneError = false
      this.phoneErrorMsg = ''
    },
    clearCodeError() {
      this.codeError = false
      this.codeErrorMsg = ''
    },
    getBindCode() {
      if (this.codeTimer > 0) return

      // 清除之前的错误状态
      this.phoneError = false
      this.phoneErrorMsg = ''

      if (!/^1[3-9]\d{9}$/.test(this.bindPhone)) {
        this.phoneError = true
        this.phoneErrorMsg = '手机号码格式不正确'
        return
      }

      // TODO: 调用发送验证码接口
      uni.showToast({
        title: '验证码已发送',
        icon: 'success'
      })
      this.codeTimer = 60
      this.codeTimerId = setInterval(() => {
        this.codeTimer--
        if (this.codeTimer <= 0) {
          clearInterval(this.codeTimerId)
          this.codeTimerId = null
        }
      }, 1000)
    },
    async submitBindPhone() {
      // 清除之前的错误状态
      this.phoneError = false
      this.phoneErrorMsg = ''
      this.codeError = false
      this.codeErrorMsg = ''

      if (!/^1[3-9]\d{9}$/.test(this.bindPhone)) {
        this.phoneError = true
        this.phoneErrorMsg = '手机号码格式不正确'
        return
      }
      if (!this.bindCode) {
        this.codeError = true
        this.codeErrorMsg = '请输入验证码'
        return
      }
      try {
        // TODO: 调用绑定手机号接口
        // const res = await request({
        //   url: '/api/user/changemobile',
        //   method: 'POST',
        //   data: {
        //     mobile: this.bindPhone,
        //     captcha: this.bindCode
        //   }
        // })

        // 临时本地修改
        this.userInfo.mobile = this.bindPhone
        uni.setStorageSync('user_info', this.userInfo)

        this.closePhoneModal()
        uni.showToast({
          title: '绑定成功',
          icon: 'success'
        })
      } catch (error) {
        this.codeError = true
        this.codeErrorMsg = '网络错误，请稍后再试'
      }
    },
    closeLogoutModal() {
      this.showLogoutModal = false
    },
    async confirmLogout() {
      try {
        await request({ url: '/api/user/logout' })
      } catch (e) {}

      // 清除本地存储
      uni.removeStorageSync(config.tokenKey)
      uni.removeStorageSync(config.tokenExpireKey)
      uni.removeStorageSync('user_info')
      uni.reLaunch({ url: '/pages/login/login' })
    }
  }
}
</script>

<style scoped>
.setting-container {
  min-height: 100vh;
  padding: 40rpx 32rpx;
  background: #180F29;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.avatar-wrapper {
  position: relative;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.avatar-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  object-fit: cover;
}

.change-avatar-btn {
  position: absolute;
  bottom: -10rpx;
  right: 20rpx;
  background: #514265;
  border-radius: 30rpx;
  padding: 8rpx 16rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.camera-icon {
  width: 32rpx;
  height: 28rpx;
  margin-right: 6rpx;
}

.avatar-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  line-height: 1.4;
}

/* 信息列表 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.info-item {
  background: #2A1840;
  border-radius: 16rpx;
  padding: 8rpx 32rpx;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #524D57;
}

.item-label {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
  flex: 1;
}

.item-value {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 16rpx;
  text-align: right;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
}

/* 退出登录区域 */
.logout-section {
  margin-top: 80rpx;
  padding: 0 32rpx 40rpx 32rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ff4757;
}

.logout-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}

/* 弹窗内容样式 */
.input-group {
  margin-bottom: 30rpx;
}

.bind-input-group {
  margin-bottom: 20rpx;
}

.input-label {
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.modal-input {
  width: 100%;
  height: 80rpx;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #fff;
  box-sizing: border-box;
  background-color: #3F3055;
  border: none;
  outline: none;
}

.modal-input:focus {
  color: #fff;
}

.modal-input::placeholder {
  color: #999;
}

.modal-input.error,
.bind-input.error {
  border: 2rpx solid #ff4757;
  background-color: rgba(255, 71, 87, 0.1);
}

.bind-input {
  width: 100%;
  height: 80rpx;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  color: #fff;
  box-sizing: border-box;
  background-color: #3F3055;
  border: none;
  outline: none;
}

.bind-input:focus {
  color: #fff;
}

.bind-input::placeholder {
  color: #999;
}

.error-tip {
  font-size: 24rpx;
  color: transparent;
  margin-top: 10rpx;
  line-height: 1.4;
  min-height: 34rpx;
}

.error-tip.visible {
  color: #ff4757;
}

.modal-tip {
  font-size: 24rpx;
  color: #fff;
  line-height: 1.5;
  margin: 10rpx 0rpx;
}

/* 按钮样式使用全局样式 */
.submit-btn {
  width: 100%;
  height: 60px;
  line-height: 60px;
  border-radius: 30px;
  font-size: 16px;
  color: #fff;
  background: url('/static/mine/button.png') no-repeat center/100% 100%;
  border: none;
  text-align: center;
  margin-bottom: 15px;
}

.bind-submit-btn {
  width: 100%;
  height: 60px;
  line-height: 60px;
  border-radius: 30px;
  font-size: 16px;
  color: #fff;
  background: url('/static/mine/button.png') no-repeat center/100% 100%;
  border: none;
  text-align: center;
  margin-top: 5px;
}

/* 手机绑定特殊样式 */
.bind-code-group {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.bind-code-group .modal-input {
  flex: 1;
}

.get-code-btn {
  background-color: #3F3055;
  color: #fff;
  font-size: 28rpx;
  padding: 20rpx 24rpx;
  border-radius: 10rpx;
  min-width: 140rpx;
  text-align: center;
  user-select: none;
  border: 2rpx solid #524D57;
}

.get-code-btn.disabled {
  color: #999;
  background-color: #2a2a2a;
}

/* 退出登录确认 */
.logout-message {
  font-size: 30rpx;
  color: #fff;
  text-align: center;
  margin-bottom: 40rpx;
  padding: 20rpx 0;
}

.logout-buttons {
  display: flex;
  gap: 20rpx;
}

.logout-cancel-btn,
.logout-confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
  border: none;
  border-radius: 12rpx;
  text-align: center;
  color: #fff;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-cancel-btn {
  background: #666;
  color: #999;
}

.logout-confirm-btn {
  background: #e74c3c;
  color: #fff;
}
</style>