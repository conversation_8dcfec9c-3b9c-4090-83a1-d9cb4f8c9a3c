/*
 * Skin: Black yellow
 * -----------
 */
.skin-black-yellow .main-header {
  background: #222d32;
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black-yellow .main-header .navbar {
  background-color: #fff;
}
.skin-black-yellow .main-header .navbar .nav > li > a {
  color: #666;
}
.skin-black-yellow .main-header .navbar .nav > li > a:hover,
.skin-black-yellow .main-header .navbar .nav > li > a:active,
.skin-black-yellow .main-header .navbar .nav > li > a:focus,
.skin-black-yellow .main-header .navbar .nav .open > a,
.skin-black-yellow .main-header .navbar .nav .open > a:hover,
.skin-black-yellow .main-header .navbar .nav .open > a:focus,
.skin-black-yellow .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #333;
}
.skin-black-yellow .main-header .navbar .nav-addtabs li > .close-tab {
  color: #333;
}
.skin-black-yellow .main-header .navbar .sidebar-toggle {
  color: #666;
}
.skin-black-yellow .main-header .navbar .sidebar-toggle:hover {
  color: #333;
  background: rgba(0, 0, 0, 0.02);
}
.skin-black-yellow .main-header .navbar .navbar-nav > li > a {
  border-right: none;
}
.skin-black-yellow .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black-yellow .main-header .navbar .navbar-right > li > a {
  border-left: none;
  border-right-width: 0;
}
@media (max-width: 767px) {
  .skin-black-yellow .main-header .navbar {
    background-color: #181f23;
  }
  .skin-black-yellow .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-black-yellow .main-header .navbar .nav > li > a:hover,
  .skin-black-yellow .main-header .navbar .nav > li > a:active,
  .skin-black-yellow .main-header .navbar .nav > li > a:focus,
  .skin-black-yellow .main-header .navbar .nav .open > a,
  .skin-black-yellow .main-header .navbar .nav .open > a:hover,
  .skin-black-yellow .main-header .navbar .nav .open > a:focus,
  .skin-black-yellow .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-black-yellow .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-black-yellow .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-black-yellow .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
}
.skin-black-yellow .main-header .logo {
  background-color: #222d32;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #222d32;
}
.skin-black-yellow .main-header .logo:hover {
  background-color: #202a2f;
}
@media (max-width: 767px) {
  .skin-black-yellow .main-header .logo {
    background-color: #181f23;
    color: #fff;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black-yellow .main-header .logo:hover {
    background-color: #161d20;
  }
}
.skin-black-yellow .main-header li.user-header {
  background-color: #222d32;
}
.skin-black-yellow .main-header .nav-addtabs > li > a,
.skin-black-yellow .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-black-yellow .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black-yellow .wrapper,
.skin-black-yellow .main-sidebar,
.skin-black-yellow .left-side {
  background-color: #222d32;
}
.skin-black-yellow .user-panel > .info,
.skin-black-yellow .user-panel > .info > a {
  color: #fff;
}
.skin-black-yellow .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-black-yellow .sidebar-menu > li.header {
  color: #4b646f;
  background: #1a2226;
}
.skin-black-yellow .sidebar-menu > li:hover > a,
.skin-black-yellow .sidebar-menu > li.active > a {
  color: #fff;
  background: #1e282c;
  border-left-color: #fff;
}
.skin-black-yellow .sidebar-menu > li > .treeview-menu {
  background: #181f23;
}
.skin-black-yellow .sidebar a {
  color: #b8c7ce;
}
.skin-black-yellow .sidebar a:hover {
  text-decoration: none;
}
.skin-black-yellow .treeview-menu > li > a {
  color: #6c8c9b;
}
.skin-black-yellow .treeview-menu > li.active > a,
.skin-black-yellow .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-black-yellow .sidebar-form {
  border-radius: 3px;
  border: 1px solid #374850;
  background-color: #374850;
  margin: 10px 10px;
}
.skin-black-yellow .sidebar-form input[type="text"],
.skin-black-yellow .sidebar-form .btn {
  box-shadow: none;
  background-color: #374850;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black-yellow .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black-yellow .sidebar-form input[type="text"]:focus,
.skin-black-yellow .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black-yellow .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-yellow .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black-yellow .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black-yellow .treeview-menu > li > a {
  padding-left: 18px;
}
.skin-black-yellow .treeview-menu > li.active > a {
  background-color: #f39c12;
}
.skin-black-yellow .sidebar-menu > li.active > a {
  color: #fff;
  background: #f39c12;
  border-left-color: #f39c12;
}
.skin-black-yellow .sidebar-menu > li:hover > a {
  border-left-color: transparent;
}
.skin-black-yellow .sidebar-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-yellow .sidebar-menu li.treeview.active > a,
.skin-black-yellow .sidebar-menu li.treeview.treeview-open > a {
  background-color: #181f23;
  border-left-color: #181f23;
}
.skin-black-yellow .sidebar-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-yellow .sidebar-menu .treeview-menu .treeview-menu {
  padding-left: 0;
}
.skin-black-yellow .sidebar-menu .treeview-menu .treeview-menu > li > a {
  padding-left: 30px;
}
.skin-black-yellow .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
.skin-black-yellow.sidebar-collapse .sidebar-menu li:hover > a,
.skin-black-yellow.sidebar-collapse .sidebar-menu li.active > a {
  color: #fff;
  background: #f39c12;
}
.skin-black-yellow.sidebar-collapse .sidebar-menu .treeview-menu li.active > a {
  color: #fff;
  background: #f39c12;
}
.skin-black-yellow.sidebar-collapse .sidebar-menu .treeview-menu li.treeview > a {
  background: transparent;
  border-left-color: transparent;
}
@media (max-width: 767px) {
  .skin-black-yellow.multiplenav .sidebar .mobilenav a.btn-app {
    background: #374850;
    color: #fff;
  }
  .skin-black-yellow.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #f39c12;
    color: #fff;
  }
}
/*# sourceMappingURL=skin-black-yellow.css.map */