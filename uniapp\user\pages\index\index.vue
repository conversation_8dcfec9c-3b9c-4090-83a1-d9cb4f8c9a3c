<template>
	<view class="container">
		<!-- 顶部信息栏 -->
		<view class="top-bar">
			<view class="user-info-wrap">
				<view class="avatar-wrap">
					<view class="avatar-level-wrap large">
						<image class="avatar small" :src="userInfo.avatar || '/static/default_avatar.png'" />
						<image class="level-avatar-bg large" src="/static/level_avatar.png" />
					</view>
				</view>
				<view class="user-detail">
					<view class="nickname" @tap="handleUserInfoClick">{{ userInfo.nickname || '未登录' }}</view>
					<view class="level-assets-row" v-if="userInfo.nickname">
						<view class="location-info">
							<text class="location-text">您的位置： xxxxxx</text>
						</view>
						<view class="asset-item" @tap.stop="goMine">
							<image class="coin-icon" src="/static/score.png" />
							<text class="asset-num">{{ score }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 公告栏轮播图 -->
		<view class="notice-swiper-wrap">
			<swiper class="notice-swiper" circular autoplay interval="5000" v-if="noticeList.length > 0">
				<swiper-item v-for="notice in noticeList" :key="notice.id">
					<view class="notice-content">
						<rich-text :nodes="notice.content"></rich-text>
					</view>
				</swiper-item>
			</swiper>
			<view v-else class="notice-loading">
				<text>加载中...</text>
			</view>
		</view>

		<!-- 合作商家列表 -->
		<view class="section-title">合作商家列表</view>
		<view class="shop-list">
			<view class="shop-item list-item" v-for="(shop, idx) in shops" :key="shop.id">
				<image class="shop-img" :src="getLogoUrl(shop.logo)" mode="aspectFill" />
				<view class="shop-info">
					<view class="shop-top-row">
						<view class="shop-title">{{ shop.name }}</view>
						<view class="shop-time">营业{{ shop.business_hours }}</view>
					</view>
					<view class="shop-desc">已经有{{ shop.number || 0 }}人用积分买单</view>
				</view>
			</view>
			<view v-if="shops.length < total" class="load-more" @click="loadMore">加载更多</view>
			<view v-else-if="total > 0 && shops.length >= total" class="load-more">没有更多了</view>
		</view>
	</view>
</template>

<script>
import request from '@/utils/request.js'
import config from '@/config.js'

export default {
	data() {
		return {
			shops: [],
			page: 1,
			show_num: 10,
			total: 0,
			loading: false,
			score: this.getLocalScore(),
			userInfo: {
				nickname: '',
				avatar: ''
			},
			noticeList: [] // 公告轮播数据
		}
	},
	onLoad() {
		this.getMerchantList()
		this.getUserDetail()
		this.loadUserInfo()
		this.getNoticeList()
	},
	onShow() {
		this.loadUserInfo()
	},
	methods: {
		getLocalScore() {
			const userInfo = uni.getStorageSync('user_info')
			return userInfo && typeof userInfo.score !== 'undefined' ? userInfo.score : '--'
		},
		async getUserDetail() {
			try {
				const res = await request({ url: '/api/user/detail', method: 'POST' })
				if (res.code === 1 && res.data && typeof res.data.score !== 'undefined') {
					this.score = res.data.score
					// 更新本地缓存user_info
					let userInfo = uni.getStorageSync('user_info') || {}
					userInfo.score = res.data.score
					userInfo.nickname = res.data.nickname || userInfo.nickname
					userInfo.avatar = res.data.avatar || userInfo.avatar
					userInfo.id = res.data.id || userInfo.id
					uni.setStorageSync('user_info', userInfo)
					// 更新当前页面的userInfo
					this.userInfo = userInfo
				}
			} catch (e) {}
		},
		async getMerchantList(loadMore = false) {
			if (this.loading) return
			this.loading = true
			try {
				const res = await request({
					url: '/api/user/get_merchant_list',
					method: 'POST',
					data: {
						page: this.page,
						show_num: this.show_num
					}
				})
				if (res.code === 1 && res.data && res.data.data) {
					const list = res.data.data
					if (loadMore) {
						this.shops = this.shops.concat(list)
					} else {
						this.shops = list
					}
					this.total = res.data.total
				}
			} finally {
				this.loading = false
			}
		},
		loadMore() {
			if (this.shops.length < this.total) {
				this.page++
				this.getMerchantList(true)
			}
		},
		goMine() {
			uni.switchTab({ url: '/pages/mine/mine' })
		},
		getLogoUrl(logo) {
			if (!logo) return '/static/logo.png'
			if (/^https?:\/\//.test(logo)) return logo
			return config.baseUrl + logo
		},
		loadUserInfo() {
			const userInfo = uni.getStorageSync('user_info')
			if (userInfo) {
				this.userInfo = userInfo
			}
		},
		handleUserInfoClick() {
			if (!this.userInfo.nickname) {
				uni.navigateTo({
					url: '/pages/login/login'
				})
			} else {
				this.goMine()
			}
		},
		// 获取公告轮播数据
		async getNoticeList() {
			try {
				const res = await request({
					url: '/api/content/list',
					data: {
						type: 1
					}
				})
				if (res.code === 1) {
					// 处理HTML内容，转换为rich-text可用的格式
					this.noticeList = (res.data || []).map(notice => ({
						...notice,
						content: this.processHtmlContent(notice.content)
					}))
				}
			} catch (error) {
				console.error('获取公告数据失败：', error)
				// 如果获取失败，设置一个默认公告
				this.noticeList = [{
					id: 1,
					content: '欢迎使用积分核销系统！'
				}]
			}
		},
		// 处理HTML内容
		processHtmlContent(htmlString) {
			if (!htmlString) return ''

			// 如果是纯文本，直接返回
			if (!htmlString.includes('<')) {
				return htmlString
			}

			// 简单的HTML标签处理，去除一些可能导致样式问题的标签
			let processedHtml = htmlString
				.replace(/<script[^>]*>.*?<\/script>/gi, '') // 移除script标签
				.replace(/<style[^>]*>.*?<\/style>/gi, '') // 移除style标签
				.replace(/<link[^>]*>/gi, '') // 移除link标签
				.replace(/style\s*=\s*["'][^"']*["']/gi, '') // 移除内联样式
				.replace(/<(\/?)h[1-6]([^>]*)>/gi, '<$1p$2>') // 将标题标签转换为p标签
				.replace(/<br\s*\/?>/gi, '\n') // 将br标签转换为换行符
				.trim()

			return processedHtml
		}
	}
}
</script>

<style scoped>
	/* 页面整体容器 */
	.container {
		padding-bottom: 100rpx;
		background: #180F29;
		min-height: 100vh;
	}

	/* 位置信息 */
	.location-info {
		margin-left: 12rpx;
	}

	.location-text {
		font-size: 24rpx;
		color: #fff;
	}
	/* 公告栏轮播图 */
	.notice-swiper-wrap {
		margin: 30rpx 24rpx 0 24rpx;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx #f3eac2;
	}

	.notice-swiper {
		width: 100%;
		height: 250rpx;
	}

	.notice-content {
		width: 100%;
		height: 100%;
		padding: 20rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
		background: linear-gradient(135deg, #2A1840 0%, #3F2F5B 100%);
	}

	.notice-content /deep/ rich-text,
	.notice-content /deep/ rich-text p,
	.notice-content /deep/ rich-text div,
	.notice-content /deep/ rich-text span {
		color: #fff !important;
		font-size: 24rpx !important;
		line-height: 1.5 !important;
		margin: 0 !important;
		padding: 0 !important;
	}

	.notice-content /deep/ rich-text p,
	.notice-content /deep/ rich-text div {
		margin-bottom: 10rpx !important;
	}

	.notice-content /deep/ rich-text p:last-child,
	.notice-content /deep/ rich-text div:last-child {
		margin-bottom: 0 !important;
	}

	/* 处理其他HTML标签 */
	.notice-content /deep/ rich-text strong,
	.notice-content /deep/ rich-text b {
		font-weight: bold !important;
		color: #FFB366 !important;
	}

	.notice-content /deep/ rich-text em,
	.notice-content /deep/ rich-text i {
		font-style: italic !important;
	}

	.notice-content /deep/ rich-text a {
		color: #7B68EE !important;
		text-decoration: underline !important;
	}

	.notice-content /deep/ rich-text ul,
	.notice-content /deep/ rich-text ol {
		padding-left: 20rpx !important;
		margin: 10rpx 0 !important;
	}

	.notice-content /deep/ rich-text li {
		margin-bottom: 5rpx !important;
	}

	.notice-loading {
		width: 100%;
		height: 250rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #2A1840 0%, #3F2F5B 100%);
		color: #999;
		font-size: 28rpx;
	}
	/* 合作商家标题 */
	.section-title {
		margin: 30rpx 30rpx 10rpx;
		font-size: 28rpx;
		color: #fff;
		font-weight: bold;
	}
	/* 合作商家列表整体 */
	.shop-list {
		margin: 0 10rpx;
	}
	/* 单个商家卡片 */
	.shop-item {
		display: flex;
		align-items: center;
		background: #2A1840;
		border-radius: 12rpx;
		margin: 16rpx 20rpx;
		padding: 30rpx 20rpx;
		border: 1rpx solid #4C3A62;
		overflow: hidden;
		position: relative;
		min-height: 120rpx;
	}
	.shop-img {
		width: 120rpx;
		height: 120rpx;
		margin: 0 20rpx 0 0;
		object-fit: cover;
		border-radius: 8rpx;
		flex-shrink: 0;
		background: #f0f0f0;
	}
	.shop-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		position: relative;
		padding: 0;
	}
	.shop-top-row {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		margin-bottom: 10rpx;
	}
	.shop-time {
		font-size: 26rpx;
		color: #fff;
		margin-right: 0;
		margin-top: 2rpx;
		text-align: right;
	}
	.shop-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-top: 0;
		line-height: 1.2;
		color: #fff;
	}
	.shop-desc {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 2rpx;
	}
	.load-more {
		text-align: center;
		color: #999;
		font-size: 26rpx;
		padding: 24rpx 0;
		cursor: pointer;
	}
</style>
