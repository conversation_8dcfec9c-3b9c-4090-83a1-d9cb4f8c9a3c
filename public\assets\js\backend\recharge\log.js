define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'recharge/log/index',
                    add_url: '',
                    edit_url: '',
                    del_url: '',
                    multi_url: '',
                    table: 'log',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='user_id']", form).addClass("selectpage").data("source", "user/user/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                $("input[name='agent_id']", form).addClass("selectpage").data("source", "agent/agent/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'id',
                sortName: 'id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true},
                        {field: 'order_no', title: __('订单号')},
                        {field: 'avatar', title: __('用户头像'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'user_id',title: __('用户信息'),formatter: function (value, row, index) {
                                if (row.mobile && row.nickname) {
                                    return row.nickname+'<br>'+row.mobile+'<br>'+row.user_id;
                                }else{
                                    return row.user_id;
                                }
                            }},
                        {field: 'type', title: __('支付方式'), operate:false},
                        {field: 'type', title: __('充值类型'), operate:false},
                        {field: 'price', title: __('充值金额'), operate:false},
                        {field: 'coin', title: __('充值金币'), operate:false},
                        {field: 'gift_coin', title: __('赠送金币'), operate:false},
                        {field: 'total_coin', title: __('到账金币'), operate:false},
                        {field: 'status', title: __('充值状态'),searchList: {"0":__('待支付'),"1":__('已支付'),"2":__('已退款')},formatter: function (value, row, index) {
                                if (row.status == 0) {
                                    return '待支付';
                                } else if (row.status == 1) {
                                    return '已支付';
                                }  else if (row.status == 2) {
                                    return '已退款';
                                } else{
                                    return '未知';
                                }
                            }},
                        {field: 'pay_time', title: __('支付时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'trade_no', title: __('流水号')},
                        {field: 'agent_id',title: __('代理信息'),formatter: function (value, row, index) {
                                if (row.agent_id) {
                                    return row.agent_name+'<br>'+row.agent_mobile+'<br>'+row.agent_id;
                                }else{
                                    return '-';
                                }
                            }},
                        {field: 'fee', title: __('分佣比例'), operate:false},
                        {field: 'commission', title: __('代理佣金'), operate:false},
                        {field: 'createtime', title: __('创建时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('充值规则'),
                                    text:'充值规则',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["80%","50%"]\'',
                                    icon: 'fa fa-gamepad',
                                    url: function (row){
                                        return "recharge/recharge/index?id=" + row.game_recharge_id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('用户信息'),
                                    text:'用户信息',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["80%","50%"]\'',
                                    icon: 'fa fa-user-circle',
                                    url: function (row){
                                        return "user/user/index?id=" + row.user_id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'money',
                                    title: __('代理信息'),
                                    text:'代理信息',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["80%","50%"]\'',
                                    icon: 'fa fa-user-secret',
                                    url: function (row){
                                        return "agent/agent/index?id=" + row.agent_id;
                                    },
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        if (row.agent_id > 0){
                                            return true;
                                        }else{
                                            return false;
                                        }
                                    }
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});