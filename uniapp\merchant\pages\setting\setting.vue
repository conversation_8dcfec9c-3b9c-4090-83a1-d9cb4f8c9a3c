<template>
  <view class="setting-container">
    <!-- LOGO区域 -->
    <view class="logo-section">
      <view class="logo-wrapper">
        <view class="merchant-logo">
          <image class="logo-img" :src="logo || '/static/default_avatar.png'" />
        </view>
        <view class="change-logo-btn" @tap="chooseLogo">
          <image class="camera-icon" src="/static/camera.png" />
          <text>更换</text>
        </view>
      </view>
      <view class="logo-tip">点击更换商铺LOGO</view>
    </view>

    <!-- 信息列表 -->
    <view class="info-list">
      <!-- 商铺名称 -->
      <view class="info-item" @tap="openNameModal">
        <view class="item-label">商铺名称</view>
        <view class="item-value">{{ name || '未设置' }}</view>
        <image class="arrow-icon" src="/static/arrow-right.png" />
      </view>
      <!-- 营业时间 -->
      <view class="info-item" @tap="showTimeModal = true">
        <view class="item-label">营业时间</view>
        <view class="item-value">
          <view class="time-display">{{ getBusinessHoursDisplay() }}</view>
        </view>
        <image class="arrow-icon" src="/static/arrow-right.png"  />
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <view class="logout-btn" @tap="logout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>

    <!-- 商铺名称编辑弹窗 -->
    <view v-if="showNameModal" class="modal-mask" @tap="showNameModal = false">
      <view class="name-modal" @tap.stop>
        <view class="modal-title">修改商铺名称</view>
        <view class="name-input-wrap">
          <input class="name-input" v-model="tempName" placeholder="请输入商铺名称" maxlength="20" />
        </view>
        <view class="modal-buttons">
          <button class="modal-btn cancel-btn" @tap="cancelNameEdit">取消</button>
          <button class="modal-btn confirm-btn" @tap="confirmNameEdit">确定</button>
        </view>
      </view>
    </view>

    <!-- 营业时间设置弹窗 -->
    <view v-if="showTimeModal" class="modal-mask" @tap="closeTimeModal">
      <view class="time-modal" @tap="stopPropagation">
        <view class="modal-title">设置营业时间</view>
        <view class="time-setting">
          <view class="time-row">
            <text class="time-label">开始时间</text>
            <!-- #ifdef H5 -->
            <view class="custom-time-picker" @click="openTimePicker('start')">
              <text class="time-display">{{ startTime }}</text>
              <text class="time-icon">🕐</text>
            </view>
            <!-- #endif -->
            <!-- #ifndef H5 -->
            <picker mode="time" :value="startTime" @change="onStartTimeChange">
              <view class="time-picker">{{ startTime }}</view>
            </picker>
            <!-- #endif -->
          </view>
          <view class="time-row">
            <text class="time-label">结束时间</text>
            <!-- #ifdef H5 -->
            <view class="custom-time-picker" @click="openTimePicker('end')">
              <text class="time-display">{{ endTime }}</text>
              <text class="time-icon">🕐</text>
            </view>
            <!-- #endif -->
            <!-- #ifndef H5 -->
            <picker mode="time" :value="endTime" @change="onEndTimeChange">
              <view class="time-picker">{{ endTime }}</view>
            </picker>
            <!-- #endif -->
          </view>
        </view>

        <!-- H5自定义时间选择器 -->
        <!-- #ifdef H5 -->
        <view v-if="showCustomTimePicker" class="custom-time-modal" @click="closeCustomTimePicker">
          <view class="custom-time-content" @click.stop>
            <view class="custom-time-header">
              <text>选择{{ currentTimeType === 'start' ? '开始' : '结束' }}时间</text>
            </view>
            <view class="time-selectors">
              <view class="time-selector">
                <text class="selector-label">小时</text>
                <view class="selector-list">
                  <view
                    v-for="hour in hours"
                    :key="hour"
                    class="selector-item"
                    :class="{ active: selectedHour === hour }"
                    @click="selectHour(hour)"
                  >
                    {{ hour }}
                  </view>
                </view>
              </view>
              <view class="time-selector">
                <text class="selector-label">分钟</text>
                <view class="selector-list">
                  <view
                    v-for="minute in getAvailableMinutes()"
                    :key="minute"
                    class="selector-item"
                    :class="{ active: selectedMinute === minute }"
                    @click="selectMinute(minute)"
                  >
                    {{ minute }}
                  </view>
                </view>
              </view>
            </view>
            <view class="custom-time-buttons">
              <button class="custom-time-btn cancel" @click="closeCustomTimePicker">取消</button>
              <button class="custom-time-btn confirm" @click="confirmCustomTime">确定</button>
            </view>
          </view>
        </view>
        <!-- #endif -->
        <view class="modal-buttons">
          <button class="modal-btn cancel-btn" @tap="closeTimeModal">取消</button>
          <button class="modal-btn confirm-btn" @tap="confirmTime">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import config from '@/config.js'
export default {
  data() {
    return {
      name: '',
      logo: '',
      logoRaw: '',
      business_hours: '',
      startTime: '09:00',
      endTime: '18:00',
      showTimeModal: false,
      showNameModal: false,
      tempName: '',
      // H5自定义时间选择器相关数据
      showCustomTimePicker: false,
      currentTimeType: 'start', // 'start' 或 'end'
      selectedHour: '09',
      selectedMinute: '00',
      hours: [],
      minutes: []
    }
  },
  onLoad(options) {
    // 从参数获取初始数据（兼容旧版本）
    this.initFromParams(options)
    // 获取最新的商户信息
    this.getMerchantInfo()
    // 初始化时间选择器数据
    this.initTimePickerData()
  },
  onShow() {
    // 每次显示页面时都获取最新数据
    this.getMerchantInfo()
  },
  methods: {
    // 获取商户信息
    async getMerchantInfo() {
      try {
        const res = await request({
          url: '/api/merchant/detail'
        })
        if (res.code === 1 && res.data) {
          this.name = res.data.name || ''
          this.logo = res.data.logo || ''
          this.business_hours = res.data.business_hours || ''
          this.parseBusinessHours()
        }
      } catch (error) {
        console.error('获取商户信息失败：', error)
      }
    },
    // 从参数初始化数据（兼容旧版本）
    initFromParams(options) {
      this.name = options.name || ''
      // 兼容 logo 为数组字符串
      let logo = ''
      if (options.logo) {
        try {
          const arr = JSON.parse(decodeURIComponent(options.logo))
          if (Array.isArray(arr) && arr.length > 0) {
            logo = arr[0]
          }
        } catch (e) {
          logo = options.logo
        }
      }
      this.logo = logo
      this.logoRaw = ''
      this.business_hours = options.business_hours || ''
      this.parseBusinessHours()
    },
    // 解析营业时间
    parseBusinessHours() {
      if (this.business_hours === '全天24小时') {
        // 24小时营业设为00:00-24:00
        this.startTime = '00:00'
        this.endTime = '24:00'
      } else if (this.business_hours && this.business_hours.includes('-')) {
        const [start, end] = this.business_hours.split('-')
        this.startTime = start.trim()
        this.endTime = end.trim()
      } else {
        // 默认营业时间
        this.startTime = '09:00'
        this.endTime = '18:00'
      }
    },
    // 获取营业时间显示文本
    getBusinessHoursDisplay() {
      if (this.startTime && this.endTime) {
        return `${this.startTime} - ${this.endTime}`
      } else {
        return '未设置'
      }
    },
    goBack() {
      uni.navigateBack()
    },
    // 时间选择器事件处理（非H5环境使用）
    onStartTimeChange(e) {
      this.startTime = e.detail.value
    },
    onEndTimeChange(e) {
      this.endTime = e.detail.value
    },
    // 阻止事件冒泡
    stopPropagation(e) {
      e.stopPropagation()
    },
    // 关闭时间弹窗
    closeTimeModal() {
      this.showTimeModal = false
    },
    // 初始化时间选择器数据
    initTimePickerData() {
      // 生成小时数组 00-24
      this.hours = []
      for (let i = 0; i <= 24; i++) {
        this.hours.push(i.toString().padStart(2, '0'))
      }
      // 生成分钟数组 00, 30
      this.minutes = ['00', '30']
    },
    // 打开自定义时间选择器
    openTimePicker(type) {
      this.currentTimeType = type
      const currentTime = type === 'start' ? this.startTime : this.endTime
      const [hour, minute] = currentTime.split(':')
      this.selectedHour = hour
      this.selectedMinute = minute
      this.showCustomTimePicker = true
    },
    // 关闭自定义时间选择器
    closeCustomTimePicker() {
      this.showCustomTimePicker = false
    },
    // 选择小时
    selectHour(hour) {
      this.selectedHour = hour
      // 如果选择24小时，分钟自动设为00
      if (hour === '24') {
        this.selectedMinute = '00'
      }
    },
    // 获取可用的分钟选项
    getAvailableMinutes() {
      // 如果选择的是24小时，只能选择00分钟
      if (this.selectedHour === '24') {
        return ['00']
      }
      // 其他小时可以选择00或30分钟
      return this.minutes
    },
    // 选择分钟
    selectMinute(minute) {
      this.selectedMinute = minute
    },
    // 确认自定义时间选择
    confirmCustomTime() {
      const newTime = `${this.selectedHour}:${this.selectedMinute}`
      if (this.currentTimeType === 'start') {
        this.startTime = newTime
      } else {
        this.endTime = newTime
      }
      this.closeCustomTimePicker()
    },
    async logout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出当前账号吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await request({ url: '/api/user/logout' })
            } catch (e) {}
            // 清除本地token和用户信息
            uni.removeStorageSync(config.tokenKey)
            uni.removeStorageSync(config.tokenExpireKey)
            uni.removeStorageSync(config.userInfo)
            uni.reLaunch({ url: '/pages/login/login' })
          }
        }
      })
    },

    async confirmTime() {
      // 保存营业时间到服务器
      const success = await this.saveTimeToServer()
      if (success) {
        this.showTimeModal = false
        uni.showToast({ title: '营业时间设置成功', icon: 'success' })
      }
    },
    // 商铺名称编辑相关方法
    openNameModal() {
      this.tempName = this.name || ''
      this.showNameModal = true
    },
    cancelNameEdit() {
      this.showNameModal = false
      this.tempName = this.name
    },
    async confirmNameEdit() {
      if (!this.tempName.trim()) {
        uni.showToast({ title: '请输入商铺名称', icon: 'none' })
        return
      }

      // 保存到服务器（saveNameToServer内部会重新获取数据）
      const success = await this.saveNameToServer(this.tempName.trim())
      if (success) {
        this.showNameModal = false
        uni.showToast({ title: '商铺名称修改成功', icon: 'success' })
      }
    },
    // 保存营业时间到服务器（只提交营业时间字段）
    async saveTimeToServer() {
      // 验证时间格式
      if (!this.startTime || !this.endTime) {
        uni.showToast({ title: '请选择营业时间', icon: 'none' })
        return false
      }

      // 检查时间是否为24小时营业
      let business_hours
      if (this.startTime === '00:00' && this.endTime === '24:00') {
        business_hours = '全天24小时'
      } else {
        business_hours = `${this.startTime}-${this.endTime}`
      }

      try {
        const res = await request({
          url: '/api/merchant/edit',
          method: 'POST',
          data: {
            business_hours
          }
        })
        if (res.code === 1) {
          // 重新获取商户信息以确保数据同步
          await this.getMerchantInfo()
          return true
        } else {
          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })
          return false
        }
      } catch (e) {
        uni.showToast({ title: '网络错误', icon: 'none' })
        return false
      }
    },
    // 保存商铺名称到服务器（只提交名称字段）
    async saveNameToServer(newName) {
      try {
        const res = await request({
          url: '/api/merchant/edit',
          method: 'POST',
          data: {
            name: newName
          }
        })
        if (res.code === 1) {
          // 重新获取商户信息以确保数据同步
          await this.getMerchantInfo()
          return true
        } else {
          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })
          return false
        }
      } catch (e) {
        uni.showToast({ title: '网络错误', icon: 'none' })
        return false
      }
    },
    async saveBusinessHours() {
      let business_hours = this.isAllDay ? '全天24小时' : `${this.startTime}-${this.endTime}`
      try {
        const res = await request({
          url: '/api/merchant/edit',
          method: 'POST',
          data: {
            business_hours,
            name: this.name,
            logo: this.logoRaw || this.logo
          }
        })
        if (res.code === 1) {
          this.business_hours = business_hours
          uni.showToast({ title: '保存成功', icon: 'success' })
        } else {
          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })
        }
      } catch (e) {
        uni.showToast({ title: '网络错误', icon: 'none' })
      }
    },
    chooseLogo() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0]
          try {
            uni.showLoading({ title: '上传中...' })

            // 上传图片
            const uploadRes = await this.uploadImage(tempFilePath)
            const data = JSON.parse(uploadRes.data)

            if (data.code === 1 && data.data && data.data.url) {
              const logoUrl = data.data.url

              // 调用更改LOGO接口
              const changeRes = await request({
                url: '/api/merchant/edit',
                method: 'POST',
                data: { logo: logoUrl }
              })

              if (changeRes.code === 1) {
                uni.showToast({ title: 'LOGO修改成功', icon: 'success' })
                // 重新获取商户信息以确保数据同步
                await this.getMerchantInfo()
              } else {
                uni.showToast({ title: changeRes.msg || 'LOGO修改失败', icon: 'none' })
              }
            } else {
              uni.showToast({ title: '图片上传失败', icon: 'none' })
            }
          } catch (error) {
            console.error('LOGO上传失败：', error)
            uni.showToast({ title: '上传失败，请重试', icon: 'none' })
          } finally {
            uni.hideLoading()
          }
        },
        fail: (error) => {
          console.error('选择图片失败：', error)
          uni.showToast({ title: '选择图片失败', icon: 'none' })
        }
      })
    },
    // 上传图片方法
    async uploadImage(filePath) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: config.baseUrl + '/api/common/upload',
          filePath,
          name: 'file',
          success: (res) => {
            resolve(res)
          },
          fail: (err) => {
            reject(err)
          }
        })
      })
    }
  }
}
</script>

<style scoped>
.setting-container {
  background: #180F29;
  min-height: 100vh;
  padding: 40rpx 32rpx;
}

/* LOGO区域 */
.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.logo-wrapper {
  position: relative;
  margin-bottom: 20rpx;
}

.merchant-logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.logo-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  object-fit: cover;
}

.change-logo-btn {
  position: absolute;
  bottom: -10rpx;
  right: 20rpx;
  background: #514265;
  border-radius: 30rpx;
  padding: 8rpx 16rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.camera-icon {
  width: 32rpx;
  height: 28rpx;
  margin-right: 6rpx;
}

.logo-tip {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  line-height: 1.4;
}

/* 信息列表 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.info-item {
  background: #2A1840;
  border-radius: 16rpx;
  padding: 8rpx 32rpx;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #524D57;
}

.item-label {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
  flex: 1;
}

.item-value {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 16rpx;
  text-align: right;
}



.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
}

.time-display {
  color: rgba(255, 255, 255, 0.7);
}

/* 退出登录区域 */
.logout-section {
  margin-top: 80rpx;
  padding: 0 32rpx 40rpx 32rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background: #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ff4757;
}

.logout-text {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}

/* 弹窗遮罩 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 商铺名称编辑弹窗 */
.name-modal {
  width: 80%;
  max-width: 600rpx;
  background: #2A1840;
  border: 1rpx solid #4C3A62;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
}

.name-input-wrap {
  margin: 30rpx 0 40rpx 0;
}

.name-input {
  width: 100%;
  height: 80rpx;
  background: #180F29;
  border: 1rpx solid #4C3A62;
  border-radius: 12rpx;
  padding: 0 20rpx;
  color: #fff;
  font-size: 28rpx;
  box-sizing: border-box;
}

.name-input::placeholder {
  color: #666;
}

/* 时间设置弹窗 */

.time-modal {
  width: 80%;
  max-width: 600rpx;
  background: #2A1840;
  border: 1rpx solid #4C3A62;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
}

.modal-title {
  font-size: 32rpx;
  color: #fff;
  text-align: center;
  margin-bottom: 40rpx;
  font-weight: bold;
}

.time-setting {
  margin-bottom: 40rpx;
}

.time-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}

.time-row:last-child {
  margin-bottom: 0;
}

.time-label {
  font-size: 28rpx;
  color: #fff;
  width: 160rpx;
}

.time-picker {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  background: #180F29;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 0 20rpx;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.time-picker:hover {
  background: #2A1840;
  border-color: #7B68EE;
}

.time-picker:active {
  background: #3A2850;
}

/* 添加时间选择器图标提示 */
.time-picker::after {
  content: '⏰';
  position: absolute;
  right: 12rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  opacity: 0.6;
}

.time-input {
  flex: 1;
  height: 60rpx;
  background: #180F29;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 0 20rpx;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  box-sizing: border-box;
}

/* H5环境下time input的特殊样式 */
.time-input::-webkit-calendar-picker-indicator {
  filter: invert(1);
  cursor: pointer;
}

/* H5专用时间选择器样式 */
.time-picker-wrapper {
  flex: 1;
  position: relative;
}

.time-input-h5 {
  width: 100%;
  height: 60rpx;
  background: #180F29;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 0 20rpx;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  box-sizing: border-box;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.time-input-h5:focus {
  border-color: #7B68EE;
  background: #2A1840;
}

/* H5自定义时间选择器 */
.custom-time-picker {
  flex: 1;
  height: 60rpx;
  background: #180F29;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
}

.custom-time-picker:hover {
  background: #2A1840;
  border-color: #7B68EE;
}

.time-display {
  color: #fff;
  font-size: 28rpx;
}

.time-icon {
  font-size: 24rpx;
  opacity: 0.7;
}

/* 自定义时间选择器弹窗 */
.custom-time-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.custom-time-content {
  width: 80%;
  max-width: 600rpx;
  background: #2A1840;
  border: 1rpx solid #4C3A62;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
}

.custom-time-header {
  text-align: center;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}

.time-selectors {
  display: flex;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.time-selector {
  flex: 1;
}

.selector-label {
  display: block;
  text-align: center;
  font-size: 28rpx;
  color: #fff;
  margin-bottom: 20rpx;
}

.selector-list {
  max-height: 300rpx;
  overflow-y: auto;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  background: #180F29;
}

.selector-item {
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #fff;
  cursor: pointer;
  border-bottom: 1rpx solid #4C3A62;
  transition: all 0.2s ease;
}

.selector-item:last-child {
  border-bottom: none;
}

.selector-item:hover {
  background: #3A2850;
}

.selector-item.active {
  background: #7B68EE;
  color: #fff;
}

.custom-time-buttons {
  display: flex;
  gap: 20rpx;
}

.custom-time-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
}

.custom-time-btn.cancel {
  background: #666;
  color: #fff;
}

.custom-time-btn.confirm {
  background: #4A90E2;
  color: #fff;
}

.modal-buttons {
  display: flex;
  gap: 20rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background: #666;
  color: #fff;
}

.confirm-btn {
  background: #4A90E2;
  color: #fff;
}
</style>