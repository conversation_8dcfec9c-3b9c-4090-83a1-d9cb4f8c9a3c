<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\MoneyLog;
use app\common\model\Payment;
use think\Db;
use think\Exception;
use think\exception\DbException;
use think\Log;

/**
 * 充值
 * @Authod Jw
 * @Time 2021/6/15
 * @package app\api\controller
 */
class Recharge extends Api
{
    protected $noNeedLogin = ['category','mock_payment_page','mock_notify','buildMockNotifyData'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * @Method 充值列表
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function list()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            $where = [];

            if ($params) {

            }

            $data = Db::name('game_recharge')
                ->where('is_delete',0)
                ->where($where)
                ->select();

            foreach ($data as &$v) {
                // 收益率
                $v['profit'] = round((($v['coin']+$v['gift_coin']) / ($v['price']*10) *100)) . '%';
            }

            $this->success('成功获取',$data);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * 充值
     * @Authod Jw
     * @Time 2025/4/28
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function recharge()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();

            if (!isset($params['id'])) {
                $this->error('ID不能为空');
            }

            $recharge = Db::name('game_recharge')->where('id',$params['id'])->find();
            if (!$recharge) {
                $this->error('充值数据不存在，请刷新页面');
            }

            Db::startTrans();
            try {
                $userInfo = $this->auth->getUserinfo();
                $before = $userInfo['money'];
                $coin = $recharge['coin']+$recharge['gift_coin'];
                Db::name('user')->where('id',$userInfo['id'])->setInc('money',$coin);

                $after = $coin+$before;
                $memo = '充值';
                //写入日志
                MoneyLog::create(['user_id' => $userInfo['id'], 'money' => $coin, 'before' => $before, 'after' => $after, 'memo' => $memo,'status'=>1]);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error('失败');
            } catch (DbException $e) {
                Db::rollback();
                $this->error('失败');
            }
            $this->success('成功');
        }else{
            $this->error('请求方式不正确');
        }
    }

    public function create_order()
    {
        if ($this->request->isPost()) {
            $params = $this->request->param();

            Db::startTrans();
            try {
                // 增强参数验证
                if (!isset($params['id']) || !is_numeric($params['id'])) {
                    throw new \Exception('套餐ID参数不合法');
                }

                // 获取套餐信息（添加字段白名单）
                $recharge = Db::name('game_recharge')
                    ->field('price,coin,gift_coin,is_delete')
                    ->find($params['id']);

                if (!$recharge || $recharge['is_delete']) {
                    throw new \Exception('套餐不存在或已下架');
                }

                // 生成唯一订单号（添加唯一性校验）
                $orderNo = 'H'.getOrderNum();

                // 构建支付参数（补充必要参数）
                $payParams = [
                    'id'      => $params['id'],
                    'type'    => $params['type'] ?? 'wechat',
                    'method'  => $params['method'] ?? 'web', // 根据支付类型设置默认method
                    'title'   => '游戏币充值',
                    'amount'  => $recharge['price'],
                    'order_no'=> $orderNo,
                    'openid'  => $params['openid'] ?? null // 微信支付需要
                ];

                // 创建支付记录（使用模型方法）
                $payment = new Payment();
                $payment->save([
                    'user_id'     => $this->auth->id,
                    'order_no'    => $orderNo,
                    'amount'      => $recharge['price'],
                    'pay_type'    => $payParams['type'],
                    'create_time' => time()
                ]);

                $agent_id = Db::name('user')->where('id',$this->auth->id)->value('p_id');
                if ($agent_id) {
                    $fee = Db::name('admin')
                        ->where('id', $agent_id)
                        ->value('fee');
                    if ($fee > 0) {
                        $commission = bcmul($recharge['price'], bcdiv($fee, 100, 4), 2);
                    }
                }

                // 创建充值记录（添加状态字段）
                Db::name('user_recharge_log')->insert([
                    'user_id'           => $this->auth->id,
                    'game_recharge_id'  => $params['id'],
                    'order_no'          => $orderNo,
                    'price'             => $recharge['price'],//支付金额
                    'coin'              => $recharge['coin'],//充值金币
                    'gift_coin'         => $recharge['gift_coin'],//赠送金币
                    'total_coin'        => $recharge['coin'] + $recharge['gift_coin'],//到账金币
                    'status'            => 0, // 0-待支付
                    'agent_id'          => $agent_id ?? 0, //代理ID
                    'fee'               => $fee ?? 0, //代理分成比例
                    'commission'        => $commission ?? 0, //代理佣金
                    'createtime'        => time()
                ]);

                // 调用支付（使用依赖注入）
                $result = $payment->pay($payParams);
                Log::record($result,'支付返回');
                // 添加类型检查
                if (!is_array($result) || !isset($result['code'])) {
                    Log::error('支付接口返回结构异常：' . gettype($result));
                    throw new \Exception('支付接口返回格式错误');
                }

                // 添加支付状态校验
                if ($result['code'] != 1) {
                    Log::error('支付接口调用失败：' . ($result['msg'] ?? '未知原因'));
                    throw new \Exception('支付接口调用失败: '.($result['msg'] ?? ''));
                }

                Db::commit(); // 事务提交
            }  catch (\Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            // 成功响应移动到事务外（确保$result已定义）
            if (isset($result) && is_array($result)) {
                $this->success('订单创建成功', $result['data']);
            }
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 模拟支付回调处理
     * @param string $type 支付类型 wechat/alipay
     */
    public function mock_notify($type = 'wechat')
    {
        try {
            // 获取订单号
            $orderNo = $this->request->param('order_no');
            if (empty($orderNo)) {
                throw new \Exception('订单号不能为空');
            }

            // 构建模拟回调数据
            $notifyData = $this->buildMockNotifyData($type, $orderNo);
            
            // 调用支付服务处理回调
            $result = \app\common\service\PaymentService::handlePaySuccess($orderNo, $notifyData);
            
            if ($result) {
                Log::info("模拟支付回调处理成功，订单号：{$orderNo}");
                return json(['code' => 1, 'msg' => 'success']);
            } else {
                Log::error("模拟支付回调处理失败，订单号：{$orderNo}");
                return json(['code' => 0, 'msg' => '处理失败']);
            }
        } catch (\Exception $e) {
            Log::error("模拟支付回调异常：{$e->getMessage()}");
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 构建模拟回调数据
     */
    private function buildMockNotifyData($type, $orderNo)
    {
        // 获取支付记录
        $payment = Db::name('payment')->where('order_no', $orderNo)->find();
        if (!$payment) {
            throw new \Exception('支付订单不存在');
        }

        $baseData = [
            'out_trade_no' => $orderNo,
            'trade_no' => 'MOCK_' . strtoupper($type) . '_' . date('YmdHis') . rand(1000, 9999),
            'amount' => $payment['amount'],
            'total_amount' => $payment['amount'],
            'total_fee' => $payment['amount'] * 100, // 微信支付金额单位为分
            'trade_status' => 'TRADE_SUCCESS',
            'buyer_id' => 'mock_buyer_id',
            'seller_id' => 'mock_seller_id',
            'gmt_payment' => date('Y-m-d H:i:s'),
            'notify_time' => date('Y-m-d H:i:s'),
        ];

        if ($type === 'wechat') {
            return array_merge($baseData, [
                'appid' => 'mock_wx_appid',
                'mch_id' => 'mock_mch_id',
                'nonce_str' => md5(uniqid()),
                'sign' => md5('mock_sign'),
                'result_code' => 'SUCCESS',
                'return_code' => 'SUCCESS',
            ]);
        } else {
            return array_merge($baseData, [
                'app_id' => 'mock_alipay_appid',
                'seller_id' => 'mock_seller_id',
                'charset' => 'utf-8',
                'sign_type' => 'RSA2',
                'sign' => 'mock_sign',
            ]);
        }
    }

    /**
     * 模拟支付页面（用于H5支付）
     */
    public function mock_payment_page()
    {
        $type = $this->request->param('type', 'wechat');
        $orderNo = $this->request->param('order_no');
        
        if (empty($orderNo)) {
            return '订单号不能为空';
        }

        // 获取支付记录
        $payment = Db::name('payment')->where('order_no', $orderNo)->find();
        if (!$payment) {
            return '支付订单不存在';
        }

        $html = $this->buildMockPaymentPage($type, $payment);
        return $html;
    }

    /**
     * 构建模拟支付页面
     */
    private function buildMockPaymentPage($type, $payment)
    {
        $title = $type === 'wechat' ? '微信支付' : '支付宝支付';
        $logo = $type === 'wechat' ? '/static/wechat-pay.png' : '/static/alipay.png';
        $createTime = date('Y-m-d H:i:s', $payment['create_time']);
        
        return <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$title} - 模拟支付</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 400px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { width: 60px; height: 60px; margin-bottom: 15px; }
        .title { font-size: 24px; color: #333; margin-bottom: 10px; }
        .subtitle { color: #666; font-size: 14px; }
        .amount { text-align: center; margin: 30px 0; }
        .amount-label { font-size: 14px; color: #666; margin-bottom: 5px; }
        .amount-value { font-size: 32px; color: #ff6b35; font-weight: bold; }
        .order-info { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .order-item { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .order-item:last-child { margin-bottom: 0; }
        .order-label { color: #666; }
        .order-value { color: #333; font-weight: bold; }
        .pay-button { width: 100%; background: #07c160; color: white; border: none; padding: 15px; border-radius: 8px; font-size: 16px; cursor: pointer; margin-top: 20px; }
        .pay-button:hover { background: #06ad56; }
        .cancel-button { width: 100%; background: #f8f9fa; color: #666; border: 1px solid #ddd; padding: 15px; border-radius: 8px; font-size: 16px; cursor: pointer; margin-top: 10px; }
        .cancel-button:hover { background: #e9ecef; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{$logo}" alt="{$title}" class="logo">
            <div class="title">{$title}</div>
            <div class="subtitle">模拟支付环境</div>
        </div>
        
        <div class="amount">
            <div class="amount-label">支付金额</div>
            <div class="amount-value">￥{$payment['amount']}</div>
        </div>
        
        <div class="order-info">
            <div class="order-item">
                <span class="order-label">订单号：</span>
                <span class="order-value">{$payment['order_no']}</span>
            </div>
            <div class="order-item">
                <span class="order-label">支付方式：</span>
                <span class="order-value">{$title}</span>
            </div>
            <div class="order-item">
                <span class="order-label">创建时间：</span>
                <span class="order-value">{$createTime}</span>
            </div>
        </div>
        
        <button class="pay-button" onclick="mockPay()">确认支付</button>
        <button class="cancel-button" onclick="cancelPay()">取消支付</button>
    </div>
    
    <script>
        function mockPay() {
            // 模拟支付延迟
            document.querySelector('.pay-button').disabled = true;
            document.querySelector('.pay-button').textContent = '支付中...';
            
            setTimeout(function() {
                // 调用模拟支付回调
                fetch('/api/recharge/mock_notify/{$type}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        order_no: '{$payment['order_no']}'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 1) {
                        alert('支付成功！');
                        // 跳转到成功页面或关闭窗口
                        if (window.opener) {
                            window.opener.postMessage({type: 'payment_success', order_no: '{$payment['order_no']}'}, '*');
                            window.close();
                        } else {
                            window.location.href = '/pages/pay/success?order_no={$payment['order_no']}';
                        }
                    } else {
                        alert('支付失败：' + data.msg);
                        document.querySelector('.pay-button').disabled = false;
                        document.querySelector('.pay-button').textContent = '确认支付';
                    }
                })
                .catch(error => {
                    console.error('支付请求失败：', error);
                    alert('支付请求失败，请重试');
                    document.querySelector('.pay-button').disabled = false;
                    document.querySelector('.pay-button').textContent = '确认支付';
                });
            }, 2000);
        }
        
        function cancelPay() {
            if (window.opener) {
                window.opener.postMessage({type: 'payment_cancel'}, '*');
                window.close();
            } else {
                window.history.back();
            }
        }
    </script>
</body>
</html>
HTML;
    }

}
