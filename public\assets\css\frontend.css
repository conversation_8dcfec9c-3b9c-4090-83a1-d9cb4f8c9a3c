@import url("../css/bootstrap.css");
@import url("../css/fastadmin.css");
@import url("../css/iconfont.css");
@import url("../libs/font-awesome/css/font-awesome.min.css");
@import url("../libs/toastr/toastr.min.css");
@import url("../libs/fastadmin-layer/dist/theme/default/layer.css");
@import url("../libs/bootstrap-table/dist/bootstrap-table.min.css");
@import url("../libs/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css");
@import url("../libs/bootstrap-daterangepicker/daterangepicker.css");
@import url("../libs/nice-validator/dist/jquery.validator.css");
@import url("../libs/bootstrap-select/dist/css/bootstrap-select.min.css");
@import url("../libs/fastadmin-selectpage/selectpage.css");
@import url("../libs/bootstrap-slider/slider.css");
.m-0 {
  margin-top: 0px !important;
  margin-right: 0px !important;
  margin-bottom: 0px !important;
  margin-left: 0px !important;
}
.mt-0 {
  margin-top: 0px !important;
}
.mr-0 {
  margin-right: 0px !important;
}
.mb-0 {
  margin-bottom: 0px !important;
}
.ml-0 {
  margin-left: 0px !important;
}
.mx-0 {
  margin-left: 0px !important;
  margin-right: 0px !important;
}
.my-0 {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}
.m-1 {
  margin-top: 5px !important;
  margin-right: 5px !important;
  margin-bottom: 5px !important;
  margin-left: 5px !important;
}
.mt-1 {
  margin-top: 5px !important;
}
.mr-1 {
  margin-right: 5px !important;
}
.mb-1 {
  margin-bottom: 5px !important;
}
.ml-1 {
  margin-left: 5px !important;
}
.mx-1 {
  margin-left: 5px !important;
  margin-right: 5px !important;
}
.my-1 {
  margin-top: 5px !important;
  margin-bottom: 5px !important;
}
.m-2 {
  margin-top: 10px !important;
  margin-right: 10px !important;
  margin-bottom: 10px !important;
  margin-left: 10px !important;
}
.mt-2 {
  margin-top: 10px !important;
}
.mr-2 {
  margin-right: 10px !important;
}
.mb-2 {
  margin-bottom: 10px !important;
}
.ml-2 {
  margin-left: 10px !important;
}
.mx-2 {
  margin-left: 10px !important;
  margin-right: 10px !important;
}
.my-2 {
  margin-top: 10px !important;
  margin-bottom: 10px !important;
}
.m-3 {
  margin-top: 15px !important;
  margin-right: 15px !important;
  margin-bottom: 15px !important;
  margin-left: 15px !important;
}
.mt-3 {
  margin-top: 15px !important;
}
.mr-3 {
  margin-right: 15px !important;
}
.mb-3 {
  margin-bottom: 15px !important;
}
.ml-3 {
  margin-left: 15px !important;
}
.mx-3 {
  margin-left: 15px !important;
  margin-right: 15px !important;
}
.my-3 {
  margin-top: 15px !important;
  margin-bottom: 15px !important;
}
.m-4 {
  margin-top: 20px !important;
  margin-right: 20px !important;
  margin-bottom: 20px !important;
  margin-left: 20px !important;
}
.mt-4 {
  margin-top: 20px !important;
}
.mr-4 {
  margin-right: 20px !important;
}
.mb-4 {
  margin-bottom: 20px !important;
}
.ml-4 {
  margin-left: 20px !important;
}
.mx-4 {
  margin-left: 20px !important;
  margin-right: 20px !important;
}
.my-4 {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}
.p-0 {
  padding-top: 0px !important;
  padding-right: 0px !important;
  padding-bottom: 0px !important;
  padding-left: 0px !important;
}
.pt-0 {
  padding-top: 0px !important;
}
.pr-0 {
  padding-right: 0px !important;
}
.pb-0 {
  padding-bottom: 0px !important;
}
.pl-0 {
  padding-left: 0px !important;
}
.px-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.py-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.p-1 {
  padding-top: 5px !important;
  padding-right: 5px !important;
  padding-bottom: 5px !important;
  padding-left: 5px !important;
}
.pt-1 {
  padding-top: 5px !important;
}
.pr-1 {
  padding-right: 5px !important;
}
.pb-1 {
  padding-bottom: 5px !important;
}
.pl-1 {
  padding-left: 5px !important;
}
.px-1 {
  padding-left: 5px !important;
  padding-right: 5px !important;
}
.py-1 {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}
.p-2 {
  padding-top: 10px !important;
  padding-right: 10px !important;
  padding-bottom: 10px !important;
  padding-left: 10px !important;
}
.pt-2 {
  padding-top: 10px !important;
}
.pr-2 {
  padding-right: 10px !important;
}
.pb-2 {
  padding-bottom: 10px !important;
}
.pl-2 {
  padding-left: 10px !important;
}
.px-2 {
  padding-left: 10px !important;
  padding-right: 10px !important;
}
.py-2 {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
.p-3 {
  padding-top: 15px !important;
  padding-right: 15px !important;
  padding-bottom: 15px !important;
  padding-left: 15px !important;
}
.pt-3 {
  padding-top: 15px !important;
}
.pr-3 {
  padding-right: 15px !important;
}
.pb-3 {
  padding-bottom: 15px !important;
}
.pl-3 {
  padding-left: 15px !important;
}
.px-3 {
  padding-left: 15px !important;
  padding-right: 15px !important;
}
.py-3 {
  padding-top: 15px !important;
  padding-bottom: 15px !important;
}
.p-4 {
  padding-top: 20px !important;
  padding-right: 20px !important;
  padding-bottom: 20px !important;
  padding-left: 20px !important;
}
.pt-4 {
  padding-top: 20px !important;
}
.pr-4 {
  padding-right: 20px !important;
}
.pb-4 {
  padding-bottom: 20px !important;
}
.pl-4 {
  padding-left: 20px !important;
}
.px-4 {
  padding-left: 20px !important;
  padding-right: 20px !important;
}
.py-4 {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}
html,
body {
  height: 100%;
}
body {
  padding-top: 60px;
  font-size: 14px;
  background: #f4f6f8;
  height: 100%;
  line-height: 1.5715;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'liga';
  -webkit-text-size-adjust: 100%;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Ubuntu, Helvetica Neue, Helvetica, Arial, PingFang SC, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Source Han Sans CN, sans-serif;
  font-weight: 400;
  color: #616161;
}
a {
  color: #007bff;
}
a:hover,
a:focus {
  color: #007bff;
}
.navbar-white {
  background-color: #fff;
  border-color: #fff;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}
.navbar-white .dropdown-menu {
  border-radius: 5px;
  -webkit-box-shadow: 0px 20px 30px rgba(83, 88, 93, 0.05), 0px 0px 30px rgba(83, 88, 93, 0.1);
  -moz-box-shadow: 0px 20px 30px rgba(83, 88, 93, 0.05), 0px 0px 30px rgba(83, 88, 93, 0.1);
  box-shadow: 0px 20px 30px rgba(83, 88, 93, 0.05), 0px 0px 30px rgba(83, 88, 93, 0.1);
}
@media (min-width: 768px) {
  .navbar-default .navbar-brand {
    height: 60px;
    line-height: 27px;
  }
  .navbar-default .navbar-nav > li > a {
    height: 60px;
    line-height: 27px;
  }
  .navbar-white .navbar-brand {
    height: 60px;
    line-height: 27px;
  }
  .navbar-white .navbar-nav > li > a {
    height: 60px;
    line-height: 27px;
    color: #555;
  }
  .navbar-white .navbar-nav > li > a:hover,
  .navbar-white .navbar-nav > li > a:focus {
    color: #007bff;
  }
  .navbar-white .navbar-nav > .active > a,
  .navbar-white .navbar-nav > .active > a:hover,
  .navbar-white .navbar-nav > .active > a:focus {
    background-color: inherit;
    color: #007bff;
  }
}
@media (max-width: 768px) {
  body {
    padding-top: 50px;
  }
  .navbar-white .navbar-nav .open .dropdown-menu {
    background: #eee;
  }
  .navbar-white .navbar-toggle {
    border-color: #ddd;
  }
  .navbar-white .navbar-toggle .icon-bar {
    background-color: #888;
  }
  .navbar-white .navbar-collapse.in {
    border-top-color: #f5f5f5;
  }
}
#header-navbar .dropdown:hover .dropdown-menu {
  display: block;
  margin-top: 0;
}
#header-navbar li.dropdown ul.dropdown-menu {
  min-width: 100px;
}
.navbar {
  border: none;
}
.navbar-nav > li > a {
  font-size: 14px;
}
.dropdown-menu > li > a {
  font-size: 14px;
  padding: 5px 20px;
}
.dropdown-menu {
  border-radius: 2px;
  border: 0px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 5px 0px;
}
.dropdown-menu li a {
  padding-top: 10px !important;
  padding-bottom: 10px;
}
.dropdown-menu > li > a {
  font-weight: 400;
  color: #444;
  padding: 5px 15px;
  padding-bottom: 10px;
}
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
  text-decoration: none;
  color: #777;
  background: rgba(0, 0, 0, 0.05);
}
.toast-top-center {
  top: 60px;
}
#toast-container > div {
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
/*修复nice-validator和summernote的编辑框冲突*/
.nice-validator .note-editor .note-editing-area .note-editable {
  display: inherit;
}
/*预览区域*/
.plupload-preview,
.faupload-preview {
  padding: 0 10px;
  margin-bottom: 0;
}
.plupload-preview li,
.faupload-preview li {
  margin-top: 10px;
}
.plupload-preview .thumbnail,
.faupload-preview .thumbnail {
  margin-bottom: 10px;
}
.plupload-preview a,
.faupload-preview a {
  display: block;
}
.plupload-preview a:first-child,
.faupload-preview a:first-child {
  height: 90px;
}
.plupload-preview a img,
.faupload-preview a img {
  height: 80px;
  object-fit: cover;
}
.layui-layer-content {
  clear: both;
}
.layui-layer-fast .layui-layer-content > table.table {
  margin-bottom: 0;
}
.layui-layer-fast .layui-layer-confirm {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: 0;
  border: 1px solid transparent;
  background: transparent;
  color: transparent;
}
.layui-layer-fast .layui-layer-confirm:focus {
  border: 1px solid #444c69;
  -webkit-border-radius: 2px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 2px;
  -moz-background-clip: padding;
  border-radius: 2px;
  background-clip: padding-box;
}
.layui-layer-fast .layui-layer-confirm:focus-visible {
  outline: 0;
}
.layui-layer-fast-msg {
  min-width: 100px;
  border-radius: 2px;
}
.input-group > .msg-box.n-right {
  position: absolute;
}
.bootstrap-select {
  min-height: 33px;
}
.bootstrap-select .status {
  background: #f0f0f0;
  clear: both;
  color: #999;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  margin-bottom: -5px;
  padding: 10px 20px;
}
.bootstrap-select .msg-box {
  position: absolute;
  right: 0;
  top: 0;
}
.bootstrap-select .bs-placeholder {
  min-height: 33px;
}
select.bs-select-hidden,
select.selectpicker {
  display: inherit !important;
  max-height: 33px;
  overflow: hidden;
}
select.bs-select-hidden[multiple],
select.selectpicker[multiple] {
  height: 31px;
  padding: 0;
  background: #f4f4f4;
}
select.bs-select-hidden[multiple] option,
select.selectpicker[multiple] option {
  color: #f4f4f4;
  zoom: 1;
  filter: alpha(opacity=0);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
}
@media not all and (min-resolution: 0.001dpcm) {
  @supports (-webkit-appearance:none) {
    select.bs-select-hidden[multiple],
    select.selectpicker[multiple] {
      visibility: hidden;
    }
  }
}
input.selectpage {
  color: transparent;
  pointer-events: none;
}
.sp_container {
  min-height: 33px;
}
.sp_container input.selectpage {
  color: inherit;
  pointer-events: inherit;
  padding-left: 12px;
  padding-right: 12px;
}
.sp_container .sp_element_box input.selectpage {
  padding-left: 0;
  padding-right: 0;
}
.sp_container .sp_element_box li:first-child input.selectpage {
  padding-left: 9px;
  padding-right: 9px;
}
/*修复radio和checkbox样式对齐*/
.radio > label,
.checkbox > label {
  margin-right: 10px;
}
.radio > label > input,
.checkbox > label > input {
  margin: 5px 0 0;
}
form.form-horizontal .control-label {
  font-weight: normal;
}
.panel-default {
  padding: 0 15px;
  border: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.panel-default > .panel-heading {
  position: relative;
  font-size: 16px;
  padding: 15px 0;
  background: #fff;
  border-bottom: 1px solid #f5f5f5;
}
.panel-default h2.page-header {
  margin-top: 0;
  height: 50px;
  line-height: 31px;
  font-size: 18px;
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
}
.panel-default > .panel-heading .panel-title {
  color: #313131;
}
.panel-default > .panel-heading .panel-title > i {
  display: none;
}
.panel-default > .panel-heading .more {
  position: absolute;
  top: 13px;
  right: 0;
  display: block;
  color: #919191;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.panel-default > .panel-heading .more:hover {
  color: #616161;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.panel-default > .panel-heading .panel-bar {
  position: absolute;
  top: 7px;
  right: 0;
  display: block;
}
@media (max-width: 767px) {
  .panel-default {
    padding: 0 10px;
  }
  .panel-default > .panel-heading {
    padding: 10px 0;
  }
  .panel-default > .panel-heading .more {
    top: 8px;
  }
   > .panel-body {
    position: relative;
    padding: 15px 0;
  }
   > .panel-footer {
    padding: 15px 0;
    background: none;
  }
}
.panel-gray {
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  -moz-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}
.panel-gray > .panel-heading {
  background-color: #f5f5f5;
  color: #919191;
}
.panel-gray > .panel-body {
  color: #919191;
  background: #fff;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}
.panel-page {
  padding: 45px 50px 50px;
  min-height: 500px;
}
.panel-page .panel-heading {
  background: transparent;
  border-bottom: none;
  margin: 0 0 30px 0;
  padding: 0;
}
.panel-page .panel-heading h2 {
  font-size: 25px;
  margin-top: 0;
}
@media (max-width: 767px) {
  .panel-page {
    padding: 15px;
    min-height: 300px;
  }
  .n-bootstrap .n-right {
    margin-top: 0;
    top: -20px;
    position: absolute;
    left: 0;
    text-align: right;
    width: 100%;
  }
  .n-bootstrap .n-right .msg-wrap {
    position: relative;
  }
  .n-bootstrap .col-xs-12 > .n-right .msg-wrap {
    margin-right: 15px;
  }
}
.nav-pills > li {
  margin-right: 5px;
}
.nav-pills > li > a {
  padding: 10px 15px;
  color: #616161;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.nav-pills > li > a:hover {
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  background-color: #f5f5f5;
}
.nav-pills > li.active > a {
  border: none;
  color: #fff;
  background: #007bff;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border-radius: 3px;
}
.nav-pills.nav-pills-sm > li > a {
  font-size: 12px;
  line-height: 1.5;
  padding: 4px 13px;
}
.fieldlist dd {
  display: block;
  margin: 5px 0;
}
.fieldlist dd input {
  display: inline-block;
  width: 300px;
}
.fieldlist dd input:first-child {
  width: 110px;
}
.fieldlist dd ins {
  width: 110px;
  display: inline-block;
  text-decoration: none;
}
/* 弹窗中的表单 */
.form-layer {
  height: 100%;
  min-height: 150px;
  min-width: 300px;
}
.form-layer .form-body {
  width: 100%;
  overflow: auto;
  top: 0;
  position: absolute;
  z-index: 10;
  bottom: 50px;
  padding: 15px;
}
.form-layer .form-footer {
  height: 50px;
  line-height: 50px;
  background-color: #ecf0f1;
  width: 100%;
  position: absolute;
  z-index: 200;
  bottom: 0;
  margin: 0;
}
.form-layer .form-footer .form-group {
  margin-left: 0;
  margin-right: 0;
}
footer.footer {
  width: 100%;
  color: #aaa;
  background: #555;
  margin-top: 25px;
}
footer.footer .copyright {
  line-height: 50px;
  text-align: center;
  background: #393939;
  margin: 0;
}
footer.footer .copyright a {
  color: #aaa;
}
footer.footer .copyright a:hover {
  color: #fff;
}
.rotate {
  -webkit-transition-duration: 0.8s;
  -moz-transition-duration: 0.8s;
  -o-transition-duration: 0.8s;
  transition-duration: 0.8s;
  -webkit-transition-property: transform;
  transition-property: transform;
  -webkit-transition-property: -webkit-transform;
  -moz-transition-property: -moz-transform;
  -o-transition-property: -o-transform;
  transition-property: -webkit-transform,-moz-transform,-o-transform,transform;
  overflow: hidden;
}
.rotate:hover {
  -webkit-transform: rotate(360deg);
  -moz-transform: rotate(360deg);
  -o-transform: rotate(360deg);
  -ms-transform: rotate(360deg);
  transform: rotate(360deg);
}
.user-section {
  background: #fff;
  padding: 15px;
  margin-bottom: 20px;
  -webkit-border-radius: 4px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 4px;
  -moz-background-clip: padding;
  border-radius: 4px;
  background-clip: padding-box;
}
.login-section {
  margin: 50px auto;
  width: 460px;
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
}
.login-section.login-section-weixin {
  min-height: 315px;
}
.login-section .logon-tab {
  margin: -15px -15px 0 -15px;
}
.login-section .logon-tab > a {
  display: block;
  padding: 20px;
  float: left;
  width: 50%;
  font-size: 16px;
  text-align: center;
  color: #616161;
  background-color: #efefef;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.login-section .logon-tab > a:hover {
  background-color: #fafafa;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.login-section .logon-tab > a.active {
  background-color: #fff;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.login-section .login-main {
  padding: 40px 45px 20px 45px;
}
.login-section .n-bootstrap .controls,
.form-section .n-bootstrap .controls {
  position: relative;
}
.login-section .n-bootstrap .input-group,
.form-section .n-bootstrap .input-group {
  position: inherit;
}
.login-section .n-bootstrap .n-right,
.form-section .n-bootstrap .n-right {
  margin-top: 0;
  top: -20px;
  position: absolute;
  left: 0;
  text-align: right;
  width: 100%;
}
.login-section .n-bootstrap .n-right .msg-wrap,
.form-section .n-bootstrap .n-right .msg-wrap {
  position: relative;
}
main.content {
  width: 100%;
  overflow: auto;
  padding: 15px;
  padding-top: 20px;
  min-height: calc(100vh - 135px);
}
.sidenav {
  padding: 20px 0 10px 0;
  margin-bottom: 20px;
  background-color: #fff;
}
.sidenav .list-group:last-child {
  margin-bottom: 0;
}
.sidenav .list-group .list-group-heading {
  list-style-type: none;
  color: #919191;
  margin-bottom: 10px;
  margin-left: 35px;
  font-size: 14px;
}
.sidenav .list-group .list-group-item {
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
  border: none;
  padding: 0;
  border-left: 2px solid transparent;
}
.sidenav .list-group .list-group-item:last-child,
.sidenav .list-group .list-group-item:first-child {
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
}
.sidenav .list-group .list-group-item:hover {
  border-left: 2px solid rgba(245, 245, 245, 0.38);
  background-color: rgba(245, 245, 245, 0.38);
}
.sidenav .list-group .list-group-item > a {
  display: block;
  color: #616161;
  padding: 10px 15px 10px 35px;
}
.sidenav .list-group .list-group-item.active {
  border-left: 2px solid #007bff;
  background-color: rgba(245, 245, 245, 0.38);
}
.sidenav .list-group .list-group-item.active > a {
  color: #007bff;
}
.nav li .avatar-text,
.nav li .avatar-img {
  height: 30px;
  width: 30px;
  line-height: 30px;
  font-size: 14px;
}
.nav li .avatar-img {
  font-size: 0;
}
.nav li .avatar-img img {
  border-radius: 30px;
  width: 30px;
  height: 30px;
}
.avatar-text,
.avatar-img {
  display: inline-block;
  box-sizing: content-box;
  color: #fff;
  text-align: center;
  vertical-align: top;
  background-color: #e8ecf3;
  font-weight: normal;
  width: 48px;
  height: 48px;
  border-radius: 48px;
  font-size: 24px;
  line-height: 48px;
}
.avatar-img {
  font-size: 0;
}
.avatar-img img {
  border-radius: 48px;
  width: 48px;
  height: 48px;
}
@media (max-width: 767px) {
  main.content {
    position: inherit;
    padding: 15px 0;
  }
  .login-section {
    width: 100%;
    margin: 20px auto;
  }
  .login-section .login-main {
    padding: 20px 0 0 0;
  }
  footer.footer {
    position: inherit;
  }
  footer.footer .copyright {
    padding: 10px;
    line-height: 30px;
  }
}
.pager .pagination {
  margin: 0;
}
.pager li {
  margin: 0 0.4em;
  display: inline-block;
}
.pager li:first-child > a,
.pager li:last-child > a,
.pager li:first-child > span,
.pager li:last-child > span {
  padding: 0.5em 1.2em;
}
.pager li > a,
.pager li > span {
  background: none;
  border: 1px solid #e6e6e6;
  border-radius: 0.25em;
  padding: 0.5em 0.93em;
  font-size: 14px;
}
.jumpto input {
  width: 50px;
  margin-left: 5px;
  margin-right: 5px;
  text-align: center;
  display: inline-block;
}
.fixed-columns,
.fixed-columns-right {
  position: absolute;
  top: 0;
  height: 100%;
  min-height: 41px;
  background-color: #fff;
  box-sizing: border-box;
  z-index: 2;
  box-shadow: 0 -1px 8px rgba(0, 0, 0, 0.08);
}
.fixed-columns .fixed-table-body,
.fixed-columns-right .fixed-table-body {
  min-height: 41px;
  overflow-x: hidden !important;
}
.fixed-columns {
  left: 0;
}
.fixed-columns-right {
  right: 0;
  box-shadow: -1px 0 8px rgba(0, 0, 0, 0.08);
}
.bootstrap-tagsinput {
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  display: inline-block;
  padding: 4px 6px;
  color: #555;
  vertical-align: middle;
  width: 100%;
  line-height: 22px;
  cursor: text;
}
.bootstrap-tagsinput input {
  border: none;
  box-shadow: none;
  outline: none;
  background-color: transparent;
  padding: 0;
  margin: 0;
  font-size: 13px;
  width: 80px;
  max-width: inherit;
}
.bootstrap-tagsinput input:focus {
  border: none;
  box-shadow: none;
}
.bootstrap-tagsinput .tagsinput-text {
  display: inline-block;
  overflow: auto;
  visibility: hidden;
  height: 1px;
  position: absolute;
  bottom: -1px;
  left: 0;
}
.bootstrap-tagsinput .tag {
  margin-right: 2px;
  color: white;
}
.bootstrap-tagsinput .tag [data-role="remove"] {
  margin-left: 5px;
  cursor: pointer;
}
.bootstrap-tagsinput .tag [data-role="remove"]:after {
  content: "x";
  padding: 0px 2px;
}
.bootstrap-tagsinput .tag [data-role="remove"]:hover {
  background-color: rgba(255, 255, 255, 0.16);
}
.autocomplete-suggestions {
  border-radius: 2px;
  background: #FFF;
  overflow: auto;
  min-width: 200px;
  -webkit-box-shadow: 0px 20px 30px rgba(83, 88, 93, 0.05), 0px 0px 30px rgba(83, 88, 93, 0.1);
  -moz-box-shadow: 0px 20px 30px rgba(83, 88, 93, 0.05), 0px 0px 30px rgba(83, 88, 93, 0.1);
  box-shadow: 0px 20px 30px rgba(83, 88, 93, 0.05), 0px 0px 30px rgba(83, 88, 93, 0.1);
}
.autocomplete-suggestions strong {
  font-weight: normal;
  color: red;
}
.autocomplete-suggestions .autocomplete-suggestion {
  padding: 5px 10px;
  white-space: nowrap;
  overflow: hidden;
}
.autocomplete-suggestions .autocomplete-selected {
  background: #F0F0F0;
}
.autocomplete-suggestions .autocomplete-group {
  padding: 5px 10px;
}
.autocomplete-suggestions .autocomplete-group strong {
  display: block;
  border-bottom: 1px solid #ddd;
}
.autocontent {
  position: relative;
}
.autocontent .autocontent-caret {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  line-height: 1;
  background: #eee;
  color: #ddd;
  vertical-align: middle;
  padding: 0 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.autocontent .autocontent-caret:hover {
  color: #ccc;
}
/*# sourceMappingURL=frontend.css.map */