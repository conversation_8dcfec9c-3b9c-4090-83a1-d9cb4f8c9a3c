define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'verification/log/index' + location.search,
                    add_url: '',
                    edit_url: '',
                    del_url: '',
                    table: 'merchant',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='user_id']", form).addClass("selectpage").data("source", "user/user/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                $("input[name='merchant_id']", form).addClass("selectpage").data("source", "verification/merchant/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据

                //这里我们手动设置底部的值
                $("#number").text(data.extend.number);
                $("#score").text(data.extend.score);
                $("#money").text(data.extend.money);
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID')},
                        {field: 'avatar', title: __('用户头像'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'user_id',title: __('用户信息'),formatter: function (value, row, index) {
                                if (row.nickname) {
                                    return row.nickname+'<br>'+row.mobile+'<br>'+row.user_id;
                                }else{
                                    return row.user_id;
                                }
                            }},
                        {field: 'merchant_id',title: __('商户信息'),formatter: function (value, row, index) {
                                if (row.merchant_name) {
                                    return row.merchant_name+'<br>'+row.merchant_mobile+'<br>'+row.merchant_id;
                                }else{
                                    return row.merchant_id;
                                }
                            }},
                        {field: 'merchant_logo', title: __('商户头像'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'score', title: __('核销积分'), operate: false},
                        {field: 'money', title: __('折算金额(元)'), operate: false},
                        {field: 'status', title: __('状态'), formatter: Table.api.formatter.status, searchList: {"0": __('失败'),"1": __('成功')}},
                        {field: 'memo', title: __('备注'), operate: false},
                        {field: 'receipt_image', title: __('小票图片'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'createtime', title: __('核销时间'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    dropdown: "更多",
                                    name: 'merchant',
                                    title: __('用户信息'),
                                    text:'用户信息',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","60%"]\'',
                                    icon: 'fa fa-user-circle-o',
                                    url: function (row){
                                        return "user/user/index?id=" + row.user_id;
                                    }
                                },
                                {
                                    dropdown: "更多",
                                    name: 'merchant',
                                    title: __('商户信息'),
                                    text:'商户信息',
                                    classname: 'btn btn-magic btn-dialog',
                                    extend:'data-area=\'["70%","60%"]\'',
                                    icon: 'fa fa-user-circle-o',
                                    url: function (row){
                                        return "verification/merchant/index?id=" + row.merchant_id;
                                    }
                                },

                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        reset_password: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});