<template>
	<view class="container" :class="{ 'force-landscape': isLandscape }">
		<!-- 顶部tab切换 -->
		<view class="pay-tabs">
			<view :class="['pay-tab', activeTab === 0 ? 'active' : '']" @tap="activeTab = 0">特惠礼包</view>
			<view :class="['pay-tab', activeTab === 1 ? 'active' : '']" @tap="activeTab = 1">普通充值</view>
		</view>

		<!-- 余额区 -->
		<view class="balance-section">
			<view class="balance-info">
				<view class="balance-left">
					<image src="/static/coin.png" class="coin-icon" />
					<text class="balance-label">账户{{ coinsName }}</text>
				</view>
				<view class="balance-value">{{ balance }}</view>
			</view>
		</view>

		<!-- tab内容区 -->
		<view v-if="loading" class="loading">加载中...</view>
		<template v-else>
			<view v-if="activeTab === 0">
				<!-- 特惠礼包内容 -->
				<view class="special-pay-list">
					<view class="special-pay-item" v-for="item in normalList" :key="item.id"
						@tap="openConfirmModal(item)">
						<view class="card-tag-header">
							<view v-if="item.gift_coin > 0" class="card-tag-left">收益{{ item.profit }}</view>
							<view v-if="item.gift_coin > 0" class="card-tag-right">赠送{{ item.gift_coin }}</view>
							<view v-else class="card-tag-full">收益{{ item.profit }}</view>
						</view>
						<view class="card-content">
							<view class="normal-amount">{{ item.coin }}</view>
							<image src="/static/coin-1.png" class="normal-coin" />
						</view>
						<view class="normal-price">￥{{ item.price }}</view>
					</view>
				</view>
				<view class="super-pay-list">
					<view class="super-pay-item" v-for="item in superList" :key="item.id" @tap="openConfirmModal(item)">
						<view class="card-header">
							<view class="card-tag-full">{{ item.price }}元超值套餐</view>
						</view>
						<view class="card-content">
							<view class="super-label">多笔充值奖上奖</view>
						</view>
						<view class="super-desc">
							<view class="super-benefit">
								<text class="super-label-blue">最大收益</text>
								<text class="orange">{{ item.profit }}</text>
							</view>
							<view class="super-get">
								<text class="super-label-blue">立即获得</text>
								<text class="blue">{{ item.coin }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view v-else>
				<!-- 普通充值内容 -->
				<view class="normal-pay-list">
					<view class="normal-pay-item" v-for="item in packageList" :key="item.id"
						@tap="openConfirmModal(item)">
						<view class="card-tag-header">
							<view v-if="item.gift_coin > 0" class="card-tag-left">收益{{ item.profit }}</view>
							<view v-if="item.gift_coin > 0" class="card-tag-right">赠送{{ item.gift_coin }}</view>
							<view v-else class="card-tag-full">收益{{ item.profit }}</view>
						</view>
						<view class="card-content">
							<view class="normal-amount">{{ item.coin }}</view>
							<image src="/static/coin-1.png" class="normal-coin" />
						</view>
						<view class="normal-price">￥{{ item.price }}</view>
					</view>
				</view>
			</view>
		</template>

		<!-- 充值说明 -->
		<view class="pay-desc">
			<view class="card-header">
				<view class="card-tag-full">充值说明</view>
			</view>
			<view class="pay-desc-list">
				<view>1、充值中心仅限于本平台使用，充值后无法提现。</view>
				<view>2、如您未满18周岁，禁止在本平台充值。</view>
				<view>3、若遇到充值未到账，请联系客服。</view>
			</view>
		</view>

		<!-- 确认充值弹窗 -->
		<UniversalModal
			:show="showConfirmModal"
			title="确认充值"
			@close="closeConfirmModal"
			size="large"
			:mask-closable="false"
		>
			<view class="modal-price-section">
				<text class="modal-price-label">{{ confirmModalData.coin }}</text>
				<text class="modal-price-value">￥{{ confirmModalData.price }}</text>
			</view>
			<view class="modal-divider"></view>
			<view class="modal-detail-list">
				<view class="modal-detail-item">
					<text class="detail-label">套餐币总数量</text>
					<text class="detail-value">{{ confirmModalData.total_coins }}</text>
				</view>
				<view class="modal-detail-item">
					<text class="detail-label">套餐基础币数量</text>
					<text class="detail-value">{{ confirmModalData.coin }}</text>
				</view>
				<view class="modal-detail-item">
					<text class="detail-label">额外赠币数量</text>
					<text class="detail-value">{{ confirmModalData.gift_coin }}</text>
				</view>
				<view class="modal-detail-item">
					<text class="detail-label">VIP加成币数量</text>
					<text class="detail-value">{{ confirmModalData.vip_coins }}</text>
				</view>
			</view>
			<view class="modal-divider"></view>

			<!-- 支付方式选择 -->
			<view class="payment-methods">
				<view class="payment-method-title">选择支付方式</view>
				<view class="payment-method-list">
					<view class="payment-method-item" :class="{ active: selectedPaymentMethod === 'wechat' }"
						@tap="selectPaymentMethod('wechat')">
						<image src="/static/wechat-pay.png" class="payment-icon" />
						<text>微信支付</text>
					</view>
					<view class="payment-method-item" :class="{ active: selectedPaymentMethod === 'alipay' }"
						@tap="selectPaymentMethod('alipay')">
						<image src="/static/alipay.png" class="payment-icon" />
						<text>支付宝支付</text>
					</view>
				</view>
			</view>

			<template #footer>
				<button class="confirm-button modal-button primary" @tap="confirmPayment">确定</button>
			</template>
		</UniversalModal>
	</view>
</template>

<script>
	import request from '@/utils/request.js'
	import UniversalModal from '@/components/UniversalModal.vue'

	export default {
		components: {
			UniversalModal
		},
		data() {
			return {
				activeTab: 0,
				balance: 0,
				loading: false,
				rechargeList: [],
				normalList: [], // 特惠礼包 is_package=0
				superList: [], // 特惠礼包 is_package=1
				packageList: [], // 普通充值
				showConfirmModal: false,
				confirmModalData: {},
				selectedPaymentMethod: 'wechat', // 默认选择微信支付
				platform: '', // 当前平台
				gameConfig: {} ,// 游戏配置
				coinsName: '金币', // 默认值，从接口获取
				isLandscape: false, // 是否为横屏游戏
			}
		},
		onLoad() {
			// 获取当前平台
			// #ifdef APP-PLUS
			this.platform = 'app'
			// #endif

			// #ifdef H5
			this.platform = 'h5'
			// #endif

			this.getGameConfig()
			this.getRechargeList()
			this.getUserInfo()
		},

		onUnload() {
			// 页面卸载时，如果用户没有返回游戏，清除游戏来源信息
			// 这样可以避免下次进入充值页面时显示过期的游戏信息
		},
		methods: {

			async getConfig() {
				try {
					const res = await request({
						url: '/api/game/get_config'
					});
					if (res.code === 1 ) {
						if (res.data.coins_name) {
							this.coinsName = res.data.coins_name;
						}
						return true;
					} else {
						this.showError('获取配置失败');
						return false;
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
					return false;
				}
			},
			async getGameConfig() {
				try {
					const res = await request({
						url: '/api/game/get_config'
					})
					if (res.code === 1) {
						this.gameConfig = res.data
					}
				} catch (error) {
					console.error('获取游戏配置失败：', error)
				}
			},
			async getUserInfo() {
				try {
					const res = await request({
						url: '/api/user/detail'
					})
					if (res.code === 1) {
						this.balance = res.data.money
					}
				} catch (error) {
					console.error('获取用户信息失败：', error)
				}
			},
			async getRechargeList() {
				try {
					this.loading = true
					const res = await request({
						url: '/api/recharge/list'
					})
					if (res.code === 1) {
						this.rechargeList = res.data
						// 处理数据分类
						this.normalList = res.data.filter(item => item.type === 2 && item.is_package === 0)
						this.superList = res.data.filter(item => item.type === 2 && item.is_package === 1)
						this.packageList = res.data.filter(item => item.type === 1)
					} else {
						uni.showToast({
							title: res.msg || '获取充值列表失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('获取充值列表失败：', error)
					uni.showToast({
						title: '网络错误',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			},
			openConfirmModal(item) {
				// 检查充值开关
				if (this.gameConfig.recharge === '0') {
					uni.showToast({
						title: '充值通道正在维护中',
						icon: 'none'
					})
					return
				}

				this.confirmModalData = {
					...item,
					vip_coins: 0,
					total_coins: item.coin + (item.gift_coin || 0) + 0
				}
				this.showConfirmModal = true
			},
			closeConfirmModal() {
				this.showConfirmModal = false
				this.confirmModalData = {}
			},
			selectPaymentMethod(method) {
				this.selectedPaymentMethod = method
			},
			async confirmPayment() {
				try {
					// 创建订单
					const orderRes = await request({
						url: '/api/recharge/create_order',
						method: 'POST',
						data: {
							id: this.confirmModalData.id,
							type: this.selectedPaymentMethod,
							method: this.platform === 'app' ? 'app' : 'wap' // 添加支付类型：app或wap
						}
					})

					if (orderRes.code !== 1) {
						uni.showToast({
							title: orderRes.msg || '创建订单失败',
							icon: 'none'
						})
						this.closeConfirmModal() // 创建订单失败时关闭弹窗
						return
					}

					const orderData = orderRes.data

					// 根据平台和支付方式处理支付
					if (this.platform === 'app') {
						await this.handleAppPayment(orderData)
					} else {
						await this.handleH5Payment(orderData)
					}

				} catch (error) {
					console.error('支付请求失败：', error)
					uni.showToast({
						title: '网络错误，请稍后再试',
						icon: 'none'
					})
				}
			},
			async handleAppPayment(orderData) {
				try {
					// #ifdef APP-PLUS
					if (this.selectedPaymentMethod === 'wechat') {
						// 检查是否为模拟支付
						if (orderData.pay_params && orderData.pay_params.appid === 'mock_wx_appid') {
							// 模拟支付：显示确认对话框
							uni.showModal({
								title: '模拟支付',
								content: `确认支付 ￥${this.confirmModalData.price} 吗？`,
								success: (res) => {
									if (res.confirm) {
										// 模拟支付延迟
										uni.showLoading({
											title: '支付中...'
										});

										setTimeout(() => {
											uni.hideLoading();
											// 调用模拟支付回调
											this.callMockPaymentCallback(orderData.order_no,
												'wechat');
										}, 2000);
									}
								}
							});
						} else {
							// 真实微信支付
							uni.requestPayment({
								provider: 'wxpay',
								orderInfo: orderData.pay_params,
								success: (res) => {
									this.handlePaymentSuccess(orderData.order_no);
								},
								fail: (err) => {
									console.error('微信支付失败：', err);
									uni.showToast({
										title: '支付失败',
										icon: 'none'
									});
								}
							});
						}
					} else if (this.selectedPaymentMethod === 'alipay') {
						// 检查是否为模拟支付
						if (orderData.pay_params && orderData.pay_params.includes('mock_alipay_order_string')) {
							// 模拟支付：显示确认对话框
							uni.showModal({
								title: '模拟支付',
								content: `确认支付 ￥${this.confirmModalData.price} 吗？`,
								success: (res) => {
									if (res.confirm) {
										// 模拟支付延迟
										uni.showLoading({
											title: '支付中...'
										});

										setTimeout(() => {
											uni.hideLoading();
											// 调用模拟支付回调
											this.callMockPaymentCallback(orderData.order_no,
												'alipay');
										}, 2000);
									}
								}
							});
						} else {
							// 真实支付宝支付
							uni.requestPayment({
								provider: 'alipay',
								orderInfo: orderData.pay_params,
								success: (res) => {
									this.handlePaymentSuccess(orderData.order_no);
								},
								fail: (err) => {
									console.error('支付宝支付失败：', err);
									uni.showToast({
										title: '支付失败',
										icon: 'none'
									});
								}
							});
						}
					}
					// #endif
				} catch (error) {
					console.error('APP支付处理失败：', error);
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					});
				}
			},

			// 调用模拟支付回调
			async callMockPaymentCallback(orderNo, type) {
				try {
					const res = await request({
						url: `/api/recharge/mock_notify/${type}`,
						method: 'POST',
						data: {
							order_no: orderNo
						}
					});

					if (res.code === 1) {
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						});
						this.closeConfirmModal();
						this.getUserInfo(); // 刷新余额
					} else {
						uni.showToast({
							title: res.msg || '支付失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('模拟支付回调失败：', error);
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					});
				}
			},
			async handleH5Payment(orderData) {
				try {
					// #ifdef H5
					if (this.selectedPaymentMethod === 'wechat') {
						// 微信H5支付
						if (orderData.redirect_url) {
							// 检查是否为模拟支付链接
							if (orderData.redirect_url.includes('/mock_payment/')) {
								// 模拟支付：打开新窗口
								const mockWindow = window.open(orderData.redirect_url, '_blank',
									'width=400,height=600');

								// 监听支付结果
								window.addEventListener('message', (event) => {
									if (event.data.type === 'payment_success') {
										this.handlePaymentSuccess(event.data.order_no);
										mockWindow.close();
									} else if (event.data.type === 'payment_cancel') {
										mockWindow.close();
										uni.showToast({
											title: '支付已取消',
											icon: 'none'
										});
									}
								});
							} else {
								// 真实支付：直接跳转
								window.location.href = orderData.redirect_url;
							}
						} else if (orderData.pay_url) {
							// 兼容旧版本
							window.location.href = orderData.pay_url;
						} else {
							// 处理微信JSAPI支付
							const wx = window.wx;
							if (wx && wx.chooseWXPay) {
								wx.chooseWXPay({
									...orderData.pay_params,
									success: (res) => {
										this.handlePaymentSuccess(orderData.order_no);
									},
									fail: (err) => {
										console.error('微信支付失败：', err);
										uni.showToast({
											title: '支付失败',
											icon: 'none'
										});
									}
								});
							} else {
								uni.showToast({
									title: '请在微信中打开',
									icon: 'none'
								});
							}
						}
					} else if (this.selectedPaymentMethod === 'alipay') {
						// 支付宝H5支付
						if (orderData.pay_url) {
							// 检查是否为模拟支付链接
							if (orderData.pay_url.includes('/mock_payment/')) {
								// 模拟支付：打开新窗口
								const mockWindow = window.open(orderData.pay_url, '_blank', 'width=400,height=600');

								// 监听支付结果
								window.addEventListener('message', (event) => {
									if (event.data.type === 'payment_success') {
										this.handlePaymentSuccess(event.data.order_no);
										mockWindow.close();
									} else if (event.data.type === 'payment_cancel') {
										mockWindow.close();
										uni.showToast({
											title: '支付已取消',
											icon: 'none'
										});
									}
								});
							} else {
								// 真实支付：直接跳转
								window.location.href = orderData.pay_url;
							}
						} else {
							// 处理支付宝JSAPI支付
							const AlipayJSBridge = window.AlipayJSBridge;
							if (AlipayJSBridge) {
								AlipayJSBridge.call('tradePay', {
									tradeNO: orderData.pay_params.tradeNO
								}, (result) => {
									if (result.resultCode === '9000') {
										this.handlePaymentSuccess(orderData.order_no);
									} else {
										uni.showToast({
											title: '支付失败',
											icon: 'none'
										});
									}
								});
							}
						}
					}
					// #endif
				} catch (error) {
					console.error('H5支付处理失败：', error);
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					});
				}
			},
			async handlePaymentSuccess(orderNo) {
				try {
					this.closeConfirmModal()
					this.getUserInfo() // 刷新余额

					// 显示充值成功提示
					uni.showToast({
						title: '充值成功',
						icon: 'success'
					})

				} catch (error) {
					console.error('查询支付结果失败：', error)
					uni.showToast({
						title: '查询支付结果失败',
						icon: 'none'
					})
				}
			}
		}
	}
</script>

<style scoped>
	/* 页面特有样式 */

	.container {
		background: #1B0538;
	}

	/* 横屏样式适配 */
	.container.force-landscape {
		width: 100vh;
		height: 100vw;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%) rotate(90deg);
		transform-origin: center;
		position: fixed;
	}

	/* 物理横屏时的适配 */
	@media screen and (orientation: landscape) {
		.container.force-landscape {
			width: 100vw !important;
			height: 100vh !important;
			top: 0 !important;
			left: 0 !important;
			transform: none !important;
			position: relative !important;
		}
	}

	/* 横屏下的内容区域适配 */
	.container.force-landscape .pay-tabs {
		/* 横屏下tab区域可能需要调整高度 */
		height: 160rpx;
	}

	.pay-tabs {
		display: flex;
		align-items: center;
		padding-bottom: 22rpx;
		justify-content: flex-start;
		background: url('/static/home/<USER>') no-repeat center/100vw 210rpx;
		width: 100vw;
		height: 180rpx;
		z-index: 100;
	}

	.pay-tab {
		flex: 1;
		text-align: center;
		font-size: 30rpx;
		color: #fff;
		padding: 18rpx 0;
		border-radius: 16rpx;
		background: url('/static/title.png') no-repeat center/100% 130%;
		font-weight: bold;
		margin: 0 8rpx;
		margin-top: 60rpx;
	}

	.pay-tab.active {
		background: url('/static/title_active.png') no-repeat center/100% 130%;
		color: #F9F98D;
	}

	.balance-section {
		display: flex;
		align-items: center;
		background: #3F2F5B;
		border-radius: 20rpx;
		margin: 24rpx;
		padding: 12rpx 24rpx;
		position: relative;
		overflow: hidden;
	}

	.balance-info {
		flex: 1;
		z-index: 2;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.balance-left {
		display: flex;
		align-items: center;
	}

	.balance-label {
		font-size: 28rpx;
		color: #fff;
		margin-left: 8rpx;
	}

	.balance-value {
		font-size: 38rpx;
		color: #fff;
		font-weight: bold;
	}

	.coin-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.balance-bg {
		width: 160rpx;
		height: 120rpx;
		position: absolute;
		right: 16rpx;
		top: 0;
		z-index: 1;
	}

	.special-pay-list {
		display: flex;
		justify-content: space-between;
		margin: 24rpx 24rpx 0 24rpx;
	}

	.special-pay-item {
		flex: 1;
		background: #3C2C56;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		margin-right: 16rpx;
		padding: 0 0 16rpx 0;
		text-align: center;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		overflow: hidden;
	}

	.special-pay-item:last-child {
		margin-right: 0;
	}

	.normal-pay-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin: 24rpx 24rpx 0 24rpx;
	}

	.normal-pay-item {
		width: 48%;
		background: #3C2C56;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 24rpx;
		padding: 0 0 16rpx 0;
		text-align: center;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		overflow: hidden;
	}

	.normal-pay-item:last-child {
		margin-right: 0;
	}

	/* 统一的卡片样式 */
	.card-header {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		background: #593E84;
		padding: 0;
	}

	.card-tag-full {
		width: 100%;
		color: #fff;
		font-size: 22rpx;
		text-align: center;
		padding: 4rpx 16rpx;
		background: #593E84;
	}

	.card-tag-header {
		width: 100%;
		display: flex;
	}

	.card-tag-left {
		color: #fff;
		font-size: 22rpx;
		text-align: center;
		padding: 4rpx 16rpx;
		background: #593E84;
		border-radius: 0;
		display: inline-block;
	}

	.card-tag-right {
		color: #fff;
		font-size: 22rpx;
		text-align: center;
		padding: 4rpx 16rpx;
		background: #A83AB6;
		border-radius: 0;
		display: inline-block;
		margin-left: auto;
	}

	.card-content {
		width: 100%;
		background: #3C2C56;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 16rpx 0 8rpx 0;
		position: relative;
	}

	.normal-coin {
		width: 100rpx;
		height: 100rpx;
		margin: 5rpx 0 8rpx 0;
	}

	.normal-amount {
		font-size: 32rpx;
		color: #fff;
		font-weight: bold;
	}

	.normal-price {
		width: 90%;
		background: #F2CB86;
		color: #331D74;
		font-size: 28rpx;
		font-weight: bold;
		border-radius: 12rpx;
		padding: 5rpx 0;
	}

	.super-pay-list {
		margin: 24rpx 24rpx 0 24rpx;
	}

	.super-pay-item {
		background: #3C2C56;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 20rpx;
		padding: 0 0 18rpx 0;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}

	.super-content {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
		justify-content: center;
	}

	.super-label {
		font-size: 32rpx;
		color: #F3CB83;
		font-weight: bold;
		text-align: center;
		font-family: inherit;
	}

	.super-icon {
		width: 72rpx;
		height: 72rpx;
	}

	.super-desc {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 24rpx;
	}

	.super-benefit {
		font-size: 24rpx;
		color: #fff;
		display: flex;
		align-items: center;
	}

	.super-get {
		font-size: 24rpx;
		color: #fff;
		display: flex;
		align-items: center;
	}

	.super-label-blue {
		color: #fff;
		font-weight: bold;
		margin-right: 12rpx;
		font-size: 30rpx;
	}

	.orange {
		color: #8C5FE5;
		font-weight: bold;
		font-size: 30rpx;
	}

	.blue {
		color: #1a5cff;
		font-weight: bold;
		font-size: 30rpx;
	}

	.pay-desc {
		margin: 12rpx 24rpx 0 24rpx;
		background: #3C2C56;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.pay-desc-list {
		font-size: 24rpx;
		color: #fff;
		line-height: 1.8;
		padding: 12rpx;
	}

	.loading {
		text-align: center;
		padding: 40rpx;
		color: #fff;
		font-size: 28rpx;
	}

	/* 确认充值弹窗样式 */
	.confirm-modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}

	.confirm-modal-content {
		background-color: #301E46;
		border-radius: 24rpx;
		width: 90vw;
		max-width: 600rpx;
		padding: 32rpx 0 24rpx 0;
		box-sizing: border-box;
		position: relative;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
	}

	.modal-header {
		display: flex;
		justify-content: center;
		/* Center the title */
		align-items: center;
		margin-bottom: 20rpx;
		position: relative;
		padding: 0 32rpx;
	}

	.modal-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
	}

	.close-icon {
		position: absolute;
		right: 24rpx;
		top: 24rpx;
		width: 48rpx;
		height: 48rpx;
	}

	.modal-price-section {
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		margin-bottom: 20rpx;
		padding: 0 32rpx;
	}

	.modal-price-label {
		font-size: 36rpx;
		/* Same size as value */
		font-weight: bold;
		color: #fff;
	}

	.modal-price-value {
		font-size: 36rpx;
		/* Same size as label */
		font-weight: bold;
		color: #ff7b2c;
	}

	.modal-divider {
		height: 2rpx;
		background-color: #593E84;
		margin: 0 32rpx 20rpx 32rpx;
	}

	.modal-detail-list {
		margin-bottom: 20rpx;
		/* Reduced margin */
		padding: 0 32rpx;
	}

	.modal-detail-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 15rpx;
	}

	.detail-label {
		font-size: 28rpx;
		color: #ccc;
	}

	.detail-value {
		font-size: 28rpx;
		color: #fff;
		font-weight: bold;
	}

	.modal-button-section {
		text-align: center;
		margin-top: 20rpx;
	}

	.confirm-button {
		width: 100%;
		max-width: 100%;
		height: 60px;
		line-height: 60px;
		background: url('/static/mine/button.png') no-repeat center/100% 100% !important;
		color: #fff !important;
		font-size: 16px;
		border-radius: 30px;
		margin: 0 auto;
		display: flex;
		align-items: center;
		justify-content: center;
		border: none !important;
		-webkit-appearance: none;
		appearance: none;
		padding: 0;
	}

	.confirm-button:disabled,
	.confirm-button[disabled] {
		background: url('/static/mine/button.png') no-repeat center/100% 100% !important;
		color: #fff !important;
		opacity: 0.6 !important;
		border: none !important;
		-webkit-appearance: none !important;
		appearance: none !important;
		-webkit-filter: none !important;
		filter: none !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		padding: 0 !important;
	}

	/* 针对H5环境的额外覆盖 */
	.confirm-button::-webkit-button,
	.confirm-button::-moz-button,
	.confirm-button::-ms-button {
		background: url('/static/mine/button.png') no-repeat center/100% 100% !important;
		border: none !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		padding: 0 !important;
	}

	/* 支付方式选择样式 */
	.payment-methods {
		margin: 20rpx 0;
		padding: 0 32rpx;
	}

	.payment-method-title {
		font-size: 28rpx;
		color: #fff;
		margin-bottom: 20rpx;
	}

	.payment-method-list {
		display: flex;
		gap: 20rpx;
	}

	.payment-method-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 10rpx;
		padding: 20rpx;
		border: 2rpx solid #593E84;
		border-radius: 12rpx;
		cursor: pointer;
		background: #3C2C56;
	}

	.payment-method-item.active {
		border-color: #1a5cff;
		background: #593E84;
	}

	.payment-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.payment-method-item text {
		font-size: 28rpx;
		color: #fff;
	}
</style>