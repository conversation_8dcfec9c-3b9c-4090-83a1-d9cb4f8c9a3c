<style>
    #one .commonsearch-table{
        padding-top:15px!important;
    }
</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="category">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="categoryList" item="vo"}
            <li><a href="#t-{$key|htmlentities}" data-value="{$key|htmlentities}" data-toggle="tab">{$vo|htmlentities}</a></li>
            {/foreach}
            {if stripos(request()->get('mimetype'),'image/')===false}
            <li class="pull-right dropdown filter-type">
                <a href="javascript:" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-filter"></i> {:__('Filter Type')}</a>
                <ul class="dropdown-menu text-left" role="menu">
                    <li class="active"><a href="javascript:" data-value="">{:__('All')}</a></li>
                    {foreach name="mimetypeList" id="item"}
                    <li><a href="javascript:" data-value="{$key|htmlentities}">{$item|htmlentities}</a></li>
                    {/foreach}
                </ul>
            </li>
            {/if}
        </ul>
    </div>

    <div class="panel-body no-padding">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh')}
                        <span><button type="button" id="faupload-image" class="btn btn-success faupload" data-mimetype="{$mimetype|default=''|htmlentities}" data-multiple="true"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                        {if request()->get('multiple') == 'true'}
                        <a class="btn btn-danger btn-choose-multi"><i class="fa fa-check"></i> {:__('Choose')}</a>
                        {/if}
                    </div>
                    <table id="table" class="table table-bordered table-hover table-nowrap" width="100%">

                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
