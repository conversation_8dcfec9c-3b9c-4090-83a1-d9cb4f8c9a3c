/*
 * Skin: Black
 * -----------
 */
.skin-black .main-header {
  -webkit-box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.05);
}
.skin-black .main-header .navbar-toggle {
  color: #333;
}
.skin-black .main-header .navbar-brand {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-black .main-header .navbar {
  background-color: #fff;
}
.skin-black .main-header .navbar .nav > li > a {
  color: #666;
}
.skin-black .main-header .navbar .nav > li > a:hover,
.skin-black .main-header .navbar .nav > li > a:active,
.skin-black .main-header .navbar .nav > li > a:focus,
.skin-black .main-header .navbar .nav .open > a,
.skin-black .main-header .navbar .nav .open > a:hover,
.skin-black .main-header .navbar .nav .open > a:focus,
.skin-black .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #444;
}
.skin-black .main-header .navbar .nav-addtabs li > .close-tab {
  color: #444;
}
.skin-black .main-header .navbar .sidebar-toggle {
  color: #666;
}
.skin-black .main-header .navbar .sidebar-toggle:hover {
  color: #444;
  background: rgba(0, 0, 0, 0.02);
}
.skin-black .main-header .navbar > .sidebar-toggle {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-black .main-header .navbar .navbar-nav > li > a {
  border-right: 1px solid #eee;
}
.skin-black .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-black .main-header .navbar .navbar-right > li > a {
  border-left: 1px solid #eee;
  border-left: none;
  border-right-width: 0;
}
.skin-black .main-header .logo {
  background-color: #222d32;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #222d32;
}
.skin-black .main-header .logo:hover {
  background-color: #202a2f;
}
@media (max-width: 767px) {
  .skin-black .main-header .logo {
    background-color: #fff;
    color: #222;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-black .main-header .logo:hover {
    background-color: #fcfcfc;
  }
}
.skin-black .main-header li.user-header {
  background-color: #222;
}
.skin-black .main-header .nav-addtabs > li > a,
.skin-black .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-black .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-black .wrapper,
.skin-black .main-sidebar,
.skin-black .left-side {
  background-color: #222d32;
}
.skin-black .user-panel > .info,
.skin-black .user-panel > .info > a {
  color: #fff;
}
.skin-black .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-black .sidebar-menu > li.header {
  color: #4b646f;
  background: #1a2226;
}
.skin-black .sidebar-menu > li:hover > a,
.skin-black .sidebar-menu > li.active > a {
  color: #fff;
  background: #1e282c;
  border-left-color: #fff;
}
.skin-black .sidebar-menu > li > .treeview-menu {
  background: #1c2529;
}
.skin-black .sidebar a {
  color: #b8c7ce;
}
.skin-black .sidebar a:hover {
  text-decoration: none;
}
.skin-black .treeview-menu > li > a {
  color: #72919f;
}
.skin-black .treeview-menu > li.active > a,
.skin-black .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-black .sidebar-form {
  border-radius: 3px;
  border: 1px solid #374850;
  background-color: #374850;
  margin: 10px 10px;
}
.skin-black .sidebar-form input[type="text"],
.skin-black .sidebar-form .btn {
  box-shadow: none;
  background-color: #374850;
  border: 1px solid transparent;
  height: 35px;
}
.skin-black .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-black .sidebar-form input[type="text"]:focus,
.skin-black .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-black .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-black .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-black .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-black.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
@media (max-width: 767px) {
  .skin-black.multiplenav .main-header .navbar {
    background-color: #222d32;
  }
  .skin-black.multiplenav .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-black.multiplenav .main-header .navbar .nav > li > a:hover,
  .skin-black.multiplenav .main-header .navbar .nav > li > a:active,
  .skin-black.multiplenav .main-header .navbar .nav > li > a:focus,
  .skin-black.multiplenav .main-header .navbar .nav .open > a,
  .skin-black.multiplenav .main-header .navbar .nav .open > a:hover,
  .skin-black.multiplenav .main-header .navbar .nav .open > a:focus,
  .skin-black.multiplenav .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-black.multiplenav .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-black.multiplenav .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-black.multiplenav .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
  .skin-black.multiplenav .main-header > .logo {
    background-color: #222d32;
    color: #fff;
    border-bottom: 0 solid transparent;
  }
  .skin-black.multiplenav .main-header > .logo:hover {
    background-color: #202a2f;
  }
  .skin-black.multiplenav .sidebar .mobilenav a.btn-app {
    background: #374850;
    color: #fff;
  }
  .skin-black.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #fff;
    color: #374850;
  }
}
/*# sourceMappingURL=skin-black.css.map */