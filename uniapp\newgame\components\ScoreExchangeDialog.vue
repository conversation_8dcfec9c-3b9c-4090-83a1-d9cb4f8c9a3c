<template>
	<UniversalModal
		:show="show"
		:title="pointName + '兑换'"
		:landscape="landscape"
		@close="close"
		size="medium"
		type="exchange"
	>

				<!-- 当前积分 -->
				<view class="info-box current-score readonly">
					<text class="section-label">当前{{ pointName }}</text>
					<image class="box-icon" src="/static/score.png" />
					<text class="box-value">{{ userScore }}</text>
				</view>

				<!-- 兑换 -->
				<view class="section-header">
					<view class="exchange-rate">
						<text class="rate-text">当前兑换比例：</text>
						<text class="rate-score">{{ scoreToCoinRate.split(':')[0] }}</text>
						<text class="rate-text">:</text>
						<text class="rate-coin">{{ scoreToCoinRate.split(':')[1] }}</text>

						<view class="score-unit">
							<text>{{ scoreToCoinRate.split(':')[0] }}{{ pointName }}兑换{{ scoreToCoinRate.split(':')[1] }}{{ coinsName }}</text>
						</view>
					</view>
				</view>
				<view class="info-box editable">
					<text class="section-label">兑换{{ coinsName }}</text>
					<image class="box-icon" src="/static/coin.png" />
					<input class="box-input" type="number" v-model="inputAmount" placeholder="0" />
					<text class="box-unit">币</text>
				</view>

				<!-- 互换图标 -->
				<view class="swap-container">
					<image class="swap-icon" src="/static/swap.png" />
				</view>

				<!-- 剩余积分 -->
				<view class="info-box readonly">
					<text class="section-label">剩余{{ pointName }}</text>
					<image class="box-icon" src="/static/score.png" />
					<text class="box-value" :style="{ color: remainingScoreColor }">{{ remainingScore }}</text>
				</view>

			<template #footer>
				<button class="submit-btn modal-button primary" @tap="submitExchange">立即兑换</button>
			</template>
		</UniversalModal>

		<!-- 兑换确认弹窗 -->
		<UniversalModal
			:show="showConfirmDialog"
			title="确认兑换"
			:landscape="landscape"
			size="medium"
			:mask-closable="false"
			@close="cancelConfirm"
		>
			<view class="confirm-content">
				<text class="confirm-text">确认要兑换吗？</text>
				<view class="confirm-details">
					<text>消耗{{ pointName }}：{{ scoreNeeded }}</text>
					<text>获得{{ coinsName }}：{{ inputAmount }}</text>
				</view>
			</view>
			<template #footer>
				<view class="modal-buttons">
					<button class="btn-cancel" @click="cancelConfirm">取消</button>
					<button class="btn-confirm" @click="confirmExchange">确认兑换</button>
				</view>
			</template>
		</UniversalModal>
	</template>

<script>
	import request from '@/utils/request.js';
	import UniversalModal from '@/components/UniversalModal.vue';

	export default {
		name: 'ScoreExchangeDialog',
		components: {
			UniversalModal
		},
		emits: ['close', 'exchangeSuccess'],
		props: {
			show: {
				type: Boolean,
				default: false
			},
			userInfo: {
				type: Object,
				default: () => ({})
			},
			landscape: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				exchangeConfig: {},
				inputAmount: null,
				coinsName: '金币', // 默认值，从接口获取
				pointName: '积分', // 默认值，从接口获取
				showConfirmDialog: false, // 确认弹窗显示状态
			};
		},
		computed: {
			userScore() {
				return this.userInfo.score || 0;
			},
			userVipLevel() {
				return this.userInfo.vip_level || 0;
			},
			// 根据输入的金币数量计算需要的积分
			scoreNeeded() {
				if (!this.inputAmount || this.inputAmount <= 0) return 0;
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 0;
				const coins = this.exchangeConfig[rateKey];
				return Math.ceil((this.inputAmount / Number(coins)) * Number(rateKey));
			},
			// 最大可兑换金币数
			maxExchangeCoins() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 0;
				const coins = this.exchangeConfig[rateKey];
				return Math.floor((this.userScore / Number(rateKey)) * Number(coins));
			},
			// 兑换比例显示
			scoreToCoinRate() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 'N/A';
				const coins = this.exchangeConfig[rateKey];
				return `${rateKey}:${coins}`;
			},
			// 剩余积分计算
			remainingScore() {
				if (!this.inputAmount || this.inputAmount <= 0) return this.userScore;
				return this.userScore - this.scoreNeeded;
			},
			remainingScoreColor() {
				return this.remainingScore < 0 ? '#ff5555' : '#FFA500';
			}
		},
		watch: {
			async show(newVal) {
				if (newVal) {
					const success = await this.getConfig();
					if (success) {
						// 默认设置为最大可兑换的金币数量
						this.inputAmount = this.maxExchangeCoins;
					}
				}
			}
		},
		methods: {
			async getConfig() {
				try {
					const res = await request({
						url: '/api/game/get_config'
					});
					if (res.code === 1) {
						this.exchangeConfig = res.data.exchange;
						if (res.data.coins_name) {
							this.coinsName = res.data.coins_name;
						}
						// 获取积分名称
						if (res.data.point_name) {
							this.pointName = res.data.point_name;
						}
						return true;
					} else {
						this.showError('获取配置失败');
						return false;
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
					return false;
				}
			},
			close() {
				this.inputAmount = null;
				this.$emit('close');
			},
			submitExchange() {
				// 验证输入
				if (!this.inputAmount || this.inputAmount <= 0) {
					this.showError('请输入有效的金币数量');
					return;
				}
				if (this.scoreNeeded > this.userScore) {
					this.showError(`${this.pointName}不足`);
					return;
				}

				// 显示确认弹窗
				this.showConfirmDialog = true;
			},

			// 取消确认
			cancelConfirm() {
				this.showConfirmDialog = false;
			},

			// 确认兑换 - 实际执行兑换操作
			async confirmExchange() {
				this.showConfirmDialog = false;

				try {
					const res = await request({
						url: '/api/user/scoreExchange',
						method: 'POST',
						data: {
							amount: this.inputAmount,
							score_cost: this.scoreNeeded
						}
					});

					if (res.code === 1) {
						uni.showToast({
							title: '兑换成功',
							icon: 'success'
						});
						this.$emit('exchangeSuccess');
						this.close();
					} else {
						this.showError(res.msg || '兑换失败');
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
				}
			},
			showError(msg) {
				uni.showToast({
					title: msg,
					icon: 'none'
				});
			}
		}
	};
</script>

<style scoped>
	/* 积分兑换弹窗样式 - 使用固定px单位，确保横屏适配一致 */

	/* 区域标签样式 */
	.section-label {
		color: #fff;
		font-size: 15px;
		font-weight: bold;
		margin-left: 5px;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 5px;
	}

	.exchange-rate {
		display: flex;
		align-items: center;
		font-size: 14px;
	}

	.rate-text {
		color: #fff;
	}

	.rate-score {
		color: #FFA500; /* 积分颜色 - 橙色 */
		font-size: 15px;
		font-weight: bold;
		margin: 0 2px;
	}

	.rate-coin {
		color: #4A90E2; /* 金币颜色 - 蓝色 */
		font-size: 15px;
		font-weight: bold;
		margin: 0 2px;
	}

	/* 信息框样式 */
	.info-box {
		background-color: #3F3055;
		border-radius: 8px;
		padding: 8px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 8px;
	}

	/* 只读信息框样式 - 更灰的背景表示只读 */
	.info-box.readonly {
		background-color: #2A1F35;
	}

	/* 可编辑信息框样式 - 蓝色边框表示可编辑 */
	.info-box.editable {
		background-color: #3F3055;
		border: 1px solid #4A90E2;
	}

	.current-score {
		margin-bottom: 15px;
	}

	.box-icon {
		width: 24px;
		height: 24px;
		margin-left: 15px;
	}

	/* 互换图标容器 */
	.swap-container {
		display: flex;
		justify-content: center;
		margin-bottom: 10px;
	}

	.swap-icon {
		width: 24px;
		height: 24px;
	}

	.box-value {
		color: #FFA500; /* 积分颜色 - 橙色 */
		font-size: 18px;
		font-weight: bold;
		flex: 1;
		text-align: center; /* 改为居中对称显示 */
		min-width: 80px;
	}

	.box-input {
		color: #4A90E2; /* 金币颜色 - 蓝色 */
		font-size: 18px;
		font-weight: bold;
		flex: 1;
		text-align: center; /* 改为居中对称显示 */
		background: transparent;
		border: none;
		outline: none;
		width: 80px;
		max-width: 80px;
		min-width: 60px;
	}

	.box-unit {
		color: #fff;
		font-size: 18px;
		font-weight: bold;
		margin-left: 10px;
	}

	.submit-btn {
		width: 100%;
		height: 60px;
		line-height: 60px;
		border-radius: 30px;
		font-size: 16px;
		color: #fff;
		background: url('/static/mine/button.png') no-repeat center/100% 100%;
		border: none;
		text-align: center;
		margin-top: 16px;
		cursor: pointer;
		transition: all 0.3s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0;
	}

	.submit-btn:hover {
		opacity: 0.9;
	}

	.score-unit {
		margin-left: 15px;
		color: #fff;
		font-size: 12px;
	}

	/* 确认弹窗样式 */
	.confirm-content {
		text-align: center;
		padding: 12px 0;
	}

	.confirm-text {
		font-size: 16px;
		color: #fff;
		margin-bottom: 16px;
		display: block;
	}

	.confirm-details {
		background: rgba(255, 255, 255, 0.1);
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 16px;
	}

	.confirm-details text {
		display: block;
		font-size: 14px;
		color: #fff;
		margin: 4px 0;
	}

	.modal-buttons {
		display: flex;
		justify-content: center;
		gap: 12px;
		width: 100%;
	}

	.btn-cancel {
		background: #ccc;
		color: #666;
		border: none;
		border-radius: 20px;
		padding: 0;
		cursor: pointer;
		flex: 1;
		font-size: 14px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.btn-confirm {
		background: #4A90E2;
		color: #fff;
		border: none;
		border-radius: 20px;
		padding: 0;
		cursor: pointer;
		flex: 1;
		font-size: 14px;
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>