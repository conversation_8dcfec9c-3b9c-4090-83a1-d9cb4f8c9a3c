<template>
	<view class="player-pro-live">
		 <view id="easyplayer" style="width:750rpx;height:420rpx;background-color: black;"></view>
	<!-- <view @click="enterFullscreen">播放</view> -->
	</view>
</template>
 
<script>
	export default {
		data() {
			return {
				playerInfo: null,
				videoUrl: "ws://*************:10086/live/stream_1.flv"
			}
		},
		onLoad() {
			
		},
		onReady() {

			this.checkPlayerAndInit()
			// let interval = setInterval(() => {
			// 	if(EasyPlayerPro){
			// 		clearInterval(interval)
			// 		this.playCreate()
			// 	}
			// 	// console.log(EasyPlayerPro)

			// },500)
			// let interval = setInterval(() => {
			// 	this.play()
			// },500)
			setTimeout(() => {
			       this.play()
			}, 500); // 延迟 500ms
		},
		methods: {
			play(){
				setTimeout((url) => {
					this.playerInfo && this.playerInfo.play(url).then(() => {
					}).catch((e) => {
					  console.error(e);
					});
				  }, 0, this.videoUrl)
			},
			checkPlayerAndInit() {
				let retryCount = 0
				const maxRetries = 10
				
				const checkAndInit = () => {
					if (typeof EasyPlayerPro !== 'undefined') {
						this.playCreate()
						return
					}
					
					if (retryCount < maxRetries) {
						retryCount++
						setTimeout(checkAndInit, 500)
					} else {
						console.error('播放器加载失败')
						uni.showToast({
							title: '播放器加载失败',
							icon: 'none'
						})
					}
				}
				
				checkAndInit()
			},
			playCreate(){
				try {
					// 使用 uni.createSelectorQuery 获取元素
					// 直接获取DOM元素
					const container = document.getElementById('easyplayer');
					
					if (!container) {
						console.error('播放器容器未找到');
						return;
					}
					
					console.log('容器元素:', container);
					
					var easyplayer = new EasyPlayerPro({
						container: container,
						decoder: './static/js/EasyPlayer-decode.js', // 使用正确的解码器路径
						videoBuffer: 0.2,
							isResize: true,
							text: "",
							loadingText: "加载中",
							useMSE: true,
							useSIMD: false,
							useWCS: false,
							isMulti: true,
							hasAudio: false,
							reconnection: true,
							showPerformance: false,
							operateBtns: {
								fullscreen: true,
								screenshot: false,
								play: false,
								audio: false,
								record: false,
								quality: false,
								performance: false,
							},
							watermarkConfig: {
								text: {
									content: 'easyplayer-pro'
								},
								right: 10,
								top: 10
							},
							playbackForwardMaxRateDecodeIFrame: 1,
							isWebrtcForOthers: true,
							demuxUseWorker: true,
							supportHls265: false,
							canvasRender:true,
							aspectRatio: true 
						});
						
						easyplayer.on("fullscreen", function (flag) {
							console.log('is fullscreen', flag)
						})
						easyplayer.on('playbackPreRateChange', (rate) => {
							easyplayer.forward(rate);
						})
						
						easyplayer.on('playbackSeek', (data) => {
							easyplayer.setPlaybackStartTime(data.ts);
						})
						this.playerInfo = easyplayer
					
				} catch (error) {
					console.error('播放器初始化失败:', error);
				}
			},
			// 单独的全屏方法，由用户点击触发
			    enterFullscreen() {
			      if (this.playerInfo) {
			        const container = document.getElementById("easyplayer");
			        if (container) {
			          // 使用原生全屏API
			          if (container.requestFullscreen) {
			            container.requestFullscreen();
			          } else if (container.webkitRequestFullscreen) {
			            container.webkitRequestFullscreen();
			          } else if (container.mozRequestFullScreen) {
			            container.mozRequestFullScreen();
			          } else if (container.msRequestFullscreen) {
			            container.msRequestFullscreen();
			          }
			        }
			      }
			    },
		}
	}
</script>
 
<style>
	
</style>