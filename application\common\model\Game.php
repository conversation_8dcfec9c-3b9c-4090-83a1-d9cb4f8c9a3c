<?php

namespace app\common\model;

use think\Cache;
use think\Db;
use think\Exception;
use think\exception\DbException;
use think\Model;
use think\Log;

/**
 * 游戏
 */
class Game extends Model
{
    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = '';
    // 追加属性
    protected $append = [
    ];

    /**
     * 开始游戏
     * @Authod Jw
     * @Time 2025/5/14
     * @param $params
     * @param [必须参数：gameSn]
     * @param [必须参数：number]
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function startGame($params)
    {
        Log::write('执行开始游戏');

        // 检查数据库连接状态
        try {
            Db::query('SELECT 1');
            Log::write('数据库连接检查成功');
        } catch (\Exception $e) {
            Log::error('数据库连接检查失败，重新连接: ' . $e->getMessage());
            Db::close();
            Db::connect();
        }

        Log::write('开始数据库事务');
        // 开启数据库事务
        Db::startTrans();
        try {
            Log::write('验证参数开始');
            if (!isset($params['gameSn']) || empty($params['gameSn'])) {
                throw new \Exception("游戏序列号为空");
            }
            $params['number'] = $params['number'] ?? 1;

            Log::write('检查缓存锁开始');
            $userId = $params['userId'] ?? 0;
            $clientType = $params['client_type'] ?? 'unknown';
            $cache_key = 'startGame_'.$params['gameSn'].'_'.$params['number'].'_'.$userId.'_'.$clientType;
            $cache = cache::get($cache_key, null);
            if ($cache) {
                Log::write("检测到重复开始游戏请求: gameSn={$params['gameSn']}, number={$params['number']}, userId={$userId}, clientType={$clientType}");
                throw new \Exception("请刷新重试");
            }
            cache::set($cache_key, 1, 5); // 延长到5秒
            Log::write('缓存锁设置成功: ' . $cache_key);

            $user_id = isset($params['userId']) ? $params['userId'] : 0;

            if (Db::name("game_log")->where('user_id',$user_id)->where('status',1)->value('id')) {
                throw new \Exception("您的另一游戏正在进行中");
            }

            Log::write('查询游戏信息开始');
            // 验证游戏是否存在且可用
            $game = Db::name('game')->where('sn',$params['gameSn'])->where('status',1)->find();
            if (!$game) {
                throw new \Exception("游戏不存在或在维护中");
            }
            Log::write('游戏信息查询成功: ' . json_encode($game));

            Log::write('查询座位信息开始');

            // 先查询所有座位状态用于调试
            $allSeats = Db::name('game_seat')
                ->where('game_id',$game['id'])
                ->where('number', $params['number'])
                ->select();
            Log::write('所有座位状态: ' . json_encode($allSeats));

            // 查找可用座位（包含倍率信息）
            $seat = Db::name('game_seat')
                ->where('game_id',$game['id'])
                ->where('number', $params['number'])
                ->where('status', 0) // 空闲座位
                ->find();
             Log::write('查询到的可用座位: ' . json_encode($seat));
            if (!$seat) {
                // 再次查询所有座位状态用于调试
                $allSeatsAfter = Db::name('game_seat')
                    ->where('game_id',$game['id'])
                    ->where('number', $params['number'])
                    ->select();
                Log::write('座位不可用时的所有座位状态: ' . json_encode($allSeatsAfter));
                throw new \Exception("座位不存在或不可用");
            }
            Log::write('座位信息查询成功');

            $coin = 0; // 开始游戏时不需要金币
            $status = 0;// 开始待确认(需等待app确认)
            $coins_before = 0; // 记录游戏前金币
            $seat_multiplier = $seat['multiplier'] ?? 1.00; // 获取座位倍率

            if ($user_id > 0) {//是uniapp
                $user = Db::name('user')->where('id',$user_id)->field('money,score')->find();
                if (!$user) {
                    throw new \Exception("用户不存在");
                }

                // 记录游戏前的用户金币和积分
                $coins_before = $user['money'];
                $score_before = $user['score'];

                Log::write("座位倍率: {$seat_multiplier}, 游戏前金币: {$coins_before}, 游戏前积分: {$score_before}");
            } else {//是app
                $status = 1;//进行中
            }

            Log::write('开始创建游戏记录');

            // 检查表结构并动态构建插入数据
            $insertData = [
                'user_id'           => $user_id,
                'game_id'           => $game['id'],
                'sn'                => $game['sn'],
                'seat_id'           => $seat['id'],
                'number'            => $seat['number'],
                'coin'              => $coin,
                'status'            => $status,
                'createtime'        => time(),
                'updatetime'        => time()
            ];

            try {
                $columns = Db::query("SHOW COLUMNS FROM fa_game_log");
                $columnNames = array_column($columns, 'Field');
                Log::write('fa_game_log表字段: ' . implode(', ', $columnNames));

                // 只有字段存在时才添加
                if (in_array('coins_before', $columnNames)) {
                    $insertData['coins_before'] = $coins_before;
                }
                if (in_array('score_before', $columnNames)) {
                    $insertData['score_before'] = $score_before ?? 0;
                }
                if (in_array('seat_multiplier', $columnNames)) {
                    $insertData['seat_multiplier'] = $seat_multiplier;
                }
            } catch (\Exception $e) {
                Log::write('检查表结构失败: ' . $e->getMessage());
            }

            // 创建游戏记录
            $gameLogId = Db::name('game_log')->insertGetId($insertData);
            Log::info('游戏记录创建成功: gameLogId=' . $gameLogId);
            if (!$gameLogId) {
                throw new \Exception('游戏记录创建失败');
            }

            Log::write('开始更新座位状态');
            // 更新座位状态为占用（使用乐观锁和超时控制）
            try {
                // 设置较短的锁等待超时时间（5秒）
                Log::write('设置座位更新锁等待超时时间为5秒');
                Db::execute('SET innodb_lock_wait_timeout = 5');

                Log::write('开始更新座位状态为占用');
                $seatUpdate = Db::name('game_seat')
                    ->where('id', $seat['id'])
                    ->where('status', 0) // 乐观锁：只有状态为0才能更新
                    ->update([
                        'status' => 1,
                        'updatetime' => time()
                    ]);

                // 恢复默认超时时间
                Log::write('恢复座位更新锁等待超时时间为50秒');
                Db::execute('SET innodb_lock_wait_timeout = 50');

                if (!$seatUpdate) {
                    Log::write('座位状态更新失败，座位已被占用 - 检测到上次游戏流程异常结束', 'error');
                    Log::write('座位占用问题详情: 游戏SN=' . $params['gameSn'] . ', 座位号=' . $params['number'] . ', 用户ID=' . $params['userId'] . ', 座位ID=' . $seat['id'], 'error');

                    // 强制清理座位状态，继续游戏流程
                    Log::write('强制清理座位状态，继续游戏流程');
                    $forceClearResult = Db::name('game_seat')
                        ->where('id', $seat['id'])
                        ->update([
                            'status' => 1,
                            'user_id' => $params['userId'],
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                    if (!$forceClearResult) {
                        Log::write('强制清理座位状态也失败，可能存在严重问题', 'error');
                        throw new \Exception('座位状态异常，无法开始游戏');
                    }

                    Log::write('座位状态强制清理成功，继续游戏流程');
                }
                Log::write('座位状态更新成功');
            } catch (\Exception $e) {
                // 恢复默认超时时间
                Log::write('座位状态更新异常，恢复锁等待超时时间');
                Db::execute('SET innodb_lock_wait_timeout = 50');

                if (strpos($e->getMessage(), 'Lock wait timeout') !== false) {
                    Log::write('座位状态更新锁等待超时: ' . $e->getMessage());
                    throw new \Exception('座位正在被其他用户操作，请稍后重试');
                } else {
                    Log::write('座位状态更新其他异常: ' . $e->getMessage());
                    throw $e;
                }
            }

            Log::info('准备提交事务: gameLogId=' . $gameLogId);

            Log::write('开始提交数据库事务');
            // 提交数据库事务
            Db::commit();
            Log::write('数据库事务提交完成');

            Log::write('清除缓存锁');
            // 清除缓存锁
            cache::rm($cache_key);

            Log::info('事务提交成功: gameLogId=' . $gameLogId);
            Log::write('开始游戏流程全部完成');
            return ['code'=>1,'msg'=>'success','data'=>['gameLogId'=>$gameLogId]];
        } catch (DbException $e) {
            Db::rollback();
            Log::error('DbException回滚: ' . $e->getMessage());
            Log::error('回滚时查找: ' . json_encode(Db::name('game_log')->order('id desc')->limit(3)->select()));
            return ['code'=>0,'msg'=>$e->getMessage()];
        } catch (Exception $e) {
            Db::rollback();
            Log::error('Exception回滚: ' . $e->getMessage());
            Log::error('回滚时查找: ' . json_encode(Db::name('game_log')->order('id desc')->limit(3)->select()));
            return ['code'=>0,'msg'=>$e->getMessage()];
        }
        return ['code'=>0,'msg'=>'fail'];
    }

    /**
     * 开始游戏结果逻辑处理
     * @Authod Jw
     * @Time 2025/5/14
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function startGameResult($params)
    {
        try {
            if (!isset($params['gameSn']) || empty($params['gameSn'])) {
                throw new \Exception("游戏序列号为空");
            }
            if (!isset($params['code']) || empty($params['code'])) {
                throw new \Exception("code为空");
            }
            $game = Db::name('game')->where('sn',$params['gameSn'])->find();
            if (!$game) {
                throw new \Exception("游戏不存在");
            }

            $params['number'] = $params['number'] ?? 1;

            // 查询游戏记录存不存在
            $game_log = Db::name('game_log')
                ->where(['game_id'=>$game['id'],'status'=>0,'number'=>$params['number']])
                ->find();
            if (!$game_log) {
                throw new \Exception("游戏记录不存在");
            }
            $update = [];
            $update['updatetime'] = time();
            if ($params['code'] == 200) {//成功
                $update['status'] = 1;

                // 游戏开始确认时不再扣除金币，金币扣除改为投币时进行
                Log::write("游戏开始确认成功，不扣除金币，金币将在投币时根据倍率扣除");

                // 如果有用户ID，记录游戏开始确认
                if ($game_log['user_id'] > 0) {
                    $user = Db::name('user')
                        ->where('id',$game_log['user_id'])
                        ->find();
                    if (!$user) {
                        throw new \Exception("ID：{$game_log['user_id']} 的用户不存在");
                    }

                    Log::write("用户 {$user['id']} 游戏开始确认，当前金币: {$user['money']}");
                }

                // 更新游戏中的玩家数量
                Db::name('game')
                    ->where('id', $game['id'])
                    ->inc('players')
                    ->update([
                        'updatetime' => time()
                    ]);

                $arr = ['code'=>1,'msg'=>'成功'];
            } else {//失败
                // 座位状态重置为空闲
                Db::name('game_seat')
                    ->where('id',$game_log['seat_id'])
                    ->update(['status'=>0,'updatetime'=>time()]);
                $update['status'] = -1;
                $arr = ['code'=>0,'msg'=>'失败'];
            }
            //修改游戏记录状态
            Db::name('game_log')->where('id',$game_log['id'])->update($update);

            return $arr;
        } catch (DbException $e) {
            return ['code'=>0,'msg'=>$e->getMessage()];
        }catch (Exception $e) {
            return ['code'=>0,'msg'=>$e->getMessage()];
        }
        return ['code'=>0,'msg'=>'失败'];
    }

    /**
     * 游戏结束
     * @Authod Jw
     * @Time 2025/5/14
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function endGame($params)
    {
        Log::write('执行结束游戏: ' . json_encode($params));

        // 检查数据库连接状态
        try {
            Db::query('SELECT 1');
        } catch (\Exception $e) {
            Log::error('数据库连接检查失败，重新连接: ' . $e->getMessage());
            Db::close();
            Db::connect();
        }

        // 开启数据库事务
        Db::startTrans();
        try {
            if (!isset($params['gameSn']) || empty($params['gameSn'])) {
                throw new \Exception('游戏序列号为空');
            }

            // 验证游戏是否存在且可用
            $game = Db::name('game')->where('sn',$params['gameSn'])->find();
            if (!$game) {
                throw new \Exception('游戏不存在或在维护中');
            }

            $where_game = [];
            if (isset($params['gameLogId'])) {//存在游戏记录ID
                $where_game['id'] = $params['gameLogId'];
            } else {
                if (!isset($params['number']) || empty($params['number'])) {
                    throw new \Exception('座位号为空');
                }
                // 座位号
                $where_game['number'] = $params['number'];
                $where_game['game_id'] = $game['id'];
            }
            $where_game['status'] = ['in',[0,1]];

            $game_log = Db::name('game_log')
                ->where($where_game)
                ->find();
            if (!$game_log) {
                throw new \Exception('游戏记录不存在');
            }




            $game_log_update = [];
            $game_log_update['updatetime'] = time();
            if ($game_log['status'] == 0) {//还未开始游戏
                $game_log_update['status'] = -1;//还未进行中，退出游戏，则修改为失败
            } else {
                $game_log_update['status'] = 2;//待确认
            }
            // 更新游戏记录状态
            $logUpdate = Db::name('game_log')
                ->where('id', $game_log['id'])
                ->update($game_log_update);
            if (!$logUpdate) {
                throw new \Exception('游戏记录状态更新失败');
            }
            // 更新游戏座位状态（使用超时控制）
            try {
                // 设置较短的锁等待超时时间（5秒）
                Db::execute('SET innodb_lock_wait_timeout = 5');

                $seatUpdate = Db::name('game_seat')
                    ->where('id', $game_log['seat_id'])
                    ->update([
                        'status' => 0,
                        'updatetime' => time()
                    ]);

                // 恢复默认超时时间
                Db::execute('SET innodb_lock_wait_timeout = 50');

                if (!$seatUpdate) {
                    throw new \Exception('座位状态更新失败');
                }
            } catch (\Exception $e) {
                // 恢复默认超时时间
                Db::execute('SET innodb_lock_wait_timeout = 50');

                if (strpos($e->getMessage(), 'Lock wait timeout') !== false) {
                    throw new \Exception('座位正在被其他用户操作，请稍后重试');
                } else {
                    throw $e;
                }
            }

            Log::info('结束游戏事务准备提交');

            // 提交数据库事务
            Db::commit();

            Log::info('结束游戏事务提交成功');
            return ['code'=>1,'msg'=>'success','data'=>$game_log];
        } catch (DbException $e) {
            Db::rollback();
            Log::error('结束游戏DbException回滚: ' . $e->getMessage());
            return ['code'=>0,'msg'=>$e->getMessage()];
        } catch (Exception $e) {
            Db::rollback();
            Log::error('结束游戏Exception回滚: ' . $e->getMessage());
            return ['code'=>0,'msg'=>$e->getMessage()];
        }
        return ['code'=>0,'msg'=>'fail'];
    }

    /**
     * 结束游戏结果逻辑处理
     * @Authod Jw
     * @Time 2025/5/14
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function endGameResult($params)
    {
        try {
            if (!isset($params['gameSn']) || empty($params['gameSn'])) {
                throw new \Exception('游戏序列号不能为空');
            }
            if (!isset($params['code']) || empty($params['code'])) {
                throw new \Exception('code为空');
            }
            if (!isset($params['coin']) && !isset($params['js'])) {
                throw new \Exception('coin为空');
            }
            if (isset($params['js'])) {//推币机，返回的是js
                $params['coin'] = $params['js'];//暂时 按1:1 比例计算
            }

            $game = Db::name('game')->where('sn',$params['gameSn'])->find();
            if (!$game) {
                throw new \Exception('游戏不存在');
            }

            $params['number'] = $params['number'] ?? 1;//座位号

            // 构建查询条件
            $where_game_log = [];
            if (isset($params['gameLogId']) && !empty($params['gameLogId'])) {
                // 如果传递了gameLogId，直接用ID查询
                $where_game_log['id'] = $params['gameLogId'];
                Log::write("使用gameLogId查询游戏记录: {$params['gameLogId']}", 'info');
            } else {
                // 否则用game_id和number查询
                $where_game_log['game_id'] = $game['id'];
                $where_game_log['number'] = $params['number'];
                Log::write("使用game_id和number查询游戏记录: game_id={$game['id']}, number={$params['number']}", 'info');
            }
            $where_game_log['status'] = ['in', [1, 2]];

            $game_log = Db::name('game_log')
                ->where($where_game_log)
                ->order('id desc')
                ->find();
            if (!$game_log) {
                Log::write("游戏记录查询失败，查询条件: " . json_encode($where_game_log), 'error');
                throw new \Exception('游戏记录不存在');
            }
            Log::write("找到游戏记录: " . json_encode($game_log), 'info');

            // 获取座位倍率
            $seat_multiplier = $game_log['seat_multiplier'] ?? 1.00;

            // 结束游戏，返还用户金币
            $user = Db::name('user')
                ->where('id',$game_log['user_id'])
                ->find();
            if (!$user) {
                throw new \Exception("ID：{$game_log['user_id']} 的用户不存在");
            }

            // 清除投币缓存，因为新的统一逻辑会处理所有投币记录
            $key = 'gameLog_'.$game_log['id'];
            $arr = Cache::get($key);
            if ($arr) {
                Cache::rm($key);
                Log::write("清除投币缓存，使用统一的投币记录逻辑", 'info');
            }

            // 获取缓存的投币信息或MQTT传递的信息
            $key = 'gameLog_'.$game_log['id'];
            $cacheData = Cache::get($key);
            $total_deducted_coins = 0; // 总共已扣除的金币

            // 优先使用MQTT传递的used_coins信息
            if (isset($params['mqtt_used_coins']) && $params['mqtt_used_coins'] > 0) {
                $total_deducted_coins = $params['mqtt_used_coins'];
                Log::write("使用MQTT传递的已使用金币: {$total_deducted_coins}");

                // 构造缓存数据用于后续处理
                $cacheData = [
                    'before' => $params['mqtt_cached_coin_before'] ?? $user['money'],
                    'coin' => $total_deducted_coins
                ];
            } elseif (isset($params['websocket_used_coins']) && $params['websocket_used_coins'] > 0) {
                // WebSocket传递的used_coins信息
                $total_deducted_coins = $params['websocket_used_coins'];
                Log::write("使用WebSocket传递的已使用金币: {$total_deducted_coins}");

                // 构造缓存数据用于后续处理
                $cacheData = [
                    'before' => $params['websocket_cached_coin_before'] ?? $user['money'],
                    'coin' => $total_deducted_coins
                ];
            } elseif ($cacheData && isset($cacheData['coin'])) {
                $total_deducted_coins = $cacheData['coin']; // 投币时已扣除的总金币
                Log::write("使用缓存的已使用金币: {$total_deducted_coins}");
            } else {
                Log::write("没有找到已使用金币信息，可能是游戏没有投币");
            }

            if ($params['code'] == 200) {//成功
                // 根据倍率计算实际赢得金币
                $actual_won_coins = $params['coin'] * $seat_multiplier;

                // 1. 处理投币扣除（MQTT协议需要在此时扣除用户money字段）
                if ($total_deducted_coins > 0) {
                    // 构建备注信息（倍率为1时不显示倍率）
                    $memo = "【{$game['name']}】 投币消费";
                    if ($seat_multiplier != 1.00) {
                        $memo .= " (倍率:{$seat_multiplier})";
                    }

                    // 获取当前用户金币
                    $current_user = Db::name('user')->where('id', $user['id'])->field('money')->find();
                    $current_money = $current_user['money'] ?? $user['money'];

                    // 统一处理：所有协议都在游戏结束时一次性扣除用户money
                    if (isset($params['mqtt_cached_coin_before'])) {
                        // MQTT协议：使用传递的游戏前金币
                        $before_money = $params['mqtt_cached_coin_before'];
                    } elseif (isset($params['websocket_cached_coin_before'])) {
                        // WebSocket协议：使用传递的游戏前金币
                        $before_money = $params['websocket_cached_coin_before'];
                    } else {
                        // 其他情况：使用缓存的游戏前金币
                        $before_money = $cacheData['before'] ?? $current_money;
                    }

                    $after_money = $before_money - $total_deducted_coins;

                    // 一次性扣除用户money字段（所有协议统一处理）
                    Db::name('user')->where('id', $user['id'])->setDec('money', $total_deducted_coins);
                    Log::write("游戏结束时一次性扣除用户money: {$total_deducted_coins}, before={$before_money}, after={$after_money}");

                    MoneyLog::create([
                        'user_id'       => $user['id'],
                        'money'         => $total_deducted_coins, // 正数，因为MoneyLog的money字段记录变化金额
                        'before'        => $before_money,
                        'after'         => $after_money,
                        'memo'          => $memo,
                        'correlation_id'   => $game_log['id'],
                        'status'        => -1 // -1=支出
                    ]);

                    Log::write("记录投币消费日志: 用户{$user['id']}, 消费{$total_deducted_coins}, before={$before_money}, after={$after_money}");
                }

                // 2. 返还赢得金币到score字段
                if ($actual_won_coins > 0) {
                    $scoreUpdate = Db::name('user')
                        ->where('id', $user['id'])
                        ->setInc('score', $actual_won_coins);
                    if (!$scoreUpdate) {
                        throw new \Exception("用户:{$user['username']} 积分返还失败");
                    }

                    // 记录到fa_user_score_log
                    $after_score = bcadd($user['score'], $actual_won_coins);

                    // 构建备注信息（倍率为1时不显示倍率）
                    $score_memo = "【{$game['name']}】 游戏赢得";
                    if ($seat_multiplier != 1.00) {
                        $score_memo .= " (倍率:{$seat_multiplier})";
                    }

                    ScoreLog::create([
                        'user_id'       => $user['id'],
                        'score'         => $actual_won_coins,
                        'before'        => $user['score'],
                        'after'         => $after_score,
                        'memo'          => $score_memo,
                        'correlation_id'   => $game_log['id'],
                        'status'        => 1 // 1=收入
                    ]);
                }

                // 重新获取用户最新数据
                $updatedUser = Db::name('user')->where('id', $user['id'])->field('money,score')->find();

                // 清除缓存
                if ($cacheData) {
                    Cache::rm($key);
                }

                $logUpdate['status'] = 3;
                $arr = ['code'=>1,'msg'=>'成功','data'=>['coin'=>$actual_won_coins, 'used_coins'=>$total_deducted_coins]];
            } else {//失败
                $logUpdate['status'] = -2;
                $arr = ['code'=>0,'msg'=>'失败'];
            }
            // 更新游戏记录的新字段
            $logUpdate['updatetime'] = time();
            $logUpdate['memo'] = $params['memo'] ?? null;

            // 检查字段是否存在，只更新存在的字段
            try {
                $columns = Db::query("SHOW COLUMNS FROM fa_game_log");
                $columnNames = array_column($columns, 'Field');

                if (in_array('end_time', $columnNames)) {
                    $logUpdate['end_time'] = time();
                }
                if (in_array('used_coins', $columnNames)) {
                    $logUpdate['used_coins'] = $total_deducted_coins;
                }
                if (in_array('won_coins', $columnNames)) {
                    $logUpdate['won_coins'] = $actual_won_coins ?? 0;
                }
                if (in_array('coins_after', $columnNames)) {
                    // 使用当前实际的money值
                    $current_user_money = Db::name('user')->where('id', $user['id'])->value('money');
                    $logUpdate['coins_after'] = $current_user_money ?? $user['money'];
                }
                if (in_array('score_after', $columnNames)) {
                    $logUpdate['score_after'] = $updatedUser['score'] ?? $user['score'];
                }
            } catch (\Exception $e) {
                Log::write('检查表结构失败: ' . $e->getMessage());
            }

            // 修改游戏记录
            Db::name('game_log')
                ->where('id',$game_log['id'])
                ->update($logUpdate);

            return $arr;
        } catch (DbException $e) {
            Log::write("endGameResult DbException: " . $e->getMessage() . " 文件: " . $e->getFile() . " 行号: " . $e->getLine(), 'error');
            return ['code'=>0,'msg'=>$e->getMessage()];
        }catch (Exception $e) {
            Log::write("endGameResult Exception: " . $e->getMessage() . " 文件: " . $e->getFile() . " 行号: " . $e->getLine(), 'error');
            return ['code'=>0,'msg'=>$e->getMessage()];
        }
        return ['code'=>0,'msg'=>'fail'];
    }

    /**
     * 推币机，投币，扣除金币
     * <AUTHOR>
     * @date 2025-6-19
     * @return
     */
    public function putInCoins($params)
    {
        try {
            if (!isset($params['gameSn']) || empty($params['gameSn'])) {
                throw new \Exception("游戏序列号为空");
            }
            $params['number'] = $params['number'] ?? 1;

            // 验证游戏是否存在且可用
            $game = Db::name('game')->where('sn',$params['gameSn'])->where('status',1)->find();
            if (!$game) {
                throw new \Exception("游戏不存在或在维护中");
            }

            $game_log = Db::name('game_log')->where(['game_id'=>$game['id'],'status'=>1,'number'=>$params['number']])->find();
            if (!$game_log) {
                throw new \Exception("游戏记录不存在");
            }
            $user = Db::name('user')->where('id',$game_log['user_id'])->find();
            if (!$user) {
                throw new \Exception("用户不存在");
            }

            // 获取座位倍率
            $seat_multiplier = $game_log['seat_multiplier'] ?? 1.00;
            $coin = $params['coin'] ?? 1; // 投币数量，默认1个
            $actual_deduct = $coin * $seat_multiplier; // 实际扣除金币

            $key = 'gameLog_'.$game_log['id'];

            // 获取当前缓存值
            $arr = Cache::get($key);
            if (empty($arr)) {
                $arr = [];
                $arr['before'] = $user['money']; // 记录游戏开始前的金币
                $arr['coin'] = $actual_deduct; // 记录实际扣除的金币
                $arr['coin_count'] = 1; // 记录投币次数
            } else {
                $arr['coin'] = $arr['coin'] + $actual_deduct; // 累计实际扣除的金币
                $arr['coin_count'] = ($arr['coin_count'] ?? 0) + 1; // 累计投币次数
            }

            // 检查缓存中的累计扣除金币是否超过用户金币
            if ($arr['before'] < $arr['coin']) {
                throw new \Exception("金币不足，需要 {$actual_deduct} 金币，当前可用 " . ($arr['before'] - $arr['coin'] + $actual_deduct) . " 金币");
            }

            // WebSocket投币：只更新缓存，不操作数据库
            // 数据库扣除将在游戏结束时统一处理
            Log::write("WebSocket投币成功: 本次投币{$actual_deduct}个金币，累计已使用 {$arr['coin']}个金币 (仅更新缓存)", 'info');
            Cache::set($key,$arr,3600*24);

            return ['code'=>1,'msg'=>'success'];
        } catch (DbException $e) {
            return ['code'=>0,'msg'=>$e->getMessage()];
        } catch (Exception $e) {
            return ['code'=>0,'msg'=>$e->getMessage()];
        }
        return ['code'=>0,'msg'=>'fail'];
    }


    /**
     * mqtt请求
     * @Authod Jw
     * @Time 2025/5/16
     * @param $data
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function mqttRequest($params)
    {
        if (isset($params['type'])) {
            // 定义需要数据库检查的类型
            $needDBCheckTypes = [
                '01', '06', 'putInCoins',
                'startGame', 'startGameResult',
                'endGame', 'endGameResult'
            ];

            if (in_array($params['type'], $needDBCheckTypes)) {
                try {
                    Db::query("SELECT 1");
                } catch (\Exception $e) {
                    Db::close();
                    Db::connect();
                    Log::info("数据库连接已重置");
                }
            }

            switch ($params['type']) {
                case '01'://推币机,开始游戏
                    return self::startGame($params);
                case '06'://推币机,结束游戏
                    return self::endGame($params);
                case 'putInCoins'://推币机,投币
                    return self::putInCoins($params);
                case 'startGame'://开始游戏
                    return self::startGame($params);
                case 'startGameResult'://开始游戏结果
                    return self::startGameResult($params);
                case 'endGame'://结束游戏
                    return self::endGame($params);
                case 'endGameResult'://开始游戏结果
                    return self::endGameResult($params);
                default:
                    return ['code'=>0,'msg'=>"未知类型"];
            }
        } else {
            return ['code'=>0,'msg'=>"类型不存在"];
        }
    }

    /**
     * 修改设备状态
     * @Authod Jw
     * @Time 2025/5/2
     * @param $gameSn
     * @param $status
     * @throws \think\Exception
     * @throws \think\db\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function updateGameOnlineStatus($gameSn,$status=1)
    {
        try {
            // 设置较短的锁等待超时时间（3秒）
            Db::execute('SET innodb_lock_wait_timeout = 3');

            $update=[];
            $update['device_status'] = $status;
            $update['updatetime'] = time(); // 添加更新时间

            if ($status == 1) {//在线
                $update['online_time'] = date('Y-m-d H:i:s',time());
            } else {//离线
                $update['offline_time'] = date('Y-m-d H:i:s',time());
            }

            // 使用更精确的WHERE条件，减少锁定范围
            $result = Db::name('game')
                ->where('sn', $gameSn)
                ->where('device_status', '<>', $status) // 只有状态不同时才更新
                ->update($update);

            // 恢复默认超时时间
            Db::execute('SET innodb_lock_wait_timeout = 50');

            if ($result !== false) { // 使用!==false，因为可能返回0（没有更新行）
                return ['code'=>1,'msg'=>'success'];
            }
            return ['code'=>0,'msg'=>'fail'];

        } catch (\Exception $e) {
            // 恢复默认超时时间
            Db::execute('SET innodb_lock_wait_timeout = 50');

            if (strpos($e->getMessage(), 'Lock wait timeout') !== false) {
                Log::error('更新设备在线状态锁等待超时: gameSn=' . $gameSn . ', status=' . $status);
                return ['code'=>0,'msg'=>'锁等待超时，请稍后重试'];
            } else {
                Log::error('更新设备在线状态异常: ' . $e->getMessage());
                throw $e;
            }
        }
    }

    /**
     *  用户游戏记录
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function userGameLog($params)
    {
        try {
            // 构建查询条件
            $where['l.user_id'] = $params['user_id'];
            $where['l.status'] = 3;

            // 分页参数
            $page = $params['page'] ?? 1;
            $pageSize = $params['show_num'] ?? 10;

            // 查询并分页
            $list = Db::name('game_log')
                ->alias('l')
                ->join(['fa_game g'],'l.game_id = g.id','left')
                ->where($where)
                ->field('l.id,l.game_id,g.name,l.number,l.status,l.createtime')
                ->order('createtime DESC')
                ->paginate([
                    'list_rows' => $pageSize,
                    'page' => $page
                ])->toArray();

            return ['code'=>1,'msg'=>'成功','data'=>$list];
        } catch (Exception $e) {
            return ['code' => 0, 'msg' => $e->getMessage()];
        }
    }

}
