<?php

namespace app\common\model;

use app\admin\library\Auth;
use fast\Random;
use think\Db;
use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Model;
use think\Validate;

/**
 * 会员模型
 * @method static mixed getByUsername($str) 通过用户名查询用户
 * @method static mixed getByNickname($str) 通过昵称查询用户
 * @method static mixed getByMobile($str) 通过手机查询用户
 * @method static mixed getByEmail($str) 通过邮箱查询用户
 */
class User extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
        'url',
    ];

    /**
     * 获取个人URL
     * @param string $value
     * @param array  $data
     * @return string
     */
    public function getUrlAttr($value, $data)
    {
        return "/u/" . $data['id'];
    }

    /**
     * 获取头像
     * @param string $value
     * @param array  $data
     * @return string
     */
    public function getAvatarAttr($value, $data)
    {
        if (!$value) {
            //如果不需要启用首字母头像，请使用
            //$value = '/assets/img/avatar.png';
            $value = letter_avatar($data['nickname']);
        }
        return $value;
    }

    /**
     * 获取会员的组别
     */
    public function getGroupAttr($value, $data)
    {
        return UserGroup::get($data['group_id']);
    }

    /**
     * 获取验证字段数组值
     * @param string $value
     * @param array  $data
     * @return  object
     */
    public function getVerificationAttr($value, $data)
    {
        $value = array_filter((array)json_decode($value, true));
        $value = array_merge(['email' => 0, 'mobile' => 0], $value);
        return (object)$value;
    }

    /**
     * 设置验证字段
     * @param mixed $value
     * @return string
     */
    public function setVerificationAttr($value)
    {
        $value = is_object($value) || is_array($value) ? json_encode($value) : $value;
        return $value;
    }

    /**
     * 变更会员余额
     * @param int    $money   余额
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function money($money, $user_id, $memo)
    {
        Db::startTrans();
        try {
            $user = self::lock(true)->find($user_id);
            if ($user && $money != 0) {
                $before = $user->money;
                //$after = $user->money + $money;
                $after = function_exists('bcadd') ? bcadd($user->money, $money, 2) : $user->money + $money;
                //更新会员信息
                $user->save(['money' => $after]);
                //写入日志
                MoneyLog::create(['user_id' => $user_id, 'money' => $money, 'before' => $before, 'after' => $after, 'memo' => $memo]);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
        }
    }

    /**
     * 变更会员积分
     * @param int    $score   积分
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function score($score, $user_id, $memo)
    {
        Db::startTrans();
        try {
            $user = self::lock(true)->find($user_id);
            if ($user && $score != 0) {
                $before = $user->score;
                $after = $user->score + $score;
                $level = self::nextlevel($after);
                //更新会员信息
                $user->save(['score' => $after, 'level' => $level]);
                //写入日志
                ScoreLog::create(['user_id' => $user_id, 'score' => $score, 'before' => $before, 'after' => $after, 'memo' => $memo]);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
        }
    }

    /**
     * 根据积分获取等级
     * @param int $score 积分
     * @return int
     */
    public static function nextlevel($score = 0)
    {
        $lv = array(1 => 0, 2 => 30, 3 => 100, 4 => 500, 5 => 1000, 6 => 2000, 7 => 3000, 8 => 5000, 9 => 8000, 10 => 10000);
        $level = 1;
        foreach ($lv as $key => $value) {
            if ($score >= $value) {
                $level = $key;
            }
        }
        return $level;
    }

    /**
     * 用户签到
     * @Authod Jw
     * @Time 2025/5/23
     * @param $user_id
     * @return array
     */
    public function doSign($user_id)
    {
        Db::startTrans();
        try {
            // 获取最后签到记录
            $lastSign = Db::name('user_sign')
                ->where('user_id', $user_id)
                ->order('sign_date DESC')
                ->lock(true)
                ->find();

            $currentDate = date('Y-m-d');
            $continuousDays = 1;

            if ($lastSign) {
                $lastDate = $lastSign['sign_date'];
                $diffDays = (strtotime($currentDate) - strtotime($lastDate)) / 86400;

                if ($diffDays == 0) {
                    throw new \Exception('今日已签到');
                }

                if ($diffDays == 1) {
                    $continuousDays = $lastSign['continuous_days'] + 1;
                    // 超过7天重置
                    if ($continuousDays > 7) {
                        $continuousDays = 1;
                    }
                }
            }

            // 获取对应奖励规则
            $rule = Db::name('sign_rule')
                ->where('day', $continuousDays)
                ->find();

            if (!$rule) {
                throw new \Exception('签到规则不存在');
            }

            // 添加签到记录
            $signData = [
                'user_id' => $user_id,
                'sign_date' => $currentDate,
                'continuous_days' => $continuousDays,
                'coin' => $rule['reward'],
                'createtime' => time()
            ];

            Db::name('user_sign')->insert($signData);

            $user = Db::name('user')->where('id',$user_id)->field('money')->find();
            if (!$user) {
                throw new \Exception('用户不存在');
            }
            Db::name('user')->where('id',$user_id)->setInc('money',$rule['reward']);

            $after = bcsub($user['money'], $rule['reward']);
            $memo = "签到";
            // 写入日志
            MoneyLog::create([
                'user_id'       => $user_id,
                'money'         => $rule['reward'],
                'before'        => $user['money'],
                'after'         => $after,
                'memo'          => $memo,
                'status'        => 1
            ]);

            Db::commit();
            return ['code'=>1,'msg'=>'成功'];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code'=>0,'msg'=>'签到失败：'.$e->getMessage()];
        } catch (DbException $e) {
            Db::rollback();
            return ['code'=>0,'msg'=>'签到失败：'.$e->getMessage()];
        }
    }

    /**
     * 修改登录密码
     * @Authod Jw
     * @Time 2025/5/30
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function reset_password($params)
    {
        Db::startTrans();
        try {
            if (!isset($params['agent_id']) && !isset($params['user_id'])) {
                exception(__("ID不能为空"));
            }
            if (!isset($params['now_password']) || empty($params['now_password'])) {
                exception(__("密码不能为空"));
            }
            if ($params['now_password'] != $params['now_password2']) {
                exception(__("两次密码不一致"));
            }
            if (!Validate::is($params['now_password'], "/^[\S]{6,16}$/")) {
                exception(__("密码长度必须在6-16位之间，不能包含空格"));
            }

            $params['salt'] = Random::alnum();

            $auth = new Auth();
            $params['password'] =$auth->getEncryptPassword($params['now_password'],$params['salt']);

            if (isset($params['agent_id'])) {//代理修改密码
                Db::name('admin')
                    ->where('id',$params['agent_id'])
                    ->update(['password'=>$params['password'],'salt'=>$params['salt'],'updatetime'=>time()]);
                //同时修改用户表的密码，同步修改
                Db::name('user')
                    ->where('agent_id',$params['agent_id'])
                    ->update(['password'=>$params['password'],'salt'=>$params['salt'],'updatetime'=>time()]);
            }
            if (isset($params['user_id'])) {//用户修改密码
                Db::name('user')
                    ->where('id',$params['user_id'])
                    ->update(['password'=>$params['password'],'salt'=>$params['salt'],'updatetime'=>time()]);
            }

            Db::commit();
            return ['code'=>1,'msg'=>'成功'];
        } catch (ValidateException $e) {
            Db::rollback();
            return ['code'=>0,'msg'=>$e->getMessage()];
        } catch (PDOException $e) {
            Db::rollback();
            return ['code'=>0,'msg'=>$e->getMessage()];
        } catch (Exception $e) {
            Db::rollback();
            return ['code'=>0,'msg'=>$e->getMessage()];
        }
    }

}
