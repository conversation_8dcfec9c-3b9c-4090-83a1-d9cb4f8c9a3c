import config from '@/config.js'

const request = (options) => {
	return new Promise((resolve, reject) => {
		const token = uni.getStorageSync(config.tokenKey)

		uni.request({
			url: config.baseUrl + options.url,
			method: options.method || 'POST',
			data: options.data || {},
			header: {
				'Content-Type': 'application/json',
				'Authorization': token ? `Bearer ${token}` : '',
				'token': token || '', // 添加FastAdmin需要的HTTP_TOKEN头
			},
			success: (res) => {
				if (res.data.code !== 1) {
					uni.showToast({
						title: res.data.msg || "请求失败",
						icon: 'none'
					})
					// token过期处理
					if (res.data.code === 401) {
						// 检查当前是否已经在登录页面，避免循环重定向
						try {
							const currentPages = getCurrentPages()
							const currentPage = currentPages[currentPages.length - 1]

							if (currentPage && currentPage.route !== 'pages/login/index') {
								uni.removeStorageSync(config.tokenKey)
								uni.reLaunch({
									url: '/pages/login/index'
								})
							} else {
								// 已经在登录页面，不需要重定向
								console.log('已在登录页面，跳过重定向')
							}
						} catch (error) {
							// 如果获取页面信息失败，直接跳转到登录页面
							console.error('获取页面信息失败：', error)
							uni.removeStorageSync(config.tokenKey)
							uni.reLaunch({
								url: '/pages/login/index'
							})
						}
					}
					// 如果 code 不为 1，也需要 resolve，以便业务层可以根据 res.code 处理
					resolve(res.data) 
				} else {
					resolve(res.data)
				}
			},
			fail: (error) => {
				uni.showToast({
					title: '网络异常',
					icon: 'none'
				})
				reject(error)
			}
		})
	})
}

export default request