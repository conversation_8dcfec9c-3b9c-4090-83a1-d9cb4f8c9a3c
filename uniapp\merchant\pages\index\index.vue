<template>
  <view class="container">
    <!-- 顶部商户信息 -->
    <view class="top-bar">
      <view class="user-info-wrap">
        <view class="avatar-wrap">
          <view class="avatar-level-wrap large">
            <image class="avatar small" :src="merchant.logo || '/static/default_avatar.png'" />
            <image class="level-avatar-bg large" src="/static/level_avatar.png" />
          </view>
        </view>
        <view class="user-detail">
          <view class="nickname-row">
            <view class="nickname">{{ merchant.name || '未设置' }}</view>
          </view>
          <view class="level-assets-row" v-if="merchant.name">
            <view class="id-bg-wrap">
              <text class="id-text">ID: {{ merchant.id || '--' }}</text>
            </view>
          </view>
        </view>
        <view class="setting-btn" @tap.stop="goSetting">
          <image src="/static/mine/setting.png" />
        </view>
      </view>
    </view>

    <!-- 公告栏轮播图 -->
    <view class="notice-swiper-wrap">
      <swiper class="notice-swiper" circular autoplay interval="5000" v-if="noticeList.length > 0">
        <swiper-item v-for="notice in noticeList" :key="notice.id">
          <view class="notice-content">
            <rich-text :nodes="notice.content"></rich-text>
          </view>
        </swiper-item>
      </swiper>
      <view v-else class="notice-loading">
        <text>加载中...</text>
      </view>
    </view>

    <!-- 统计卡片 -->
    <view class="stat-cards">
      <view class="stat-card card-container">
        <text class="stat-title">今日核销总积分</text>
        <text class="stat-value">{{ merchant.stats.today_score || 0 }}</text>
        <view class="stat-footer">
          <text class="stat-label">较昨天</text>
          <text class="stat-rate" :class="scoreRateClass">{{ scoreRateText }}</text>
        </view>
      </view>
      <view class="stat-card card-container">
        <text class="stat-title">今日核销总次数</text>
        <text class="stat-value">{{ merchant.stats.today_count || 0 }}</text>
        <view class="stat-footer">
          <text class="stat-label">较昨天</text>
          <text class="stat-rate" :class="countRateClass">{{ countRateText }}</text>
        </view>
      </view>
    </view>

    <!-- 菜单列表 -->
    <view class="menu-list">
      <view class="menu-item list-item" @click="onMenuClick('withdraw')">
        <text class="menu-text">余额提现</text>
        <image class="arrow-icon" src="/static/arrow-right.png" />
      </view>
      <view class="menu-item list-item" @click="onMenuClick('account')">
        <text class="menu-text">账户管理</text>
        <image class="arrow-icon" src="/static/arrow-right.png" />
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import { checkLogin } from '@/utils/auth.js'

export default {
  data() {
    return {
      merchant: {
        id: '',
        name: '',
        logo: '',
        business_hours: '',
        stats: {
          today_score: 0,
          score_rate: 0,
          today_count: 0,
          count_rate: 0
        }
      },
      noticeList: [] // 公告轮播数据
    }
  },
  computed: {
    scoreRateText() {
      const rate = this.merchant.stats.score_rate
      if (rate === null || rate === undefined || rate === '--') return '--'
      const num = Number(rate)
      if (isNaN(num)) return '--'
      return (num >= 0 ? '+' : '') + num + '%'
    },
    scoreRateClass() {
      const rate = this.merchant.stats.score_rate
      if (rate === null || rate === undefined || rate === '--') return ''
      return Number(rate) >= 0 ? 'rate-plus' : 'rate-minus'
    },
    countRateText() {
      const rate = this.merchant.stats.count_rate
      if (rate === null || rate === undefined || rate === '--') return '--'
      const num = Number(rate)
      if (isNaN(num)) return '--'
      return (num >= 0 ? '+' : '') + num + '%'
    },
    countRateClass() {
      const rate = this.merchant.stats.count_rate
      if (rate === null || rate === undefined || rate === '--') return ''
      return Number(rate) >= 0 ? 'rate-plus' : 'rate-minus'
    }
  },
  onLoad() {
    this.getMerchantInfo()
    this.getNoticeList()
  },
  onShow() {
    checkLogin()
    this.getMerchantInfo()
  },
  methods: {
    async getMerchantInfo() {
      try {
        const res = await request({
          url: '/api/merchant/detail'
        })
        if (res.code === 1) {
          this.merchant = res.data
        } else {
          uni.showToast({ title: res.msg || '获取商户信息失败', icon: 'none' })
        }
      } catch (e) {
        uni.showToast({ title: '网络错误', icon: 'none' })
      }
    },
    goSetting() {
      // 兼容多个 logo，先传递主 logo 字段
      let logos = []
      if (this.merchant.logo) {
        if (Array.isArray(this.merchant.logo)) {
          logos = this.merchant.logo
        } else {
          logos = [this.merchant.logo]
        }
      }
      const logoParam = encodeURIComponent(JSON.stringify(logos))
      uni.navigateTo({
        url: `/pages/setting/setting?name=${encodeURIComponent(this.merchant.name)}&business_hours=${encodeURIComponent(this.merchant.business_hours)}&logo=${logoParam}`
      })
    },
    onMenuClick(type) {
      if (type === 'account') {
        uni.navigateTo({ url: '/pages/account/account' })
        return
      }
      if (type === 'withdraw') {
        uni.navigateTo({ url: '/pages/withdraw/index' })
        return
      }
      uni.showToast({ title: '功能开发中', icon: 'none' })
    },
    // 获取公告轮播数据
    async getNoticeList() {
      try {
        const res = await request({
          url: '/api/content/list',
          data: {
            type: 1
          }
        })
        if (res.code === 1) {
          // 处理HTML内容，转换为rich-text可用的格式
          this.noticeList = (res.data || []).map(notice => ({
            ...notice,
            content: this.processHtmlContent(notice.content)
          }))
        }
      } catch (error) {
        console.error('获取公告数据失败：', error)
        // 如果获取失败，设置一个默认公告
        this.noticeList = [{
          id: 1,
          content: '欢迎使用商户核销系统！'
        }]
      }
    },
    // 处理HTML内容
    processHtmlContent(htmlString) {
      if (!htmlString) return ''

      // 如果是纯文本，直接返回
      if (!htmlString.includes('<')) {
        return htmlString
      }

      // 简单的HTML标签处理，去除一些可能导致样式问题的标签
      let processedHtml = htmlString
        .replace(/<script[^>]*>.*?<\/script>/gi, '') // 移除script标签
        .replace(/<style[^>]*>.*?<\/style>/gi, '') // 移除style标签
        .replace(/<link[^>]*>/gi, '') // 移除link标签
        .replace(/style\s*=\s*["'][^"']*["']/gi, '') // 移除内联样式
        .replace(/<(\/?)h[1-6]([^>]*)>/gi, '<$1p$2>') // 将标题标签转换为p标签
        .replace(/<br\s*\/?>/gi, '\n') // 将br标签转换为换行符
        .trim()

      return processedHtml
    }
  }
}
</script>

<style scoped>
/* 页面整体容器 */
.container {
  background: #180F29;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

/* 昵称行 */
.nickname-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* ID背景包装器 */
.id-bg-wrap {
  position: relative;
  width: 180rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('/static/mine/id.png') no-repeat center/180rpx 70rpx;
}

.id-text {
  position: relative;
  z-index: 2;
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
  text-align: center;
  width: 100%;
  margin-top: 5rpx;
}

.setting-btn {
  position: absolute;
  right: 50rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.setting-btn image {
  width: 35rpx;
  height: 35rpx;
  object-fit: contain;
}

/* 公告栏轮播图 */
.notice-swiper-wrap {
  margin: 30rpx 24rpx 0 24rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx #f3eac2;
}

.notice-swiper {
  width: 100%;
  height: 250rpx;
}

.notice-content {
  width: 100%;
  height: 100%;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: linear-gradient(135deg, #2A1840 0%, #3F2F5B 100%);
}

.notice-content /deep/ rich-text,
.notice-content /deep/ rich-text p,
.notice-content /deep/ rich-text div,
.notice-content /deep/ rich-text span {
  color: #fff !important;
  font-size: 24rpx !important;
  line-height: 1.5 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.notice-content /deep/ rich-text p,
.notice-content /deep/ rich-text div {
  margin-bottom: 10rpx !important;
}

.notice-content /deep/ rich-text p:last-child,
.notice-content /deep/ rich-text div:last-child {
  margin-bottom: 0 !important;
}

/* 处理其他HTML标签 */
.notice-content /deep/ rich-text strong,
.notice-content /deep/ rich-text b {
  font-weight: bold !important;
  color: #FFB366 !important;
}

.notice-content /deep/ rich-text em,
.notice-content /deep/ rich-text i {
  font-style: italic !important;
}

.notice-content /deep/ rich-text a {
  color: #7B68EE !important;
  text-decoration: underline !important;
}

.notice-content /deep/ rich-text ul,
.notice-content /deep/ rich-text ol {
  padding-left: 20rpx !important;
  margin: 10rpx 0 !important;
}

.notice-content /deep/ rich-text li {
  margin-bottom: 5rpx !important;
}

.notice-loading {
  width: 100%;
  height: 250rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #2A1840 0%, #3F2F5B 100%);
  color: #999;
  font-size: 28rpx;
}

/* 统计卡片 */
.stat-cards {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 20rpx;
  gap: 20rpx;
}

.stat-card {
  flex: 1;
  background: #2A1840;
  border: 1rpx solid #4C3A62;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-title {
  font-size: 24rpx;
  color: #fff;
  margin-bottom: 15rpx;
}

.stat-value {
  font-size: 40rpx;
  color: #fff;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.stat-footer {
  display: flex;
  align-items: center;
}

.stat-label {
  font-size: 22rpx;
  color: #fff;
  margin-right: 8rpx;
}

.stat-rate {
  font-size: 22rpx;
  font-weight: bold;
  color: #fff;
}

.rate-plus {
  color: #48A578;
}

.rate-minus {
  color: #ff4757;
}

/* 菜单列表 */
.menu-list {
  margin: 0 10rpx;
}

.menu-item {
  background: #2A1840;
  border-radius: 12rpx;
  margin: 16rpx 20rpx;
  padding: 30rpx 20rpx;
  border: 1rpx solid #4C3A62;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-text {
  font-size: 30rpx;
  color: #fff;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.7;
}
</style>
