# WebRTC真实连接统计功能

## 概述

HeiqiPlayer组件现在支持获取真实的WebRTC连接统计信息，而不是之前的模拟数据。这包括：

1. **真实的可用连接数量** - 从信令服务器获取实际在线的发送端数量
2. **真实的连接类型** - 通过WebRTC统计API分析实际的连接类型（本地/STUN/TURN）
3. **详细的客户端信息** - 获取所有在线客户端的详细信息

## 功能特性

### 1. 连接类型检测
- **H (Host)**: 本地直连，性能最佳
- **S (STUN)**: STUN服务器穿透，适中性能
- **T (TURN)**: TURN中继服务器，性能较低但兼容性最好

### 2. 实时统计更新
- 每秒自动收集连接统计信息
- 实时更新连接类型和可用连接数
- 自动检测连接状态变化

### 3. 客户端列表管理
- 获取所有在线客户端列表
- 区分发送端和接收端
- 实时更新客户端状态

## API接口

### 组件方法

#### `getRealConnectionStats()`
获取真实的连接统计信息
```javascript
this.$refs.player.getRealConnectionStats()
```

#### `updateConnectionInfo(type, availableCount)`
更新连接信息（内部调用）
- `type`: 连接类型 ('H', 'S', 'T')
- `availableCount`: 可用连接数量

### 组件事件

#### `@connectionStatsUpdated`
连接统计更新事件
```javascript
onConnectionStatsUpdated(stats) {
  console.log('发送端数量:', stats.senders)
  console.log('接收端数量:', stats.receivers)
  console.log('总客户端数:', stats.total)
  console.log('客户端列表:', stats.clients)
}
```

### 组件属性

#### 新增的数据属性
```javascript
data() {
  return {
    connectionType: 'H',        // 连接类型
    availableConnections: 0,    // 可用连接数量
  }
}
```

## 使用示例

### 基本使用
```vue
<template>
  <HeiqiPlayer
    ref="player"
    :senderId="senderId"
    :signalingUrl="signalingUrl"
    @connectionStatsUpdated="onConnectionStatsUpdated"
  />
</template>

<script>
export default {
  methods: {
    // 获取连接统计
    refreshStats() {
      this.$refs.player.getRealConnectionStats()
    },
    
    // 处理统计更新
    onConnectionStatsUpdated(stats) {
      this.connectionStats = stats
      console.log('连接统计更新:', stats)
    }
  }
}
</script>
```

### 高级使用
```vue
<template>
  <view>
    <HeiqiPlayer
      ref="player"
      :senderId="senderId"
      :signalingUrl="signalingUrl"
      @connected="onConnected"
      @connectionStatsUpdated="onConnectionStatsUpdated"
    />
    
    <view class="stats-display">
      <text>连接类型: {{ getConnectionTypeDesc(connectionType) }}</text>
      <text>可用连接: {{ availableConnections }}</text>
      <text>在线发送端: {{ senderCount }}</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      connectionType: 'H',
      availableConnections: 0,
      senderCount: 0
    }
  },
  
  methods: {
    onConnected() {
      // 连接成功后立即获取统计
      this.$refs.player.getRealConnectionStats()
      
      // 定期刷新统计（可选）
      this.statsTimer = setInterval(() => {
        this.$refs.player.getRealConnectionStats()
      }, 10000) // 每10秒刷新一次
    },
    
    onConnectionStatsUpdated(stats) {
      this.senderCount = stats.senders
      this.availableConnections = stats.senders
      
      // 获取连接类型
      if (this.$refs.player) {
        this.connectionType = this.$refs.player.connectionType
      }
    },
    
    getConnectionTypeDesc(type) {
      const types = {
        'H': '本地连接',
        'S': 'STUN穿透', 
        'T': 'TURN中继'
      }
      return types[type] || '未知'
    }
  },
  
  beforeDestroy() {
    if (this.statsTimer) {
      clearInterval(this.statsTimer)
    }
  }
}
</script>
```

## 测试页面

项目中包含了一个完整的测试页面：`pages/test-real-connection-stats.vue`

### 测试功能
1. 连接/断开WebRTC
2. 实时显示连接统计
3. 查看在线客户端列表
4. 连接日志记录

### 访问方式
在uni-app项目中导航到测试页面：
```javascript
uni.navigateTo({
  url: '/pages/test-real-connection-stats'
})
```

## 技术实现

### 信令服务器交互
1. 注册成功后自动发送 `list_clients` 消息
2. 接收 `client_list` 响应并解析客户端信息
3. 实时更新可用连接数量

### WebRTC统计分析
1. 使用 `RTCPeerConnection.getStats()` API
2. 分析ICE候选类型确定连接方式
3. 监控连接状态变化

### 跨平台兼容
- H5环境：直接使用WebRTC API
- App环境：通过renderjs实现
- 统一的事件接口

## 注意事项

1. **信令服务器支持**: 确保信令服务器支持 `list_clients` 消息类型
2. **权限要求**: 需要网络访问权限
3. **性能考虑**: 统计收集会消耗一定CPU资源
4. **错误处理**: 网络异常时会自动重试

## 故障排除

### 常见问题

1. **可用连接数始终为0**
   - 检查信令服务器是否在线
   - 确认发送端是否已连接
   - 查看控制台是否有错误信息

2. **连接类型显示错误**
   - 确保WebRTC连接已建立
   - 检查ICE服务器配置
   - 查看网络环境是否支持P2P连接

3. **统计信息不更新**
   - 检查WebSocket连接状态
   - 确认组件事件监听是否正确
   - 查看是否有JavaScript错误

### 调试方法

1. 启用详细日志
2. 使用测试页面验证功能
3. 检查网络连接状态
4. 查看WebRTC连接统计

## 更新日志

### v1.0.0 (2025-01-15)
- 初始版本发布
- 支持真实连接统计获取
- 添加连接类型检测
- 实现客户端列表管理
- 提供完整的测试页面
