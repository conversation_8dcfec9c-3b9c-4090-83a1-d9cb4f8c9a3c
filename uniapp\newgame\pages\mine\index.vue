<template>
	<view class="mine-container">
		<!-- 顶部用户信息区 -->
		<view class="top-bar">
			<view class="user-info-wrap">
				<view class="avatar-wrap">
					<view class="avatar-level-wrap large">
						<image class="avatar small" :src="userInfo.avatar || defaultAvatar" />
						<image class="level-avatar-bg large"
							:src="userInfo.level_avatar || '/static/level_avatar.png'" />
					</view>
				</view>
				<view class="user-detail">
					<view class="nickname-row">
						<view class="nickname" @tap="handleUserInfoClick">{{ userInfo.nickname || '未登录' }}</view>
						<view class="setting-btn" @tap.stop="goProfile">
							<image src="/static/mine/setting.png" />
						</view>
					</view>
					<view class="level-assets-row" v-if="userInfo.nickname">
						<view class="id-bg-wrap">
							<text class="id-text">ID: {{ userInfo.id }}</text>
						</view>
						<view class="asset-item" @tap.stop="goCharge">
							<image class="coin-icon" src="/static/coin.png" />
							<text class="asset-num">{{ formatNumber(userInfo.money) }}</text>
							<image class="asset-action-icon" src="/static/add.png" />
						</view>
						<view class="asset-item" @click="openExchangeDialog">
							<image class="diamond-icon" src="/static/score.png" />
							<text class="asset-num">{{ formatNumber(userInfo.score) }}</text>
							<image class="asset-action-icon" src="/static/change.png" />
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 我的应用区 -->
		<view class="section-card">
			<view class="section-title-wrap">
				<image class="title-bg" src="/static/mine/mine.png" />
				<text class="section-title">我的应用</text>
			</view>
			<view class="section-content">
				<view class="entry-item" v-for="(item, idx) in mainEntries" :key="item.text"
					:style="{width: (100/mainEntries.length)+'%'}" @tap="item.action">
					<image class="entry-icon" :src="item.icon" />
					<text>{{ item.text }}</text>
				</view>
			</view>
		</view>

		<!-- 我的账号 -->
		<view class="section-card">
			<view class="section-title-wrap">
				<image class="title-bg" src="/static/mine/mine.png" />
				<text class="section-title">我的账号</text>
			</view>
			<view class="section-content">
				<view class="entry-item" v-for="(item, idx) in filteredServiceEntries" :key="item.text"
					@tap="item.action">
					<image class="entry-icon" :src="item.icon" />
					<text>{{ item.text }}</text>
				</view>
			</view>
		</view>

		<!-- 我的服务区 -->
		<view class="section-card">
			<view class="section-title-wrap">
				<image class="title-bg" src="/static/mine/mine.png" />
				<text class="section-title">我的服务</text>
			</view>
			<view class="section-content">
				<view class="entry-item" v-for="(item, idx) in accountEntries" :key="item.text"
					:style="{width: (100/accountEntries.length)+'%'}"
					@tap="item.text === '用户协议' ? goUserProtocol() : (item.text === '平台声明' ? goStatement() : item.action)">
					<image class="entry-icon" :src="item.icon" />
					<text>{{ item.text }}</text>
				</view>
			</view>
		</view>

		<!-- 实名认证弹窗 -->
		<UniversalModal
			:show="showAuthModal"
			title="实名认证"
			@close="closeAuthModal"
			size="medium"
		>
			<view class="input-group">
				<view class="input-label">真实姓名</view>
				<input class="modal-input" :class="{error: realNameError}" v-model="realName" placeholder="请确保与您身份证上的姓名一致" @focus="clearRealNameError" />
				<view class="error-tip" :class="{visible: realNameError}">{{ realNameErrorMsg || '　' }}</view>
			</view>
			<view class="input-group">
				<view class="input-label">身份证号</view>
				<input class="modal-input" :class="{error: idNumberError}" v-model="idNumber" placeholder="请输入18位身份证号" type="idcard" @focus="clearIdNumberError" />
				<view class="error-tip" :class="{visible: idNumberError}">{{ idNumberErrorMsg || '　' }}</view>
			</view>
			<view class="disclaimer-text">
				您的实名信息仅用于提供给相关部门进行认证，判断是否成年。我们不会将此信息用于其他场景，请务必保证您的身份证信息为真实有效的本人信息，一经实名认证通过即认定为成年本人游玩。
			</view>
			<button class="submit-btn modal-button primary" @tap="submitAuth">提交</button>
		</UniversalModal>

		<!-- 手机绑定弹窗 -->
		<UniversalModal
			:show="showBindModal"
			title="绑定手机号"
			@close="closeBindModal"
			size="medium"
		>
			<view class="input-group">
				<input class="bind-input modal-input" :class="{error: phoneError}" v-model="bindPhone" placeholder="请输入手机号" maxlength="11" @focus="clearPhoneError" />
				<view class="error-tip" :class="{visible: phoneError}">{{ phoneErrorMsg || '　' }}</view>
			</view>
			<view class="input-group bind-code-group">
				<input class="bind-input modal-input" :class="{error: codeError}" v-model="bindCode" placeholder="请输入验证码" value="123456" maxlength="6" @focus="clearCodeError" />
				<view class="get-code-btn" :class="{disabled: codeTimer>0}" @tap="getBindCode">
					{{ codeTimer>0 ? codeTimer+'s' : '获取验证码' }}
				</view>
			</view>
			<view class="error-tip" :class="{visible: codeError}">{{ codeErrorMsg || '　' }}</view>

			<template #footer>
				<button class="bind-submit-btn modal-button primary" @tap="submitBindPhone">提交</button>
			</template>
		</UniversalModal>

		<!-- 邀请/兑换码弹窗 -->
		<UniversalModal
			:show="showInviteCodeModal"
			title="邀请/兑换码"
			@close="closeInviteCodeModal"
			size="medium"
		>
			<view class="input-group">
				<view class="input-label">邀请/兑换码</view>
				<input class="modal-input" :class="{error: inviteCodeError}" v-model="inviteCode" placeholder="请输入邀请码或兑换码" @focus="clearInviteCodeError" />
				<view class="error-tip" :class="{visible: inviteCodeError}">{{ inviteCodeErrorMsg || '　' }}</view>
			</view>
			<button class="submit-btn modal-button primary" @tap="submitInviteCode">提交</button>
		</UniversalModal>

		<ScoreExchangeDialog :show="showExchangeDialog" :userInfo="userInfo" @close="showExchangeDialog = false" @exchangeSuccess="getUserInfo" />
	</view>
</template>

<script>
	import request from '@/utils/request.js'
	import config from '@/config.js'
	import ScoreExchangeDialog from '@/components/ScoreExchangeDialog.vue'
	import UniversalModal from '@/components/UniversalModal.vue'
	export default {
		components: {
			ScoreExchangeDialog,
			UniversalModal
		},
		data() {
			return {
				userInfo: {},
				defaultAvatar: '/static/avatar.png',
				isFirstLoad: true,
				mainEntries: [{
						icon: '/static/mine/charge.png',
						text: '充值',
						action: () => this.goCharge()
					},
					{
						icon: '/static/mine/record.png',
						text: '记录',
						action: () => this.goRecord()
					},
				],
				serviceEntries: [{
						icon: '/static/mine/agent.png',
						text: '代理中心',
						action: () => this.goAgent()
					},
					{
						icon: '/static/mine/auth.png',
						text: '实名认证',
						action: () => this.goAuth()
					},
					{
						icon: '/static/mine/phone.png',
						text: '手机绑定',
						action: () => this.goPhone()
					},
					{
						icon: '/static/mine/invite.png',
						text: '邀请好友',
						action: () => this.goInvite()
					},
					{
						icon: '/static/mine/code.png',
						text: '邀请/兑换码',
						action: () => this.openInviteCodeModal()
					}

				],
				accountEntries: [{
						icon: '/static/mine/feedback.png',
						text: '隐私协议',
						action: () => this.goPrivacy()
					},
					{
						icon: '/static/mine/user.png',
						text: '用户协议',
						action: () => this.goUserProtocol()
					},
					{
						icon: '/static/mine/statement.png',
						text: '平台声明',
						action: () => this.goStatement()
					},
					{
						icon: '/static/mine/about.png',
						text: '关于我们',
						action: () => this.goAbout()
					},
				],
				showAuthModal: false,
				showBindModal: false,
				realName: '',
				idNumber: '',
				bindPhone: '',
				bindCode: '',
				codeTimer: 0,
				codeTimerId: null,
				showDevDialog: false,
				devDialogMsg: '',
				showExchangeDialog: false,
				showInviteCodeModal: false,
				inviteCode: '',
				// 实名认证错误状态
				realNameError: false,
				realNameErrorMsg: '',
				idNumberError: false,
				idNumberErrorMsg: '',
				// 邀请/兑换码错误状态
				inviteCodeError: false,
				inviteCodeErrorMsg: '',
				// 手机绑定错误状态
				phoneError: false,
				phoneErrorMsg: '',
				codeError: false,
				codeErrorMsg: ''
			}
		},
		computed: {
			filteredServiceEntries() {
				return this.serviceEntries.filter(item => {
					if (item.text === '代理中心') {
						return this.userInfo && this.userInfo.agent_id > 0;
					}
					return true;
				});
			}
		},
		onLoad() {
			// 页面首次加载时获取用户信息
			this.getUserInfo()
		},
		onShow() {
			// 如果不是首次加载，且有token，则刷新用户信息
			if (!this.isFirstLoad && uni.getStorageSync(config.tokenKey)) {
				this.getUserInfo()
			}
			this.isFirstLoad = false
		},
		methods: {
			// 获取用户信息
			async getUserInfo() {
				try {
					const res = await request({
						url: '/api/user/detail',
					})
					if (res.code === 1) {
						this.userInfo = res.data
						// 更新本地存储
						uni.setStorageSync(config.userInfo, res.data)
					} else {
						uni.showToast({
							title: res.msg || '获取用户信息失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('获取用户信息失败：', error)
				}
			},
			handleUserInfoClick() {
				if (!this.userInfo.nickname) {
					uni.navigateTo({
						url: '/pages/login/index'
					})
				} else {
					this.goProfile()
				}
			},
			goProfile() {
				uni.navigateTo({
					url: '/pages/mine/info'
				})
			},
			goCharge() {
				uni.switchTab({
					url: '/pages/pay/index'
				})
			},
			goRecord() {
				uni.navigateTo({
					url: '/pages/mine/records'
				})
			},
			goBag() {},
			goFeedback() {},
			goAuth() {
				if (this.userInfo.realname) { // 存在真实姓名
					uni.showToast({
						title: '您已认证过了',
						icon: 'none'
					})
					return
				}
				this.showAuthModal = true
			},
			goPhone() {
				this.showBindModal = true
			},
			goService() {},
			goPrivacy() {
				const url = `${config.baseUrl}/api/user/privacy_xieyi`
				uni.navigateTo({
					url: '/pages/webview/webview?url=' + encodeURIComponent(url)
				})
			},
			goUserProtocol() {
				const url = `${config.baseUrl}/api/user/user_xieyi`
				uni.navigateTo({
					url: '/pages/webview/webview?url=' + encodeURIComponent(url)
				})
			},
			goTeen() {},
			goStatement() {
				const url = `${config.baseUrl}/api/user/platform`
				uni.navigateTo({
					url: '/pages/webview/webview?url=' + encodeURIComponent(url)
				})
			},
			goCancel() {},
			goVip() {},
			goInvite() {
				uni.navigateTo({
					url: '/pages/mine/invite'
				})
			},
			goAbout() {},
			goAgent() {
				uni.navigateTo({
					url: '/pages/agent/agent'
				})
			},
			closeAuthModal() {
				this.showAuthModal = false
				this.realName = ''
				this.idNumber = ''
				// 清除错误状态
				this.realNameError = false
				this.realNameErrorMsg = ''
				this.idNumberError = false
				this.idNumberErrorMsg = ''
			},
			clearRealNameError() {
				this.realNameError = false
				this.realNameErrorMsg = ''
			},
			clearIdNumberError() {
				this.idNumberError = false
				this.idNumberErrorMsg = ''
			},
			clearInviteCodeError() {
				this.inviteCodeError = false
				this.inviteCodeErrorMsg = ''
			},
			clearPhoneError() {
				this.phoneError = false
				this.phoneErrorMsg = ''
			},
			clearCodeError() {
				this.codeError = false
				this.codeErrorMsg = ''
			},
			async submitAuth() {
				// 清除之前的错误状态
				this.realNameError = false
				this.realNameErrorMsg = ''
				this.idNumberError = false
				this.idNumberErrorMsg = ''

				if (!this.realName) {
					this.realNameError = true
					this.realNameErrorMsg = '请输入真实姓名'
					return
				}
				if (!this.idNumber) {
					this.idNumberError = true
					this.idNumberErrorMsg = '请输入身份证号'
					return
				}

				try {
					const res = await request({
						url: '/api/user/realAuth',
						method: 'POST',
						data: {
							realname: this.realName,
							idcard: this.idNumber
						}
					})

					if (res.code === 1) {
						this.closeAuthModal()
						uni.showToast({
							title: '认证成功',
							icon: 'success'
						})
						this.getUserInfo() // 认证成功后刷新用户信息，确保 auth_status 更新
					} else {
						// 根据错误信息判断是姓名还是身份证的问题
						const errorMsg = res.msg || '认证失败'
						if (errorMsg.includes('姓名') || errorMsg.includes('真实姓名')) {
							this.realNameError = true
							this.realNameErrorMsg = errorMsg
						} else {
							this.idNumberError = true
							this.idNumberErrorMsg = errorMsg
						}
					}
				} catch (error) {
					console.error('实名认证请求失败：', error)
					this.idNumberError = true
					this.idNumberErrorMsg = '网络错误，请稍后再试'
				}
			},
			closeBindModal() {
				this.showBindModal = false
				this.bindPhone = ''
				this.bindCode = ''
				if (this.codeTimerId) {
					clearInterval(this.codeTimerId)
					this.codeTimerId = null
				}
				this.codeTimer = 0
				// 清除错误状态
				this.phoneError = false
				this.phoneErrorMsg = ''
				this.codeError = false
				this.codeErrorMsg = ''
			},
			getBindCode() {
				if (this.codeTimer > 0) return

				// 清除之前的错误状态
				this.phoneError = false
				this.phoneErrorMsg = ''

				if (!/^1[3-9]\d{9}$/.test(this.bindPhone)) {
					this.phoneError = true
					this.phoneErrorMsg = '手机号码格式不正确'
					return
				}

				// TODO: 调用发送验证码接口
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				})
				this.codeTimer = 60
				this.codeTimerId = setInterval(() => {
					this.codeTimer--
					if (this.codeTimer <= 0) {
						clearInterval(this.codeTimerId)
						this.codeTimerId = null
					}
				}, 1000)
			},
			async submitBindPhone() {
				// 清除之前的错误状态
				this.phoneError = false
				this.phoneErrorMsg = ''
				this.codeError = false
				this.codeErrorMsg = ''

				if (!/^1[3-9]\d{9}$/.test(this.bindPhone)) {
					this.phoneError = true
					this.phoneErrorMsg = '手机号码格式不正确'
					return
				}
				if (!this.bindCode) {
					this.codeError = true
					this.codeErrorMsg = '请输入验证码'
					return
				}
				try {
					const res = await request({
						url: '/api/user/changemobile',
						method: 'POST',
						data: {
							mobile: this.bindPhone,
							captcha: this.bindCode
						}
					})
					if (res.code === 1) {
						this.closeBindModal()
						uni.showToast({
							title: '绑定成功',
							icon: 'success'
						})
						this.getUserInfo && this.getUserInfo()
					} else {
						this.codeError = true
						this.codeErrorMsg = res.msg || '绑定失败'
					}
				} catch (error) {
					this.codeError = true
					this.codeErrorMsg = '网络错误，请稍后再试'
				}
			},
			showDevTip(msg = '功能正在开发中...') {
				this.devDialogMsg = msg
				this.showDevDialog = true
			},

			// 格式化数字显示
			formatNumber(num) {
				if (!num && num !== 0) return '0'
				const number = Number(num)
				if (number < 10000000) {
					// 千万以下直接显示原数字
					return number.toString()
				} else if (number < 100000000) {
					// 千万到亿之间显示为千万单位
					return (number / 10000000).toFixed(1) + '千万'
				} else {
					// 亿以上显示为亿单位
					return (number / 100000000).toFixed(1) + '亿'
				}
			},

			openExchangeDialog() {
				this.showExchangeDialog = true;
			},

			// 显示邀请/兑换码弹窗
			openInviteCodeModal() {
				this.showInviteCodeModal = true;
			},

			// 关闭邀请/兑换码弹窗
			closeInviteCodeModal() {
				this.showInviteCodeModal = false;
				this.inviteCode = '';
				// 清除错误状态
				this.inviteCodeError = false;
				this.inviteCodeErrorMsg = '';
			},

			// 提交邀请/兑换码
			async submitInviteCode() {
				// 清除之前的错误状态
				this.inviteCodeError = false;
				this.inviteCodeErrorMsg = '';

				if (!this.inviteCode.trim()) {
					this.inviteCodeError = true;
					this.inviteCodeErrorMsg = '请输入邀请/兑换码';
					return;
				}

				try {
					const res = await request({
						url: '/api/user/useInviteCode',
						method: 'POST',
						data: {
							code: this.inviteCode.trim()
						}
					});

					if (res.code === 1) {
						this.closeInviteCodeModal();
						uni.showToast({
							title: res.msg || '使用成功',
							icon: 'success'
						});
						this.getUserInfo(); // 刷新用户信息
					} else {
						this.inviteCodeError = true;
						this.inviteCodeErrorMsg = res.msg || '使用失败';
					}
				} catch (error) {
					console.error('使用邀请/兑换码失败：', error);
					this.inviteCodeError = true;
					this.inviteCodeErrorMsg = '网络错误，请稍后再试';
				}
			},
		}
	}
</script>

<style scoped>
	.mine-container {
		background: #180F29;
		min-height: 100vh;
		padding-bottom: 32rpx;
	}

	/* 顶部信息栏样式 */
	.top-bar {
		display: flex;
		align-items: center;
		padding-bottom: 22rpx;
		justify-content: flex-start;
		background: url('/static/home/<USER>') no-repeat center/100vw 210rpx;
		width: 100vw;
		height: 180rpx;
		z-index: 100;
	}

	.user-info-wrap {
		display: flex;
		align-items: center;
		width: 100%;
	}

	.avatar-wrap {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
	}

	.avatar-level-wrap.large {
		position: relative;
		width: 120rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.avatar.small {
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
		position: absolute;
		left: 35rpx;
		top: 30rpx;
		z-index: 2;
		object-fit: cover;
	}

	.level-avatar-bg.large {
		width: 140rpx;
		height: 140rpx;
		border-radius: 50%;
		position: absolute;
		left: 0;
		top: 0;
		z-index: 3;
		object-fit: cover;
		pointer-events: none;
	}

	.user-detail {
		flex: 1;
		margin-left: 16rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.nickname-row {
		display: flex;
		align-items: center;
	}

	.nickname {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		width: 85%;
	}

	.level-assets-row {
		display: flex;
		align-items: center;
		margin-top: 6rpx;
	}

	.id-bg-wrap {
		position: relative;
		width: 180rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: url('/static/mine/id.png') no-repeat center/180rpx 70rpx;
	}

	.id-text {
		position: relative;
		z-index: 2;
		color: #fff;
		font-size: 26rpx;
		font-weight: bold;
		text-align: center;
		width: 100%;
		margin-top: 5rpx;
	}

	.setting-btn {
		display: flex;
		align-items: center;
		margin-left: 12rpx;
	}

	.setting-btn image {
		width: 25rpx;
		height: 25rpx;
		object-fit: contain;
	}

	/* 统一的功能区域样式 */
	.section-card {
		position: relative;
		background: #45325F;
		margin: 62rpx 32rpx 0 32rpx;
		border-radius: 24rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
		padding: 40rpx 0 0 0;
		/* 顶部padding为标题悬浮预留空间 */
		overflow: visible;
	}

	.section-title-wrap {
		position: absolute;
		top: -50rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 2;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 350rpx;
		height: 100rpx;
	}

	.title-bg {
		width: 350rpx;
		height: 100rpx;
		position: absolute;
		left: 0;
		top: 0;
		z-index: 1;
		object-fit: contain;
	}

	.section-title {
		position: relative;
		z-index: 2;
		font-size: 32rpx;
		color: #FFEF5B;
		padding: 8rpx 32rpx;
	}

	.section-content {
		display: flex;
		flex-wrap: wrap;
		padding: 24rpx;
	}

	.entry-item {
		width: 25%; /* 固定宽度，每行最多4个 */
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 32rpx;
		font-size: 24rpx;
		color: #fff;
	}

	.entry-icon {
		width: 72rpx;
		height: 72rpx;
		margin-bottom: 8rpx;
	}

	/* 保留通用的输入框样式 */

	.input-label {
		font-size: 28rpx;
		color: #fff;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.modal-input {
		height: 80rpx;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
		color: #fff;
		/* 修改为白色 */
		box-sizing: border-box;
		background-color: #3F3055;
	}

	.modal-input:focus {
		color: #fff;
	}

	.modal-input::placeholder {
		color: #999;
	}

	.modal-input.error {
		border: 2rpx solid #ff4757;
		background-color: rgba(255, 71, 87, 0.1);
	}

	.error-tip {
		font-size: 24rpx;
		color: transparent; /* 默认透明 */
		margin-top: 10rpx;
		line-height: 1.4;
		min-height: 34rpx; /* 固定高度占位 */
	}

	.error-tip.visible {
		color: #ff4757; /* 有错误时显示红色 */
	}

	.disclaimer-text {
		font-size: 24rpx;
		color: #fff;
		line-height: 1.5;
		margin: 10rpx 0rpx;
	}

	.submit-btn {
		width: 100%;
		height: 60px;
		line-height: 60px;
		border-radius: 30px;
		font-size: 16px;
		color: #fff;
		background: url('/static/mine/button.png') no-repeat center/100% 100%;
		border: none;
		text-align: center;
		margin-bottom: 15px;
	}


	.bind-input {
		width: 100%;
		height: 80rpx;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 30rpx;
		color: #fff;
		box-sizing: border-box;
		background-color: #3F3055;
		border: none;
		outline: none;
	}

	.bind-input:focus {
		color: #fff;
	}

	.bind-input::placeholder {
		color: #999;
	}

	.bind-input.error {
		border: 2rpx solid #ff4757;
		background-color: rgba(255, 71, 87, 0.1);
	}

	.bind-code-group {
		position: relative;
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.bind-code-group .bind-input {
		flex: 1;
	}

	.get-code-btn {
		background-color: #3F3055;
		color: #fff;
		font-size: 28rpx;
		padding: 20rpx 24rpx;
		border-radius: 10rpx;
		min-width: 140rpx;
		text-align: center;
		user-select: none;
		border: 2rpx solid #524D57;
	}

	.get-code-btn.disabled {
		color: #999;
		background-color: #2a2a2a;
	}

	.bind-submit-btn {
		width: 100%;
		height: 60px;
		line-height: 60px;
		border-radius: 30px;
		font-size: 16px;
		color: #fff;
		background: url('/static/mine/button.png') no-repeat center/100% 100%;
		border: none;
		text-align: center;
		margin-top: 5px;
	}


</style>