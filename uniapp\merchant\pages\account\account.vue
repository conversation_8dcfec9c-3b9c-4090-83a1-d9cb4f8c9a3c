<template>
  <view class="container">
    <!-- 订单筛选 -->
    <view class="order-filter">
      <picker mode="date" :value="startDate" @change="onStartDateChange">
        <view class="date-input">{{ startDate || '开始日期' }}</view>
      </picker>
      <text class="date-sep">至</text>
      <picker mode="date" :value="endDate" @change="onEndDateChange">
        <view class="date-input">{{ endDate || '结束日期' }}</view>
      </picker>
      <button class="search-btn" @click="onSearch">查询</button>
    </view>
    <!-- 订单列表 -->
    <view class="order-list">
      <view class="order-item" v-for="(item, idx) in accounts" :key="item.id">
        <view class="order-row"><text class="order-label">用户ID：</text><text class="order-value">{{ item.user_id }}</text></view>
        <view class="order-row"><text class="order-label">核销时间：</text><text class="order-value">{{ formatTime(item.createtime) }}</text></view>
        <view class="order-row"><text class="order-label">核销积分：</text><text class="order-value">{{ item.score }}</text></view>
        <view class="order-row"><text class="order-label">状态：</text><text class="order-value" :class="item.status === 1 ? 'success' : 'fail'">{{ item.status === 1 ? '成功' : '失败' }}</text></view>
        <view class="order-row" v-if="item.memo"><text class="order-label">备注：</text><text class="order-value">{{ item.memo }}</text></view>
      </view>
      <view v-if="accounts.length < total" class="load-more" @click="loadMore">加载更多</view>
      <view v-else-if="total > 0 && accounts.length >= total" class="load-more">没有更多了</view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
function formatTime(val) {
  if (!val) return '--'
  const d = new Date(val * 1000)
  const y = d.getFullYear()
  const m = (d.getMonth() + 1).toString().padStart(2, '0')
  const day = d.getDate().toString().padStart(2, '0')
  const h = d.getHours().toString().padStart(2, '0')
  const min = d.getMinutes().toString().padStart(2, '0')
  const s = d.getSeconds().toString().padStart(2, '0')
  return `${y}.${m}.${day} ${h}:${min}:${s}`
}
function dateToTimestamp(dateStr, isEnd) {
  if (!dateStr) return ''
  const time = isEnd ? '23:59:59' : '00:00:00'
  return Math.floor(new Date(dateStr + ' ' + time).getTime() / 1000)
}
export default {
  data() {
    return {
      startDate: '',
      endDate: '',
      accounts: [],
      page: 1,
      show_num: 10,
      total: 0,
      loading: false
    }
  },
  onLoad() {
    this.getAccountList()
  },
  methods: {
    async getAccountList(loadMore = false) {
      if (this.loading) return
      this.loading = true
      try {
        const res = await request({
          url: '/api/merchant/hx_log',
          method: 'POST',
          data: {
            page: this.page,
            show_num: this.show_num,
            start_time: dateToTimestamp(this.startDate, false),
            end_time: dateToTimestamp(this.endDate, true)
          }
        })
        if (res.code === 1 && res.data && res.data.data) {
          const list = res.data.data
          if (loadMore) {
            this.accounts = this.accounts.concat(list)
          } else {
            this.accounts = list
          }
          this.total = res.data.total
        }
      } finally {
        this.loading = false
      }
    },
    loadMore() {
      if (this.accounts.length < this.total) {
        this.page++
        this.getAccountList(true)
      }
    },
    onStartDateChange(e) {
      this.startDate = e.detail.value
    },
    onEndDateChange(e) {
      this.endDate = e.detail.value
    },
    onSearch() {
      this.page = 1
      this.getAccountList()
    },
    formatTime
  }
}
</script>

<style scoped>
.container {
  padding-bottom: 100rpx;
  background: #180F29;
  min-height: 100vh;
}
.order-filter {
  display: flex;
  align-items: center;
  margin: 40rpx 30rpx 30rpx 30rpx;
  font-size: 24rpx;
}
.date-input {
  width: 240rpx;
  height: 60rpx;
  margin: 0 8rpx;
  border: 1rpx solid #4C3A62;
  border-radius: 8rpx;
  padding: 0 18rpx;
  font-size: 26rpx;
  background: #2A1840;
  color: #fff;
  display: flex;
  align-items: center;
  line-height: 60rpx;
  box-sizing: border-box;
}
.date-sep {
  margin: 0 8rpx;
  color: #999;
  font-size: 26rpx;
}
.search-btn {
  margin-left: 10rpx;
  font-size: 24rpx;
  padding: 0 28rpx;
  height: 60rpx;
  background: #8F75EB;
  color: #fff;
  border-radius: 8rpx;
  border: none;
}
.order-list {
  margin: 0 30rpx;
}
.order-item {
  border: 2rpx solid #4C3A62;
  border-radius: 14rpx;
  margin-bottom: 28rpx;
  padding: 32rpx 28rpx;
  background: #2A1840;
  font-size: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
}
.order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.order-label {
  color: #999;
}
.order-value {
  color: #fff;
}
.order-value.success {
  color: #48A578 !important;
}
.order-value.fail {
  color: #ff4757 !important;
}
.order-row:last-child {
  margin-bottom: 0;
}
.load-more {
  text-align: center;
  color: #999;
  font-size: 26rpx;
  padding: 24rpx 0;
  cursor: pointer;
}
</style> 