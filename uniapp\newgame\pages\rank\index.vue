<template>
	<view class="container">
		<!-- 顶部切换栏和top3 -->
		<view class="rank-top-fixed">
			<view class="rank-tabs">
				<view v-for="(tab, idx) in ['COIN榜', '街机榜']" :key="tab"
					:class="['rank-tab', {active: currentTab === idx}]" @tap="currentTab = idx">
					{{ tab }}
				</view>
			</view>
			<view class="rank-top3">
				<view class="top3-item top2">
					<view class="top3-avatar-stack">
						<view class="top3-avatar-bg"></view>
						<image class="top3-avatar" :src="top3[1].avatar" />
						<image class="top3-avatar-frame" src="/static/rank/top2.png" />
					</view>
					<view class="top3-bottom top2-bottom">
						<view class="top3-name">{{ top3[1].name }}</view>
						<view class="top3-score">{{ top3[1].score }}</view>
					</view>
				</view>
				<view class="top3-item top1">
					<view class="top3-avatar-stack">
						<view class="top3-avatar-bg"></view>
						<image class="top3-avatar" :src="top3[0].avatar" />
						<image class="top3-avatar-frame" src="/static/rank/top1.png" />
					</view>
					<view class="top3-bottom top1-bottom">
						<view class="top3-name">{{ top3[0].name }}</view>
						<view class="top3-score">{{ top3[0].score }}</view>
					</view>
				</view>
				<view class="top3-item top3">
					<view class="top3-avatar-stack">
						<view class="top3-avatar-bg"></view>
						<image class="top3-avatar" :src="top3[2].avatar" />
						<image class="top3-avatar-frame" src="/static/rank/top3.png" />
					</view>
					<view class="top3-bottom top3-bottom-bg">
						<view class="top3-name">{{ top3[2].name }}</view>
						<view class="top3-score">{{ top3[2].score }}</view>
					</view>
				</view>
			</view>
			<view class="rank-header-row">
				<view class="header-col">排名</view>
				<view class="header-col">玩家</view>
				<view class="header-col">积分</view>
				<view class="header-col">奖励</view>
			</view>
		</view>
		<!-- 排行榜列表 -->
		<scroll-view class="rank-list-scroll" :scroll-y="true" :style="scrollStyle" @scrolltolower="loadMore">
			<view v-for="(item, idx) in visibleList" :key="item.id" class="rank-row">
				<view class="rank-col rank-index">{{ idx + 1 }}</view>
				<view class="rank-col rank-user">
					<view class="rank-avatar-circle">
						<image class="rank-avatar" :src="item.avatar" />
					</view>
					<view class="rank-user-info">
						<view class="rank-username">{{ item.name }}</view>
					</view>
				</view>
				<view class="rank-col rank-score">{{ item.score }}</view>
				<view class="rank-col rank-reward">{{ item.reward }}</view>
			</view>
		</scroll-view>
		<!-- 底部当前用户 -->
		<view class="rank-bottom-fixed">
			<view class="rank-row self-row">
				<view class="rank-col rank-index">-</view>
				<view class="rank-col rank-user">
					<view class="rank-avatar-circle">
						<image class="rank-avatar" :src="self.avatar" />
					</view>
					<view class="rank-username">{{ self.name }}</view>
				</view>
				<view class="rank-col rank-score">{{ self.score }}</view>
				<view class="rank-col rank-reward">{{ self.reward }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentTab: 0, // 0:COIN榜 1:街机榜
				top3: [{
						avatar: '/static/avatar.png',
						name: '昵称1',
						level: '黄金勇士III',
						score: 3894827
					},
					{
						avatar: '/static/avatar.png',
						name: '昵称2',
						level: '黄金勇士III',
						score: 2752497
					},
					{
						avatar: '/static/avatar.png',
						name: '昵称3',
						level: '铂金领主I',
						score: 2260618
					}
				],
				rankList: [{
						id: 1,
						avatar: '/static/avatar.png',
						name: '昵称1',
						level: '黄金勇士III',
						score: 3894827,
						reward: '2000金币'
					},
					{
						id: 2,
						avatar: '/static/avatar.png',
						name: '昵称2',
						level: '黄金勇士III',
						score: 2752497,
						reward: '1500金币'
					},
					{
						id: 3,
						avatar: '/static/avatar.png',
						name: '昵称3',
						level: '铂金领主I',
						score: 2260618,
						reward: '1000金币'
					},
					{
						id: 4,
						avatar: '/static/avatar.png',
						name: '昵称4',
						level: '白银游侠II',
						score: 1964257,
						reward: '800金币'
					},
					{
						id: 5,
						avatar: '/static/avatar.png',
						name: '昵称5',
						level: '黄金勇士II',
						score: 1808368,
						reward: '600金币'
					},
					{
						id: 6,
						avatar: '/static/avatar.png',
						name: '昵称6',
						level: '黄金勇士II',
						score: 1500000,
						reward: '500金币'
					},
					{
						id: 7,
						avatar: '/static/avatar.png',
						name: '昵称7',
						level: '黄金勇士I',
						score: 1200000,
						reward: '400金币'
					},
					{
						id: 8,
						avatar: '/static/avatar.png',
						name: '昵称8',
						level: '黄金勇士I',
						score: 1000000,
						reward: '300金币'
					},
					{
						id: 9,
						avatar: '/static/avatar.png',
						name: '昵称9',
						level: '青铜战神I',
						score: 800000,
						reward: '200金币'
					},
					{
						id: 10,
						avatar: '/static/avatar.png',
						name: '昵称10',
						level: '青铜战神I',
						score: 600000,
						reward: '100金币'
					},
					{
						id: 11,
						avatar: '/static/avatar.png',
						name: '昵称11',
						level: '黄金勇士III',
						score: 580000,
						reward: '90金币'
					},
					{
						id: 12,
						avatar: '/static/avatar.png',
						name: '昵称12',
						level: '黄金勇士III',
						score: 570000,
						reward: '80金币'
					},
					{
						id: 13,
						avatar: '/static/avatar.png',
						name: '昵称13',
						level: '铂金领主I',
						score: 560000,
						reward: '70金币'
					},
					{
						id: 14,
						avatar: '/static/avatar.png',
						name: '昵称14',
						level: '白银游侠II',
						score: 550000,
						reward: '60金币'
					},
					{
						id: 15,
						avatar: '/static/avatar.png',
						name: '昵称15',
						level: '黄金勇士II',
						score: 540000,
						reward: '50金币'
					},
					{
						id: 16,
						avatar: '/static/avatar.png',
						name: '昵称16',
						level: '黄金勇士II',
						score: 530000,
						reward: '40金币'
					},
					{
						id: 17,
						avatar: '/static/avatar.png',
						name: '昵称17',
						level: '黄金勇士I',
						score: 520000,
						reward: '30金币'
					},
					{
						id: 18,
						avatar: '/static/avatar.png',
						name: '昵称18',
						level: '黄金勇士I',
						score: 510000,
						reward: '20金币'
					},
					{
						id: 19,
						avatar: '/static/avatar.png',
						name: '昵称19',
						level: '青铜战神I',
						score: 500000,
						reward: '10金币'
					},
					{
						id: 20,
						avatar: '/static/avatar.png',
						name: '昵称20',
						level: '青铜战神I',
						score: 490000,
						reward: '5金币'
					}
				],
				self: {
					avatar: '/static/avatar.png',
					name: '佰赏香氛',
					level: '青铜战神I',
					score: 0,
					reward: '0金币'
				},
				scrollStyle: '',
				visibleCount: 15
			}
		},
		computed: {
			visibleList() {
				return this.rankList.slice(0, this.visibleCount)
			}
		},
		mounted() {
			// 计算滚动区域高度，减去顶部和底部固定区高度
			this.calcScrollStyle();
		},
		methods: {
			calcScrollStyle() {
				// 顶部高度约420rpx，底部120rpx
				this.scrollStyle = 'height: calc(100vh - 420rpx - 120rpx);';
			},
			loadMore() {
				if (this.visibleCount < this.rankList.length) {
					this.visibleCount = Math.min(this.visibleCount + 5, this.rankList.length)
				}
			}
		},
		watch: {
			currentTab(val) {
				// TODO: 切换榜单时请求不同数据
			}
		}
	}
</script>

<style scoped>
	/* 页面特有样式 */

	.container {
		background: url('/static/rank/content.png') no-repeat center center;
		background-size: cover;
		position: relative;
	}

	.rank-top-fixed {
		/* 不再sticky，直接占位 */
		width: 100%;
		background: transparent;
		box-shadow: none;
	}

	.rank-tabs {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 32rpx 0;
	}

	.rank-tab {
		font-size: 32rpx;
		color: #fff;
		width: 50%;
		height: 110rpx;
		line-height: 110rpx;
		text-align: center;
		background: url('/static/title.png') no-repeat center/100% 100%;
		border: none;
		font-weight: bold;
		margin: 0 10rpx;
		box-sizing: border-box;
	}

	.rank-tab.active {
		background: url('/static/title_active.png') no-repeat center/100% 100%;
		color: #F9F98D;
	}

	.rank-top3 {
		display: flex;
		justify-content: center;
		align-items: flex-end;
		margin-bottom: 0;
		margin-top: 50rpx;
	}

	.top3-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		/* width: 250rpx; */
		background: none;
		border-radius: 24rpx;
		box-shadow: none;
		padding: 0;
		position: relative;
	}

	.top3-avatar-stack {
		width: 160rpx;
		height: 160rpx;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.top3-avatar-bg {
		width: 160rpx;
		height: 160rpx;
		background: #1A0A3F;
		border-radius: 50%;
		position: absolute;
		left: 50%;
		top: 60%;
		transform: translate(-50%, -50%);
		z-index: 2;
	}

	.top3-avatar {
		width: 86rpx;
		height: 106rpx;
		position: absolute;
		left: 50%;
		top: 65%;
		transform: translate(-50%, -50%);
		z-index: 2;
	}

	.top3-avatar-frame {
		width: 260rpx;
		height: 300rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		z-index: 3;
		pointer-events: none;
	}

	.top3-bottom {
		width: 160rpx;
		height: 160rpx;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: relative;
		z-index: 1;
	}

	.top1-bottom {
		background: url('/static/rank/top1_bottom.png') no-repeat center/100% 100%;
	}

	.top2-bottom {
		background: url('/static/rank/top2_bottom.png') no-repeat center/100% 100%;
	}

	.top3-bottom-bg {
		background: url('/static/rank/top3_bottom.png') no-repeat center/100% 100%;
	}

	.top3-name {
		font-size: 24rpx;
		color: #fff;
		font-weight: bold;
		margin-top: 20rpx;
		margin-bottom: 2rpx;
		max-width: 120rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.top3-score {
		font-size: 22rpx;
		color: #fff;
		font-weight: bold;
	}

	.rank-header-row {
		display: flex;
		align-items: center;
		background: url('/static/rank/title.png') no-repeat center/100% 100%;
		border-radius: 0;
		width: 94%;
		margin: 0 auto;
		padding: 24rpx 0 24rpx 0;
		margin-top: 30rpx;
		font-size: 26rpx;
		color: #E1C18A;
		font-weight: bold;
	}

	.header-col {
		flex: 1;
		text-align: center;
	}

	.rank-list-scroll {
		width: 100%;
		background: transparent;
		overflow: auto;
		padding-bottom: 180rpx;
		padding-top: 0;
	}

	.rank-row {
		display: flex;
		align-items: center;
		background: url('/static/rank/rank.png') no-repeat center/100% 100%;
		border-bottom: none;
		padding: 18rpx 0;
		font-size: 26rpx;
		margin: 16rpx auto 0 auto;
		border-radius: 16rpx;
		min-height: 80rpx;
		width: 94%;
	}

	.rank-col {
		text-align: center;
	}

	.rank-index {
		flex: 1 0 0;
		color: #fff;
		font-weight: bold;
	}

	.rank-user {
		flex: 2 1 0;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		gap: 12rpx;
		padding-left: 8rpx;
	}

	.rank-avatar-circle {
		width: 68rpx;
		height: 68rpx;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.18);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.rank-avatar {
		width: 48rpx;
		height: 58rpx;
	}

	.rank-user-info {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
	}

	.rank-username {
		max-width: 120rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-size: 26rpx;
		color: #fff;
	}

	.rank-score {
		flex: 1 1 0;
		color: #fff;
		font-weight: bold;
	}

	.rank-reward {
		flex: 1 1 0;
		color: #F9F98D;
		font-weight: bold;
	}

	.rank-bottom-fixed {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 90rpx;
		background: transparent;
		z-index: 20;
		box-shadow: none;
	}

	.self-row {
		background: #180934;
		font-weight: bold;
		color: #fff;
		min-height: 80rpx;
		width: 100%;
		margin: 0 auto;
		/* border-top: 1px solid #fff; */
	}

	.top1 {
		margin: 0rpx 70rpx 50rpx 70rpx;
	}
</style>