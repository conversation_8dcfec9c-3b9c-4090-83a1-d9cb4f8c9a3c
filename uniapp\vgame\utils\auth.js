// utils/auth.js
import config from '@/config.js'

export default {
  /**
   * 检查登录状态
   * @returns {boolean} 是否已登录
   */
  checkLogin() {
    const token = uni.getStorageSync(config.tokenKey)
    const expireTime = uni.getStorageSync(config.tokenExpireKey)
 
    // 基础检查：token是否存在
    if (!token) return false

    // 进阶检查：token是否过期（可选）
    if (expireTime && Date.now() > expireTime) {
      this.clearToken()
      return false
    }
	const userInfo = uni.getStorageSync(config.userInfo);
	if (!userInfo) return false
	 
    return true
  },

  /**
   * 强制跳转登录页（含提示）
   */
  redirectToLogin() {
    if (!this.checkLogin()) {
      uni.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 1500,
        complete: () => {
          uni.reLaunch({ url: '/pages/login/login' })
        }
      })
    }
  },

  /**
   * 清除登录凭证
   */
  clearToken() {
    uni.removeStorageSync(config.tokenKey)
    uni.removeStorageSync(config.tokenExpireKey)
    uni.removeStorageSync(config.userInfo)
  }
}