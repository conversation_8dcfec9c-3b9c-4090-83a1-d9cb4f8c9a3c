<template>
  <view class="coin-game-container">
    <!-- 加载中蒙层 -->
    <view v-if="loading" class="loading-mask">
      <view class="loading-content">
        <image class="loading-logo" src="/static/loading-logo.png" mode="widthFix" />
        <view class="loading-bar-wrap">
          <view class="loading-text">加载中...</view>
          <view class="loading-bar-bg">
            <view class="loading-bar" :style="{ width: progress + '%' }"></view>
          </view>
          <view class="loading-percent">{{ progress }}%</view>
        </view>
      </view>
    </view>
    <!-- 主内容 -->
    <view v-else style="height: 100%;">
      <!-- HeiqiPlayer播放器 -->
      <HeiqiPlayer
        ref="heiqiPlayer"
        v-if="currentPlayerMode === 'heiqiplayer'"
        :defaultSignalingUrl="gameDetail.heiqiplayer_signaling_url"
        :defaultSenderId="gameDetail.heiqiplayer_sender_id"
        :autoConnect="true"
        :showConnectionControls="false"
        :showStats="true"
        :rotation="90"
        @connected="onWebRTCConnected"
        @connectionFailed="onWebRTCFailed"
        @streamReceived="onStreamReceived"
        @videoStatsUpdated="onVideoStatsUpdated"
        @switchPlayer="onSwitchPlayer"
      />

      <!-- 左上角状态 -->
      <view class="status-box">
        <template v-if="gameDetail.players && gameDetail.players.length">
          <image class="avatar-img-circle" :src="gameDetail.players[0].avatar" style="width:64rpx;height:64rpx;vertical-align:middle;margin-right:12rpx;" />
          <view class="occupy-nickname" style="display:inline-block;vertical-align:middle;color:#fff;font-size:28rpx;font-weight:bold;">{{ gameDetail.players[0].nickname }}</view>
        </template>
        <template v-else>
          {{ statusText }}
        </template>
      </view>
      <!-- 金币显示框，单独绝对定位 -->
      <view v-if="gameDetail.is_full === 2 && gameDetail.userInfo" class="status-coin-box-abs">
        <image class="status-coin-icon" src="/static/coin.png" />
        <text class="status-coin-num">{{ gameDetail.userInfo.money }}</text>
      </view>
      <!-- 右上角头像、静音按钮和退出按钮 -->
      <view class="avatar-timer-box-multi">
        <view class="avatar-group-box">
          <view v-if="userAvatars.length === 0 && gameDetail.userInfo" class="avatar-group-item">
            <image class="avatar-img-group" :src="gameDetail.userInfo.avatar || avatarUrl" />
          </view>
          <view v-for="(user, idx) in userAvatars" :key="user.id" class="avatar-group-item" :style="{ zIndex: 100 - idx }">
            <image class="avatar-img-group" :src="user.avatar" />
          </view>
        </view>

        <!-- 静音按钮 -->
        <button class="mute-button" @click="toggleVideoAudio">
          <text class="mute-icon">{{ isVideoMuted ? '🔇' : '🔊' }}</text>
        </button>

        <!-- 退出按钮 -->
        <view class="exit-btn" @click="handleExit">×</view>
      </view>
      <!-- 右上角头像和退出按钮下方60s倒计时 -->
      <view v-if="showIdleCountdown" class="idle-countdown-bar">
        <text class="idle-countdown-num">{{ idleCountdown }}S</text>
      </view>
      <!-- 底部按钮和金币数量 -->
      <view class="bottom-bar">
        <view v-if="gameDetail.is_full !== 1" class="coin-count-box">
          <image class="coin-icon" src="/static/coin.png" />
          <text>x{{ gameDetail.coin }}</text>
        </view>
        <view class="button-group">
          <button
            v-if="gameDetail.is_full === 2"
            class="action-btn auto-btn"
            :class="{ 'auto-active': autoMode }"
            @click="handleAuto"
            type="button"
          >
            {{ autoBtnText }}
          </button>
          <button
            class="play-btn"
            @click="startGame"
            type="button"
          >
            {{ playBtnText }}
          </button>
          <button
            v-if="gameDetail.is_full === 2"
            class="action-btn wiper-btn"
            @click="handleWiper"
            type="button"
          >
            雨刷
          </button>
        </view>
      </view>
      <!-- 雨刷按钮上方倒计时 -->
      <view v-if="gameDetail.is_full === 2 && leaveCountdown > 0 && !autoMode" class="leave-countdown-bar">
        <text class="leave-countdown-num">{{ leaveCountdown }}S</text>
      </view>
      <!-- 左下角功能按钮 -->
      <view class="func-btn-box" @click="toggleFuncDialog">
        <image class="func-btn-icon" src="/static/func/gear.png" />
        <view class="func-btn-text">功能</view>
      </view>
    </view>
    <!-- 退出对话框 -->
    <view v-if="showExitDialog" class="exit-dialog-mask">
      <view class="exit-dialog">
        <view class="exit-dialog-title">退出房间</view>
        <image class="exit-dialog-img" src="/static/coin.png" mode="widthFix" />
        <view class="exit-dialog-msg">是否结算并退出当前房间？</view>
        <view class="exit-dialog-btns">
          <button class="exit-btn-cancel" @click="handleSettleAndExit" type="button">结算并退出</button>
          <button class="exit-btn-confirm" @click="handleSettleCurrent" type="button">结算本局</button>
        </view>
        <view class="exit-dialog-close" @click="showExitDialog=false">×</view>
      </view>
    </view>
    <view v-if="showSettleMask" class="settle-mask">
      <view class="settle-dialog">
        <view class="settle-title">结算中</view>
        <image class="settle-img" src="/static/settle-loading.png" mode="widthFix" />
        <view class="settle-msg">结算时间稍长，请耐心等待！</view>
      </view>
    </view>
    <!-- 下机提醒弹窗 -->
    <view v-if="showLeaveDialog && !autoMode" class="leave-dialog-mask">
      <view class="leave-dialog leave-dialog-transparent">
        <view class="leave-title leave-title-yellow">下机提醒</view>
        <view class="leave-count leave-count-large">{{ leaveCountdown }}</view>
        <view class="leave-msg leave-msg-yellow">倒计时结束前，若未进行任何操作<br>自动结算并退出游戏</view>
        <button class="leave-btn" @click="handleContinueGame" type="button">继续游戏</button>
      </view>
    </view>
    <!-- 请等待提示蒙层 -->
    <view v-if="showWaitMask" class="wait-mask">
      <view class="wait-dialog">
        <view class="wait-title">请等待</view>
        <view class="wait-msg">请稍候...</view>
      </view>
    </view>
    <!-- 功能弹窗 -->
    <view v-if="showFuncDialog" class="func-dialog-mask" @click="closeFuncDialog">
      <view class="func-dialog" @click.stop>
        <view class="func-row">
          <view class="func-item">
            <image class="func-item-icon" src="/static/func/feedback.png" />
            <view class="func-item-text">设备反馈</view>
          </view>
          <view class="func-item" @click.stop="openSettingDialog">
            <image class="func-item-icon" src="/static/func/setting.png" />
            <view class="func-item-text">设置</view>
          </view>
          <view class="func-item" @click.stop="openServiceDialog">
            <image class="func-item-icon" src="/static/func/service.png" />
            <view class="func-item-text">联系客服</view>
          </view>
          <view class="func-item" @click.stop="openRuleDialog">
            <image class="func-item-icon" src="/static/func/rule.png" />
            <view class="func-item-text">玩法说明</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 玩法说明弹窗 -->
    <view v-if="showRuleDialog" class="rule-dialog-mask">
      <view class="rule-dialog">
        <view class="rule-dialog-title">玩法说明</view>
        <view class="rule-dialog-close" @click="showRuleDialog = false">×</view>
        <scroll-view scroll-y="true" class="rule-dialog-content">
          <view v-html="gameDetail.description"></view>
        </scroll-view>
      </view>
    </view>
    <!-- 联系客服弹窗 -->
    <view v-if="showServiceDialog" class="rule-dialog-mask">
      <view class="rule-dialog">
        <view class="rule-dialog-title">联系客服</view>
        <view class="rule-dialog-close" @click="showServiceDialog = false">×</view>
        <view class="service-dialog-content">
          <image class="service-qr" :src="gameDetail.service_qr || '/static/service_qr.png'" mode="widthFix" />
          <view class="service-tip">请扫描二维码添加平台客服</view>
        </view>
      </view>
    </view>
    <!-- 设置弹窗 -->
    <view v-if="showSettingDialog" class="setting-dialog-mask">
      <view class="setting-dialog">
        <view class="setting-dialog-title">设置</view>
        <view class="setting-dialog-close" @click="showSettingDialog = false">×</view>
        <view class="setting-list">
          <view class="setting-divider"></view>
          <view class="setting-item" v-for="(label, key) in settingLabels" :key="key">
            <view class="setting-label">{{ label }}</view>
            <view class="setting-switch" :class="{ on: setting[key] }" @click="toggleSetting(key)">
              <view class="switch-knob"></view>
              <view
                class="switch-text"
                :class="setting[key] ? 'switch-on' : 'switch-off'"
              >{{ setting[key] ? 'ON' : 'OFF' }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 恭喜获得弹窗 -->
    <view v-if="showRewardDialog" class="reward-dialog-mask">
      <view class="reward-dialog">
        <view class="reward-title">恭喜获得</view>
        <image class="reward-coin-img" src="/static/coin.png" />
        <view class="reward-coin-num">{{ rewardCoin }}</view>
        <view class="reward-dialog-close" @click="showRewardDialog = false">×</view>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/utils/request.js'
import config from '@/config.js'
import mqtt from 'mqtt'
import HeiqiPlayer from '@/components/HeiqiPlayer.vue'

export default {
  components: {
    HeiqiPlayer
  },
  data() {
    return {
      avatarUrl: '/static/avatar.png',
      gameDetail: {},
      loading: true,
      progress: 0,
      progressTimer: null,
      mqttClient: null,
      clientId: 'uniappMQTT-' + Math.random().toString(36).substr(2, 6) + Date.now().toString(36),
      mqttConnected: false,
      autoMode: false,
      showExitDialog: false,
      showSettleMask: false,
      autoInterval: null,
      showLeaveDialog: false,
      leaveCountdown: 80,
      leaveTimer: null,
      userAvatars: [],
      showIdleCountdown: false,
      idleCountdown: 60,
      idleTimer: null,
      showFuncDialog: false,
      showWaitMask: false,
      subscribedAckTopic: null,
      _autoExitTriggered: false,
      showRuleDialog: false,
      showServiceDialog: false,
      showSettingDialog: false,
      setting: {
        fullscreen: true,
        gameSound: false,
        effectSound: false,
        winPopup: false
      },
      showRewardDialog: false,
      rewardCoin: 0,

      // 播放器相关
      isVideoMuted: true, // 视频静音状态，默认静音
      lastClickTime: 0, // 最后点击时间
      clickDebounceTime: 300, // 防抖时间间隔(毫秒)
    }
  },
  computed: {
    // 根据gameDetail确定当前播放器模式
    currentPlayerMode() {
      // 如果gameDetail中有playermode字段，使用它；否则默认使用heiqiplayer
      if (this.gameDetail && this.gameDetail.playermode) {
        return this.gameDetail.playermode
      }
      return 'heiqiplayer' // 默认播放器
    },

    statusText() {
      if (this.gameDetail.is_full === 1 && this.gameDetail.userInfo) {
        return this.gameDetail.userInfo.nickname || '占位中'
      }
      if (this.gameDetail.is_full === 0) return '空闲中'
      if (this.gameDetail.is_full === 2) return '游戏中'
      return ''
    },
    playBtnText() {
      if (this.gameDetail.is_full === 1) return '占位中'
      if (this.gameDetail.is_full === 2) {
        return this.autoMode ? '自动中' : '投币'
      }
      return 'PLAY'
    },
    autoBtnText() {
      return this.autoMode ? '取消' : '托管'
    },
    settingLabels() {
      return {
        fullscreen: '全屏幕画面',
        gameSound: '游戏声音'
      }
    }
  },
  onLoad(options) {
    console.log('onLoad', Date.now(), options);
    const gameId = options.gameId
    if (gameId) {
      this.fetchGameData(gameId)
      this.gameId = gameId
    }
    // 动态进度条
    this.progress = 0
    this.loading = true
    this.progressTimer = setInterval(() => {
      if (this.progress < 100) {
        this.progress += 5
        if (this.progress > 100) this.progress = 100
      } else {
        clearInterval(this.progressTimer)
        this.loading = false
      }
    }, 100)
    // 连接MQTT
    this.initMqtt()
    // 启动下机倒计时（如果is_full=2）
    // 注意：如需更精确可在fetchGameData后判断is_full
    // this.startLeaveCountdown();
  },
  onUnload() {
    console.log('onUnload', Date.now());
    if (this.progressTimer) clearInterval(this.progressTimer)
    this.disconnectMqtt()
    // 清除托管定时器
    if (this.autoInterval) {
      clearInterval(this.autoInterval);
      this.autoInterval = null;
    }
    // 清除下机倒计时
    if (this.leaveTimer) {
      clearInterval(this.leaveTimer);
      this.leaveTimer = null;
    }
    // 清除空闲倒计时
    if (this.idleTimer) {
      clearInterval(this.idleTimer);
      this.idleTimer = null;
    }
  },
  methods: {
    // 防抖处理
    isClickDebounced() {
      const currentTime = Date.now()
      if (currentTime - this.lastClickTime < this.clickDebounceTime) {
        return true
      }
      this.lastClickTime = currentTime
      return false
    },

    // 切换视频音频
    toggleVideoAudio() {
      // 防抖处理
      if (this.isClickDebounced()) {
        console.log('静音按钮防抖，忽略重复点击')
        return
      }

      this.isVideoMuted = !this.isVideoMuted

      // 调用HeiqiPlayer的toggleAudio方法
      if (this.$refs.heiqiPlayer) {
        this.$refs.heiqiPlayer.toggleAudio()
      }

      // 显示Toast提示
      uni.showToast({
        title: this.isVideoMuted ? '已静音' : '已开启声音',
        icon: 'none',
        duration: 1000
      })
    },

    // HeiqiPlayer事件处理
    onWebRTCConnected() {
      console.log('HeiqiPlayer连接成功')
    },

    onWebRTCFailed(error) {
      console.log('HeiqiPlayer连接失败:', error)
      uni.showToast({
        title: `HeiqiPlayer连接失败: ${error}`,
        icon: 'none'
      })
    },

    onStreamReceived(stream) {
      console.log('收到HeiqiPlayer视频流', stream)
    },

    // 视频统计信息更新
    onVideoStatsUpdated(stats) {
      if (!stats) {
        console.warn('视频统计信息为空')
        return
      }
      // 可以在这里处理视频统计信息
    },

    // 播放器切换处理
    onSwitchPlayer(targetMode) {
      if (this.gameDetail) {
        this.gameDetail.playermode = targetMode
      }

      uni.showToast({
        title: `已切换到${targetMode === 'easyplayer' ? 'EasyPlayer' : 'HeiqiPlayer'}`,
        icon: 'success'
      })
    },

    handleExit() {
      if (this.gameDetail.is_full === 2) {
        this.showExitDialog = true;
      } else {
        uni.switchTab({ url: '/pages/index/index' });
      }
    },
    async fetchGameData(gameId) {
      try {
        const res = await request({
          url: '/api/game/detail',
          data: { gameId: gameId || this.gameId }
        })
        if (res.code === 1) {
          this.gameDetail = res.data
          // 拿到sn后再订阅
          this.subscribeAckTopic()
          // 根据is_full状态启动或停止倒计时
          if (this.gameDetail.is_full === 2) {
            this.startLeaveCountdown();
            this.showIdleCountdown = false;
            this.stopIdleCountdown();
            this.showWaitMask = false;
          } else if (this.gameDetail.is_full === 0) {
            this.startIdleCountdown();
            this.stopLeaveCountdown();
          } else {
            this.stopLeaveCountdown();
            this.stopIdleCountdown();
          }
          // 自动金币不足时自动结算并弹窗
          if (
            this.gameDetail.is_full === 2 &&
            this.gameDetail.userInfo &&
            this.gameDetail.userInfo.money <= 0
          ) {
            if (!this._autoExitTriggered) {
              this._autoExitTriggered = true;
              const userInfo = this.gameDetail.userInfo;
              const sn = this.gameDetail.sn;
              if (sn) {
                const pubTopic = `mqtt/${sn}/sub/02`;
                const payload = JSON.stringify({
                  type: '06',
                  number: 1,
                  client_type: 'uniapp',
                  userId: userInfo.id
                });
                console.log('自动金币不足，发送type=06 MQTT', pubTopic, payload);
                this.sendMessage(pubTopic, payload);
                this.showSettleMask = true;
              }
            }
          } else {
            this._autoExitTriggered = false;
          }
          // 获取所有玩家详细信息
          this.userAvatars = [];
          if (this.gameDetail.players && this.gameDetail.players.length) {
            const avatarPromises = this.gameDetail.players.map(player =>
              request({ url: '/api/user/detail', data: { user_id: player.id } })
            );
            const avatarResults = await Promise.all(avatarPromises);
            this.userAvatars = avatarResults.map((res, idx) => {
              if (res.code === 1 && res.data && res.data.avatar) {
                return { avatar: res.data.avatar, id: this.gameDetail.players[idx].id };
              } else {
                // fallback: 用players里的头像
                return { avatar: this.gameDetail.players[idx].avatar, id: this.gameDetail.players[idx].id };
              }
            });
          }
        }
      } catch (e) {
        uni.showToast({ title: '获取游戏数据失败', icon: 'none' })
      }
    },
    initMqtt() {
      const mqttUrl = config.mqttUrl
      const mqttUsername = config.mqttUsername
      const mqttPassword = config.mqttPassword
      const client = mqtt.connect(mqttUrl, {
        username: mqttUsername,
        password: mqttPassword,
        clientId: this.clientId,
        clean: true,
        connectTimeout: 4000,
        reconnectPeriod: 1000,
        keepalive: 30
      })
      this.mqttClient = client
      client.on('connect', () => {
        this.mqttConnected = true
        console.log('MQTT连接成功')
      })
      client.on('message', (topic, message) => {
        this.handleAckMessage(topic, message)
      })
      client.on('error', err => {
        this.mqttConnected = false
        console.error('MQTT连接错误', err)
      })
      client.on('close', () => {
        this.mqttConnected = false
        console.log('MQTT连接关闭')
      })
      client.on('offline', () => {
        this.mqttConnected = false
        console.log('MQTT离线')
      })
    },
    disconnectMqtt() {
      if (this.mqttClient && this.mqttClient.connected) {
        this.mqttClient.end()
        this.mqttClient = null
        this.mqttConnected = false
        console.log('MQTT disconnected')
      }
    },
    subscribeAckTopic() {
      // 订阅ACK主题，sn为接口返回的唯一标识
      const sn = this.gameDetail.sn
      if (!sn) {
        console.warn('sn字段不存在，无法订阅主题')
        return
      }
      const ackTopic = `mqtt/${sn}/pub/02ack`
      if (this.subscribedAckTopic === ackTopic) {
        // 已经订阅过，无需重复订阅
        return
      }
      this.subscribedAckTopic = ackTopic
      if (this.mqttClient) {
        this.mqttClient.subscribe(ackTopic, { qos: 1 }, (err) => {
          if (!err) {
            console.log('订阅ACK主题成功:', ackTopic)
          }
        })
      }
    },
    handleAckMessage(topic, message) {
      try {
        const topicParts = topic.split('/')
        const senderClientId = topicParts[3]
        if (senderClientId !== '02ack') {
          return;
        }
        const data = JSON.parse(message.toString())
        console.log('收到ACK响应:', data)          

        if (data.type === 'update_game') {
          this.fetchGameData()
        }
        // 奖励弹窗逻辑
        if (data.coin != null && !isNaN(data.coin) && Number(data.coin) >= 0) {
          this.showSettleMask = false;
          this.rewardCoin = Number(data.coin);
          this.showRewardDialog = true;
        }
      } catch (e) {
        console.error('消息解析失败:', e)
      }
    },
    // 点击PLAY按钮开始游戏
    async startGame() {
      console.log('投币按钮点击', Date.now());
      // 点击PLAY时，暂停并重置右上角60s倒计时
      this.stopIdleCountdown();
      this.idleCountdown = 60;
      this.showIdleCountdown = false;
      try {
        const userInfo = this.gameDetail.userInfo || {}
        const currentMoney = userInfo.money || 0
        if (currentMoney <= 0) {
          uni.showToast({
            title: '金币不足，请先充值',
            icon: 'none',
            duration: 2000
          })
          setTimeout(() => {
            uni.switchTab({ url: '/pages/pay/index' })
          }, 1500)
          return
        }
        const sn = this.gameDetail.sn
        if (!sn) {
          uni.showToast({ title: '游戏信息异常', icon: 'none' })
          return
        }
        const pubTopic = `mqtt/${sn}/sub/02`
        const payload = JSON.stringify({
          type: this.gameDetail.is_full === 2 ? '02' : '01',
          number: 1,
          client_type: 'uniapp',
          userId: userInfo.id,
          coin: this.gameDetail.is_full === 2 ? 1 : 0
        })
        this.sendMessage(pubTopic, payload)
        // 立即重置倒计时（只要不是托管）
        if (!this.autoMode) {
          console.log('重置倒计时');
          this.stopLeaveCountdown();
          this.startLeaveCountdown();
        }
        if (this.gameDetail.is_full === 0) {
          this.showWaitMask = true;
        }
        // fetchGameData异步执行
        await this.fetchGameData()
      } catch (error) {
        console.error('开始游戏失败:', error)
        uni.showToast({ title: '操作失败', icon: 'none' })
      }
    },
    // 托管按钮点击事件
    handleAuto() {
      console.log('托管按钮点击', Date.now(), '当前autoMode:', this.autoMode);
      this.autoMode = !this.autoMode
      if (this.autoMode) {
        // 启动定时器每秒发送type=02的MQTT
        this.autoInterval = setInterval(() => {
          const userInfo = this.gameDetail.userInfo || {};
          const sn = this.gameDetail.sn;
          if (!sn) return;
          const pubTopic = `mqtt/${sn}/sub/02`;
          const payload = JSON.stringify({
            type: '02',
            number: 1,
            client_type: 'uniapp',
            userId: userInfo.id
          });
          this.sendMessage(pubTopic, payload);
        }, 1000);
        // 托管时停止下机倒计时并隐藏弹窗
        this.stopLeaveCountdown();
        this.showLeaveDialog = false;
      } else {
        // 清除定时器
        if (this.autoInterval) {
          clearInterval(this.autoInterval);
          this.autoInterval = null;
        }
        // 取消托管后重新开始倒计时
        console.log('取消托管重置倒计时');
        this.stopLeaveCountdown();
        this.startLeaveCountdown();
      }
    },
    // 雨刷按钮点击事件
    handleWiper() {
      console.log('雨刷按钮点击', Date.now());
      const userInfo = this.gameDetail.userInfo || {};
      const sn = this.gameDetail.sn;
      if (!sn) {
        uni.showToast({ title: '游戏信息异常', icon: 'none' });
        return;
      }
      const pubTopic = `mqtt/${sn}/sub/02`;
      const payload = JSON.stringify({
        type: '03',
        number: 1,
        client_type: 'uniapp',
        userId: userInfo.id
      });
      this.sendMessage(pubTopic, payload);
      // 重置下机倒计时
      console.log('雨刷重置倒计时');
      this.stopLeaveCountdown();
      this.startLeaveCountdown();
    },
    async handleSettleAndExit() {
      await this.sendSettleMqtt();
      this.showExitDialog = false;
      uni.switchTab({ url: '/pages/index/index' });
    },
    async handleSettleCurrent() {
      await this.sendSettleMqtt();
      this.showExitDialog = false;
      this.showSettleMask = true;
      this.stopLeaveCountdown(); // 立即停止80S倒计时
      // 结算完成后关闭蒙层的逻辑可根据实际情况调整
      // setTimeout(() => { this.showSettleMask = false }, 3000);
    },
    async sendSettleMqtt() {
      const userInfo = this.gameDetail.userInfo || {};
      const sn = this.gameDetail.sn;
      if (!sn) {
        uni.showToast({ title: '游戏信息异常', icon: 'none' });
        return;
      }
      const pubTopic = `mqtt/${sn}/sub/02`;
      const payload = JSON.stringify({
        type: '06',
        number: 1,
        client_type: 'uniapp',
        userId: userInfo.id
      });
      this.sendMessage(pubTopic, payload);
      // 若需等待MQTT回执可在此处处理
      return new Promise(resolve => setTimeout(resolve, 800));
    },
    startLeaveCountdown() {
      console.log('startLeaveCountdown: called, leaveTimer=', this.leaveTimer, 'leaveCountdown=', this.leaveCountdown);
      this.stopLeaveCountdown(); // 先清理
      this.leaveCountdown = 80;
      this.leaveTimer = setInterval(() => {
        console.log('leaveTimer tick, leaveCountdown=', this.leaveCountdown);
        if (this.leaveCountdown > 0) {
          this.leaveCountdown--;
          console.log('leaveCountdown--, now=', this.leaveCountdown);
          if (this.leaveCountdown === 55) {
            this.showLeaveDialog = true;
            console.log('showLeaveDialog = true');
          }
        } else {
          console.log('leaveCountdown reached 0, will send type=06 and disconnect');
          this.stopLeaveCountdown();
          // 发送type=06的MQTT
          const userInfo = this.gameDetail.userInfo || {};
          const sn = this.gameDetail.sn;
          if (sn) {
            const pubTopic = `mqtt/${sn}/sub/02`;
            const payload = JSON.stringify({
              type: '06',
              number: 1,
              client_type: 'uniapp',
              userId: userInfo.id
            });
            console.log('send type=06 MQTT', pubTopic, payload);
            this.sendMessage(pubTopic, payload);
            setTimeout(() => {
              console.log('disconnectMqtt after type=06');
              this.disconnectMqtt();
            }, 300);
          } else {
            console.log('disconnectMqtt (no sn) after type=06');
            this.disconnectMqtt();
          }
          this.showLeaveDialog = false;
          this.leaveCountdown = 0;
          this.showSettleMask = true;
        }
      }, 1000);
      console.log('startLeaveCountdown: timer created', this.leaveTimer);
    },
    stopLeaveCountdown() {
      console.log('stopLeaveCountdown: called, leaveTimer=', this.leaveTimer);
      if (this.leaveTimer) {
        clearInterval(this.leaveTimer);
        this.leaveTimer = null;
        console.log('stopLeaveCountdown: timer cleared');
      }
    },
    handleContinueGame() {
      console.log('继续游戏按钮点击', Date.now());
      // 发送type=02的MQTT
      const userInfo = this.gameDetail.userInfo || {};
      const sn = this.gameDetail.sn;
      if (sn) {
        const pubTopic = `mqtt/${sn}/sub/02`;
        const payload = JSON.stringify({
          type: '02',
          number: 1,
          client_type: 'uniapp',
          userId: userInfo.id
        });
        this.sendMessage(pubTopic, payload);
      }
      this.showLeaveDialog = false;
      // 倒计时重置为80秒重新开始
      console.log('继续游戏重置倒计时');
      this.stopLeaveCountdown();
      this.startLeaveCountdown();
    },
    startIdleCountdown() {
      if (this.idleTimer) return;
      this.idleCountdown = 60;
      this.showIdleCountdown = true;
      this.idleTimer = setInterval(() => {
        if (this.idleCountdown > 0) {
          this.idleCountdown--;
        } else {
          clearInterval(this.idleTimer);
          this.idleTimer = null;
          this.showIdleCountdown = false;
          uni.switchTab({ url: '/pages/index/index' });
        }
      }, 1000);
    },
    stopIdleCountdown() {
      if (this.idleTimer) {
        clearInterval(this.idleTimer);
        this.idleTimer = null;
      }
      this.showIdleCountdown = false;
    },
    toggleFuncDialog() {
      this.showFuncDialog = !this.showFuncDialog;
    },
    closeFuncDialog() {
      this.showFuncDialog = false;
    },
    openRuleDialog() {
      this.showFuncDialog = false;
      this.showRuleDialog = true;
    },
    openServiceDialog() {
      this.showFuncDialog = false;
      this.showServiceDialog = true;
    },
    openSettingDialog() {
      this.showSettingDialog = true;
      this.showFuncDialog = false;
    },
    toggleSetting(key) {
      this.setting[key] = !this.setting[key];
    },
    sendMessage(pubTopic, payload) {
      console.log('发MQTT', pubTopic, payload);
      if (!this.mqttClient || !this.mqttClient.connected) {
        uni.showToast({ title: 'MQTT未连接', icon: 'none' })
        return
      }
      this.mqttClient.publish(pubTopic, payload, { qos: 0 }, (err) => {
        if (err) {
          console.error('消息发送失败:', err)
          uni.showToast({ title: '指令发送失败', icon: 'none' })
        }
      })
    },
  }
}
</script>

<style scoped>
.coin-game-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #1a1a2a;
  overflow: hidden;
}
.loading-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: #fff;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.loading-content {
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.loading-logo {
  width: 320rpx;
  margin: 0 auto 60rpx auto;
  display: block;
}
.loading-bar-wrap {
  width: 80vw;
  max-width: 500rpx;
  margin: 0 auto;
  position: relative;
}
.loading-text {
  font-size: 28rpx;
  color: #222;
  margin-bottom: 8rpx;
  font-weight: bold;
}
.loading-bar-bg {
  width: 100%;
  height: 12rpx;
  background: #eee;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 4rpx;
}
.loading-bar {
  height: 100%;
  background: #ffe066;
  width: 0;
  border-radius: 8rpx;
  transition: width 0.2s;
}
.loading-percent {
  position: absolute;
  right: 0;
  top: -32rpx;
  font-size: 24rpx;
  color: #222;
}
.status-box {
  position: absolute;
  top: 3vh;
  left: 3vh;
  padding: 10rpx 32rpx;
  border: 2rpx solid #00ff99;
  border-radius: 24rpx;
  background: rgba(0,255,153,0.08);
  color: #00ff99;
  font-size: 32rpx;
  font-weight: bold;
  z-index: 10;
}
.avatar-timer-box-multi {
  position: absolute;
  top: 3vh;
  right: 3vh;
  display: flex;
  align-items: center;
  gap: 12rpx;
  z-index: 10;
}
.avatar-group-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.12);
  border-radius: 32rpx;
  padding: 8rpx 24rpx 8rpx 8rpx;
  min-width: 80rpx;
  min-height: 64rpx;
  box-shadow: 0 2rpx 8rpx #0002;
}
.avatar-group-item {
  position: static;
  margin-left: -32rpx;
}
.avatar-group-item:first-child {
  margin-left: 0;
}
.avatar-img-group {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  background: #fff;
  box-shadow: 0 2rpx 8rpx #0003;
}
/* 静音按钮样式 */
.mute-button {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #666666;
  background: rgba(102,102,102,0.08);
  color: #666666;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  cursor: pointer;
  transition: background 0.2s;
  padding: 0;
}
.mute-button:active {
  background: rgba(102,102,102,0.18);
  transform: scale(0.95);
}
.mute-icon {
  color: #666666;
  font-size: 24rpx;
  line-height: 1;
  font-weight: bold;
}

.exit-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #ffd700;
  background: rgba(255,215,0,0.08);
  color: #ffd700;
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8rpx;
  cursor: pointer;
  transition: background 0.2s;
}
.exit-btn:active {
  background: rgba(255,215,0,0.18);
}
.bottom-bar {
  position: absolute;
  bottom: 60rpx;
  left: 0;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
}
.coin-count-box {
  display: flex;
  align-items: center;
  padding: 8rpx 28rpx;
  border: 2rpx solid #00cfff;
  border-radius: 24rpx;
  background: rgba(0,207,255,0.08);
  color: #00cfff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}
.coin-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}
.button-group {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.action-btn {
  width: 120rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx #4CAF5055;
  letter-spacing: 1rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.auto-btn {
  background: linear-gradient(90deg, #2196F3 0%, #1976D2 100%);
  box-shadow: 0 4rpx 16rpx #2196F355;
}
.auto-btn.auto-active {
  background: linear-gradient(90deg, #F44336 0%, #D32F2F 100%);
  box-shadow: 0 4rpx 16rpx #F4433655;
}
.wiper-btn {
  background: linear-gradient(90deg, #FF9800 0%, #F57C00 100%);
  box-shadow: 0 4rpx 16rpx #FF980055;
}
.play-btn {
  width: 200rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #ffe066 0%, #ffb347 100%);
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 4rpx 16rpx #ffb34755;
  letter-spacing: 2rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8rpx;
}
.exit-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.exit-dialog {
  width: 80vw;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx #0002;
  padding: 40rpx 24rpx 32rpx 24rpx;
  position: relative;
  text-align: center;
}
.exit-dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
}
.exit-dialog-img {
  width: 120rpx;
  margin-bottom: 24rpx;
}
.exit-dialog-msg {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 32rpx;
}
.exit-dialog-btns {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}
.exit-btn-cancel {
  flex: 1;
  background: #eee;
  color: #aaa;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin-right: 12rpx;
}
.exit-btn-confirm {
  flex: 1;
  background: linear-gradient(90deg, #a18fff 0%, #6f6bff 100%);
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin-left: 12rpx;
}
.exit-dialog-close {
  position: absolute;
  right: 24rpx;
  top: 24rpx;
  width: 60rpx;
  height: 60rpx;
  font-size: 44rpx;
  color: #fff;
  background: #555;
  border: 4rpx solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
  box-shadow: 0 2rpx 8rpx #0001;
  cursor: pointer;
  transition: background 0.2s;
  z-index: 10;
  line-height: 1;
  padding: 0;
}
.exit-dialog-close:active {
  background: #333;
}
.settle-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 100000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.settle-dialog {
  width: 80vw;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx #0002;
  padding: 40rpx 24rpx 32rpx 24rpx;
  text-align: center;
}
.settle-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 24rpx;
}
.settle-img {
  width: 120rpx;
  margin-bottom: 24rpx;
}
.settle-msg {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 0;
}
.leave-countdown-bar {
  position: absolute;
  right: 32rpx;
  bottom: 380rpx;
  background: rgba(0,0,0,0.3);
  border-radius: 32rpx;
  height: 56rpx;
  min-width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  z-index: 20;
}
.leave-countdown-num {
  color: #FFD600;
  font-size: 36rpx;
  font-weight: bold;
  letter-spacing: 2rpx;
}
.leave-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 100001;
  display: flex;
  align-items: center;
  justify-content: center;
}
.leave-dialog {
  width: 80vw;
  background: transparent;
  border-radius: 24rpx;
  box-shadow: none;
  padding: 40rpx 24rpx 32rpx 24rpx;
  text-align: center;
  position: relative;
}
.leave-dialog-transparent {
  background: transparent;
  box-shadow: none;
}
.leave-title-yellow {
  color: #FFD600;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}
.leave-count.leave-count-large {
  position: static;
  margin: 0 auto 24rpx auto;
  font-size: 64rpx;
  color: #fff;
  font-weight: bold;
  background: #ff4d4f;
  border-radius: 50%;
  width: 160rpx;
  height: 160rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: 0 2rpx 16rpx #ff4d4f55;
}
.leave-msg-yellow {
  font-size: 28rpx;
  color: #FFD600;
  margin: 32rpx 0 24rpx 0;
}
.leave-btn {
  width: 70vw;
  background: linear-gradient(90deg, #ffb347 0%, #ff4d4f 100%);
  color: #fff;
  font-size: 32rpx;
  border-radius: 40rpx;
  font-weight: bold;
}
.status-coin-box-abs {
  position: absolute;
  left: 24rpx;
  top: 220rpx;
  display: flex;
  align-items: center;
  padding: 8rpx 28rpx;
  border: 2rpx solid #FFD600;
  border-radius: 24rpx;
  background: rgba(255,214,0,0.08);
  color: #FFD600;
  font-size: 32rpx;
  font-weight: bold;
  z-index: 20;
}
.status-coin-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}
.status-coin-num {
  font-size: 32rpx;
  font-weight: bold;
}
.avatar-img-circle {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  border: 2rpx solid #fff;
  background: #fff;
  box-shadow: 0 2rpx 8rpx #0003;
}
.idle-countdown-bar {
  position: absolute;
  right: 24rpx;
  top: 220rpx;
  background: rgba(0,0,0,0.3);
  border-radius: 32rpx;
  height: 48rpx;
  min-width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24rpx;
  z-index: 20;
}
.idle-countdown-num {
  color: #FFD600;
  font-size: 32rpx;
  font-weight: bold;
  letter-spacing: 2rpx;
}
.func-btn-box {
  position: absolute;
  left: 32rpx;
  bottom: 40rpx;
  width: 80rpx;
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  z-index: 30;
}
.func-btn-icon {
  width: 80rpx;
  height: 80rpx;
  display: block;
  position: absolute;
  left: 0;
  top: 0;
}
.func-btn-text {
  position: absolute;
  left: 0;
  top: 50rpx;
  width: 80rpx;
  text-align: center;
  color: #ffd600;
  font-size: 28rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 8rpx #0006;
  pointer-events: none;
}
.func-dialog-mask {
  position: fixed;
  left: 0; right: 0; bottom: 0; top: 0;
  background: rgba(0,0,0,0.5);
  z-index: 100003;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
}
.func-dialog {
  margin-left: 24rpx;
  margin-bottom: 180rpx;
  width: 330rpx;
  max-width: 330rpx;
  background: rgba(0,0,0,0.6);
  border-radius: 32rpx;
  padding: 24rpx 0 16rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.func-row {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 0;
  width: 100%;
}
.func-item {
  width: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 8rpx 16rpx 8rpx;
}
.func-item-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}
.func-item-text {
  color: #fff;
  font-size: 18rpx;
  margin-top: 2rpx;
}
.wait-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 100002;
  display: flex;
  align-items: center;
  justify-content: center;
}
.wait-dialog {
  width: 60vw;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx #0002;
  padding: 40rpx 24rpx 32rpx 24rpx;
  text-align: center;
}
.wait-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
  margin-bottom: 24rpx;
}
.wait-msg {
  font-size: 28rpx;
  color: #666;
}
.rule-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 100010;
  display: flex;
  align-items: center;
  justify-content: center;
}
.rule-dialog {
  width: 80vw;
  max-width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx #0002;
  padding: 32rpx 24rpx 24rpx 24rpx;
  position: relative;
  text-align: left;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
}
.rule-dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  text-align: center;
}
.rule-dialog-close {
  position: absolute;
  right: 24rpx;
  top: 24rpx;
  width: 50rpx;
  height: 50rpx;
  font-size: 44rpx;
  color: #fff;
  background: #555;
  border: 4rpx solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
  box-shadow: 0 2rpx 8rpx #0001;
  cursor: pointer;
  transition: background 0.2s;
  z-index: 10;
  line-height: 1;
  padding: 0;
}
.rule-dialog-close:active {
  background: #333;
}
.rule-dialog-content {
  flex: 1;
  overflow-y: auto;
  font-size: 26rpx;
  color: #222;
  line-height: 1.7;
  max-height: 30vh;
  padding-right: 8rpx;
}
.service-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx 0 0 0;
}
.service-qr {
  width: 340rpx;
  height: 340rpx;
  margin-bottom: 32rpx;
  border-radius: 16rpx;
  background: #fff;
}
.service-tip {
  font-size: 28rpx;
  color: #222;
  margin-top: 12rpx;
  text-align: center;
}
.setting-dialog-mask {
  position: fixed; left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 100011;
  display: flex;
  align-items: center;
  justify-content: center;
}
.setting-dialog {
  width: 80vw;
  max-width: 520rpx;
  background: #fff;
  border-radius: 28rpx;
  box-shadow: 0 8rpx 32rpx #0002;
  padding: 0 0 32rpx 0;
  position: relative;
  text-align: center;
  display: flex;
  flex-direction: column;
}
.setting-dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 32rpx 0 24rpx 0;
  text-align: center;
}
.setting-dialog-close {
  position: absolute;
  right: 24rpx;
  top: 24rpx;
  width: 50rpx;
  height: 50rpx;
  font-size: 44rpx;
  color: #fff;
  background: #555;
  border: 4rpx solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
  box-shadow: 0 2rpx 8rpx #0001;
  cursor: pointer;
  transition: background 0.2s;
  z-index: 10;
  line-height: 1;
  padding: 0;
}
.setting-list {
  width: 100%;
  margin-top: 0;
}
.setting-divider {
  width: 100%;
  height: 1rpx;
  background: #eee;
  margin-bottom: 0;
}
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  height: 90rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 28rpx;
  background: transparent;
}
.setting-item:last-child {
  border-bottom: none;
}
.setting-label {
  font-weight: bold;
  color: #222;
}
.setting-switch {
  width: 100rpx;
  height: 44rpx;
  border-radius: 22rpx;
  background: #eee;
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: background 0.2s;
  padding: 0 12rpx;
  box-sizing: border-box;
  justify-content: space-between;
}
.setting-switch.on {
  background: linear-gradient(90deg, #a18fff 0%, #6f6bff 100%);
}
.switch-knob {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  left: 8rpx;
  transition: left 0.2s;
  z-index: 2;
}
.setting-switch.on .switch-knob {
  left: 56rpx;
}
.switch-text {
  font-size: 22rpx;
  font-weight: bold;
  width: 40rpx;
  text-align: center;
  z-index: 1;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.switch-on {
  color: #fff;
  left: 16rpx;
}
.switch-off {
  color: #a18fff;
  right: 16rpx;
}
.setting-switch.on .switch-on {
  color: #fff;
}
.setting-switch:not(.on) .switch-off {
  color: #a18fff;
}
.reward-dialog-mask {
  position: fixed; left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 100012;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reward-dialog {
  width: 80vw;
  max-width: 520rpx;
  background: #fff;
  border-radius: 28rpx;
  box-shadow: 0 8rpx 32rpx #0002;
  padding: 0 0 32rpx 0;
  position: relative;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.reward-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #FFD600;
  background: #2196F3;
  border-radius: 16rpx 16rpx 0 0;
  margin: 0;
  padding: 24rpx 0 16rpx 0;
  width: 100%;
  text-align: center;
  letter-spacing: 2rpx;
}
.reward-coin-img {
  width: 100rpx;
  height: 100rpx;
  margin: 32rpx 0 12rpx 0;
}
.reward-coin-num {
  font-size: 36rpx;
  color: #2196F3;
  font-weight: bold;
  margin-bottom: 24rpx;
}
.reward-dialog-close {
  position: absolute;
  left: 50%;
  bottom: -40rpx;
  transform: translateX(-50%);
  width: 50rpx;
  height: 50rpx;
  font-size: 44rpx;
  color: #fff;
  background: #555;
  border: 4rpx solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
  box-shadow: 0 2rpx 8rpx #0001;
  cursor: pointer;
  transition: background 0.2s;
  z-index: 10;
  line-height: 1;
  padding: 0;
}
</style> 