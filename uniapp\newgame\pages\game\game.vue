<template>
	<view class="container" :class="{ 'force-landscape': gameInfo.is_landscape === 1 }">
		<!-- 加载动画 -->
		<view v-if="isLoading" class="loading-mask">
			<view class="loading-content">
				<image class="loading-logo" src="/static/loading-logo.png" mode="widthFix" />
				<view class="loading-bar-wrap">
					<view class="loading-text">加载中...</view>
					<view class="loading-bar-bg">
						<view class="loading-bar" :style="{ width: progress + '%' }"></view>
					</view>
					<view class="loading-percent">{{ progress }}%</view>
				</view>
			</view>
		</view>

		<view v-if="!isLoading">
			<EasyPlayer
				v-if="currentPlayerMode === 'easyplayer'"
				:videoUrl="videoUrl"
				:autoPlay="true"
				@ready="onPlayerReady"
				@error="onPlayerError"
				@playing="onPlayerPlaying"
			/>

			<!-- HeiqiPlayer模式 -->
			<HeiqiPlayer
				ref="heiqiPlayer"
				v-if="currentPlayerMode === 'heiqiplayer'"
				:defaultSignalingUrl="gameInfo.heiqiplayer_signaling_url"
				:defaultSenderId="gameInfo.heiqiplayer_sender_id"
				:autoConnect="true"
				:showConnectionControls="false"
				:showStats="true"
        		:rotation="gameInfo.video_rotation || 0"
        		:isLandscape="gameInfo.is_landscape || 0"
				@connected="onWebRTCConnected"
				@connectionFailed="onWebRTCFailed"
				@streamReceived="onStreamReceived"
				@videoStatsUpdated="onVideoStatsUpdated"
				@switchPlayer="onSwitchPlayer"
			/>

			<!-- 动态游戏控制组件 -->
			<view class="game-ui-layer" v-if="showGameControls">
				<!-- 调试按钮和当前控制组件显示已移除 -->
				<component
					:is="currentControlComponent"
					:key="currentControlComponent"
					ref="gameControl"
					:gameInfo="gameInfo"
					:userInfo="userInfo"
					:seats="seats"
					:isVideoMuted="isVideoMuted"
					:isAuto="isAuto"
					:cachedCoin="cachedCoin"
					:gameSettings="setting"
					@toggleVideoAudio="toggleVideoAudio"
					@exitGame="exitGame"
					@startGame="startGame"
					@show-exit-dialog="onShowExitDialog"
					@show-settle-mask="onShowSettleMask"
					@show-wait-mask="onShowWaitMask"
					:showIdleCountdown="showIdleCountdown"
					:idleCountdown="idleCountdown"
					@updateIdleCountdownStyle="logAndSetIdleCountdownStyle"
					@show-func-dialog="onShowFuncDialog"
					@show-score-exchange-dialog="onShowScoreExchangeDialog"
				/>
			</view>
		</view>
		<!-- 退出弹窗 -->
		<UniversalModal
			:show="showExitDialog"
			title="退出房间"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="exit"
			@close="showExitDialog = false"
		>
			<image class="modal-image" src="@/static/coin.png" mode="aspectFit" />
			<view>是否结算并退出当前房间？</view>

			<template #footer>
				<view class="modal-buttons">
					<button class="btn-cancel" @click="handleSettleAndExit">结算并退出</button>
					<button class="btn-confirm" @click="handleSettleCurrent">结算本局</button>
				</view>
			</template>
		</UniversalModal>
		<!-- 积分兑换弹窗 -->
		<ScoreExchangeDialog
			:show="showScoreExchangeDialog"
			:userInfo="userInfo"
			:landscape="gameInfo.is_landscape === 1"
			@close="closeScoreExchangeDialog"
			@exchangeSuccess="handleExchangeSuccess"
		/>
		<!-- 结算中弹窗 -->
		<UniversalModal
			:show="showSettleMask"
			title="结算中"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="settle"
			@close="showSettleMask = false"
		>
			<image class="modal-image" src="/static/settle-loading.png" mode="aspectFit" />
			<view>结算时间稍长，请耐心等待！</view>
		</UniversalModal>

		<!-- 请等待弹窗 -->
		<UniversalModal
			:show="showWaitMask"
			title="请等待"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="wait"
			@close="showWaitMask = false"
		>
			<view>请稍候...</view>
		</UniversalModal>

		<!-- 错误提示弹窗 -->
		<UniversalModal
			:show="showErrorDialog"
			:title="errorDialogTitle"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="error"
			@close="handleErrorDialogClose"
		>
			<view>{{ errorDialogMsg }}</view>
			<template #footer>
				<button class="btn-single" @click="handleErrorDialogClose">确定</button>
			</template>
		</UniversalModal>

		<!-- 奖励弹窗 -->
		<UniversalModal
			:show="showRewardDialog"
			title="恭喜获得"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="reward"
			@close="showRewardDialog = false"
		>
			<image class="modal-image" src="/static/coin.png" mode="aspectFit" />
			<view class="number">{{ rewardCoin }}</view>
		</UniversalModal>
		<!-- 右上角头像容器 -->
		<!-- 删除 game.vue 里的头像容器，只保留倒计时条 -->
		<view v-if="showIdleCountdown" class="idle-countdown-bar">
			<text class="idle-countdown-num">{{ idleCountdown }}S</text>
		</view>

		<!-- 功能弹窗 -->
		<UniversalModal
			:show="showFuncDialog"
			title="功能菜单"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="function"
			@close="closeFuncDialog"
		>
			<view class="func-grid">
				<view class="func-item" @click.stop="onFeedback">
					<image class="modal-image" src="/static/func/feedback.png" />
					<view>设备反馈</view>
				</view>
				<view class="func-item" @click.stop="openSettingDialog">
					<image class="modal-image" src="/static/func/setting.png" />
					<view>设置</view>
				</view>
				<view class="func-item" @click.stop="openServiceDialog">
					<image class="modal-image" src="/static/func/service.png" />
					<view>联系客服</view>
				</view>
				<view class="func-item" @click.stop="openRuleDialog">
					<image class="modal-image" src="/static/func/rule.png" />
					<view>玩法说明</view>
				</view>
			</view>
		</UniversalModal>

		<!-- 设置弹窗 -->
		<UniversalModal
			:show="showSettingDialog"
			title="设置"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="setting"
			@close="closeSettingDialog"
		>
			<view class="setting-item" v-for="(label, key) in settingLabels" :key="key">
				<view>{{ label }}</view>
				<view class="setting-switch" :class="{ on: setting[key] }" @click="toggleSetting(key)">
					<view class="switch-knob"></view>
				</view>
			</view>
		</UniversalModal>

		<!-- 玩法说明弹窗 -->
		<UniversalModal
			:show="showRuleDialog"
			title="玩法说明"
			:landscape="gameInfo.is_landscape === 1"
			size="large"
			type="rule"
			@close="closeRuleDialog"
		>
			<view class="rule-dialog-content" v-html="gameInfo.description"></view>
		</UniversalModal>

		<!-- 联系客服弹窗 -->
		<UniversalModal
			:show="showServiceDialog"
			title="联系客服"
			:landscape="gameInfo.is_landscape === 1"
			size="medium"
			type="service"
			@close="closeServiceDialog"
		>
			<image class="service-qr" :src="gameInfo.service_qr || '/static/service_qr.png'" mode="widthFix" />
			<view>请扫描二维码添加平台客服</view>
		</UniversalModal>
	</view>



	
</template>

<script>
	import {
		GameWebSocket
	} from '@/utils/websocket.js';
	import request from '@/utils/request'
	import config from '@/config.js'
	import EasyPlayer from '@/components/EasyPlayer.vue'
	import HeiqiPlayer from '@/components/HeiqiPlayer.vue'
	import FishControl from '@/components/GameControls/FishControl.vue'
	import OnekeyControl from '@/components/GameControls/OnekeyControl.vue'
	import UniversalModal from '@/components/UniversalModal.vue'
	import ScoreExchangeDialog from '@/components/ScoreExchangeDialog.vue'
	import PaiPaiLeControl from '@/components/GameControls/PaiPaiLeControl.vue'
	import ClawControl from '@/components/GameControls/ClawControl.vue'
	import BaseDialog from '@/components/BaseDialog.vue';

	export default {
		components: {
			EasyPlayer,
			HeiqiPlayer,
			FishControl,
			OnekeyControl,
			UniversalModal,
			ScoreExchangeDialog,
			PaiPaiLeControl,
			ClawControl,
			BaseDialog
		},
		props: {
			gameId: {
				type: [String, Number],
				default: null
			},
			is_landscape: {
				type: [String, Number],
				default: 0
			}
		},

		data() {
			const data = {
				isLoading: true, // 控制加载动画显示
				playerInfo: null, // 播放器实例
				cachedCoin: 0, // 缓存金币（用于传递给子组件）
				gameId: null, // 游戏ID（从页面参数获取）
				gameSn: null, // 游戏序列号
				videoUrl: '', // 视频流地址
				gameInfo: {}, // 游戏信息
				seats: [], // 座位信息
				userInfo: {}, // 用户信息
				defaultAvatar: '/static/default_avatar.png', // 默认头像
				ws: null, // WebSocket实例
				hasConnected: false, // WebSocket连接状态
				isDisconnected: false, // 是否已断开连接
				isPageHidden: false, // 页面是否隐藏
				hideTimer: null, // 页面隐藏延迟断开定时器
				hideStartTime: 0, // 页面隐藏开始时间
				number: 0, // 玩家编号
				isAuto: false, // 自动模式状态
				firePressTimer: null, // 发射按钮长按定时器
				fireInterval: null, // 发射按钮间隔定时器
				isLongPress: false, // 是否长按
				leftPressTimer: null, // 左方向键长按定时器
				leftInterval: null, // 左方向键间隔定时器
				rightPressTimer: null, // 右方向键长按定时器
				rightInterval: null, // 右方向键间隔定时器
				isLeftLongPress: false, // 左方向键是否长按
				isRightLongPress: false, // 右方向键是否长按
				progress: 0,
				progressTimer: null,
				// 发炮时间相关
				lastFireTime: 0, // 最后发炮时间
				exitCountdown: 0, // 退出倒数时间
				isExitWaiting: false, // 是否正在等待退出
				exitCountdownTimer: null, // 退出倒数定时器
				wasAutoMode: false, // 退出时是否处于自动模式
				// 防抖相关
				lastClickTime: 0, // 最后点击时间
				clickDebounceTime: 300, // 防抖时间间隔(毫秒)

				// 音频控制相关
				isVideoMuted: true, // 视频静音状态，默认为静音
				showGameControls: false, // 控制游戏按钮显示，需要收到register_back消息后才显示
				showExitDialog: false,
				showSettleMask: false,
				showWaitMask: false,
				showErrorDialog: false,
				errorDialogMsg: '',
				errorDialogTitle: '错误提示',
				isGameTimeout: false, // 标识是否是游戏超时
				isGameEnd: false, // 标识是否是游戏结束
				showRewardDialog: false,
				rewardCoin: 0,
				showIdleCountdown: false,
				idleCountdown: 60,
				idleTimer: null,
				showFuncDialog: false,
				showRuleDialog: false,
				showServiceDialog: false,
				showSettingDialog: false,
				setting: {
					fullscreen: true,
					gameSound: false,
					effectSound: false,
					winPopup: false
				},
				// 投币弹窗相关
				showCoinModal: false,
				coinButtons: [],
				// 积分兑换弹窗
				showScoreExchangeDialog: false,
				coinButtons: [],
				// 横屏模式滚动条控制
				originalBodyOverflow: undefined,
				originalHtmlOverflow: undefined
			};
			// console.log('[game.vue] gameInfo:', data.gameInfo)
			return data;
		},
		// 页面准备就绪
		onReady() {
			// 页面准备就绪，组件会自动初始化
		},
		// 页面隐藏时
		onHide() {
			console.log('页面隐藏，30秒后断开WebRTC连接')
			this.isPageHidden = true
			this.hideStartTime = Date.now()

			// 设置30秒延迟断开定时器
			this.hideTimer = setTimeout(() => {
				if (this.isPageHidden) {
					console.log('页面隐藏超过30秒，断开WebRTC连接')
					this.disconnectWebRTC()
				}
			}, 30000) // 30秒
		},

		// 页面显示时
		onShow() {
			console.log('页面显示，检查是否需要重新连接')

			// 清除隐藏定时器
			if (this.hideTimer) {
				clearTimeout(this.hideTimer)
				this.hideTimer = null
				console.log('页面恢复显示，取消延迟断开')
			}

			// 如果之前因为页面隐藏而断开，现在重新连接
			if (this.isPageHidden && this.gameInfo && this.gameInfo.sn) {
				const hideTime = Date.now() - this.hideStartTime
				console.log(`页面恢复显示，隐藏时长: ${Math.round(hideTime/1000)}秒`)

				// 如果已经断开连接，则重新连接
				if (this.isDisconnected) {
					console.log('页面恢复显示，自动重新连接WebRTC')
					this.reconnectWebRTC()
				}
			}

			this.isPageHidden = false
		},
		// 组件挂载时
		mounted() {
			// 页面挂载完成
			this.setupFullscreenDetection();
		},
		// 监听游戏状态变化
		watch: {
			'gameInfo.is_full': {

				handler(newVal) {
					// debugger
					if (newVal !== 2) {
						this.startIdleCountdown()
					} else {
						this.stopIdleCountdown()
					}
				},
				immediate: true
			},
			'gameInfo.is_landscape': {
				handler(newVal) {
					// 当横屏模式变化时，控制滚动条
					if (newVal === 1) {
						this.disableLandscapeScroll()
					} else {
						this.enableLandscapeScroll()
					}
				},
				immediate: true
			}
		},
		// 计算属性
		computed: {
			// 根据gameInfo.playermode确定当前播放器模式
			currentPlayerMode() {
				// 如果gameInfo中有playermode字段，使用它；否则默认使用heiqiplayer
				if (this.gameInfo && this.gameInfo.playermode) {
					return this.gameInfo.playermode
				}
				return 'heiqiplayer' // 默认播放器
			},

			// 游戏状态文本
			gameStatusText() {
				if (!this.gameInfo) return '加载中...'
				switch (this.gameInfo.is_full) {
					case 0:
						return '当前空闲'
					case 1:
						return '座位已满'
					case 2:
						return this.userInfo.nickname || '正在游戏中'
					default:
						return '等待启动中...'
				}
			},

			// 设置项标签
			settingLabels() {
				return {
					fullscreen: '全屏幕画面',
					gameSound: '游戏声音'
				}
			},

			// 根据control_type动态选择控制组件
			currentControlComponent() {
				const component = (() => {
					if (!this.gameInfo || !this.gameInfo.control_type) {
						return 'OnekeyControl' // 默认使用单键控制
					}

					switch (this.gameInfo.control_type) {
						case 'fish':
						case 'fishing':
						case 'shooting':	
						case 'gun':
						case 'arcade':
							return 'FishControl'
						case 'paipai':
						case 'paipai-le':
						case 'paipai_le':
						case 'paipaile':
							return 'PaiPaiLeControl'
						case 'claw':
						case 'claw-machine':
						case 'claw_machine':
						case 'clawmachine':
							return 'ClawControl'
						case 'onekey':
						case 'simple':
						case 'basic':
						default:
							return 'OnekeyControl'
					}
				})()

				console.log('当前控制组件:', component, '游戏类型:', this.gameInfo?.control_type)
				return component
			},
			// 玩家信息映射
			playersMap() {
				const map = {}
				if (this.gameInfo.players?.length) {
					this.gameInfo.players.forEach(player => {
						map[player.seat_id] = player
					})
				}
				return map
			},
			dialogMaskStyle() {
				let base =
					'position: fixed; left:0;top:0;right:0;bottom:0;z-index:999999;display:flex;align-items:center;justify-content:center;background:rgba(0,0,0,0.5);';
				if (this.gameInfo && this.gameInfo.is_landscape === 1) {
					base += 'transform: rotate(90deg); transform-origin: center;';
				}
				return base;
			},

			// 积分兑换相关计算属性
			userScore() {
				return this.userInfo.score || 0;
			},

		},
		// 页面加载时
		onLoad(options) {
			// 优先用参数初始化横竖屏，保证初始渲染方向正确
			if (options.is_landscape !== undefined) {
				this.gameInfo.is_landscape = Number(options.is_landscape)
			} else if (this.is_landscape !== undefined) {
				this.gameInfo.is_landscape = Number(this.is_landscape)
			}

			// 延迟申请全屏，确保页面完全加载（仅移动端）
			setTimeout(() => {
				this.requestFullscreenIfMobile()
			}, 1000)

			// 强制显示加载动画至少2秒
			const startTime = Date.now()
			const minLoadingTime = 2000 // 最小加载时间2秒

			// 从页面参数或props获取 gameId
			this.gameId = options.gameId || this.gameId

			// 加载动画进度条递增逻辑
			this.progress = 0
			this.progressTimer = setInterval(() => {
				if (this.progress < 100) {
					this.progress += 5
					if (this.progress > 100) this.progress = 100
				} else {
					clearInterval(this.progressTimer)
					this.progressTimer = null
					this.isLoading = false
				}
			}, 100)

			// 获取游戏数据
			this.fetchGameData().then(() => {
				const elapsedTime = Date.now() - startTime
				const remainingTime = Math.max(0, minLoadingTime - elapsedTime)

				// 如果已经过了最小加载时间，直接关闭加载动画
				if (remainingTime <= 0) {
					this.isLoading = false
					return
				}

				// 否则等待剩余时间
				setTimeout(() => {
					this.isLoading = false
				}, remainingTime)

				this.connectWebSocket()
			}).catch(() => {
				uni.showToast({
					title: '获取游戏数据失败',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			})
		},
		// 页面卸载时
		onUnload() {
			console.log('页面卸载，断开所有连接')
			// 退出全屏模式
			this.exitFullscreen()
			// 清理全屏检测和恢复页面滚动
			this.cleanupFullscreenDetection()
			this.enablePageScroll()
			// 恢复横屏模式的滚动条
			this.enableLandscapeScroll()
			this.disconnectWebRTC() // 这会同时断开WebRTC和信令连接
			this.clearCountdown()
			this.disconnectWebSocket() // 断开游戏WebSocket连接
			clearTimeout(this.firePressTimer)
			clearInterval(this.fireInterval)
			clearTimeout(this.leftPressTimer)
			clearInterval(this.leftInterval)
			clearTimeout(this.rightPressTimer)
			clearInterval(this.rightInterval)
			// 清除加载动画进度条定时器
			if (this.progressTimer) {
				clearInterval(this.progressTimer)
				this.progressTimer = null
			}
			this.stopIdleCountdown();
		},
		// 组件销毁前
		beforeDestroy() {
			console.log('组件销毁前，断开所有连接')
			this.disconnectWebRTC() // 这会同时断开WebRTC和信令连接
			this.disconnectWebSocket() // 断开游戏WebSocket连接
			// 组件会自动清理播放器资源
		},
		methods: {
			logAndSetIdleCountdownStyle(style) {
				this.idleCountdownStyle = style;
			},
			// 防抖处理
			isClickDebounced() {
				const currentTime = Date.now()
				if (currentTime - this.lastClickTime < this.clickDebounceTime) {
					return true // 在防抖时间内，忽略点击
				}
				this.lastClickTime = currentTime
				return false
			},

			// 切换播放器模式
			switchPlayerMode() {
				const currentMode = this.currentPlayerMode
				const targetMode = currentMode === 'easyplayer' ? 'heiqiplayer' : 'easyplayer'
				const currentName = currentMode === 'easyplayer' ? 'EasyPlayer' : 'HeiqiPlayer'
				const targetName = targetMode === 'easyplayer' ? 'EasyPlayer' : 'HeiqiPlayer'

				uni.showModal({
					title: '切换播放器',
					content: `当前使用${currentName}播放器，是否切换到${targetName}播放器？`,
					success: (res) => {
						if (res.confirm) {
							// 更新gameInfo中的playermode
							this.gameInfo.playermode = targetMode

							uni.showToast({
								title: `已切换到${targetName}播放器`,
								icon: 'success'
							})
						}
					}
				})
			},

			// EasyPlayer事件处理
			onPlayerReady(player) {
				console.log('EasyPlayer准备就绪', player)
				this.playerInfo = player
			},

			onPlayerError(error) {
				console.error('EasyPlayer错误:', error)
				uni.showToast({
					title: 'EasyPlayer播放错误',
					icon: 'none'
				})
			},

			onPlayerPlaying(url) {
				console.log('EasyPlayer开始播放:', url)
			},

			// HeiqiPlayer事件处理
			onWebRTCConnected() {
				console.log('HeiqiPlayer连接成功')
			},

			onWebRTCFailed(error) {
				console.log('HeiqiPlayer连接失败:', error)
				uni.showToast({
					title: `HeiqiPlayer连接失败: ${error}`,
					icon: 'none'
				})
			},

			onStreamReceived(stream) {
				console.log('收到HeiqiPlayer视频流', stream)
			},

			// 视频统计信息更新
			onVideoStatsUpdated(stats) {
				if (!stats) {
					console.warn('视频统计信息为空')
					return
				}
				// console.log('视频统计信息更新:', {
				// 	fps: stats.fps,
				// 	bitrate: stats.bitrate,
				// 	resolution: stats.resolution,
				// 	packetsLost: stats.packetsLost,
				// 	packetsReceived: stats.packetsReceived
				// })
			},

			// 切换播放器
			onSwitchPlayer(targetMode) {
				console.log('切换播放器到:', targetMode)

				// 更新游戏信息中的播放器模式
				if (this.gameInfo) {
					this.gameInfo.playermode = targetMode
				}

				uni.showToast({
					title: `已切换到${targetMode === 'easyplayer' ? 'EasyPlayer' : 'HeiqiPlayer'}`,
					icon: 'success'
				})
			},
			// 处理发射按钮按下
			handleFireStart() {

				this.isLongPress = false
				// 记录发炮时间
				this.lastFireTime = Date.now()
				this.firePressTimer = setTimeout(() => {
					this.isLongPress = true
					this.handleDirection(15)
					this.fireInterval = setInterval(() => this.handleDirection(15), 150)
				}, 150)
			},
			// 处理发射按钮释放
			handleFireEnd() {
				clearTimeout(this.firePressTimer)
				clearInterval(this.fireInterval)
				if (!this.isLongPress) {
					this.handleDirection(15)
				}
				// 记录发炮时间
				this.lastFireTime = Date.now()
				this.handleDirection(17)
				this.isLongPress = false
			},
			// 切换自动模式
			toggleAuto() {
				// 防抖处理
				if (this.isClickDebounced()) {
					console.log('自动模式按钮防抖，忽略重复点击')
					return
				}

				this.isAuto = !this.isAuto
				this.handleDirection(this.isAuto ? 18 : 17)
			},

			// 连接WebSocket
			connectWebSocket() {
				if (!this.ws) {
					//debugger
					this.ws = new GameWebSocket(config.WebSocket)
					this.setupWebSocketEvents()
					// 设置全局WebSocket引用，供子组件使用
					window.gameWebSocket = this.ws
				}
				this.ws.connect(this.gameSn)
				this.hasConnected = true
			},
			// 断开WebRTC连接和信令连接
			disconnectWebRTC() {
				// 避免重复调用
				if (this.isDisconnected) {
					console.log('已经断开连接，跳过重复调用')
					return
				}

				console.log('断开WebRTC连接，停止视频流传输')
				this.isDisconnected = true

				// 断开WebRTC连接（这会同时断开信令连接）
				if (this.$refs.heiqiPlayer) {
					this.$refs.heiqiPlayer.disconnectWebRTC()
				}

				console.log('WebRTC连接和信令连接已断开，流量传输已停止')
			},

			// 重新连接WebRTC
			reconnectWebRTC() {
				console.log('开始重新连接WebRTC')

				// 重置断开状态
				this.isDisconnected = false

				// 重新连接WebRTC
				if (this.$refs.heiqiPlayer) {
					this.$refs.heiqiPlayer.reconnectWebRTC()
				} else {
					console.log('HeiqiPlayer组件未找到，无法重新连接')
				}
			},

			// 断开WebSocket连接
			disconnectWebSocket() {
				if (this.ws) {
					this.ws.disconnect()
					this.ws = null
					this.hasConnected = false
				}
			},
			// 设置WebSocket事件监听
			setupWebSocketEvents() {
				this.ws.on('onMessage', data => {
					// 检查是否有错误状态
					if (data.status === 'fail' && data.message) {
						// 显示错误弹窗
						this.errorDialogMsg = data.message;
						this.showErrorDialog = true;
						this.showSettleMask = false;
						this.showWaitMask = false;
						return
					}

					// 发送全局事件给子组件
					console.log('game.vue: 发送事件总线消息', data.type)
					uni.$emit('websocket-message', data)

					// 处理正常消息
					if (data.type === 'update_game') {
						this.fetchGameData().then(() => {
							this.showWaitMask = false;
						});
					} else if (data.type === 'startGame_back') {
						// 处理游戏开始响应
						if (data.status === 'success' || data.code === 200) {
							console.log('游戏开始成功')
						} else {
							console.error('游戏开始失败:', data.message || data.msg)
							uni.showToast({
								title: data.message || data.msg || '游戏开始失败',
								icon: 'none'
							})
							this.showWaitMask = false;
						}
					} else if (data.type === 'endGame_back') {
						// 处理游戏结束响应
						if (data.status === 'success' || data.code === 200) {
							// 获取金币信息
							const wonCoins = data.data?.won_coins || 0;
							const usedCoins = data.data?.used_coins || 0;

							// 显示游戏结束成功提示，包含赢得金币信息
							let content = '游戏已成功结束！';
							if (wonCoins > 0) {
								content += `\n赢得金币：${wonCoins}`;
							}
							if (usedCoins > 0) {
								content += `\n已使用金币：${usedCoins}`;
							}
							
							this.showSettleMask = false;
							// 使用自定义弹窗显示游戏结束信息
							this.isGameEnd = true;
							this.errorDialogTitle = '游戏结束';
							this.errorDialogMsg = content;
							this.showErrorDialog = true;
						} else {
							console.error('游戏结束失败:', data.message || data.msg)
							uni.showToast({
								title: data.message || data.msg || '游戏结束失败',
								icon: 'none'
							})
							this.showSettleMask=false;
						}
					} else if (data.type === 'coin_update') {
						// 处理金币更新消息
						this.handleCoinUpdate(data)
					} else if (data.type === 'queryGameState_back') {
						// 处理查询游戏状态响应
						this.handleQueryGameStateResponse(data)
					} else if (data.type === 'message_received') {
						// 处理消息接收确认
						this.handleMessageReceived(data)
					} else if (data.type === 'autoExit') {
						// 处理自动退出消息
						this.handleAutoExit(data)
					} else if (data.type === 'register_back') {
						// 处理注册响应消息
						this.handleRegisterBack(data)
					} else if (data.type === 'GameStatus') {
						// 处理游戏状态消息
						this.handleGameStatus(data)
					}
					// if (data.type === 'endGame_back' && data.status === 'success') {
					//     this.Exit()
					// }
					if (
						data.coin != null && !isNaN(data.coin) && Number(data.coin) >= 0 &&
						data.user_id && this.userInfo && String(data.user_id) === String(this.userInfo.id)
					) {
						this.showSettleMask = false;
						this.rewardCoin = Number(data.coin);
						this.showRewardDialog = true;
					}
				})
			},
			// 开始倒计时
			startCountdown() {
				this.clearCountdown()
				this.countdownTimer = setInterval(() => {
					if (this.countdown <= 0) {
						this.forceExit()
						return
					}
					this.countdown--
				}, 1000)
			},
			// 清除倒计时
			clearCountdown() {
				if (this.countdownTimer) {
					clearInterval(this.countdownTimer)
					this.countdownTimer = null
				}
			},
			// 正常退出
			Exit() {
				console.log('正常退出，断开所有连接')
				// 退出全屏模式
				this.exitFullscreen()
				// 清理全屏检测和恢复页面滚动
				this.cleanupFullscreenDetection()
				this.enablePageScroll()
				// 恢复横屏模式的滚动条
				this.enableLandscapeScroll()
				this.disconnectWebRTC() // 这会同时断开WebRTC和信令连接
				this.disconnectWebSocket() // 断开游戏WebSocket连接
				// 检查上一页是否是当前页面
				const pages = getCurrentPages()
				if (pages.length <= 1 || pages[pages.length - 2].route === pages[pages.length - 1].route) {
					//如果没有上一页或上一页就是当前页，则跳转到首页
					uni.switchTab({
						url: '/pages/index/index'
					})
				} else {
					//正常返回上一页
					uni.navigateBack()
				}
			},
			// 强制退出
			forceExit() {
				this.clearCountdown()
				uni.navigateBack()
			},
			// 获取游戏数据
			async fetchGameData() {
				try {
					const res = await request({
						url: '/api/game/detail',
						data: {
							gameId: this.gameId
						}
					})

					this.gameInfo = res.data
					this.seats = res.data.seats || []
					this.userInfo = res.data.userInfo || {}
					this.videoUrl = res.data.video_url
					this.gameSn = this.gameInfo.sn

					// 检查游戏状态
					this.checkGameStatus()

					// 调试信息：显示播放器配置
				} catch (error) {
					console.error('获取游戏数据失败:', error)
					uni.showToast({
						title: '获取游戏数据失败',
						icon: 'none'
					})
				}
			},

			// 检查游戏状态
			checkGameStatus() {
				if (this.gameInfo && this.gameInfo.status !== 1) {
					let statusText = '';
					switch(this.gameInfo.status) {
						case 0:
							statusText = '下线';
							break;
						case 2:
							statusText = '维护中';
							break;
						default:
							statusText = '不可用';
					}

					console.log(`游戏状态检查: 游戏${statusText}，状态码=${this.gameInfo.status}`);

					uni.showModal({
						title: '游戏提示',
						content: `当前游戏处于${statusText}状态，无法进入游戏`,
						showCancel: false,
						confirmText: '返回',
						success: (res) => {
							if (res.confirm) {
								// 返回上一页或首页
								const pages = getCurrentPages();
								if (pages.length <= 1) {
									uni.switchTab({
										url: '/pages/index/index'
									});
								} else {
									uni.navigateBack();
								}
							}
						}
					});
					return false;
				}
				return true;
			},
			// 获取玩家头像
			getPlayerAvatar(seat) {
				return this.playersMap[seat.id]?.avatar || this.defaultAvatar
			},
			// 获取玩家名称
			getPlayerName(seat) {
				return this.playersMap[seat.id]?.nickname || '空闲'
			},
			// 开始游戏
			async startGame(seat) {
				// 防抖处理
				if (this.isClickDebounced()) {
					console.log('开始游戏按钮防抖，忽略重复点击')
					return
				}

				try {
					// const currentMoney = this.userInfo.money || 0
					// if (currentMoney <= 0) {
					// 	uni.showToast({
					// 		title: '金币不足，请先充值',
					// 		icon: 'none',
					// 		duration: 2000
					// 	})
					// 	setTimeout(() => {
					// 		uni.switchTab({ url: '/pages/pay/index' })
					// 	}, 1500)
					// 	return
					// }
					if (this.ws?.readyState === 1) {
						this.ws.send({
							type: 'startGame',
							number: seat.number,
							userId: this.userInfo.id
						})
					}
				} catch (error) {
					// 请求失败时不立即断开WebSocket，让用户有机会重试
					console.error('请求失败:', error)
					uni.showToast({
						title: '网络请求失败，请重试',
						icon: 'none'
					})
				}
			},

			// 切换视频音频
			toggleVideoAudio() {
				// 防抖处理
				if (this.isClickDebounced()) {
					console.log('静音按钮防抖，忽略重复点击')
					return
				}

				this.isVideoMuted = !this.isVideoMuted
				// 同步更新设置中的gameSound状态
				this.setting.gameSound = !this.isVideoMuted

				console.log('toggleVideoAudio - isVideoMuted:', this.isVideoMuted)
				console.log('toggleVideoAudio - setting.gameSound:', this.setting.gameSound)

				// 调用HeiqiPlayer的toggleAudio方法
				if (this.$refs.heiqiPlayer) {
					this.$refs.heiqiPlayer.toggleAudio()
				}

				// 显示Toast提示
				uni.showToast({
					title: this.isVideoMuted ? '已静音' : '已开启声音',
					icon: 'none',
					duration: 1000
				})
			},

			// 处理金币更新消息
			handleCoinUpdate(data) {
				console.log('game.vue.handleCoinUpdate 收到消息:', data)
				console.log('gameControl ref 存在:', !!this.$refs.gameControl)
				console.log('gameControl.handleCoinUpdate 方法存在:', !!this.$refs.gameControl?.handleCoinUpdate)

				if (data.type === 'coin_update' && data.code === 200) {
					// 更新用户金币信息和缓存金币
					if (data.cachedCoin !== undefined) {
						this.userInfo.money = data.cachedCoin
						this.cachedCoin = data.cachedCoin
						console.log('game.vue 金币更新:', data.cachedCoin, '已使用:', data.coinUsed)
					}

					// 通知子组件处理金币更新
					this.$nextTick(() => {
						if (this.$refs.gameControl && this.$refs.gameControl.handleCoinUpdate) {
							console.log('准备调用子组件 handleCoinUpdate')
							this.$refs.gameControl.handleCoinUpdate(data)
						} else {
							console.warn('子组件或方法不存在，无法调用 handleCoinUpdate')
							console.log('gameControl ref:', this.$refs.gameControl)
							console.log('所有 refs:', Object.keys(this.$refs))
						}
					})
				} else {
					console.warn('金币更新条件不满足:', data.type, data.code)
				}
			},

			// 处理查询游戏状态响应
			handleQueryGameStateResponse(data) {
				console.log('handleQueryGameStateResponse 被调用:', data)

				if (data.type === 'queryGameState_back' && data.code === 200) {
					console.log('查询游戏状态响应:', data.data)

					// 如果有游戏状态数据，就更新缓存金币
					if (data.data) {
						console.log('游戏状态数据:', {
							cachedCoin: data.data.cachedCoin,
							gameStatus: data.data.gameStatus,
							gameLogId: data.data.gameLogId
						})

						// 更新缓存金币
						if (data.data.cachedCoin !== undefined) {
							this.cachedCoin = data.data.cachedCoin
							console.log('更新缓存金币:', data.data.cachedCoin)
						}

						// 通知子组件更新金币显示（不管金币是否为0）
						this.$nextTick(() => {
							if (this.$refs.gameControl && this.$refs.gameControl.handleGameStateUpdate) {
								console.log('调用子组件 handleGameStateUpdate')
								this.$refs.gameControl.handleGameStateUpdate(data.data)
							} else {
								console.warn('子组件或 handleGameStateUpdate 方法不存在')
								console.log('gameControl ref:', this.$refs.gameControl)
								console.log('所有 refs:', Object.keys(this.$refs))

								// 如果方法不存在，直接调用 handleCoinUpdate
								if (this.$refs.gameControl && this.$refs.gameControl.handleCoinUpdate) {
									console.log('尝试调用 handleCoinUpdate 作为替代')
									const coinUpdateData = {
										type: 'coin_update',
										code: 200,
										cachedCoin: data.data.cachedCoin,
										coinUsed: data.data.coinUsed
									}
									this.$refs.gameControl.handleCoinUpdate(coinUpdateData)
								}
							}
						})

						// 更新游戏状态显示
						if (data.data.gameStatus === 'playing') {
							console.log('游戏进行中，游戏记录ID:', data.data.gameLogId, '缓存金币:', data.data.cachedCoin)
						} else {
							console.log('游戏空闲状态，缓存金币:', data.data.cachedCoin)
						}
					} else {
						console.warn('查询游戏状态响应中没有 data 字段')
					}
				} else if (data.code !== 200) {
					console.warn('查询游戏状态失败:', data.message || '未知错误')
				}
			},

			// 处理消息接收确认
			handleMessageReceived(data) {
				console.log('game.vue收到消息接收确认:', data)
				if (data.type === 'message_received' && data.code === 200) {
					console.log('准备通知子组件显示消息冒泡')
					// 通知子组件显示消息冒泡
					if (this.$refs.gameControl && this.$refs.gameControl.handleMessageReceived) {
						this.$refs.gameControl.handleMessageReceived(data)
					} else {
						console.warn('子组件或方法不存在')
					}
				}
			},

			// 退出游戏
			async exitGame() {
				// 只做页面跳转，不做任何退出判断和弹窗
				// 退出全屏模式
				this.exitFullscreen()
				// 清理全屏检测和恢复页面滚动
				this.cleanupFullscreenDetection()
				this.enablePageScroll()
				// 恢复横屏模式的滚动条
				this.enableLandscapeScroll()
				this.disconnectWebSocket()
				const pages = getCurrentPages()
				if (pages.length <= 1 || pages[pages.length - 2].route === pages[pages.length - 1].route) {
					uni.switchTab({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack()
				}
			},
			// 处理方向控制
			handleDirection(direction) {
				console.log('按键：', direction)
				// 对于加注和切换武器按钮添加防抖处理
				if ((direction === 16 || direction === 14) && this.isClickDebounced()) {
					console.log(`方向控制${direction}防抖，忽略重复点击`)
					return
				}
				// 如果是发炮指令，记录发炮时间
				if (direction === 15) {
					this.lastFireTime = Date.now()
				}

				if (this.ws?.readyState === 1) {
					this.ws.send({
						type: 'command',
						number: this.gameInfo.number,
						status: direction,
					})
				}
			},
			// 处理左方向键按下
			handleLeftStart() {
				this.isLeftLongPress = false
				this.handleDirection(12)
				this.leftPressTimer = setTimeout(() => {
					this.isLeftLongPress = true
					this.leftInterval = setInterval(() => this.handleDirection(12), 150)
				}, 150)
			},
			// 处理左方向键释放
			handleLeftEnd() {
				clearTimeout(this.leftPressTimer)
				clearInterval(this.leftInterval)
				this.isLeftLongPress = false
			},
			// 处理右方向键按下
			handleRightStart() {
				this.isRightLongPress = false
				this.handleDirection(13)
				this.rightPressTimer = setTimeout(() => {
					this.isRightLongPress = true
					this.rightInterval = setInterval(() => this.handleDirection(13), 150)
				}, 150)
			},
			// 处理右方向键释放
			handleRightEnd() {
				clearTimeout(this.rightPressTimer)
				clearInterval(this.rightInterval)
				this.isRightLongPress = false
			},
			startProgress() {
				// 模拟加载进度
				const timer = setInterval(() => {
					this.progress += 20
					if (this.progress >= 100) {
						clearInterval(timer)
					}
				}, 200)
			},
			onShowSettleMask(val) {
				this.showSettleMask = !!val;
			},
			onShowWaitMask(val) {
				this.showWaitMask = !!val;
			},
			onShowExitDialog() {
				this.showExitDialog = true;
			},
			handleSettleAndExit() {
				// 1. 发送 websocket endGame 指令
				if (this.ws && this.ws.readyState === 1) {
					this.ws.send({
						type: 'endGame',
						gameLogId: this.gameInfo.gameLogId
					});
				}
				// 2. 不立即关闭弹窗，等待一会再跳转
				setTimeout(() => {
					this.exitGame();
				}, 800);
			},
			handleSettleCurrent() {
				// 1. 发送 websocket endGame 指令
				if (this.ws && this.ws.readyState === 1) {
					this.ws.send({
						type: 'endGame',
						gameLogId: this.gameInfo.gameLogId
					});
				}
				// 2. 弹出结算中弹窗
				this.showSettleMask = true;
				// 3. 关闭退出弹窗
				this.showExitDialog = false;
			},
			startIdleCountdown() {
				if (this.idleTimer) return;
				this.idleCountdown = 60;
				this.showIdleCountdown = true;
				this.idleTimer = setInterval(() => {
					if (this.idleCountdown > 0) {
						this.idleCountdown--;
					} else {
						clearInterval(this.idleTimer);
						this.idleTimer = null;
						this.showIdleCountdown = false;
						this.exitGame();
					}
				}, 1000);
			},
			stopIdleCountdown() {
				if (this.idleTimer) {
					clearInterval(this.idleTimer);
					this.idleTimer = null;
				}
				this.showIdleCountdown = false;
			},
			toggleFuncDialog() {
				this.showFuncDialog = !this.showFuncDialog;
			},
			closeFuncDialog() {
				this.showFuncDialog = false;
			},
			onFeedback() {
				this.closeFuncDialog();
				// TODO: 设备反馈逻辑
				uni.showToast({
					title: '设备反馈功能开发中',
					icon: 'none'
				});
			},
			onShowFuncDialog() {
				this.showFuncDialog = true;
			},
			onShowScoreExchangeDialog() {
				console.log('显示积分兑换弹窗')
				this.showScoreExchangeDialog = true;
			},

			// 关闭积分兑换弹窗
			closeScoreExchangeDialog() {
				this.showScoreExchangeDialog = false;
			},
			// 积分兑换成功处理
			handleExchangeSuccess() {
				this.getUserInfo(); // 刷新用户信息
				this.closeScoreExchangeDialog();
			},
			// 获取用户信息
			async getUserInfo() {
				try {
					const res = await request({
						url: '/api/user/detail',
					})
					if (res.code === 1) {
						this.userInfo = res.data
						// 更新本地存储
						uni.setStorageSync(config.userInfo, res.data)
					}
				} catch (error) {
					console.error('获取用户信息失败:', error)
				}
			},
			openSettingDialog() {
				this.closeFuncDialog();
				this.showSettingDialog = true;
			},
			openServiceDialog() {
				// console.log('openServiceDialog called');
				this.closeFuncDialog();
				this.showServiceDialog = true;
			},
			openRuleDialog() {
				// console.log('openRuleDialog called');
				this.closeFuncDialog();
				this.showRuleDialog = true;
			},
			closeRuleDialog() {
				this.showRuleDialog = false;
			},
			closeSettingDialog() {
				this.showSettingDialog = false;
			},
			closeServiceDialog() {
				this.showServiceDialog = false;
			},
			handleErrorDialogClose() {
				this.showErrorDialog = false;
				// 如果是游戏超时，退出游戏
				if (this.isGameTimeout) {
					this.isGameTimeout = false;
					this.errorDialogTitle = '错误提示'; // 重置标题
					this.Exit();
				}
				// 如果是游戏结束，退出游戏
				else if (this.isGameEnd) {
					this.isGameEnd = false;
					this.errorDialogTitle = '错误提示'; // 重置标题
					// 用户确认退出，通知子组件隐藏控制按钮
					uni.$emit('user-confirmed-exit', { success: true })
					// 延迟一下再退出，给子组件时间隐藏按钮
					setTimeout(() => {
						this.Exit()
					}, 300)
				}
			},
			toggleSetting(key) {
				this.setting[key] = !this.setting[key];

				// 如果是游戏声音设置，同步更新isVideoMuted状态
				if (key === 'gameSound') {
					this.isVideoMuted = !this.setting[key];
					console.log('toggleSetting - gameSound:', this.setting[key]);
					console.log('toggleSetting - isVideoMuted:', this.isVideoMuted);

					// 调用HeiqiPlayer的toggleAudio方法
					if (this.$refs.heiqiPlayer) {
						this.$refs.heiqiPlayer.toggleAudio()
					}
				}
			},

			// 投币弹窗相关方法
			showCoinDialog(buttons) {
				this.coinButtons = buttons || [];
				this.showCoinModal = true;
			},
			hideCoinDialog() {
				this.showCoinModal = false;
				this.coinButtons = [];
			},
			handleCoinSelect(button) {
				this.hideCoinDialog();
				// 通知子组件处理投币
				if (this.$refs.gameControl && this.$refs.gameControl.handleCoinConfirm) {
					this.$refs.gameControl.handleCoinConfirm(button);
				}
			},

			// 检测设备类型
			isMobileDevice() {
				// #ifdef H5
				const userAgent = navigator.userAgent.toLowerCase();
				const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
				const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent);
				const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

				// 检测屏幕尺寸（移动设备通常小于768px）
				const isSmallScreen = window.innerWidth <= 768;

				console.log('设备检测:', {
					userAgent: userAgent,
					isMobile: isMobile,
					isTablet: isTablet,
					isTouchDevice: isTouchDevice,
					isSmallScreen: isSmallScreen,
					screenWidth: window.innerWidth
				});

				return isMobile || (isTouchDevice && isSmallScreen);
				// #endif

				// #ifdef APP-PLUS
				// App环境默认为移动设备
				return true;
				// #endif

				// #ifdef MP
				// 小程序环境默认为移动设备
				return true;
				// #endif
			},

			// 仅在移动端申请全屏
			requestFullscreenIfMobile() {
				if (!this.isMobileDevice()) {
					console.log('PC端设备，跳过全屏申请');
					return;
				}

				console.log('移动端设备，申请全屏');
				this.requestFullscreen();
			},

			// 申请全屏
			requestFullscreen() {
				try {
					// #ifdef H5
					// H5环境下申请全屏
					const element = document.documentElement;
					if (element.requestFullscreen) {
						element.requestFullscreen().catch(err => {
							console.log('全屏申请失败:', err);
						});
					} else if (element.webkitRequestFullscreen) {
						element.webkitRequestFullscreen();
					} else if (element.mozRequestFullScreen) {
						element.mozRequestFullScreen();
					} else if (element.msRequestFullscreen) {
						element.msRequestFullscreen();
					}
					console.log('已申请全屏模式');
					// #endif

					// #ifdef APP-PLUS
					// App环境下设置全屏
					plus.navigator.setFullscreen(true);
					console.log('App已设置全屏模式');
					// #endif

					// #ifdef MP
					// 小程序环境暂不支持全屏
					console.log('小程序环境不支持全屏');
					// #endif
				} catch (error) {
					console.log('全屏申请异常:', error);
				}
			},

			// 退出全屏
			exitFullscreen() {
				try {
					// #ifdef H5
					// 检查文档是否处于活跃状态和是否处于全屏模式
					if (document.fullscreenElement || document.webkitFullscreenElement ||
						document.mozFullScreenElement || document.msFullscreenElement) {

						// 检查文档是否活跃
						if (document.visibilityState === 'visible' || !document.hidden) {
							if (document.exitFullscreen) {
								document.exitFullscreen().catch(err => {
									console.log('退出全屏失败:', err);
								});
							} else if (document.webkitExitFullscreen) {
								document.webkitExitFullscreen();
							} else if (document.mozCancelFullScreen) {
								document.mozCancelFullScreen();
							} else if (document.msExitFullscreen) {
								document.msExitFullscreen();
							}
							console.log('已退出全屏模式');
						} else {
							console.log('文档不活跃，跳过退出全屏');
						}
					} else {
						console.log('当前不在全屏模式');
					}
					// #endif

					// #ifdef APP-PLUS
					plus.navigator.setFullscreen(false);
					console.log('App已退出全屏模式');
					// #endif
				} catch (error) {
					console.log('退出全屏异常:', error);
				}
			},

			// 开始退出倒计时
			startExitCountdown() {
				this.clearExitCountdown()
				this.exitCountdown = 10
				this.isExitWaiting = true

				this.exitCountdownTimer = setInterval(() => {
					this.exitCountdown--
					if (this.exitCountdown <= 0) {
						this.clearExitCountdown()
						this.handleExitCountdownEnd()
					}
				}, 1000)
			},

			// 清除退出倒计时
			clearExitCountdown() {
				if (this.exitCountdownTimer) {
					clearInterval(this.exitCountdownTimer)
					this.exitCountdownTimer = null
				}
				this.exitCountdown = 0
				this.isExitWaiting = false
			},

			// 退出倒计时结束处理
			handleExitCountdownEnd() {
				console.log('退出倒计时结束，自动返回首页')
				uni.switchTab({
					url: '/pages/index/index'
				})
			},

			// 处理自动退出消息
			handleAutoExit(data) {
				console.log('收到自动退出消息:', data)

				// 立即断开WebRTC连接，停止视频流传输
				this.disconnectWebRTC()
				console.log('自动退出：已断开WebRTC连接')

				// 恢复横屏模式的滚动条
				this.enableLandscapeScroll()

				// 显示自动退出提示
				this.isGameTimeout = true;
				this.errorDialogTitle = '游戏超时';
				this.showErrorDialog = true;
				this.errorDialogMsg = data.message || '游戏已自动退出，请重新开始';
			},

			// 处理注册响应消息
			handleRegisterBack(data) {
				console.log('收到注册响应消息:', data)

				if (data.status === 'success' || data.code === 200) {
					// 注册成功，显示游戏控制按钮
					this.showGameControls = true
					console.log('注册成功，显示游戏控制按钮')
				} else {
					console.error('注册失败:', data.message || data.msg)
					uni.showToast({
						title: data.message || data.msg || '注册失败',
						icon: 'none'
					})
				}
			},

			// 处理游戏状态消息
			handleGameStatus(data) {
				console.log('收到游戏状态消息:', data)

				const code = data.code || 0

				// 处理501错误码：账号没分了
				if (code === 501) {
					this.handleInsufficientFunds(data.message || '账号已经没分了，请投币')
				} else {
					// 其他状态码的处理
					console.log('游戏状态更新:', code, data.message)
				}
			},

			// 处理余额不足（统一的501处理方法）
			handleInsufficientFunds(message) {
				console.log('处理余额不足:', message)

				// 显示余额不足提示框
				uni.showModal({
					title: '余额不足',
					content: message,
					showCancel: false,
					confirmText: '投币',
					success: (res) => {
						if (res.confirm) {
							// 用户点击投币，显示投币弹窗
							this.showCoinDialog()
						}
					}
				})
			},

			// 设置全屏检测
			setupFullscreenDetection() {
				// #ifdef H5
				try {
					// 监听全屏状态变化
					document.addEventListener('fullscreenchange', this.handleFullscreenChange);
					document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
					document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
					document.addEventListener('MSFullscreenChange', this.handleFullscreenChange);

					// 初始检查全屏状态
					this.checkFullscreenStatus();

					console.log('已设置iOS全屏状态检测');
				} catch (error) {
					console.log('设置全屏检测失败:', error);
				}
				// #endif
			},

			// 检查全屏状态
			checkFullscreenStatus() {
				// #ifdef H5
				const isFullscreen = !!(document.fullscreenElement ||
					document.webkitFullscreenElement ||
					document.mozFullScreenElement ||
					document.msFullscreenElement ||
					window.navigator.standalone || // iOS Safari全屏模式
					window.innerHeight === screen.height); // 检查是否占满屏幕高度

				if (isFullscreen) {
					this.disablePageScroll();
				} else {
					this.enablePageScroll();
				}
				// #endif
			},

			// 处理全屏状态变化
			handleFullscreenChange() {
				this.checkFullscreenStatus();
			},

			// 禁用页面滚动（只在全屏时）
			disablePageScroll() {
				// #ifdef H5
				try {
					// 只禁用document级别的滚动，不影响游戏元素
					document.body.style.overflow = 'hidden';
					document.documentElement.style.overflow = 'hidden';
					document.body.style.position = 'fixed';
					document.body.style.width = '100%';
					document.body.style.height = '100%';

					// 只阻止页面级别的滚动事件，不影响游戏按键
					document.addEventListener('touchmove', this.preventPageScroll, { passive: false });

					console.log('已禁用页面滚动，保持iOS全屏状态');
				} catch (error) {
					console.log('禁用页面滚动失败:', error);
				}
				// #endif
			},

			// 启用页面滚动
			enablePageScroll() {
				// #ifdef H5
				try {
					// 恢复document的滚动
					document.body.style.overflow = '';
					document.documentElement.style.overflow = '';
					document.body.style.position = '';
					document.body.style.width = '';
					document.body.style.height = '';

					// 移除事件监听器
					document.removeEventListener('touchmove', this.preventPageScroll);

					console.log('已启用页面滚动');
				} catch (error) {
					console.log('启用页面滚动失败:', error);
				}
				// #endif
			},

			// 阻止页面滚动但不影响游戏操作
			preventPageScroll(e) {
				// 检查事件目标是否是游戏控制元素
				const target = e.target;
				const gameElements = [
					'.game-ui-layer',
					'.game-controls',
					'.dpad-btn',
					'.action-btn',
					'.fire-button',
					'.game-button',
					'button'
				];

				// 如果触摸的是游戏元素，允许事件继续
				const isGameElement = gameElements.some(selector => {
					return target.closest && target.closest(selector);
				});

				if (!isGameElement) {
					// 只阻止非游戏元素的滚动
					e.preventDefault();
					return false;
				}
			},

			// 清理全屏检测
			cleanupFullscreenDetection() {
				// #ifdef H5
				try {
					document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
					document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
					document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
					document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);

					console.log('已清理全屏检测');
				} catch (error) {
					console.log('清理全屏检测失败:', error);
				}
				// #endif
			},

			// 禁用横屏模式下的滚动条
			disableLandscapeScroll() {
				// #ifdef H5
				try {
					// 保存原始的overflow样式，以便恢复
					if (!this.originalBodyOverflow) {
						this.originalBodyOverflow = document.body.style.overflow || '';
					}
					if (!this.originalHtmlOverflow) {
						this.originalHtmlOverflow = document.documentElement.style.overflow || '';
					}

					// 禁用body和html的滚动条
					document.body.style.overflow = 'hidden';
					document.documentElement.style.overflow = 'hidden';

					console.log('横屏模式：已禁用body和html滚动条');
				} catch (error) {
					console.log('禁用横屏滚动条失败:', error);
				}
				// #endif
			},

			// 启用横屏模式下的滚动条
			enableLandscapeScroll() {
				// #ifdef H5
				try {
					// 恢复原始的overflow样式
					if (this.originalBodyOverflow !== undefined) {
						document.body.style.overflow = this.originalBodyOverflow;
						this.originalBodyOverflow = undefined;
					}
					if (this.originalHtmlOverflow !== undefined) {
						document.documentElement.style.overflow = this.originalHtmlOverflow;
						this.originalHtmlOverflow = undefined;
					}

					console.log('已恢复body和html滚动条');
				} catch (error) {
					console.log('恢复横屏滚动条失败:', error);
				}
				// #endif
			}
		},
	}
</script>

<style scoped>
	/* =====================
   CSS变量定义
   ===================== */
	:root {
		--primary-color: #7d71f4;
		--primary-gradient: linear-gradient(90deg, #7d71f4 0%, #a18fff 100%);
		--secondary-color: #666666;
		--danger-color: #ff3333;
		--warning-color: #ffcc00;
		--success-color: #00c853;
		--text-primary: #222;
		--text-secondary: #888;
		--text-white: #fff;
		--bg-overlay: rgba(0, 0, 0, 0.7);
		--bg-overlay-light: rgba(0, 0, 0, 0.3);
		--border-radius-sm: 8rpx;
		--border-radius-md: 16rpx;
		--border-radius-lg: 24rpx;
		--border-radius-xl: 32rpx;
		--shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		--shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
		--shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
		--transition-fast: 0.2s;
		--transition-normal: 0.3s;
	}

	/* =====================
   主容器与横竖屏适配
   ===================== */
	.container {
		position: fixed;
		width: 100vw;
		height: 100vh;
		top: 0;
		left: 0;
		background: #000;
		overflow: hidden;
		transform: none;
		transform-origin: center;
		/* 禁用iOS上的滚动和弹性效果 */
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: none;
		touch-action: manipulation;
	}

	.container.force-landscape {
		width: 100vh;
		height: 100vw;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%) rotate(90deg);
	}

	@media screen and (orientation: landscape) {
		.container.force-landscape {
			width: 100vw !important;
			height: 100vh !important;
			top: 0 !important;
			left: 0 !important;
			transform: none !important;
		}
	}

	/* =====================
   播放器相关样式
   ===================== */
	.easy-player-container,
	.heiqi-player-container {
		position: absolute;
		z-index: 1;
	}

	/* =====================
   游戏UI层
   ===================== */
	.game-ui-layer {
		position: absolute;
		width: 100vw;
		height: 100vh;
		top: 0;
		left: 0;
		pointer-events: auto;
		z-index: 10;
	}

	.container.force-landscape .game-ui-layer {
		width: 100vh;
		height: 100vw;
	}

	/* =====================
   状态与头像容器
   ===================== */
	.status-container,
	.avatar-container,
	.button-container,
	.game-controls {
		position: relative;
		z-index: 2;
	}

	.status-container {
		position: absolute;
		top: 5%;
		left: 5%;
		z-index: 3;
		background: var(--bg-overlay);
		border-radius: var(--border-radius-md);
		padding: 10px 15px;
		backdrop-filter: blur(4px);
	}

	.status-item {
		color: var(--text-white);
		font-size: 16px;
		line-height: 1.5;
		text-shadow: 1px 1px 2px #000;
	}

	.avatar-container {
		position: absolute;
		top: 5%;
		right: 5%;
		z-index: 3;
		display: flex;
		align-items: center;
		background: var(--bg-overlay);
		border-radius: 30px;
		padding: 5px 10px;
		backdrop-filter: blur(4px);
	}

	.avatar-item,
	.avatar-wrapper {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin: 0 5px;
	}

	.avatar-image {
		width: 20px;
		height: 20px;
		border-radius: 50%;
		border: 2px solid gold;
	}

	.avatar-name {
		color: var(--text-white);
		font-size: 12px;
		margin-top: 2px;
		max-width: 60px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* 功能按钮通用样式 */
	.exit-button,
	.mute-button,
	.mode-switch-button {
		width: 30px;
		height: 30px;
		border-radius: 20px;
		border: none;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-left: 10px;
		padding: 0;
		transition: transform var(--transition-fast);
	}

	.exit-button {
		background-color: var(--danger-color);
	}

	.mute-button {
		background-color: var(--secondary-color);
	}

	.mode-switch-button {
		background-color: #2196F3;
	}

	.exit-icon,
	.mute-icon,
	.mode-switch-icon {
		color: var(--text-white);
		font-size: 18px;
		line-height: 1;
		font-weight: bold;
	}

	.exit-button:active,
	.mute-button:active,
	.mode-switch-button:active {
		transform: scale(0.95);
	}

	.player-mode-info {
		font-size: 14px;
		opacity: 0.8;
	}

	/* =====================
   倒计时条
   ===================== */
	.idle-countdown-bar {
		position: absolute;
		right: 70px;
		top: 75px;
		background: rgba(0,0,0,0.3);
		border-radius: 16px;
		height: 24px;
		min-width: 50px;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 12px;
		z-index: 20;
		border: 2px solid #FFD600;
		margin-right: 15rpx;
	}

	.container.force-landscape .idle-countdown-bar {
		right: 70px;
		top: 75px;
	}

	.idle-countdown-num {
		color: #FFD600;
		font-size: 16px;
		font-weight: bold;
		letter-spacing: 1px;
	}

	/* =====================
   底部按钮与控制区
   ===================== */
	.button-container {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 20%;
		display: flex;
		flex-direction: row;
		z-index: 2;
		background: rgba(0, 0, 0, 0.5);
		backdrop-filter: blur(4px);
	}

	.button-column {
		flex: 1;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: flex-start;
		padding-top: 5px;
	}

	.button-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		height: 100%;
		width: 80%;
	}

	.game-button {
		width: 50%;
		height: 10px;
		min-height: 25px;
		background: var(--warning-color);
		border-radius: var(--border-radius-md);
		display: flex;
		justify-content: center;
		align-items: center;
		color: var(--text-white);
		font-weight: bold;
		border: none;
		box-shadow: var(--shadow-md);
		font-size: 14px;
		margin-bottom: 8px;
		transition: transform var(--transition-fast);
	}

	.game-button:active {
		transform: scale(0.95);
	}

	.game-controls {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 4;
		display: flex;
		justify-content: space-between;
		padding: 0 20px;
		pointer-events: none;
		width: 100%;
		height: 60%;
	}

	/* =====================
   加载动画样式 - 使用固定px单位，不受屏幕方向影响
   ===================== */
	.loading-mask {
		position: fixed;
		inset: 0;
		background: #fff;
		z-index: 9999;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.loading-content {
		width: 300px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.loading-logo {
		width: 160px;
		margin: 0 auto 30px auto;
		display: block;
	}

	.loading-bar-wrap {
		width: 250px;
		margin: 0 auto;
		position: relative;
	}

	.loading-text {
		font-size: 14px;
		color: #333;
		margin-bottom: 4px;
		font-weight: bold;
		text-align: center;
		width: 100%;
	}

	.loading-bar-bg {
		width: 100%;
		height: 6px;
		background: #eee;
		border-radius: 3px;
		overflow: hidden;
		margin-bottom: 2px;
	}

	.loading-bar {
		height: 100%;
		background: #ffe066;
		width: 0;
		border-radius: 3px;
		transition: width 0.3s ease;
	}

	.loading-percent {
		position: absolute;
		right: 0;
		top: -16px;
		font-size: 12px;
		color: #333;
	}

	/* =====================
   方向键与功能按钮
   ===================== */
	.directional-pad {
		width: 15vmax;
		height: 15vmax;
		position: absolute;
		filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.5));
		pointer-events: auto;
		left: 5vmax;
		bottom: 5vmax;
	}

	.dpad-circle {
		width: 100%;
		height: 100%;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.15);
		backdrop-filter: blur(4px);
		position: relative;
		border: 2px solid rgba(255, 255, 255, 0.3);
	}

	.dpad-circle::after {
		content: '';
		position: absolute;
		width: 20px;
		height: 20px;
		background: rgba(255, 255, 255, 0.3);
		border-radius: 50%;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	.dpad-btn {
		position: absolute;
		width: 5vmax;
		height: 5vmax;
		border: none;
		background: transparent;
		padding: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		color: white;
		cursor: pointer;
		transition: transform 0.02s;
		transform-origin: center;
	}

	.dpad-btn::before {
		content: '';
		position: absolute;
		width: 0;
		height: 0;
		border-style: solid;
		transition: opacity var(--transition-fast), filter var(--transition-fast);
	}

	.dpad-btn:active {
		transform: scale(1.1);
	}

	.dpad-btn:active::before {
		opacity: 0.8;
	}

	/* 方向键位置与箭头 */
	.up {
		top: -5%;
		left: 33%;
	}

	.up::before {
		border-width: 0 15px 26px 15px;
		border-color: transparent transparent rgba(255, 255, 255, 0.9) transparent;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -30%);
	}

	.down {
		bottom: -8%;
		left: 33%;
	}

	.down::before {
		border-width: 26px 15px 0 15px;
		border-color: rgba(255, 255, 255, 0.9) transparent transparent transparent;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -70%);
	}

	.left {
		left: 0%;
		top: 33%;
	}

	.left::before {
		border-width: 15px 26px 15px 0;
		border-color: transparent rgba(255, 255, 255, 0.9) transparent transparent;
		top: 50%;
		left: 50%;
		transform: translate(-70%, -50%);
	}

	.right {
		right: 0%;
		top: 33%;
	}

	.right::before {
		border-width: 15px 0 15px 26px;
		border-color: transparent transparent transparent rgba(255, 255, 255, 0.9);
		top: 50%;
		left: 50%;
		transform: translate(-30%, -50%);
	}

	/* 功能按钮区域 */
	.action-buttons {
		position: absolute;
		width: 15vmax;
		height: 15vmax;
		pointer-events: auto;
		bottom: 5vmax;
		right: 5vmax;
	}

	.action-btn {
		width: 8vmax;
		height: 8vmax;
		border-radius: 50%;
		color: var(--text-white);
		display: flex;
		align-items: center;
		justify-content: center;
		border: 2px solid rgba(255, 255, 255, 0.5);
		box-shadow: var(--shadow-md);
		position: absolute;
		transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
		backdrop-filter: blur(4px);
	}

	.action-btn:active {
		transform: scale(0.98);
		filter: brightness(0.9);
		opacity: 0.9;
	}

	.action-btn>text,
	.fire-button>text {
		white-space: nowrap;
	}

	.fire-button {
		width: 8vmax;
		height: 8vmax;
		border-radius: 50%;
		background: rgba(255, 0, 0, 0.8);
		position: absolute;
		color: var(--text-white);
		font-size: 2vmax;
		display: flex;
		align-items: center;
		justify-content: center;
		right: 0vmax;
		bottom: -8vmax;
		border: 2px solid rgba(255, 255, 255, 0.5);
		transform: translate(-50%, -50%);
		z-index: 2;
		backdrop-filter: blur(4px);
		transition: background var(--transition-fast);
	}

	.fire-button:active {
		background: rgba(255, 0, 0, 0.7);
	}

	.bet-btn {
		background: rgba(255, 165, 0, 0.8);
		left: -2.5vmax;
		bottom: 4vmax;
		width: 7vmax;
		height: 7vmax;
		font-size: 1.5vmax;
	}

	.weapon-switch-btn {
		background: rgba(0, 150, 255, 0.8);
		left: -6vmax;
		bottom: -4vmax;
		width: 7vmax;
		height: 7vmax;
		font-size: 1.5vmax;
	}

	.auto-btn {
		background: rgba(0, 200, 0, 0.8);
		left: 6vmax;
		bottom: 5vmax;
		width: 7vmax;
		height: 7vmax;
		font-size: 1.5vmax;
	}

	.auto-btn.auto-active {
		background: rgba(255, 0, 0, 0.8);
	}

	/* 弹窗样式 */
	.modal-image {
	  width: 50px !important;
	  height: 40px !important;
	  object-fit: contain;
	}
</style>

