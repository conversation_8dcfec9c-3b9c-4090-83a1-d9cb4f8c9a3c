// utils/websocket.js

export class GameWebSocket {
	constructor(url) {
		this.url = url;
		this.socketTask = null; // 使用 uniapp 的 SocketTask
		this.gameSn = null;
		this.heartbeatTimer = null; // 心跳定时器
		this.reconnectAttempts = 0; // 重连次数
		this.maxReconnectAttempts = 3; // 最大重连次数
		this.readyState = 3; // 初始状态为 CLOSED
		this.reconnectTimer = null; // 新增重连定时器引用
		this.shouldReconnect = true; // 控制是否允许重连

		// 定义 WebSocket 状态常量（兼容所有平台）
		this.states = {
			CONNECTING: 0,
			OPEN: 1,
			CLOSING: 2,
			CLOSED: 3
		};
		// 事件回调（可根据需要扩展）
		this.callbacks = {
			onOpen: [],
			onClose: [],
			onError: [],
			onMessage: []
		};
	}

	/**
	 * 连接 WebSocket 服务器
	 */
	connect(gameSn) {
		if (this.socketTask && this.readyState === 1) {
			console.log('WebSocket 已连接');
			return;
		}

		// 更新状态为 CONNECTING
		this.readyState = 0;

		// 使用 uniapp 的 API 创建连接
		this.socketTask = uni.connectSocket({
			url: this.url,
			success: () => {
				this.readyState = 0; // CONNECTING
				console.log('WebSocket 连接初始化成功');
			},
			fail: (err) => {
				this.readyState = 3; // CLOSED
				console.error('WebSocket 连接失败:', err);
			}
		});

		// 监听事件
		this.socketTask.onOpen(() => {
			console.log('WebSocket 连接已建立');
			this.readyState = 1; // OPEN
			this._resetReconnectAttempts();

			// 发送注册消息
			this.send({
				type: 'register',
				client_type: 'uniapp',
				gameSn: gameSn,
			});
			

			this._startHeartbeat();
			this.callbacks.onOpen.forEach(cb => cb());
		});

		this.socketTask.onMessage((res) => {
			try {
				const data = JSON.parse(res.data);
				console.log('收到消息:', data);

				if (data.type === 'heartbeat_ack') return;
				this.callbacks.onMessage.forEach(cb => cb(data));
			} catch (e) {
				console.error('消息解析失败:', e);
			}
		});

		this.socketTask.onClose(() => {
			console.log('WebSocket 连接关闭');
			this.readyState = 3; // CLOSED
			this._stopHeartbeat();
			this.callbacks.onClose.forEach(cb => cb());

			// 只在允许时重连
			if (this.shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
				this.reconnectTimer = setTimeout(() => { // 保存定时器引用
					this.reconnectAttempts++;
					this.connect(gameSn);
				}, 3000);
			}
		});

		this.socketTask.onError((err) => {
			console.error('WebSocket 错误:', err);
			this.callbacks.onError.forEach(cb => cb(err));
		});
	}

	/**
	 * 发送消息
	 * @param {Object} data - 符合协议的消息对象
	 */
	send(data) {
		if (this.readyState === 1) {
			this.socketTask.send({
				data: JSON.stringify(data),
				success: () => {
					// console.log('消息发送成功:', data);
				},
				fail: (err) => {
					console.error('消息发送失败:', err);
				}
			});
		} else {
			console.error('WebSocket 未连接');
		}
	}

	/**
	 * 发送消息
	 * @param {string} direction - up/down/left/right
	 */
	sendMessage(type,number,direction) {
		this.send({
			type: type,
			number:number,
			status: direction,
			source: 'uniapp',
			timestamp: Date.now()
		});
	}

	/**
	 * 关闭连接
	 */
	disconnect() {
		// 标记不进行重连
		this.shouldReconnect = false;
		// 清除所有定时器
		clearTimeout(this.reconnectTimer);
		this._stopHeartbeat();
		if (this.socketTask) {
			this.socketTask.close();
		}
	}

	// --- 私有方法 ---
	_startHeartbeat() {
		this.heartbeatTimer = setInterval(() => {
			this.send({
				type: 'heartbeat'
			});
		}, 5000); // 5 秒发送一次心跳
	}

	_stopHeartbeat() {
		if (this.heartbeatTimer) {
			clearInterval(this.heartbeatTimer);
			this.heartbeatTimer = null;
		}
	}

	_resetReconnectAttempts() {
		this.reconnectAttempts = 0;
	}
	// --- 私有方法 ---
	_getReadyState() {
		// 小程序中无法直接获取 readyState，需通过自定义状态管理
		return this.socketTask ? this.states.OPEN : this.states.CLOSED;
	}

	// --- 事件注册 ---
	on(eventName, callback) {
		if (this.callbacks[eventName]) {
			this.callbacks[eventName].push(callback);
		}
	}
}