import config from '@/config.js'

const request = (options) => {
	return new Promise((resolve, reject) => {
		const token = uni.getStorageSync(config.tokenKey)

		uni.request({
			url: config.baseUrl + options.url,
			method: options.method || 'POST',
			data: options.data || {},
			header: {
				'Content-Type': 'application/json',
				'Authorization': token ? `Bearer ${token}` : '',
				'token': token || '', // 添加FastAdmin需要的HTTP_TOKEN头
			},
			success: (res) => {
				if (res.data.code !== 1) {
					uni.showToast({
						title: res.data.msg || "请求失败",
						icon: 'none'
					})
					// token过期处理
					if (res.data.code === 401) {
						uni.removeStorageSync(config.tokenKey)
						uni.reLaunch({
							url: '/pages/login/index'
						})
					}
					// 如果 code 不为 1，也需要 resolve，以便业务层可以根据 res.code 处理
					resolve(res.data) 
				} else {
					resolve(res.data)
				}
			},
			fail: (error) => {
				uni.showToast({
					title: '网络异常',
					icon: 'none'
				})
				reject(error)
			}
		})
	})
}

export default request