<template>
  <view
    v-if="show"
    class="setting-dialog-mask"
    :class="{ landscape: landscape }"
    @click="onClose"
  >
    <view
      class="setting-dialog"
      :class="{ landscape: landscape }"
      @click.stop
    >
      <view class="setting-dialog-title-row">
        <view class="setting-dialog-title">{{ title }}</view>
        <view class="setting-dialog-close-btn" @click.stop="onClose">
          <text class="close-x">×</text>
        </view>
      </view>
      <scroll-view
        class="setting-dialog-content"
        scroll-y="true"
        :show-scrollbar="true"
      >
        <slot />
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BaseDialog',
  props: {
    show: Boolean,
    title: String,
    onClose: Function,
    landscape: Boolean
  }
}
</script>

<style scoped>
.setting-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 100010;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 横屏时不再旋转，只调整宽高 */
.setting-dialog-mask.landscape {
  /* 可根据需要调整对齐方式 */
}
.setting-dialog {
  width: 600rpx;
  height: 400rpx;
  max-width: 600rpx;
  min-width: 500rpx;
  max-height: 800rpx;
  min-height: 400rpx;
  background: #301E46;  /* 统一紫色主题 */
  border-radius: 32rpx;
  box-shadow: 0 4rpx 16rpx #0006;
  padding: 32rpx 32rpx 24rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.setting-dialog.landscape {
  width: 480rpx;
  height: auto;
  max-width: 480rpx;
  min-width: 400rpx;
  max-height: 600rpx;
  min-height: 300rpx;
  /* 不再使用 transform: rotate(-90deg) */
}
.setting-dialog-title-row {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  flex-shrink: 0;
  position: relative;
}
.setting-dialog-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;  /* 白色标题 */
  text-align: center;
  flex: 1;
}
.setting-dialog-close-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);  /* 半透明白色背景 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0;
  cursor: pointer;
}
.close-x {
  font-size: 32rpx;
  color: #fff;  /* 白色关闭按钮 */
  font-weight: bold;
  line-height: 1;
}
.setting-dialog-content {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  font-size: 28rpx;
  color: #444;
  line-height: 1.7;
  text-align: center;
  word-break: break-all;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style> 