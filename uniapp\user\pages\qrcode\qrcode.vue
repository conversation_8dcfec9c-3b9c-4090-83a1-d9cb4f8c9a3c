<template>
  <view class="qrcode-container">
    <view class="qrcode-circle">
      <sansnn-u-qrcode :value="qrText" size="200" canvas-id="qrcode-canvas"></sansnn-u-qrcode>
    </view>
    <view class="qrcode-tip">请让商家扫码核销</view>
    <view class="qrcode-expire">
      <text v-if="remainSeconds > 0">二维码将在 {{ Math.floor(remainSeconds/60) }}分{{ remainSeconds%60 }}秒后过期</text>
      <text v-else>二维码已过期，请刷新页面</text>
    </view>
  </view>
</template>

<script>
import config from '@/config.js'
import SansnnUQrcode from '@/uni_modules/Sansnn-uQRCode/components/u-qrcode/u-qrcode.vue'
import request from '@/utils/request.js'

export default {
  components: {
    'sansnn-u-qrcode': SansnnUQrcode
  },
  data() {
    return {
      qrText: '',
      expireSeconds: 300,
      remainSeconds: 300,
      timer: null
    }
  },
  async onLoad() {
    const userInfo = uni.getStorageSync(config.userInfo) || {}
    const userId = userInfo.id || ''
    try {
      const res = await request({
        url: '/api/user/gen_verify_qrcode',
        method: 'POST',
        data: { user_id: userId }
      })
      if (res && res.code === 1 && res.data) {
        const { user_id, ts, sign } = res.data
        this.qrText = `userId=${user_id}&ts=${ts}&sign=${sign}`
        const now = Math.floor(Date.now() / 1000)
   
        if (this.remainSeconds < 0) this.remainSeconds = 0
        this.startTimer()
      } else {
        this.qrText = '二维码生成失败'
        uni.showToast({ title: res.msg || '二维码生成失败', icon: 'none' })
      }
    } catch (e) {
      this.qrText = '二维码生成失败'
      uni.showToast({ title: '二维码生成失败', icon: 'none' })
    }
  },
  onUnload() {
    if (this.timer) clearInterval(this.timer)
  },
  methods: {
    startTimer() {
      if (this.timer) clearInterval(this.timer)
      this.timer = setInterval(() => {
        if (this.remainSeconds > 0) {
          this.remainSeconds--
        } else {
          clearInterval(this.timer)
        }
      }, 1000)
    }
  }
}
</script>

<style scoped>
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #180F29;
  padding: 40rpx;
}
.qrcode-circle {
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 80rpx;
  border: 4rpx solid #2A1840;
}
.qrcode-tip {
  margin-top: 80rpx;
  font-size: 32rpx;
  color: #fff;
  text-align: center;
  font-weight: 500;
}
.qrcode-expire {
  margin-top: 40rpx;
  font-size: 28rpx;
  color: #FFB366;
  text-align: center;
  padding: 20rpx;
  background: rgba(255, 179, 102, 0.1);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 179, 102, 0.3);
  max-width: 600rpx;
}
</style>