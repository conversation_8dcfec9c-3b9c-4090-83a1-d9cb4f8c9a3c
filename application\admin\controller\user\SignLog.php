<?php

namespace app\admin\controller\user;

use app\common\controller\Backend;
use think\Db;

/**
 * 签到记录
 * @icon fa fa-user
 */
class SignLog extends Backend
{
    /**
     * @var \app\admin\model\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            $admin = get_admin_info();
            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
                //代理只查看自己的用户
                $where_arr['u.p_id'] = $admin['admin_id'];
            }

            if ($search_arr) {
                //状态
                if (isset($search_arr['status'])){
                    $search_arr['l.status'] = $search_arr['status'];
                    unset($search_arr['status']);
                }
                //创建时间
                if (isset($search_arr['createtime'])) {
                    $create_time = explode(' - ',$search_arr['createtime']);
                    $where_arr['l.createtime'] = ['between',[strtotime($create_time[0]),strtotime($create_time[1])]];
                    unset($search_arr['createtime']);
                }
            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('user_sign')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->where($where)
                ->where($where_arr)
                ->count();

            $list = Db::name('user_sign')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->where($where)
                ->where($where_arr)
                ->order('l.id', $order)
                ->field('l.*,u.nickname,u.mobile')
                ->limit($offset, $limit)
                ->select();

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }



}
